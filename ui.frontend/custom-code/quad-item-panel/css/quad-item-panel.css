.priority-require-container {
    padding-top: 12px;
    padding-bottom: 12px;
}

.priority-require-wrapper {
    color: #fff;
    position: relative;
}

.priorityrequire-root {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

.priorityrequire-container {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
}

.priorityrequire-item {
    flex-grow: 0;
    max-width: 52%;
    flex-basis: 52%;
    padding: 12px;
    padding-left: 0;
}

.priorityrequire-label {
    color: #b4a393;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.priorityrequire-item>h2 {
    font-weight: 300;
    line-height: 1.25;
    font-size: 1.75rem;
}

.priorityrequire-item>p {
    margin: 8px 0 0;
    color: #c5c5c5;
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5;
}

.priorityrequire-inner-list {
    padding-top: 32px;
    display: flex;
    flex-wrap: wrap;
    gap: 24px
}

.priorityrequire-inner-item {
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 47%;
}

.priorityrequire-large-inner-item {
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
}

.priorityrequire-inner-content {
    border: 1px solid transparent;
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-image: linear-gradient(#000, #000), linear-gradient(#ecd7b0, #bd7e50 40.68%, #000 84.82%);
    border-radius: 8px;
}

.priority-inner-icon {
    display: flex;
    padding: 16px;
}

.text-base {
    font-size: 1rem; /* 16px */
    line-height: 1.5rem; /* 24px */
}
  
.font-semibold {
    font-weight: 600;
}


.priorityrequire-large-inner-item>.priority-inner-icon {
    padding: 0;
}

.priority-inner-icon>img,
.priority-inner-icon>p>img {
    width: 16px;
    height: 16px;
    color: #fff;
    position: relative;
}

.priority-inner-icon>img {
    margin-right: 16px;
}

.priority-inner-icon>p>strong>img {
    margin-right: 8px;
    margin-bottom: -2px;
}

.priority-inner-icon>div>p:nth-child(1) {
    font-weight: 600;
}

.priority-inner-icon>div>p:nth-child(2),
.priorityrequire-large-inner-item-text>p {
    margin-top: 8px;
    color: #e2ded7;
}

.priorityrequire-large-inner-item-subtitle {
    color: #e2ded7;
}

.priorityrequire-large-inner-item-text>p{
    line-height: 1.2;
}

.priorityrequire-button-link {
    max-width: 310px;
    margin-top: 32px;
}

.priorityrequire-button-link.full-width {
    max-width: 100%;
}

.priorityrequire-button-link>a {
    position: relative;
    overflow: hidden;
    background-color: transparent;
    display: inline-flex;
    padding: 16px 24px;
    border-radius: 8px;
    border: none;
    min-width: max-content;
    width: 100%;
    z-index: 1;
    justify-content: space-between;
}

.priorityrequire-button-link>a::before {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #8d8175 -24.11%, #35322b 305.36%);
    top: 0;
    left: 0;
    z-index: -1;
    opacity: 1;
    transition: all .6s ease-out;
}

.priorityrequire-button-link>a>span {
    line-height: 1.5;
    font-weight: 600;
}

.priorityrequire-button-link>a:hover::before {
    background: var(--primary-white);
}

.priorityrequire-button-link>a:hover span {
    color: var(--gray-600);
}

.priorityrequire-button-link>a:hover .priorityrequire-button-arrow{
    filter: invert(0.8);
}

.priorityrequire-large-secondary-text {
    margin-top: 16px; 
    width: fit-content; 
    color: #e2ded7;
}

.priorityrequire-large-secondary-text > .cta-custom {
    display: flex;
    align-items: center;
    gap: 12px;
}

.priorityrequire-large-secondary-text > .cta-custom:hover {
    text-decoration: underline;
}

@media (max-width: 1025px) {
    .priorityrequire-item {
        padding: 0;
    }

    .priority-require-container {
        padding-top: 16px;
        padding-bottom: 16px;
    }
}

@media (max-width: 992px) {
    .priority-require-container {
        margin-top: 0;
        margin-bottom: 0;
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .priorityrequire-inner-item {
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
    }

    .footer-info {
        flex-direction: column;
        grid-gap: 8px;
        gap: 8px;
    }
}

@media (max-width: 767px) {
    .tcb-largeImage{
        padding-bottom: 0 !important;
    }

    .tcb-sectionContainer .tcb-bgImage {
        background-position: unset !important;
        height: 100%;
    }

    .priorityrequire-information-list {
        padding-top: 240px;
        padding-bottom: 32px;
    }

    .priorityrequire-item {
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
        padding: 0;
    }

    .priorityrequire-inner-list {
        gap: 16px
    }

    .priorityrequire-button-link>a {
        padding: 12px 16px;
    }

    .priorityrequire-button-link {
        max-width: 328px;
    }
}

@media screen and (max-width: 320px) {
    .tcb-sectionContainer .tcb-bgImage:has(.priority-require-container) {
        background-position: 50% !important;
    }
}
