setInterval(function () {
    let container = document.querySelector('.style__TabHeadings-sc-3bnrxj-0');
    if (container) {
      let tabLink = container.querySelectorAll('.style__TabHeading-sc-3bnrxj-4.hLwJGj:not(.mvp-button-back):not(.mvp-button)');
      if (tabLink) {
        tabLink.forEach(box => {
          let dataTrackingEvent = box.getAttribute('data-tracking-click-event');
          if (!dataTrackingEvent) {
            box.setAttribute('data-tracking-click-event', 'linkClick');
            box.setAttribute('data-tracking-click-info-value', "{'linkClick' : '" + box.textContent + "'}");
            box.setAttribute('data-tracking-web-interaction-value', "{'webInteractions': {'name': 'Private Banking Banner','type': 'other'}}");
          } 
        });
      }
      let cta = container.querySelectorAll('.style__TabHeading-sc-3bnrxj-4.hLwJGj.mvp-button');
      if (cta) {
        cta.forEach(box => {
          let dataTrackingEvent = box.getAttribute('data-tracking-click-event');
          if (!dataTrackingEvent) {
            box.setAttribute('data-tracking-click-event', 'cta');
            box.setAttribute('data-tracking-click-info-value', "{'cta' : 'icon_contact_us'}");
            box.setAttribute('data-tracking-web-interaction-value', "{'webInteractions': {'name': 'Private Banking Banner','type': 'other'}}");
          }        
        });
      }
    }  
  }, 150);