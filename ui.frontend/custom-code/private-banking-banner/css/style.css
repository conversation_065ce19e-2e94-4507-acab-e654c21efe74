html {
    overflow: unset !important;
}

body {
    height: auto !important;
}

main {
    max-width: 1920px;
    margin-left: auto;
    margin-right: auto;
}

#mvp2 {
    z-index: 0;
    position: relative;
}

footer {
    position: relative;
}

.header {
    @media screen and (min-width: 1025px) {
        position: fixed !important;
        z-index: 1 !important;
    }
}

.private-banking-banner-container {
    width: 100%;
}

.csZnSq {
    position: fixed !important;
    top: 120px !important;
}

.fiHlIw {
    top: 45vh !important;
}

.ePNOaC {
    margin-top: 11% !important;
}

.style__TabWrapper-sc-1ajux4j-0 {
    padding-top: 0 !important;
}

.style__Title-sc-q7j1l4-0 {
    opacity: 1 !important;
}

.style__ImageParalaxContain-sc-1tr3r30-4,
.style__ImageBox-sc-1tr3r30-5,
.style__GIT-sc-eecg8g-0,
.style__ParallaxItemContent-sc-1tr3r30-8 {
    translate: 0 100px !important;
}

@media (max-width: 1280px) {
    html {
        overflow: auto !important;
    }

    .style__Title-sc-q7j1l4-0 {
        font-size: 32px !important;
        line-height: 42px !important;
    }

    .gDLorE {
        margin-top: 5% !important;
    }
}

@media (max-width: 1025px) {
    .header {
        padding-right: unset !important;
    }
    
    .style__ImageParalaxContain-sc-1tr3r30-4,
    .style__ImageBox-sc-1tr3r30-5,
    .style__GIT-sc-eecg8g-0,
    .style__ParallaxItemContent-sc-1tr3r30-8 {
        translate: 0 65px !important;
    }

    .csZnSq {
        position: absolute !important;
        top: 70px !important;
    }
}

@media (max-width: 992px) {
    .dBqkrX {
        font-size: 32px !important;
        line-height: 40px !important;
    }
}

@media (max-width: 769px) {
    div[role="tablist"] {
        top: calc(100% - 150px) !important;
        bottom: unset !important;
    }

    .fiHlIw {
        top: calc(50% - 52px) !important;
    }
}
