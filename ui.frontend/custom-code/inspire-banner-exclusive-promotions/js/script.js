setTimeout(function () {
    $(document).ready(function () {
        $(window).on('resize', function () {
            changeRegisterBtnPosition();
        });
        changeRegisterBtnPosition();
        
        function changeRegisterBtnPosition() {
            let $bannerImage = $('#tcb-inspire-banner');
            let $bannerImageMobile = $('#tcb-inspire-banner-mobile');
            let naturalRatio = $bannerImage[0].naturalWidth / $bannerImage[0].naturalHeight;
            let renderedRatio = $bannerImage.width() / $bannerImage.height();
            let $bannerWrapper = $('#tcb-inspire-banner-wrapper');
            // case 1: width of image fit width of wrapper
            let btnLeft = ($bannerWrapper.width() - $bannerImage[0].clientWidth) / 2;
            // case 2: height of image fit height of wrapper
            if (renderedRatio > naturalRatio) {
                btnLeft = ($bannerWrapper.width() - $bannerImage.height() * naturalRatio) / 2;
            }
            btnLeft = btnLeft > 0 ? btnLeft : 0;
            let $registerBtn = $('#tcb-inspire-register');
            $registerBtn.css({left: btnLeft});
            $registerBtn.removeClass('hide');

            // The image wrapper is absolute and cannot be resized according to the content inside with css, so it must use javascript
            if ($(window).width() <= 640) {
                $('.tcb-hero-banner.inspire').height($bannerImageMobile.height());
            } else {
                $('.tcb-hero-banner.inspire').height($bannerImage.height());
            }        
        }

        //scroll to inspire privilege
        let $footerContentItem = $('.footer-content__item');
        $footerContentItem.click(function() {
            let inspirePrivilegeSectionId = $(this).attr('data-scroll-id');
            if (window.innerWidth > 767){
                $('.inspire-exclusive__container[responsive-type="desktop"] .tab-menu-item:eq(1)').click();
                let $inspireTabsSection = $('.inspire-exclusive__container[responsive-type="desktop"] [role="tabpanel"]:eq(1)');
                let $inspirePrivilegeSection = $inspireTabsSection.find('.inspire-privilege:eq(' + inspirePrivilegeSectionId + ')');
                if ($inspirePrivilegeSection.length) {
                    setTimeout(() => {
                        window.scrollTo({ top: $inspirePrivilegeSection.position().top });
                    }, 400);
                }
            }
            else {
                $('.inspire-exclusive__container[responsive-type="mobile"] .tab-menu-mobile-item.active').removeClass('active');
                $('.inspire-exclusive__container[responsive-type="mobile"] .tab-menu-mobile-item:eq(1)').addClass('active');
                let $inspireTabsSection = $('.inspire-exclusive__container[responsive-type="mobile"] .tab-menu-mobile-item:eq(1)');
                let $inspirePrivilegeSection = $inspireTabsSection.find('.inspire-privilege:eq(' + inspirePrivilegeSectionId + ')');
                if ($inspirePrivilegeSection.length) {
                    window.scrollTo({ top: $inspirePrivilegeSection.position().top + 1200 });
                }
            }
        })
    });
}, 500);