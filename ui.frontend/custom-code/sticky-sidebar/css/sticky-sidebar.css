@keyframes slideLeftIn {
  0% {
    right: -50%;
  }
  100% {
    right: 0;
  }
}
@keyframes slideBottomIn {
  0% {
    bottom: -50%;
  }
  100% {
    bottom: 0;
  }
}
.sidebar-menu {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
  animation: 0.5s ease-out 0s 1 slideLeftIn;
}
.sidebar-menu .sidebar-menu_item:hover .sidebar-menu_item-title {
  color: var(--primary-black);
}
.sidebar-menu:hover .sidebar-menu_background {
  width: 250px;
  opacity: 1;
  transition: all 0.5s ease-in-out;
}
.sidebar-menu_chat {
  width: 60px;
  height: 130px;
  position: relative;
}
.sidebar-menu_chat-content {
  content: "";
  top: -1px;
  right: 0;
  bottom: 0;
  left: 0;
  background: var(--primary-white);
  clip-path: path("M0.158762 0.5C0.0540066 1.31855 0 2.15297 0 3C0 11.4953 5.43252 18.7218 13.0131 21.395C13.9605 21.6104 14.8937 21.8631 15.811 22.1515C16.3902 22.2624 16.75 22.5 16.75 22.5C16.8164 22.5219 16.75 22.5 16.9244 22.5219C30.9462 27.4425 41 40.7972 41 56.5C41 72.2028 30.9462 85.5575 16.9244 90.4781C16.75 90.5 16.75 90.5 16.75 90.5C16.75 90.5 16.3888 90.6668 15.811 90.8485C14.8936 91.1369 13.9605 91.3896 13.0132 91.6049C5.43254 94.2782 0 101.505 0 110C0 120.77 8.73045 129.5 19.5 129.5H60V0.5H0.158762Z");
  position: absolute;
  z-index: 1;
}
.sidebar-menu_chat-shadow {
  filter: blur(8px);
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: absolute;
}
.sidebar-menu_chat-shadow:after {
  content: "";
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.1);
  clip-path: path("M0.158762 0.5C0.0540066 1.31855 0 2.15297 0 3C0 11.4953 5.43252 18.7218 13.0131 21.395C13.9605 21.6104 14.8937 21.8631 15.811 22.1515C16.3902 22.2624 16.75 22.5 16.75 22.5C16.8164 22.5219 16.75 22.5 16.9244 22.5219C30.9462 27.4425 41 40.7972 41 56.5C41 72.2028 30.9462 85.5575 16.9244 90.4781C16.75 90.5 16.75 90.5 16.75 90.5C16.75 90.5 16.3888 90.6668 15.811 90.8485C14.8936 91.1369 13.9605 91.3896 13.0132 91.6049C5.43254 94.2782 0 101.505 0 110C0 120.77 8.73045 129.5 19.5 129.5H60V0.5H0.158762Z");
  position: absolute;
}
.sidebar-menu_section {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  background: var(--primary-background);
  gap: 24px;
  padding: 24px 16px 16px;
}
.sidebar-menu_section--top {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
  color: var(--secondary-grey-100);
}
.sidebar-menu_item {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.sidebar-menu_item:hover .sidebar-menu_item-icon {
  filter: none;
  opacity: 1;
}
.sidebar-menu_item-title {
  position: absolute;
  top: 0;
  right: 60px;
  width: 0;
  overflow: hidden;
  text-align: right;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  opacity: 1;
  border-bottom: 1px solid var(--light-border);
}
.sidebar-menu_item-text {
  margin-top: 8px;
  padding-bottom: 20px;
  cursor: pointer;
  width: 100%;
  line-height: 1.25;
}
.sidebar-menu_item-title--for-floating {
  right: 90px;
  top: -10px;
  border-bottom: none;
  cursor: pointer;
  color: var(--secondary-grey-100);
}
.sidebar-menu_item-icon {
  filter: saturate(0) brightness(100%);
  opacity: 0.4;
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}
.sidebar-menu_item-title:not(.sidebar-menu_item-title--for-floating) {
  color: var(--secondary-mid-grey-100);
  min-height: 37px;
}
.sidebar-menu_background {
  background: rgba(245, 245, 245, 0.95);
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  border-top-left-radius: 30px;
  border-bottom-left-radius: 30px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  opacity: 0;
  width: 0;
}
.float-chat-icon {
  width: 58px;
  height: 58px;
  background: var(--primary-red) url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/send-message.svg") no-repeat center;
  position: relative;
  top: 26px;
  right: 24px;
  border-radius: 36px;
  z-index: 2;
  box-shadow: 0 5px 10px rgba(255, 158, 158, 0.5);
  cursor: pointer;
}
.sidebar-menu:hover .sidebar-menu_item-title {
  width: 160px;
  opacity: 1;
  transition: width 0s;
  transition-delay: 0.5s;
}
@media screen and (max-width: 979px) {
  .sidebar-menu {
    width: 400px;
    position: fixed;
    right: auto;
    top: auto;
    left: 50%;
    bottom: 0;
    transform: translate3d(-50%, 0, 0);
    display: flex;
    flex-direction: row;
    animation: 0.5s ease-out 0s 1 slideBottomIn;
  }
  .sidebar-menu_section.sidebar-menu_section--top {
    padding: 16px;
    flex-basis: 100%;
  }
  .sidebar-menu_section--top {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-left-radius: 0;
    flex-direction: row;
    column-gap: 16px;
  }
  .sidebar-menu_item {
    max-width: 33%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    color: var(--secondary-mid-grey-100);
  }
  .sidebar-menu_item-text {
    margin: 0;
    padding: 0;
  }
  .sidebar-menu_chat {
    width: 180px;
    height: inherit;
  }
  .sidebar-menu_item-title {
    position: static;
    opacity: 1;
    font-size: 12px;
    animation: none;
    border-bottom: none;
    width: inherit;
    min-width: 0 !important;
    text-align: center;
    justify-content: center;
  }
  .sidebar-menu_item-title:not(.sidebar-menu_item-title--for-floating) {
    min-height: 0;
    height: auto;
  }
  .sidebar-menu_background {
    display: none;
  }
  .sidebar-menu_chat-content {
    background: none;
    background-image: url(https://d1kndcit1zrj97.cloudfront.net/uploads/aspire_mark_dc39da9d03.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    clip-path: none;
  }
  .float-chat-icon {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .sidebar-menu:hover .sidebar-menu_chat .sidebar-menu_item-title, .sidebar-menu:hover .sidebar-menu_chat:hover .sidebar-menu_item-title, .sidebar-menu_item-title.sidebar-menu_item-title--for-floating {
    position: absolute;
    z-index: 1;
    width: 60%;
    top: 25px;
    left: 20%;
    justify-content: center;
    line-height: 1.25;
  }
  .sidebar-menu:hover .sidebar-menu_item-title {
    width: auto;
  }
}
@media screen and (max-width: 640px) {
  .sidebar-menu_section.sidebar-menu_section--top {
    padding-left: 8px;
  }
}
@media screen and (max-width: 468px) {
  .sidebar-menu {
    width: 100%;
  }
}
@media screen and (max-width: 429px) {
  .sidebar-menu {
    width: 100%;
  }
  .sidebar-menu_chat {
    width: 40%;
    height: 100%;
    right: 0;
    position: absolute;
  }
}
@media screen and (max-width: 375px) {
  .sidebar-menu_section.sidebar-menu_section--top {
    padding-left: 16px;
  }
}
