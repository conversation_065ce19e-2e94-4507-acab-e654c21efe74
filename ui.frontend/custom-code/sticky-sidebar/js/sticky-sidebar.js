$(document).ready(function () {
    let stickySidebar = $('.sticky-sidebar-component');

    stickySidebar.each(function () {
        //scroll to section on user click
        let sidebarMenuItem = $(this).find('.sidebar-menu_item');
        sidebarMenuItem.click(function () {
            let menuItemSectionId = $(this).attr('class').split(/\s+/)[1];
            let selectedTitle = $('#' + menuItemSectionId);
            window.scrollTo({ top: selectedTitle.position().top });
            if (menuItemSectionId == 'how-to-join') {
                let stepAccordionFirstTab = $(document).find('.accordion-inspire-component .item').first();
                stepAccordionFirstTab.addClass('show');
                stepAccordionFirstTab.removeClass('hide');
            }
        })
    });
});