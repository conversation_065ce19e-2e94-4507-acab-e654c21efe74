window.onload = function() {
    setTimeout(function() {
        let backHomeBtn = document.querySelector('div[role="tablist"] .btn-back-to-home');
        // check tracking attribute
        let trackingAttr1 = backHomeBtn.getAttribute("data-tracking-click-event");
        if (trackingAttr1 === null || trackingAttr1 === false) {
            backHomeBtn.setAttribute("data-tracking-click-event", "linkClick");
            backHomeBtn.setAttribute("data-tracking-click-info-value", "{'linkClick' : ''}");
            backHomeBtn.setAttribute(
                "data-tracking-web-interaction-value", "{'webInteractions': {'name': 'Private Banking Contact Details', 'type': 'other'}}"
            );
            backHomeBtn.click();
        }
    
        let itemEfectBtn = document.querySelector('div[role="tablist"] .tab-item-efect');
        // check tracking attribute
        let trackingAttr2 = itemEfectBtn.getAttribute("data-tracking-click-event");
        if (trackingAttr2 === null || trackingAttr2 === false) {
            itemEfectBtn.setAttribute("data-tracking-click-event", "linkClick");
            itemEfectBtn.setAttribute("data-tracking-click-info-value", "{'linkClick' : 'WHY US'}");
            itemEfectBtn.setAttribute(
                "data-tracking-web-interaction-value", "{'webInteractions': {'name': 'Private Banking Contact Details', 'type': 'other'}}"
            );
            itemEfectBtn.click();
        }
    }, 1000);
};