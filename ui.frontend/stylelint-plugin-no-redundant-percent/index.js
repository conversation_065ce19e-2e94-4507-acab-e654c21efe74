// stylelint-plugin-no-redundant-percent/index.js
const stylelint = require("stylelint");

const ruleName = "plugin/no-redundant-percent";
const messages = stylelint.utils.ruleMessages(ruleName, {
  rejected: (value) => `Avoid redundant use of '%' in: ${value}`,
});

module.exports = stylelint.createPlugin(ruleName, function (primaryOption, secondaryOptions) {
  return function (root, result) {
    root.walkDecls((decl) => {
      const value = decl.value;

      // Kiểm tra nếu value là calc(100%) hoặc các biến thể tương đương
      const redundantPercent = /calc\s*\(\s*100%\s*\)/;

      if (redundantPercent.test(value)) {
        stylelint.utils.report({
          message: messages.rejected(value),
          node: decl,
          result,
          ruleName,
        });
      }
    });
  };
});

module.exports.ruleName = ruleName;
module.exports.messages = messages;