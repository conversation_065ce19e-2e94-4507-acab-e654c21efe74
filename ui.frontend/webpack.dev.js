const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const glob = require('glob');
const dotenv = require('dotenv');
dotenv.config();

const SOURCE_ROOT = __dirname + '/src/main/webpack';

const RESOURCE_PATH = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources';

module.exports = env => {

  const writeToDisk = env && Boolean(env.writeToDisk);

  // Automatically get all HTML files
  const htmlFiles = glob.sync(`${SOURCE_ROOT}/static/**/*.html`);
  const htmlPlugins = htmlFiles.map(filePath => {
    const relativePath = path.relative(`${SOURCE_ROOT}/static`, filePath);
    return new HtmlWebpackPlugin({
      inject: true,
      template: filePath,
      filename: `tcb/${relativePath.replace(/\\/g, '/')}`, // normalize path on Windows
      chunks: ['tcb']
    });
  });

  return merge(common, {
    mode: 'development',
    performance: {
      hints: 'warning',
      maxAssetSize: 1048576,
      maxEntrypointSize: 1048576
    },
    plugins: [
      ...htmlPlugins,
      new HtmlWebpackPlugin({
        template: path.resolve(__dirname, SOURCE_ROOT + '/static/index.html')
      }),
    ],
    devServer: {
      proxy: [
        {
          changeOrigin: true,
          context: ["/content", "/etc.clientlibs", "/etc/", "/api", "/graphql"],
          target: process.env.PROXY_TARGET,
          headers: {
            Authorization: "Basic YWRtaW46YWRtaW4=",
          },
          pathRewrite: {
            "^/content/([^?]*)": "/content/$1?wcmmode=disabled",
            "^/api/([^?]*)": "/api/$1?wcmmode=disabled",
          },
          bypass: function (req, res, proxyOptions) {
            if (req.url.indexOf(RESOURCE_PATH) > -1) {
              const resourceUrl = req.url.replace(RESOURCE_PATH, "/clientlib-site");
              console.log("Skipping proxy for browser request.");
              console.log("Routing local path: from: ", req.url, ", to: ", resourceUrl);
              return resourceUrl;
            }

            if (req.url.startsWith("/etc.clientlibs/techcombank/clientlibs/clientlib-site.")) {
              const newUrl = "/clientlib-site/site" + (req.url.endsWith("js") ? ".js" : ".css");
              console.log("Skipping proxy for browser request.");
              console.log("Routing local path: from: ", req.url, ", to: ", newUrl);
              return newUrl;
            }
          },
        },
      ],
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
      watchFiles: ['src/**/*'],
      hot: false,
      devMiddleware: {
        writeToDisk: writeToDisk
      }
    }
  });
};
