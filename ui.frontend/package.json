{"name": "aem-maven-archetype", "version": "1.0.0", "description": "Generates an AEM Frontend project with Webpack", "repository": {"type": "git", "url": ""}, "private": true, "main": "src/main/webpack/site/main.ts", "license": "SEE LICENSE IN LICENSE.txt", "scripts": {"dev": "webpack --env dev --config ./webpack.dev.js && clientlib --verbose", "prod": "webpack --config ./webpack.prod.js && clientlib --verbose", "start": "webpack-dev-server --open --config ./webpack.dev.js", "sync": "aemsync -d -p ../ui.apps/src/main/content", "chokidar": "chokidar -c \"clientlib\" ./dist", "aemsyncro": "aemsync -w ../ui.apps/src/main/content", "watch": "npm-run-all --parallel start choki<PERSON> aemsyncro", "test": "jest --coverage --config ./jest.config.js"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.3.3", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/preset-env": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^5.7.0", "@typescript-eslint/parser": "^5.7.0", "acorn": "^6.1.0", "aem-clientlib-generator": "^1.8.0", "aemsync": "^4.0.1", "autoprefixer": "^9.2.1", "babel-jest": "^29.7.0", "browserslist": "^4.2.1", "chokidar-cli": "^3.0.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^10.1.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "cssnano": "^5.0.12", "eslint": "^8.4.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-webpack-plugin": "^3.1.1", "glob-import-loader": "^1.2.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.4.5", "pdfjs-dist": "3.11.174", "postcss": "^8.2.15", "postcss-loader": "^3.0.0", "sass": "^1.45.0", "sass-loader": "^12.4.0", "source-map-loader": "^0.2.4", "style-loader": "^0.14.1", "terser-webpack-plugin": "^5.2.5", "ts-loader": "^9.2.6", "tsconfig-paths-webpack-plugin": "^3.2.0", "typescript": "^4.8.2", "webpack": "^5.65.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.6.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@gabrielfins/ripple-effect": "^1.0.5", "dompurify": "3.1.6", "dotenv": "^16.5.0", "glob": "^11.0.2", "jquery": "^3.7.0", "jquery-ui": "^1.12.1", "lightweight-charts": "3.4.0", "moment": "^2.29.4", "prettier-eslint": "^16.4.2", "slick-carousel": "^1.8.1", "string.prototype.replaceall": "^1.0.10", "stylelint": "^16.19.1", "stylelint-declaration-strict-value": "^1.10.11", "swiper": "^8.4.7"}, "browserslist": ["last 2 version", "> 1%"]}