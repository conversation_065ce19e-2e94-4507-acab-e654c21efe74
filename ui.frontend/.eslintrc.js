module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module'
  },
  plugins: ['sonarjs'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:sonarjs/recommended-legacy'
  ],
  globals: {
    jQuery: 'readonly',
    $: 'readonly',
  },
  env: {
    browser: true,
    node: true,
    es2021: true
  },
  overrides: [
    {
      files: ["*.js","*.scss"],
    }
  ],
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
  },
  rules: {
    'prefer-const': ['error', {
      destructuring: 'all',
      ignoreReadBeforeAssign: true,
    }],
    "curly": ["warn", "all"],
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "ordered-imports": "off",
    "object-literal-sort-keys": "off",
    "max-len": ["warn", { "code": 120 }],
    "new-parens": "warn",
    "no-bitwise": "warn",
    "no-cond-assign": "warn",
    "no-trailing-spaces": "off",
    "eol-last": ["warn", "always"],
    "func-style": ["error", "declaration", { "allowArrowFunctions": true }],
    "semi": ["error", "always"],
    "no-var": "error", // enforce const/let
    "@typescript-eslint/no-empty-function": "off",

    // 🔽 YOUR CUSTOM RULES
    "no-undef": "error",
    "quotes": ["error", "single", { "avoidEscape": true }], // single quote
    "no-ternary": "error", // disallow ternary expressions
    "no-console": ["error", { allow: ["warn", "error"] }], // disallow console.log
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }], // no unused vars
    "indent": ["error", 2, { "SwitchCase": 1 }], // 2 spaces formatting
    "@typescript-eslint/no-magic-numbers": [
      "warn",
      {
        "ignore": [0, 1, -1], // common exceptions
        "ignoreEnums": true,
        "ignoreNumericLiteralTypes": true,
        "ignoreReadonlyClassProperties": true,
        "enforceConst": false,
        "detectObjects": false
      }
    ],
    "no-restricted-syntax": [
      "error",
      {
        selector: "OptionalMemberExpression",
        message: "Optional chaining is not allowed."
      },
      {
        selector: "OptionalCallExpression",
        message: "Optional chaining is not allowed."
      }
    ],
    "sonarjs/no-identical-expressions": "error",
    'sonarjs/cognitive-complexity': ['error', 10]
  },
};
