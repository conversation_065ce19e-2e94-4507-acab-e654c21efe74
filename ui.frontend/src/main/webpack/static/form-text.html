<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title></title>
    <meta name="template" content="page-content" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-base.css" type="text/css">
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.css" type="text/css">
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css">
</head>

<body class="page basicpage">

    <div class="sectioncontainer">
        <div class="tcb-sectionContainer">
            <div class="tcb-content-container" style="padding-top:20px; padding-bottom: 20px;">
                <form class="cmp-form" onsubmit="validateForm(event)">
                    <!-- form text -->
                    <div class="text section width-50">
                        <div class="cmp-form-text">
                            <label for="form-text-*********">Text 1 <span>*</span></label>
                            <input data-type="text"
                                   data-required="Vui lòng nhập họ và tên" class="cmp-form-text__text"
                                   data-cmp-hook-form-text="input" id="form-text-*********" name="text1"
                                   placeholder="Please input Full name" type="text">
                        </div>
                    </div>
                    <div class="text section width-50">
                        <div class="cmp-form-text">
                            <label for="form-text-605074167">Text 2 <span>*</span></label>
                            <input data-type="phone" class="cmp-form-text__text"
                                   data-cmp-hook-form-text="input" id="form-text-605074167" name="text1"
                                   placeholder="Please input Phone" type="tel">
                        </div>
                    </div>
                    <div class="text section width-50">
                        <div class="cmp-form-text" >
                            <label for="form-text-605074168">Text 3 <span>*</span></label>
                            <input data-type="email"
                                   data-required="Vui lòng nhập email" class="cmp-form-text__text"
                                   data-format="Invalid Email"
                                   data-cmp-hook-form-text="input" id="form-text-605074168" name="text1"
                                   placeholder="Please input Email" type="email">
                        </div>
                    </div>
                    <div class="text section width-80">
                        <!--    Form currency -->
                        <div class="cmp-form-text" >
                            <label for="form-text-605074169">Text 4 <span>*</span></label>
                            <input data-type="currency"
                                   data-required="Vui lòng nhập giá" class="cmp-form-text__text"
                                   data-cmp-hook-form-text="input" id="form-text-605074169" name="text1"
                                   placeholder="Please input currency" type="currency">
                        </div>
                    </div>

                    <div class="text section width-50">
                        <div class="cmp-form-text">
                            <label class="text-input-label">Ngày cấp<span>*</span></label>
                            <div class="datepicker">
                                <div class="datepicker-section">
                                    <input
                                    data-required="Vui lòng chọn ngày"
                                    type="text"
                                    class="datepicker-input"
                                    data-change-year="true"
                                    data-accept-all-date="true"
                                    placeholder="Chọn ngày/tháng/năm"
                                    data-year-title="Year:"
                                    value=""
                                    readonly />
                                    <div class="date-icon">
                                    <div>
                                        <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/calendar.svg">
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="dropdown section width-50" data-required="Vui lòng chọn sản phẩm vay">
                        <div class="cmp-form-options__drop-down">
                            <label>Sản phẩm vay<span>*</span></label>
                            <div class="dropdown-wrapper" data-placeholder="Chọn sản phẩm vay" data-selected="Vay mua bất động sản">
                                <div class="dropdown-inputbase" role="button">
                                    <input class="dropdown-input" name="San_pham_vay" aria-hidden="true" tabindex="-1"
                                        value="" />
                                    <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-icon-down-red.svg" alt="">
                                </div>
                                <div class="dropdown-backdrop" hidden></div>
                                <ul class="list-dropdown" hidden>
                                    <li class="dropdown-item" selected role="button" data-value="Vay mua bất động sản">
                                        <span>
                                            <p>Vay mua bất động sản</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item disabled"  role="button" data-value="Vay mua ô tô">
                                        <span>
                                            <p>Vay mua ô tô</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item" role="button"
                                        data-value="Vay tiêu dùng có tài sản đảm bảo">
                                        <span>
                                            <p>Vay tiêu dùng có tài sản đảm bảo</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item" role="button"
                                        data-value="Vay tiêu dùng không có tài sản đảm bảo">
                                        <span>
                                            <p>Vay tiêu dùng không có tài sản đảm bảo</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item" role="button" data-value="Vay vốn hộ kinh doanh">
                                        <span>
                                            <p>Vay vốn hộ kinh doanh</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item" role="button" data-value="Vay xây, sửa nhà">
                                        <span>
                                            <p>Vay xây, sửa nhà</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item" role="button" data-value="Vay du học">
                                        <span>
                                            <p>Vay du học</p>
                                        </span>
                                    </li>
                                    <li class="dropdown-item" role="button" data-value="Sản phẩm khác">
                                        <span>
                                            <p>Sản phẩm khác</p>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                    </div>
                    <!--    Form Option -->
                    <div class="radio section width-50" data-required="Vui lòng chọn giới tính">
                        <div class="cmp-form-options__field--radio">
                            <label>
                                Giới tính <span>*</span>
                            </label>
                            <div class="radio-wrapper one-column small">
                                <div class="radio-item">
                                    <label for="" class="radio-label">
                                        <input name="gioi-tinh" type="radio" value="0" />
                                        <div>
                                            <p>Nam</p>
                                        </div>
                                    </label>
                                </div>
                                <div class="radio-item">
                                    <label for="" class="radio-label">
                                        <input name="gioi-tinh" type="radio" value="1" checked/>
                                        <div>
                                            <p>Nữ</p>
                                        </div>
                                    </label>
                                </div>
                                <div class="radio-item">
                                    <label for="" class="radio-label">
                                        <input name="gioi-tinh" type="radio" value="2" disabled />
                                        <div>
                                            <p>Disable</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="checkbox section width-80" data-required="Vui lòng chọn dịch vụ muốn đăng ký">
                        <div class="cmp-form-options__field--checkbox">
                            <label>
                                Chọn dịch vụ muốn đăng ký <span>*</span>
                            </label>
                            <div class="checkbox-wrapper">
                                <div class="checkbox-item">
                                    <label for="" class="checkbox-label">
                                        <input name="" type="checkbox" value="0" />
                                        <div>
                                            <p>F@st i-bank</p>
                                        </div>
                                    </label>
                                </div>
                                <div class="checkbox-item">
                                    <label for="" class="checkbox-label">
                                        <input name="" type="checkbox" value="0" disabled />
                                        <div>
                                            <p>F@st MobiPay</p>
                                        </div>
                                    </label>
                                </div>
                                <div class="checkbox-item">
                                    <label for="" class="checkbox-label">
                                        <input name="" type="checkbox" value="0" />
                                        <div>
                                            <p>Home Banking</p>
                                        </div>
                                    </label>
                                </div>
                                <div class="checkbox-item">
                                    <label for="" class="checkbox-label">
                                        <input name="" type="checkbox" value="0" />
                                        <div>
                                            <p>F@st Mobile</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="text section width-50">
                        <div class="cmp-form-text">
                            <label for="form-text-*********">Text Non Required</label>
                            <input data-type="text" class="cmp-form-text__text"
                                   data-cmp-hook-form-text="input" id="form-text-*********" name="text1"
                                   placeholder="Please input Text" type="text">
                        </div>
                    </div>
                    <div class="text section width-50">
                        <div class="cmp-form-text">
                            <label for="form-text-*********">Text Password <span>*</span></label>
                            <input data-type="password"
                                   data-required="Vui lòng nhập Password" class="cmp-form-text__text"
                                   data-cmp-hook-form-text="input" id="form-text-*********" name="text1"
                                   placeholder="Please input Password" type="password">
                        </div>
                    </div>

                    <div class="text section width-50">
                        <div class="cmp-form-text">
                            <label >Demo1<span> *</span></label>
                            <textarea class="cmp-form-text__textarea" data-cmp-hook-form-text="input" id="form-text-763657360" placeholder="Help Message" name="HienTran" data-required="BBBBBBBBB" rows="2"></textarea>
                        </div>
                    </div>

                    <div class="checkbox section width-100" data-required="Required text">
                        <div class="cmp-form-options__field--term">
                            <div class="term-title">
                                Confirm
                            </div>
                            <div class="checkbox-wrapper">
                                <div class="checkbox-item">
                                    <label for="" class="checkbox-label">
                                        <input name="" type="checkbox" value="0" />
                                        <div>
                                            <p>
                                                I confirm that I have read and agreed to the
                                                <a href="https://media.techcombank.com/uploads/Dieu-khoan-va-dieu-kien-9cdb796273-7f53a69705.pdf" target="_blank">
                                                    <strong><u>terms and conditions</u></strong>
                                                </a>
                                            </p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="recaptcha section width-100">
                        <iframe title="reCAPTCHA"
                            src="https://www.google.com/recaptcha/api2/anchor?ar=2&k=6Ldoe28gAAAAAJQC2HvxItZByXLgGsKq8bs9-1pU&co=aHR0cHM6Ly90ZWNoY29tYmFuay5jb206NDQz&hl=en&type=image&v=x19joXI_IeQnFJ7YnfDapSZq&theme=light&size=normal&badge=bottomright&cb=sq8d0ixb3af"
                            width="304" height="78" role="presentation" name="a-f849yd1z1ons" frameborder="0"
                            scrolling="no"
                            sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox"></iframe>
                    </div>

                    <div class="button aem-GridColumn aem-GridColumn--default--12 width-100">
                        <button type="SUBMIT" class="cmp-form-button" name="submit">Submit
                            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow-icon.svg"
                                alt="" class="cmp-form-button__icon" aria-hidden="true" />
                        </button>
                    </div>
                </form>

            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"
        integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>
    <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.js"></script>
    <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-base.js"></script>
    <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site.js"></script>
</body>

</html>
