<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Pick Card Filter</title>
    <meta name="template" content="page-content" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css">

</head>

<body class="page basicpage credit-card-listing-compare">

    <div class="credit-card-listing__content card-listing-picker-filter__content" data-add-label="Thêm để so sánh"
        data-added-label="Đã thêm">
        <div class="content-wrapper credit-card-listing__container">
            <div class="wrapper-credit-card-listing-title">
                <h6 class="credit-card-listing__title">What are you looking for? <PERSON><PERSON>um has been the industry's standard dummy text ever since the 1500s,</h6>
                <p class="credit-card-listing__desciption">Let's us help you Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,</p>
            </div>
            <div class="credit-card-listing__items">
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-dam">Tích dặm</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/hoan-tien">Hoàn tiền</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/hoan-tien">Hoàn tiền</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/hoan-tien">Hoàn tiền</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/hoan-tien">Hoàn tiền</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button filter-selected" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-diem-chi-tieu">Tích điểm chi
                        tiêu</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button filter-selected" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-diem-chi-tieu">Tích điểm chi
                        tiêu</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button filter-selected" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-diem-chi-tieu">Tích điểm chi
                        tiêu</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button filter-selected" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-diem-chi-tieu">Tích điểm chi
                        tiêu</button>
                </div>

            </div>
        </div>
    </div>


</body>

</html>