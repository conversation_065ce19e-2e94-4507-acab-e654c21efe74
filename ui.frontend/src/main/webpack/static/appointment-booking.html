<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>appointment-booking</title>
    <meta name="template" content="page-content"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-base.css" type="text/css">
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.css" type="text/css">
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css">
</head>
<body class="page basicpage">
<section class="container section__margin-medium">
  <div class="content-wrapper">
    <div class="appointment-booking">
      <div class="cta-button--light btn-contactus">
        <a class="cta-button" href="#bookingVisit">
            <span class="cmp-button__text">Đặt lịch tại chi nhánh</span>
            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow-icon.svg"
                 alt="" class="cmp-button__icon" aria-hidden="true"/>
        </a>
      </div>
      <div class="popup modal-hidden" id="bookingVisit">
        <div class="popup-content popup__container">
          <div class="booking booking-visit" 
            data-today-label="Today" 
            data-tomorrow-label="Tomorrow"
            data-api-branch="/content/techcombank/web/vn/en/atm-test"
            data-api-branch-suffix=""
            data-api-service="/content/techcombank/web/vn/en/atm-test"
            data-api-booking-slot="/content/techcombank/web/vn/en/atm-test"
            data-api-booking-confirm="/content/techcombank/web/vn/en/atm-test"
            data-serviceId = "79"
            >
            <input hidden name="selectedBranch" value="">
            <input hidden name="selectedService" value="">
            <div class="title">
              <h2>Đặt lịch hẹn tại Chi nhánh</h2>
              <span class="close popup__close-icon">
                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/gray-close.svg"/>
              </span>
            </div>
            <div class="booking-container-wrapper">
              <div class="client booking-container hidden">
                <div class="type">
                  <p>Tôi Là:</p>
                  <div class="client-list">
                    <tcb-tracker value="btn_personal_customer">
                      <div class="item">
                        <span class="personal">Khách Hàng Cá Nhân</span>
                        <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-chevron-right.svg">
                      </div>
                    </tcb-tracker>
                    <tcb-tracker value="btn_business_customer">
                      <div class="item">
                          <span class="bussiness">Khách Hàng Doanh Nghiệp</span>
                          <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-chevron-right.svg">
                      </div>
                    </tcb-tracker>
                  </div>
                </div>
              </div>
              <div class="service booking-container">
                <div class="type">
                  <p>Bạn đang tìm kiếm dịch vụ nào?</p>
                  <div class="service-list">
                  </div>
                </div>
              </div>
              <div class="branch booking-container">
                <div class="name">
                  <p>Tại chi nhánh nào?</p>
                  <div class="search-filter">
                    <div class="city filter">
                        <div class="option">
                            <div class="dropdown-text">
                              <span class="dropdown-label">Tỉnh/Thành Phố</span>
                              <span class="dropdown-body"></span>
                            </div>
                            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-icon-down-red.svg" alt="arrow down">
                        </div>
                        <div class="select-options">
                            <ul>
                            </ul>
                        </div>
                    </div>
                    <div class="district filter">
                        <div class="option">
                            <div class="dropdown-text">
                              <span class="dropdown-label">Quận/Huyện</span>
                              <span class="dropdown-body"></span>
                            </div>
                            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-icon-down-red.svg" alt="arrow down">
                        </div>
                        <div class="select-options">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
                </div>
                <div class="address">
                  <p>Danh sách chi nhánh:</p>
                  <div class="address-list"></div>
                </div>
              </div>
              <div class="schedule booking-container">
                <div class="date">
                  <div class="today"></div>
                  <div class="tomorrow"></div>
                </div>
                <div class="time">
                  <p>Bạn muốn đặt lịch lúc mấy giờ?</p>
                  <div class="time-list"></div>
                </div>
              </div>
              <form class="form booking-container">
                <h3>Vui lòng cung cấp thông tin để hoàn tất lịch hẹn:</h3>
                <div class="input-form">
                  <div class="input taxt-code">
                    <label for="name">AccNo/TaxCode<span>*</span></label><br />
                    <input type="text" id="taxCode" name="taxCode" placeholder="0123456789" autocomplete="off" /><br />
                    <p class="form-error-message" id="taxCode-error">AccNo/TaxCode is required.</p>
                  </div>
                  <div class="input">
                    <label for="name">Họ và Tên</label><br />
                    <input type="text" id="name" name="name" placeholder="Họ và tên" autocomplete="off"/><br />
                  </div>
                  <div class="input">
                    <label for="email">Email<span>*</span></label
                    ><br />
                    <input type="text" id="emailAddress" name="emailAddress" placeholder="Email" autocomplete="off" /><br />
                    <p class="form-error-message" id="emailAddress-error-required">Email is required.</p>
                    <p class="form-error-message" id="emailAddress-error-invalid">Email is invalid</p>
                  </div>
                  <div class="input">
                    <label for="phone">Số điện thoại<span>*</span></label
                    ><br />
                    <input type="text" id="phone" maxlength="10" name="phone" placeholder="Số điện thoại" autocomplete="off"/><br />
                    <p class="form-error-message" id="phone-error">Phone is required.</p>
                    <p class="form-error-message" id="phone-error-invalid">Phone is invalid</p>
                  </div>
                </div>
                <div class="captcha">
                  <iframe title="reCAPTCHA"
                      src="https://www.google.com/recaptcha/api2/anchor?ar=2&k=6Ldoe28gAAAAAJQC2HvxItZByXLgGsKq8bs9-1pU&co=aHR0cHM6Ly90ZWNoY29tYmFuay5jb206NDQz&hl=en&type=image&v=vm_YDiq1BiI3a8zfbIPZjtF2&theme=light&size=normal&badge=bottomright&cb=cdjiuw2p7v87"
                      width="304" height="78" role="presentation" name="a-f849yd1z1ons" frameborder="0"
                      scrolling="no"
                      sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox"></iframe>
                </div>
                <input type="submit" class="btn-submit" value="Đặt lịch hẹn" />
              </form>
              <div class="confirm-appointment">
                <div class="confirm-content-wrapper" >
                  <div class="comfirm-img-wrapper">
                     <span>
                        <img src="https://media.techcombank.com/uploads/icon_calendar_b49948ad4c.png?w=1920&amp;q=75" decoding="async" data-nimg="fill" sizes="100vw" srcset="https://media.techcombank.com/uploads/icon_calendar_b49948ad4c.png?w=360&amp;q=75 360w, https://media.techcombank.com/uploads/icon_calendar_b49948ad4c.png?w=576&amp;q=75 576w, https://media.techcombank.com/uploads/icon_calendar_b49948ad4c.png?w=1080&amp;q=75 1080w, https://media.techcombank.com/uploads/icon_calendar_b49948ad4c.png?w=1200&amp;q=75 1200w, https://media.techcombank.com/uploads/icon_calendar_b49948ad4c.png?w=1920&amp;q=75 1920w">
                        <noscript></noscript>
                     </span>
                  </div>
                  <h6 class="confirm-label">Your Ticket is confirmed.</h6>
                  <h6 class="confirm-label date-appointment">See you on</h6>
                  <h6 class="confirm-label ticket-number">Ticket number:</h6>
                  <h4 class="confirm-ticket-number"></h4>
                  <p class="confirm-note">Please show this ticket number to the staff on your booking slot. Ticket will expire 10 minutes after appointment time.</p>
               </div>
              </div>
              <div class="loading">
                <div></div><div></div><div></div><div></div>
              </div>
              <div class="loading_backdrop"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<script src="https://code.jquery.com/jquery-3.7.0.min.js" integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>
<script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.js"></script>
<script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-base.js"></script>
<script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site.js"></script>
</body>
</html>