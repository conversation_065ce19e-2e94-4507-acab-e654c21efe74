<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Insurance Gain</title>
  <meta name="template" content="page-content" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />
  <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-base.css" type="text/css">
  <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.css" type="text/css">
  <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css">
</head>

<body class="page basicpage">
  <section class="container section__margin-medium">
    <div class="insurance-gain" data-insurance-gain="" data-invalid-date="Invalid date!" data-invalid-age="The age must be lower than 69" data-currency-input="VNĐ">
      <div class="insurance-gain__container">
        <div class="insurance-gain__content">
          <div class="insurance-gain__title">
            <h2>Illustration of insurance benefits</h2>
          </div>
          <div class="insurance-gain__panel">
            <div class="panel-inputs">
              <div class="input-items">
                <div class="item__label">
                  <div class="label__text">Date of birth</div>
                </div>
                <div class="item__input-fields">
                  <div class="input-fields__date-time-wrapper">
                    <div class="date-time-wrapper__input-field" inputmode="numeric">
                      <input aria-invalid="false" name="day" placeholder="DD" type="text" maxlength="2"
                        class="date-time-wrapper__input" value="" />
                    </div>
                    <span class="date-time-wrapper__separator">-</span>
                    <div class="date-time-wrapper__input-field" inputmode="numeric">
                      <input aria-invalid="false" name="month" placeholder="MM" type="text" maxlength="2"
                        class="date-time-wrapper__input" value="" />
                    </div>
                    <span class="date-time-wrapper__separator">-</span>
                    <div class="date-time-wrapper__input-field" inputmode="numeric">
                      <input aria-invalid="false" name="year" placeholder="YYYY" type="text" maxlength="4"
                        class="date-time-wrapper__input" value="" />
                    </div>
                  </div>
                  <p class="input-fields__error-msg"></p>
                </div>
                <div class="item__label">
                  <div class="label__text">Sex</div>
                </div>
                <div class="item__input-fields">
                  <div class="input-fields__drop-down">
                    <div class="drop-down__container">
                      <div class="drop-down__controls">
                        <div class="drop-down__select">Male</div>
                        <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg" />
                      </div>
                      <div class="select-option">
                        <div class="option gender selected" value="0">Male</div>
                        <div class="option gender" value="1">Female</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="item__label">
                  <div class="label__text">
                    The amount you want to be primarily insured
                    <div class="icon-info">
                      <span class="icon">
                        <img alt="" src="https://d1kndcit1zrj97.cloudfront.net/uploads/icon_8c14fabb1a.png?w=1920&q=75"
                          decoding="async" data-nimg="fill" sizes="100vw" srcset="
                          https://d1kndcit1zrj97.cloudfront.net/uploads/icon_8c14fabb1a.png?w=360&q=75   360w,
                          https://d1kndcit1zrj97.cloudfront.net/uploads/icon_8c14fabb1a.png?w=576&q=75   576w,
                          https://d1kndcit1zrj97.cloudfront.net/uploads/icon_8c14fabb1a.png?w=1080&q=75 1080w,
                          https://d1kndcit1zrj97.cloudfront.net/uploads/icon_8c14fabb1a.png?w=1200&q=75 1200w,
                          https://d1kndcit1zrj97.cloudfront.net/uploads/icon_8c14fabb1a.png?w=1920&q=75 1920w
                        " />
                      </span>
                      <div class="tooltiptext">Là số tiền mà Khách hàng muốn được bảo hiểm theo sản phẩm chính (không bao gồm các sản phẩm bổ trợ)</div>
                    </div>
                  </div>
                </div>
                <div class="item__input-fields">
                  <div class="input-field__currency-field currency-suffix" inputmode="numeric">
                    <input aria-invalid="false" name="moneyValue" maxlength="25" type="text" id="insuranceSL"
                      class="date-time-wrapper__input money__input-field" value="" />
                    <div class="date-time-wrapper__input-extra">
                      <p class="currency__place-holder">VND</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="panel-info">
              <span>
                <img alt="" src="https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46472_eb32ac968a.svg?w=1920&q=75"
                  decoding="async" data-nimg="fill" class="InsuranceGainPanel_background__v_Ipd" sizes="100vw" srcset="
                  https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46472_eb32ac968a.svg?w=360&q=75   360w,
                  https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46472_eb32ac968a.svg?w=576&q=75   576w,
                  https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46472_eb32ac968a.svg?w=1080&q=75 1080w,
                  https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46472_eb32ac968a.svg?w=1200&q=75 1200w,
                  https://d1kndcit1zrj97.cloudfront.net/uploads/Frame_46472_eb32ac968a.svg?w=1920&q=75 1920w
                " />
              </span>
              <div class="panel-info__content">
                <div class="panel-info__content-text">
                  <div class="info-content-text__icon">
                    <span>
                      <img alt="" src="https://d1kndcit1zrj97.cloudfront.net/uploads/money_0f9fe50e94.svg?w=360&q=75"
                        decoding="async" data-nimg="fixed" srcset="
                        https://d1kndcit1zrj97.cloudfront.net/uploads/money_0f9fe50e94.svg?w=96&q=75  1x,
                        https://d1kndcit1zrj97.cloudfront.net/uploads/money_0f9fe50e94.svg?w=360&q=75 2x
                      " />
                    </span>
                  </div>
                  <div class="info-content-text__label">
                    <p>Annual premium payment amount</p>
                    <h3 class="insurance-fee">0 VND</h3>
                  </div>
                </div>
                <div class="panel-info__content-button">
                <div class="cta-button--light">
                    <a class="cta-button" href="#">
                        <span class="cmp-button__text">Sign up for consultation</span>
                    </a>
                </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"
    integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>
  <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.js"></script>
  <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-base.js"></script>
  <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site.js"></script>
</body>

</html>