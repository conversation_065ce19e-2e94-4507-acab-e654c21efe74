<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Insurance Gain</title>
    <meta name="template" content="page-content" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-base.css" type="text/css">
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.css" type="text/css">
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css">

    <style>        
        .hero-inspire__footer .title-cmp {
            padding-bottom: 0;
        }
        .hero-inspire__footer .bold-text {
            font-size: 24px;
            font-weight: 500;
        }
        .tcb-hero-banner.inspire {
    overflow: unset;
    min-height: 445px;
}
.tcb-hero-banner.inspire .btn-label ::before {
    clip-path: path("M9.20531 1.23356C8.81578 0.844022 8.18422 0.844023 7.79468 1.23356C7.4054 1.62285 7.40511 2.25392 7.79405 2.64356L12.67 7.52824H1.5C0.947714 7.52824 0.5 7.97596 0.5 8.52824C0.5 9.08053 0.947715 9.52824 1.5 9.52824H12.67L7.79405 14.4129C7.40511 14.8026 7.4054 15.4336 7.79468 15.8229C8.18422 16.2125 8.81578 16.2125 9.20532 15.8229L16.5 8.52824L9.20531 1.23356Z");
}
.tcb-hero-banner.inspire .tcb-hero-banner_content.tcb-hero-banner_content--medium {
    height: unset;
}
.tcb-hero-banner.inspire .tcb-hero-banner_body {
    flex-direction: row;
    max-width: 1312px;
    margin-left: auto;
    margin-right: auto;
}
.tcb-hero-banner.inspire .tcb-hero-banner_body .content {
    padding-left: 250px;
    padding-top: 44px;
}
.tcb-hero-banner.inspire .content .text-img {
    width: 100%;
    display: flex;
    justify-content: center;
}
.tcb-hero-banner.inspire .content .text-img img {
    width: 100%;
    max-width: 341px;
    object-fit: contain;
}
.tcb-hero-banner.inspire .btn {
    max-width: 320px;
    display: flex;
    border-radius: 7px;
    align-items: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 16px;
    border: none;
    padding: 16px 24px;
    cursor: pointer;
    z-index: 6;
}
.tcb-hero-banner.inspire .btn span {
    font-size: 16px;
    font-weight: 600;
    color: #212121;
}
.tcb-hero-banner.inspire .btn img {
    margin-left: 24px;
    transition-duration: 300ms;
}
.tcb-hero-banner.inspire .btn:hover img {
    transform: translate(5px);
}
.tcb-hero-banner.inspire .sub-background {
    position: absolute;
    z-index: 5;
    top: 0px;
    right: calc((100% - 1440px) / 2);
}
.tcb-hero-banner.inspire .sub-background picture {
    display: flex;
    justify-content: center;
    width: 100%;
}
.tcb-hero-banner_background-image {
    object-fit: contain !important;
    height: 100%;
    width: auto !important;
    margin: 0 auto;
}
.tcb-hero-banner_background {
    background-color: #f9faf3;
    min-height: 445px;
}
.hero-inspire__footer {
    position: relative;
    color: #212121;
    background-image: linear-gradient(161.08deg, #fff 13.33%, #efeff7 60.68%);
}
.hero-inspire__footer::before {
    content: "";
    width: 308px;
    height: 100%;
    top: 0px;
    left: 0px;
    position: absolute;
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: cover;
    background-image: url(http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/inspire-banner-exclusive-promotions-6.png);
}
.hero-inspire__footer .title-cmp__title {
    font-size: 32px;
    line-height: 45px;
    letter-spacing: 0;
    font-weight: 600;
    text-align: left;
    z-index: 1;
    padding-bottom: 0;
}
.hero-inspire__footer .hero-inspire__footer-content {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    width: 85%;
    margin-top: 24px;
}
.hero-inspire__footer .footer-content__item {
    margin-left: 20px;
    margin-right: 20px;
    padding: 14px;
    display: flex;
    position: relative;
}
.footer-content__item {
    cursor: pointer;
}
.hero-inspire__footer .footer-content__item:first-child {
    margin-left: 0px;
    margin-right: 20px;
}
.hero-inspire__footer .footer-content__item:last-child {
    margin-left: 20px;
    margin-right: 0px;
}
.hero-inspire__footer .footer-content__item::before {
    content: "";
    border-right: 2px solid transparent;
    background: linear-gradient(180deg, rgba(97, 97, 97, 0), rgba(97, 97, 97, 0.6980392157), rgba(97, 97, 97, 0)) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    position: absolute;
    inset: 0;
    right: -20px;
}
.hero-inspire__footer .footer-content__item:nth-child(3n + 4):before {
    content: "";
    border: unset;
    background: transparent;
}
.hero-inspire__footer .footer-content__item p {
    color: var(--light-secondary-text);
}
.hero-inspire__footer .check-icon {
    width: 22px;
}
@media screen and (max-width: 767px) {
    /* inspire hero */
    .tcb-hero-banner.inspire .tcb-hero-banner_body .content {
        padding-left: 16px;
    }

    .tcb-hero-banner.inspire .content .text-img img {
        max-width: 240px;
    }

    .hero-inspire__footer .title-cmp__title {
        font-size: 24px;
        line-height: 36px;
    }

    .hero-inspire__footer .hero-inspire__footer-content {
        grid-template-columns: repeat(1, minmax(0, 1fr));
        width: 100%;
    }

    .hero-inspire__footer .footer-content__item {
        margin: 0;
        padding: 0;
    }

    .hero-inspire__footer .footer-content__item:first-child {
        margin: 0;
        padding: 0;
    }

    .hero-inspire__footer .footer-content__item:last-child {
        margin: 0;
        padding: 0;
    }

    .hero-inspire__footer .footer-content__item::before {
        content: "";
        border: unset;
        background: transparent;
    }

    .hero-inspire__footer .footer-content__item p {
        margin-left: 8px;
    }
}


.tcb-hero-banner_background .btn {
    position: absolute;
    bottom: 20px;
}

.tcb-hero-banner_background-image.for-desktop {
    display: block;
    min-width: 990px;
    min-height: 445px;
}
.tcb-hero-banner_background-image.for-mobile {
    display: none;
}

@media screen and (max-width: 768px) {
    .tcb-hero-banner_background .btn {
        display: none !important;
    }
}

@media screen and (max-width: 640px) {
    .tcb-hero-banner_background-image.for-desktop {
        display: none;
    }
    .tcb-hero-banner_background-image.for-mobile {
        display: block;
        width: 100%;
        height: auto;
    }
}

    </style>
</head>

<body class="page basicpage">

    <!-- hero-inspire.html -->
    <div class="tcb-hero-banner inspire">
        <div class="tcb-hero-banner_background">
            <img class="tcb-hero-banner_background-image for-desktop" id="tcb-inspire-banner"
                src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/inspire-banner-exclusive-promotions-4.png"
                alt="Logo" />
            <img class="tcb-hero-banner_background-image for-mobile"
                src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/inspire-banner-exclusive-promotions-5.png"
                alt="Logo" />
            <button class="btn white-bg hide" id="tcb-inspire-register"
                data-tracking-click-event="cta"
                data-tracking-click-info-value="{'cta' : '@textIn(.btn-label)'}"
                data-tracking-web-interaction-value="{'webInteractions': {'name': 'Inspire Banner & Exclusive Promotions','type': 'other'}}">
                <span class="btn-label">Register now</span>
                <img
                    src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/red-arrow-right.svg"
                    alt="arrow"
                />
            </button>
        </div>
        <div class="tcb-hero-banner_content tcb-hero-banner_content--medium">
            <div class="tcb-hero-banner_body">
                <div class="content">                    
                </div>
            </div>
        </div>
    </div>
    <div class="container hero-inspire__footer section__padding-medium">
        <div class="content-wrapper">
            <div class="title-cmp">
                <div class="title-cmp__title">Exclusive promotions</div>
            </div>
            <div class="hero-inspire__footer-content">
                <div class="footer-content__item"
                    data-tracking-click-event="linkClick"
                    data-tracking-click-info-value="{'linkClick' : '@textIn(.content-label)'}"
                    data-tracking-web-interaction-value="{'webInteractions': {'name': 'Inspire Banner & Exclusive Promotions','type': 'other'}}">
                    <span class="check-icon">
                        <img
                            src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/tick.svg"
                            alt="tick"
                        />
                    </span>
                    <div>+<span class="bold-text">0,1</span>% savings interest rate</div>
                </div>
                <div class="footer-content__item"
                    data-tracking-click-event="linkClick"
                    data-tracking-click-info-value="{'linkClick' : '@textIn(.content-label)'}"
                    data-tracking-web-interaction-value="{'webInteractions': {'name': 'Inspire Banner & Exclusive Promotions','type': 'other'}}">
                    <span class="check-icon">
                        <img
                            src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/tick.svg"
                            alt="tick"
                        />
                    </span>
                    <div>Get up to <span class="bold-text">2</span>% cashback with the payment card</div>
                </div>
                <div class="footer-content__item"
                    data-tracking-click-event="linkClick"
                    data-tracking-click-info-value="{'linkClick' : '@textIn(.content-label)'}"
                    data-tracking-web-interaction-value="{'webInteractions': {'name': 'Inspire Banner & Exclusive Promotions','type': 'other'}}">
                    <span class="check-icon">
                        <img
                            src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/tick.svg"
                            alt="tick"
                        />
                    </span>
                    <div>Membership lifetime annual fee waiver for the card</div>
                </div>
                <div class="footer-content__item"
                    data-tracking-click-event="linkClick"
                    data-tracking-click-info-value="{'linkClick' : '@textIn(.content-label)'}"
                    data-tracking-web-interaction-value="{'webInteractions': {'name': 'Inspire Banner & Exclusive Promotions','type': 'other'}}">
                    <span class="check-icon">
                        <img
                            src="http://localhost:4502/content/dam/techcombank/custom-code/inspire-banner-exclusive-promotions/images/tick.svg"
                            alt="tick"
                        />
                    </span>
                    <div>Zero fee for converting to installment payments in the first year of insurance</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"
        integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>
    <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.js"></script>
    <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-base.js"></script>
    <script type="text/javascript" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site.js"></script>
    <script>
        $(function () {
            $(window).on('resize', function () {
                changeRegisterBtnPosition();
            });
            changeRegisterBtnPosition();
        });

        function changeRegisterBtnPosition() {
            let $bannerImage = $('#tcb-inspire-banner');
            let $registerBtn = $('#tcb-inspire-register');
            $registerBtn.css({left: ($(window).width() - $bannerImage.width()) / 2});
        }
    </script>
</body>

</html>