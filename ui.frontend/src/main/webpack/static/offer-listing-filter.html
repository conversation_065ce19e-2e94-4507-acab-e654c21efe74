<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Offer Listing Filter component</title>
    <meta name="template" content="page-content"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-base.css" type="text/css"/>
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.css" type="text/css"/>
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css"/>
</head>
<body class="page basicpage">
<div class="offer-listing-filter__header"></div>
<section class="container section__margin-medium">
    <div class="content-wrapper offer-listing-filter__body">
        <div class="offer-listing-promotions" data-url="data-url" data-search-count="Showing %count promotions" data-view-more="view more"
             data-day-label="ngày" data-empty-promotion-label="No offers can be found">
            <div class="offer-listing-promotions__wrapper">
                <div class="offer-filter__container enhanced-offer-filter__container" data-tracking-click-event="articleFilter"
                data-tracking-click-info-value="{'articleFilter':''}"
                data-tracking-web-interaction-value="{'webInteractions': {'name': '${component.title}','type': 'other'}}">
                    <!-- Filter title -->
                    <div class="offer-filter__title filter--border-bottom">
                        <h6>Sort</h6>
                    </div>
                    <!-- Sort checkbox item -->
                    <div class="offer-filter__sort-by-wrap offer-filter__checkbox analytics-ol-sort">
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__radio" data-sort="mostpopular"
                                       name="sort" type="radio"
                                       value="mostpopular" checked/>
                                <span>Most popular</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__radio" data-sort="latest"
                                       name="sort" type="radio"
                                       value="latest"/>
                                <span>Latest</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__radio" data-sort="more time to consider"
                                       name="sort" type="radio"
                                       value="more-time-to-consider"/>
                                <span>More time to consider</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__radio" data-sort="expiring soon"
                                       name="sort" type="radio"
                                       value="expiring-soon"/>
                                <span>Expiring soon</span>
                            </label>
                        </div>
                    </div>

                    <!--PRODUCTS-->
                    <div class="offer-filter__title filter--border-bottom">
                        <h6>Products</h6>
                    </div>
                    <!-- Checkbox list item -->
                    <div class="offer-filter__checkbox product-checkbox analytics-product-type">
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-product="credit card"
                                       name="techcombank:promotion-products/credit-card" type="checkbox"
                                       value="credit-card" checked>
                                <span>Credit card</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-product="debit card"
                                       name="techcombank:promotion-products/debit-card" type="checkbox"
                                       value="debit-card" checked>
                                <span>Debit card</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-product="account"
                                       name="techcombank:promotion-products/account" type="checkbox"
                                       value="account" checked>
                                <span>Account</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-product="techcombank priority"
                                       name="techcombank:promotion-products/techcombank-priority" type="checkbox"
                                       value="techcombank-priority" checked>
                                <span>Techcombank Priority</span>
                            </label>
                        </div>
                    </div>

                    <!--CATEGORY-->
                    <div class="offer-filter__title filter--border-bottom">
                        <h6>Category</h6>
                    </div>


                    <!-- Checkbox list item -->
                    <div class="offer-filter__checkbox category-checkbox analytics-ol-categories">
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="cuisine"
                                       name="techcombank:promotion-categories/cuisine" type="checkbox"
                                       value="cuisine" checked>
                                <span>Cuisine</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="travel and food ordering"
                                       name="techcombank:promotion-categories/travel-and-food-ordering" type="checkbox"
                                       value="travel-and-food-ordering" checked>
                                <span>Travel and Food ordering</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="travel and entertainment"
                                       name="techcombank:promotion-categories/travel-and-entertainment" type="checkbox"
                                       value="travel-and-entertainment" checked>
                                <span>Travel and Entertainment</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="shopping"
                                       name="techcombank:promotion-categories/shopping" type="checkbox"
                                       value="shopping" checked>
                                <span>Shopping</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="healthcare"
                                       name="techcombank:promotion-categories/healthcare" type="checkbox"
                                       value="healthcare" checked>
                                <span>Healthcare</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="education"
                                       name="techcombank:promotion-categories/education" type="checkbox"
                                       value="education" checked>
                                <span>Education</span>
                            </label>
                        </div>
                        <div class="offer-filter__checkbox-item">
                            <label class="checkbox-item__wrapper">
                                <input class="input__checkbox" data-category="others"
                                       name="techcombank:promotion-categories/others" type="checkbox"
                                       value="others" checked>
                                <span>Others</span>
                            </label>
                        </div>
                    </div>

                    <!--MERCHANT-->
                    <div class="offer-filter__title filter--border-bottom">
                        <h6>Select merchant</h6>
                    </div>
                    <!-- Dropdown item -->
                    <div class="offer-filter__dropdown analytics-ol-merchant">
                        <div class="dropdown__wrapper">
                            <div class="dropdown__display">
                                <span class="display__text text--ellipsis" data-placeholder="Select merchant">
                                    Select merchant
                                </span>
                                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg"/>
                            </div>
                            <div class="dropdown__list dropdown--ease-transition --bordered --shadow">
                                <ul>
                                    <li class="dropdown__item" data-merchant-type="all"
                                        value="all">
                                        Select all
                                    </li>
                                    <li class="dropdown__item" data-merchant-type="siam premium outlets bangkok"
                                        value="siam premium outlets bangkok">
                                        SIAM Premium outlets Bangkok
                                    </li>
                                    <li class="dropdown__item" data-merchant-type="living house"
                                        value="living house">
                                        Living house
                                    </li>
                                </ul>
                                <input class="dropdown-selected-value" id="merchantType" type="hidden" value=""/>
                            </div>
                        </div>
                    </div>
                </div>

                <!--RESPONSIVE VIEW-->
                <div class="offer-listing-filter__button">
                    <div class="search-primary-container">
                        <div class="search-box">
                            <div class="input-container">
                                <div class="search-box_icon">
                                    <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg"/>
                                </div>
                                <input aria-invalid="false" id="offerListingFilterSearchMobile" autocomplete="off"
                                       placeholder="Search for promotion" type="text" aria-autocomplete="list"
                                       autocapitalize="none" spellcheck="false"
                                       class="ui-autocomplete-input search-input"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="offer-listing-filter__button">
                    <button class="btn-open-filter">
                        <span>Filters</span>
                        <span class="tcb-filter-button-icon">
                            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/filter.svg"/>
                        </span>
                    </button>
                </div>

                <div class="offer-listing-filter__button">
                    <div class="offer-filter__dropdown analytics-ol-sort-mobile">
                        <div class="dropdown__wrapper">
                            <div class="dropdown__display">
                                <div class="sort-title">
                                    <small class="display__title">Sort</small>
                                    <span class="display__text text--ellipsis"></span>
                                </div>
                                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg"/>
                            </div>
                            <div class="dropdown__list dropdown--ease-transition --bordered --shadow">
                                <ul>
                                    <li class="dropdown__item" data-sort-type="most popular"
                                        value="most-popular">
                                        Most popular
                                    </li>
                                    <li class="dropdown__item" data-sort-type="latest"
                                        value="latest">
                                        Latest
                                    </li>
                                    <li class="dropdown__item" data-sort-type="more time to consider"
                                        value="more-time-to-consider">
                                        More time to consider
                                    </li>
                                    <li class="dropdown__item" data-sort-type="expiring soon"
                                        value="expiring-soon">
                                        Expiring soon
                                    </li>
                                </ul>
                                <input class="dropdown-selected-value" id="merchantTypeMobile" type="hidden"
                                       value=""/>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="offer-cards__container">
                    <div class="offer-cards__wrapper">
                        <!--SEARCH SECTION-->
                        <div class="search-area">
                            <div class="search-primary-container">
                                <div class="search-box">
                                    <div class="input-container">
                                        <div class="search-box_icon">
                                            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg"/>
                                        </div>
                                        <input aria-invalid="false" id="offerListingFilterSearchDesktop" autocomplete="off"
                                               placeholder="Search for promotion" type="text" aria-autocomplete="list"
                                               autocapitalize="none" spellcheck="false"
                                               class="ui-autocomplete-input search-input"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="news-filter">
                                <div class="information-filter__load-more">
                                    <button type="button" class="load-more__button">
                                        <span>View result</span>
                                        <span class="load-more__button-icon">
                                            <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow.svg"
                                                 alt="red-arrow"
                                            />
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!--TOTAL COUNT-->
                        <div class="promotion-total-count"></div>
                        <!--PROMOTION CARD-->
                        <div class="card-promotion__list"></div>
                        <!--PAGINATION-->
                        <div class="pagination">
                            <div class="pag-container"></div>
                        </div>
                    </div>
                </div>

                <div class="tcb-modal offer-listing-enhance-mobile" hidden>
                    <div class="offer-filter__header">
                        <span class="title">Filters</span>
                    </div>
                    <div class="news_filter-group">
                        <!--PRODUCTS-->
                        <div class="offer-filter__title filter--border-bottom">
                            <h6>Products</h6>
                            <span class="material-symbols-outlined btn-close hidden">close</span>
                        </div>
                        <!-- Checkbox list item -->
                        <div class="offer-filter__checkbox product_filter">
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-product="credit card"
                                           name="techcombank:promotion-products/credit-card" type="checkbox"
                                           value="credit-card" checked>
                                    <span>Credit card</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-product="debit card"
                                           name="techcombank:promotion-products/debit-card" type="checkbox"
                                           value="debit-card" checked>
                                    <span>Debit card</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-product="account"
                                           name="techcombank:promotion-products/account" type="checkbox"
                                           value="account" checked>
                                    <span>Account</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-product="techcombank priority"
                                           name="techcombank:promotion-products/techcombank-priority" type="checkbox"
                                           value="techcombank-priority" checked>
                                    <span>Techcombank Priority</span>
                                </label>
                            </div>
                        </div>

                        <!--CATEGORY-->
                        <div class="offer-filter__title filter--border-bottom">
                            <h6>Category</h6>
                            <span class="material-symbols-outlined btn-close hidden">close</span>
                        </div>
                        <!-- Checkbox list item -->
                        <div class="offer-filter__checkbox category_filter">
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="cuisine"
                                           name="techcombank:promotion-categories/cuisine" type="checkbox"
                                           value="cuisine" checked>
                                    <span>Cuisine</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="travel and food ordering"
                                           name="techcombank:promotion-categories/travel-and-food-ordering"
                                           type="checkbox"
                                           value="travel-and-food-ordering" checked>
                                    <span>Travel and Food ordering</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="travel and entertainment"
                                           name="techcombank:promotion-categories/travel-and-entertainment" type="checkbox"
                                           value="travel-and-entertainment" checked>
                                    <span>Travel and Entertainment</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="shopping"
                                           name="techcombank:promotion-categories/shopping" type="checkbox"
                                           value="shopping" checked>
                                    <span>Shopping</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="healthcare"
                                           name="techcombank:promotion-categories/healthcare" type="checkbox"
                                           value="healthcare" checked>
                                    <span>Healthcare</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="education"
                                           name="techcombank:promotion-categories/education" type="checkbox"
                                           value="education" checked>
                                    <span>Education</span>
                                </label>
                            </div>
                            <div class="offer-filter__checkbox-item-mobile">
                                <label class="checkbox-item__wrapper">
                                    <input class="input__checkbox" data-category="others"
                                           name="techcombank:promotion-categories/others" type="checkbox"
                                           value="others" checked>
                                    <span>Others</span>
                                </label>
                            </div>
                        </div>

                        <!-- Filter title -->
                        <div class="offer-filter__title filter--border-bottom">
                            <h6>Select merchant</h6>
                        </div>
                        <!-- Dropdown item -->
                        <div class="offer-filter__dropdown analytics-ol-merchant-mobile">
                            <div class="dropdown__wrapper">
                                <div class="dropdown__display">
                                    <span class="display__text text--ellipsis" data-placeholder="Select merchant">Select merchant</span>
                                    <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg"/>
                                </div>
                                <div class="dropdown__list dropdown--ease-transition --bordered --shadow">
                                    <ul>
                                        <li class="dropdown__item" data-merchant-type="all"
                                            value="all">
                                            Select all
                                        </li>
                                        <li class="dropdown__item" data-merchant-type="siam premium outlets bangkok"
                                            value="siam premium outlets bangkok">
                                            SIAM Premium outlets Bangkok
                                        </li>
                                        <li class="dropdown__item" data-merchant-type="living house"
                                            value="living house">
                                            Living house
                                        </li>
                                    </ul>
                                    <input class="dropdown-selected-value" id="cardType" type="hidden" value=""/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tcb-modal_action-bar">
                        <div class="tcb-button tcb-button--light-black">
                            Apply filter
                            <img alt="Red arrow"
                                 src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow.svg"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="offer-listing-filter__footer"></div>
<script crossorigin="anonymous"
        integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g="
        src="https://code.jquery.com/jquery-3.7.0.min.js">
</script>
<script src="/etc.clientlibs/techcombank/clientlibs/clientlib-dependencies.js"
        type="text/javascript">
</script>
<script src="/etc.clientlibs/techcombank/clientlibs/clientlib-base.js"
        type="text/javascript">
</script>
<script src="/etc.clientlibs/techcombank/clientlibs/clientlib-site.js"
        type="text/javascript">
</script>
</body>
</html>