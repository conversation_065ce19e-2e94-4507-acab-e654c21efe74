<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Pick Card Filter</title>
    <meta name="template" content="page-content" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />
    <link rel="stylesheet" href="/etc.clientlibs/techcombank/clientlibs/clientlib-site.css" type="text/css">

</head>

<div class="promotionfilterprimary">

    <div class="credit-card-listing__content card-list-picker-filter__content">
        <div class="credit-card-listing__container">
            <div class="wrapper-credit-card-listing-title">
                <p class="credit-card-listing__title">Ưu đãi ẩm thực khi bạn sở hữu</p>
            </div>
            <div class="credit-card-listing__items">
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-dam">Tích dặm</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/hoan-tien">Hoàn tiền</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/hoan-tien">Hoàn tiền</button>
                </div>
                <div class="filter__item">
                    <button class="credit-card-listing__button filter-selected" type="button"
                        data-card-type="techcombank:card-types/vi/credit-card/tich-diem-chi-tieu">Tích điểm chi
                        tiêu</button>
                </div>

            </div>
        </div>
    </div>

</div>

</html>