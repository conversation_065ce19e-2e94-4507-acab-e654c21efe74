<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="0719c3b8-8ec3-471e-886d-a9483b7084b3"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="cb1edd15-4572-4628-8b46-9db5d4741692"  >
</g>
<g transform="matrix(67.5 0 0 67.5 540 540)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: nonzero; opacity: 1;"  transform=" translate(-8, -5.28)" d="M 0.929195 0.964055 C 1.45263 0.434263 2.3079 0.433797 2.83191 0.963018 L 8.00003 6.18251 L 13.1681 0.963017 C 13.6922 0.433797 14.5474 0.434264 15.0709 0.964055 C 15.5858 1.48528 15.5858 2.32373 15.0709 2.84496 L 8.00003 10.0017 L 0.929195 2.84495 C 0.414227 2.32373 0.414227 1.48528 0.929195 0.964055 Z" stroke-linecap="round" />
</g>
</svg>