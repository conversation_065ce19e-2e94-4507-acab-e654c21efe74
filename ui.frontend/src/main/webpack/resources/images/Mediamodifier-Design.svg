<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="058efe9e-403b-45e4-8d5a-dbdf83df22ec"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="49622a53-7604-4a7d-9697-d2a8e020d52f"  >
</g>
<g transform="matrix(67.5 0 0 67.5 540 540)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -0.02 -4.82)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-7.98, -2.68)" d="M 0.00197138 2.70287 C 0.000882615 2.31105 0.317635 1.99253 0.709458 1.99144 L 15.2533 1.95103 C 15.6452 1.94994 15.9637 2.26669 15.9648 2.65851 C 15.9659 3.05034 15.6491 3.36885 15.2573 3.36994 L 0.713401 3.41036 C 0.321578 3.41144 0.00306014 3.09469 0.00197138 2.70287 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0 -0.24)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8, -7.26)" d="M 0.0185992 7.287 C 0.0175104 6.89517 0.334263 6.57666 0.726086 6.57557 L 15.27 6.53515 C 15.6618 6.53407 15.9803 6.85082 15.9814 7.24264 C 15.9825 7.63446 15.6657 7.95298 15.2739 7.95407 L 0.730028 7.99448 C 0.338206 7.99557 0.0196879 7.67882 0.0185992 7.287 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0.02 4.35)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-8.02, -11.85)" d="M 0.0352274 11.8711 C 0.0341387 11.4793 0.350891 11.1608 0.742714 11.1597 L 15.2866 11.1193 C 15.6784 11.1182 15.9969 11.4349 15.998 11.8268 C 15.9991 12.2186 15.6824 12.5371 15.2905 12.5382 L 0.746657 12.5786 C 0.354834 12.5797 0.0363162 12.2629 0.0352274 11.8711 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 3.78 -4.99)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-11.78, -2.51)" d="M 11.7712 0.00197159 C 12.163 0.000882831 12.4816 0.317635 12.4827 0.709458 L 12.4927 4.31132 C 12.4937 4.70314 12.177 5.02166 11.7852 5.02275 C 11.3934 5.02384 11.0748 4.70709 11.0737 4.31526 L 11.0637 0.713401 C 11.0626 0.321578 11.3794 0.00306035 11.7712 0.00197159 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 1.58 4.4)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-9.58, -11.9)" d="M 9.57702 9.38743 C 9.96884 9.38634 10.2874 9.70309 10.2885 10.0949 L 10.2985 13.6968 C 10.2995 14.0886 9.9828 14.4071 9.59097 14.4082 C 9.19915 14.4093 8.88063 14.0925 8.87954 13.7007 L 8.86954 10.0989 C 8.86845 9.70703 9.1852 9.38852 9.57702 9.38743 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -3.47 -0.14)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(237,27,36); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-4.53, -7.36)" d="M 4.51934 4.84937 C 4.91117 4.84829 5.22968 5.16504 5.23077 5.55686 L 5.24078 9.15872 C 5.24187 9.55055 4.92512 9.86906 4.5333 9.87015 C 4.14147 9.87124 3.82296 9.55449 3.82187 9.16267 L 3.81186 5.5608 C 3.81077 5.16898 4.12752 4.85046 4.51934 4.84937 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>