// Stylesheets
import './main.scss';

// Javascript or Typescript
import './**/*.js';
import './**/*.ts';
import '../components/**/*.js';
import { TcbTracker } from './scripts/tcb-tracker';
import { TcbGlossary } from './scripts/glossary';
import { TcbRecommendPrePagePopup } from './scripts/tcb-recommend-pre-page-popup';
import { TcbGlossaryTooltip } from './scripts/glossary-tooltip';
import { TcbSearchEngineMobile } from './scripts/tcb-search-engine-mobile';
import { TcbGoogleSearch } from './scripts/tcb-google-search';
import { TcbSearchPrimary } from './scripts/search-primary';
import { RecommendCategory } from './scripts/recommend-category';
import { TcbGlobalAnnouncement } from './scripts/tcb-global-announcement';
import { TcbButton } from './scripts/tcb-button';
import { TcbHeader } from './scripts/tcb-header';
import { TcbAutoEarning } from './scripts/tcb-auto-earning';
import { OfferDetailElement } from './scripts/offer-detail-element';
import { CardTypeFilterComponent } from './scripts/credit-card-hub';
import { OfferAccordionElement } from './scripts/offer-accordion-element';
import { TcbPromotionBannerCarousel } from './scripts/promotion-hub-banner';
import { OfferCardComponent } from './scripts/hub-page';
import { ExclusiveOffer } from './scripts/exclusive-offer-listing';
import { SlidePromotion } from './scripts/banner-card-detail';
import { CardPicker } from './scripts/pick-card-listing';
import { BannerContainer } from './scripts/banner-container';
import { CardBenefitComponent } from './scripts/card-benefit';
import { SecondaryFilterComponent } from './scripts/secondary-filter';
import { TcbPromotionCard } from './scripts/tcb-promotion-card';
import { TcbPromotionProductList } from './scripts/promotion-product-listing';
import { PromotionFilterPrimary } from './scripts/promotion-filter-primary';
import { TcbNeedPromotionSearch } from './scripts/tcb-need-promotion-search';
import { TcbResultPage } from './scripts/tcb-result-page';
import { TcbResultPageMembership } from './scripts/tcb-result-page-membership';
import { PromotionHubTarget } from './scripts/promotion-hub-target';

customElements.define('tcb-tracker', TcbTracker);
customElements.define('tcb-recommend-pre-page-popup', TcbRecommendPrePagePopup);
customElements.define('tcb-glossary', TcbGlossary);
customElements.define('tcb-glossary-tooltip', TcbGlossaryTooltip);
customElements.define('tcb-search-engine-mobile', TcbSearchEngineMobile);
customElements.define('tcb-google-search', TcbGoogleSearch);
customElements.define('tcb-search-primary', TcbSearchPrimary);
customElements.define('tcb-global-announcement', TcbGlobalAnnouncement);
customElements.define('tcb-button', TcbButton);
customElements.define('tcb-header', TcbHeader);
customElements.define('tcb-auto-earning', TcbAutoEarning);
customElements.define('tcb-offer-listing-detail', OfferDetailElement);
customElements.define('tcb-credit-card-hub', CardTypeFilterComponent);
customElements.define('tcb-offer-accordion', OfferAccordionElement);
customElements.define('tcb-promotion-banner-carousel', TcbPromotionBannerCarousel);
customElements.define('tcb-offer-card-component', OfferCardComponent);
customElements.define('tcb-exclusive-offer', ExclusiveOffer);
customElements.define('tcb-slide-promotion', SlidePromotion);
customElements.define('tcb-card-picker', CardPicker);
customElements.define('tcb-banner-container', BannerContainer);
customElements.define('tcb-card-benefit', CardBenefitComponent);
customElements.define('tcb-secondary-filter', SecondaryFilterComponent);
customElements.define('tcb-promotion-card', TcbPromotionCard);
customElements.define('tcb-promotion-product-listing', TcbPromotionProductList);
customElements.define('tcb-promotion-filter-primary', PromotionFilterPrimary);

customElements.define('tcb-need-promotion-search', TcbNeedPromotionSearch);
customElements.define('tcb-result-page', TcbResultPage);
customElements.define('tcb-result-page-membership', TcbResultPageMembership);
customElements.define('tcb-promotion-hub-target', PromotionHubTarget);
customElements.define('tcb-recommend-category', RecommendCategory);
