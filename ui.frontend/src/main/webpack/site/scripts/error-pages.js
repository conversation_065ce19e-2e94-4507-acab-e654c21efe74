import { AA_ATTRIBUTE } from './analytics';

$(document).ready(function () {
  // Error Page Analytics
  if ($('.error-page-component').length > 0) {
    let errorMessageVal = $('.error-page-component').data().trackingClickInfoValue;
    if (errorMessageVal) {
      let formattedJson = $('.error-page-component').data().errorMessage.replace(/'/g, '’');
      let jsonStr = errorMessageVal.replace(/'/g, '"');
      let parsedJson = JSON.parse(jsonStr);
      parsedJson.errorMessage = formattedJson;
      let updatedValues = JSON.stringify(parsedJson).replace(/"/g, "'");
      $('.error-page-component').attr(AA_ATTRIBUTE.CLICK_INFO_VALUE, updatedValues);
    }
    $('.error-page-component').trigger('click');
  }
});
