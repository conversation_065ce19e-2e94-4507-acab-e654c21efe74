import 'jquery-ui/ui/widgets/autocomplete';
import moment from 'moment';

const itemSection = (type, name, address, src, phone, lat, long, id, canBook) => `
<div class="item">
    <div class="image">
        <img
        src="${src}"
        alt />
    </div>
    <div class="content">
        <p class="type">${type}</p>
        <p class="name">${name}</p>
        <p class="address">${address}</p>
        <p class="phone">${phone}</p>
        <input type="hidden" class="lattitude" value="${lat}"/>
        <input type="hidden" class="longtitude" value="${long}"/>
        <input type="hidden" class="atmBranchItemId" value="${id}"/>
        <input type="hidden" class="canBook" value="${canBook}"/>
    </div>
</div>
`;

const atmIcon = 'https://d1kndcit1zrj97.cloudfront.net/uploads/icon_atm_2dd4ca616f.png?w=1920&q=75';
const cdmIcon =
  'https://dehs0b8bq571d.cloudfront.net/uploads/Property_1_Active_1_b881a6b4f8_118c494fc0.png?w=1080&q=75';
const branchicon = 'https://d1kndcit1zrj97.cloudfront.net/uploads/icon_branch_e24da9e40d.png?w=1920&q=75';

let srcGoogleMap = 'https://www.google.com/maps/embed/v1/place?key=';
const googleHref = 'https://www.google.com/maps/search/?api=1&query=';

const date = new Date();
let timeList = [
];

const timeNow = date.getHours() + ':' + date.getSeconds();
const dayOfWeek = date.getDay();

const itemTimeSelect = (setTime, className, itemId) => `
<div class="item">
    <h3 class="${className ?? ''}"  itemId="${itemId ?? ''}">${setTime}</h3>
    <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-chevron-right.svg">
</div>
`;


let onlyATM = [];
let onlyCDM = [];
let branchList = [];

const EMAIL = 'emailAddress';
const PHONE = 'phone';
const TAXCODE = 'taxCode';
let CityList = [];
let defaultLat = 21.***************;
let defaultLong = 105.**************;

const appendItems = (element, type, arr) => {
  let icon;
  if (type == 'ATM') {
    icon = atmIcon;
  } else if (type == 'CDM') {
    icon = cdmIcon;
  } else {
    icon = branchicon;
  }

  if (arr && arr.length > 0) {
    for (let item of arr) {
      const latTitude = item.lat ?? item.atmLatitude;
      const longTidude = item.long ?? item.atmLongitude;
      const id = item.atmId ?? item.branchIdNumber;
      const canBook = item.canBook || false;
      const itemHtml = itemSection(type, item.atmName ?? item.branchNm, item.atmAddress ?? item.pstlAdr?.adrLine, icon, item.phone, latTitude, longTidude, id, canBook);
      element.append(itemHtml);
    }
  }
};

const removeAccents = (str) => {
  return str
    ?.normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D');
};

const changeStrToSearch = (str) => {
  return removeAccents(str?.toLowerCase().replace(/\s/g, ''));
};

const searchAddress = (searchTag, arr, mode) => {
  const newArr = [];

  for (let item of arr) {
    if (searchTag) {
      for (let tag of searchTag) {
        const searchValue = changeStrToSearch(tag);
        let add;
        if (mode == "name") {
          add = changeStrToSearch(item.atmName ?? item.branchNm);
        } else {
          add = changeStrToSearch(item.atmAddress ?? item?.pstlAdr?.adrLine);
        }

        if (add.indexOf(searchValue) != -1) {
          newArr.push(item);
          break;
        }
      }
    }
  }

  return newArr;
};

const filterNameOrAddress = (searchInput, dataInput, mode) => {
  const searInputTmp = changeStrToSearch(searchInput);
  return (dataInput && dataInput.length > 0) ? dataInput.filter((item) => {
    if (mode === 'name') {
      const itemTmp = changeStrToSearch(item.atmName ?? item?.branchNm);
      return itemTmp?.includes(searInputTmp);
    } else {
      const itemTmp = changeStrToSearch(item.atmAddress ?? item?.pstlAdr?.adrLine);
      return itemTmp?.includes(searchInput);
    }
  }) : [];
}

const getAutocompleteList = (arr) => {
  const newArr = [];
  for (let item of arr) {
    newArr.push(item.atmName ?? item.branchNm);
  }

  return newArr;
};

const appendResults = (atmResults, cdmResults, branchResults, resultEl, searchType, message, branchLabel) => {
  // append atm list
  resultEl.find('.atm-list').empty();
  appendItems(resultEl.find('.atm-list'), 'ATM', atmResults);

  // append cdm list
  resultEl.find('.cdm-list').empty();
  appendItems(resultEl.find('.cdm-list'), 'CDM', cdmResults);


  // append branch list
  resultEl.find('.branch-list').empty();
  appendItems(resultEl.find('.branch-list'), branchLabel, branchResults);

  let count = searchType == 'ATM' ? atmResults.length + ' '+ searchType: searchType == 'CDM' ? cdmResults.length + ' '+  searchType : branchResults.length + ' '+  branchLabel;
  const regex = /\$count/g;
  const replaceMessage = '<span class="count">' + count + '</span>';
  resultEl.find('.result-message').html(message.replace(regex, replaceMessage));
  if(searchType === "ATM" && atmResults.length > 0) {
    setMapWithArr($('.atm-map-component').find('#atm-map'),atmResults, searchType);
  } else if(searchType === "CDM" && cdmResults.length > 0) {
    setMapWithArr($('.atm-map-component').find('#atm-map'),cdmResults, searchType);
  } else if(branchResults.length > 0) {
    setMapWithArr($('.atm-map-component').find('#atm-map'),branchResults, searchType);
  }
};

const appendFilterResults = (searChName, selectedCityindex, selectedDistrictIndex, searchResult, searchType, message, branchLabel) => {
  let atmResults;
  let cdmResults;
  let branchResults;

  if (selectedCityindex) {

    // filter by city
    const atmCityFilter = onlyATM.filter((atm) => atm.cityCode == selectedCityindex);
    const cdmCityFilter = onlyCDM.filter((cdm) => cdm.cityCode == selectedCityindex);
    const branchCityFilter = branchList.filter((branch) => branch.cityId == selectedCityindex);

    // filter by district
    if (selectedDistrictIndex) {
      atmResults = onlyATM.filter((atm) => atm.cityCode == selectedCityindex && atm.districtCode == selectedDistrictIndex);
      cdmResults = onlyCDM.filter((cdm) => cdm.cityCode == selectedCityindex && cdm.districtCode == selectedDistrictIndex);
      branchResults = branchList.filter((branch) => branch.cityId == selectedCityindex && branch.districtId == selectedDistrictIndex);
    } else {
      atmResults = atmCityFilter;
      cdmResults = cdmCityFilter;
      branchResults = branchCityFilter || branchList;
    }
  } else {
    atmResults = onlyATM;
    cdmResults = onlyCDM;
    branchResults = branchList;
  }
  atmResults = filterNameOrAddress(searChName, atmResults, 'name');
  cdmResults = filterNameOrAddress(searChName, cdmResults, 'name');
  branchResults = filterNameOrAddress(searChName, branchResults, 'name');
  appendResults(atmResults, cdmResults, branchResults, searchResult, searchType, message, branchLabel);
};

const setMapWithArr = (map, arr, type) => {
  const atmCoordinates = `${arr[0].atmLatitude}, ${arr[0].atmLongitude}`;
  const branchCoordinates = `${arr[0].lat}, ${arr[0].long}`;
  const coordinates = type === "ATM" || type === "CDM" ? atmCoordinates : branchCoordinates;
  map.attr('src', srcGoogleMap + coordinates);
}

const setMap = (map, item) => {
  if(item && item.length > 0) {
    const location = item.find('.lattitude').val() + ','+ item.find('.longtitude').val();
    map.attr('src', srcGoogleMap + location);
  }
};

$(document).ready(function () {
  $('.branches-atm-locate input[name=youAre]').on('change',function(e){

    let typeCUstomer = $('input[name=youAre]:checked').parent().find('.branches-radio-checkmark span:first-child').text();
    let types=[".atm-type",".branch-type",".cdm-type"];
    let bracnhValue='';
    let branchValueStr='';
    let branchValueJson={};
    let updatedValues = '';
    $.each(types,(i,v)=>{
      if($(v).data().trackingClickInfoValue) {
        bracnhValue = $(v).data().trackingClickInfoValue
        branchValueStr = bracnhValue.replace(/'/g, '"');
        branchValueJson = JSON.parse(branchValueStr);
        branchValueJson.customerType = typeCUstomer;
        updatedValues = JSON.stringify(branchValueJson).replace(/"/g, "'");
        $(v).attr("data-tracking-click-info-value", updatedValues);
      }
    });
    $('input[name="youAre"]').prop('checked', false);
    $(this).prop('checked', true);
  })

  $('.atm-map-component').each(function (i, element) {
    srcGoogleMap += $(this).attr("data-map-key") + '&q=';
  });
});

// ATM Map component
$(document).ready(function () {
  const atmMap = $('.atm-map-component');

  atmMap.each(function (i, element) {
    const typeSearchMapBtn = $(this).find('.search-type button');
    const ItemSection = $(this).find('.show-list');
    const atmListItem = ItemSection.find('.atm-list');
    const branchListItem = ItemSection.find('.branch-list');
    const cdmListItem = ItemSection.find('.cdm-list');
    const mapIframe = $(this).find('#atm-map');
    const atmDetail = $(this).find('.detail-item');
    const itemImageDetail = atmDetail.find('.atm img');
    const itemNameDetail = atmDetail.find('.name');
    const itemAddressDetail = atmDetail.find('.address');
    const expandCardsPhone = $(this).find('.expand_cards_phone');
    const itemContactNumber = $(expandCardsPhone).find('.contact-number');

    const comebackBtn = atmDetail.find('.comeback');
    const direction = atmDetail.find('.direction');
    const branchBtn = atmDetail.find('.branch-btn');

    const noOption = $(this).attr("data-no-option");
    const resultMessage = $(this).attr("data-result-message");
    const placeHolder = $(this).attr("data-district-place-holder");

    const searchFilter = $(this).find('.search-filter');
    const cityFilter = searchFilter.find('.city');
    const districtFilter = searchFilter.find('.district');
    const citySelect = cityFilter.find('.option');
    const cityOptions = cityFilter.find('.select-options');
    const districtSelect = districtFilter.find('.option');
    const districtOptions = districtFilter.find('.select-options');

    const searchInput = $(this).find('.search-input');
    const searchResult = $(this).find('.search-result');

    let searchMapState = 1;
    let atmCdmCities = [];
    const api = $(this).attr("data-api");
    const apiBranch = $(this).attr("data-api-branch");
    const apiBrahchSuffix = $(this).attr("data-api-branch-suffix");
    const dataBranchLabel = $(this).attr("data-branch-label");
    let selectedCityindex;

    getCurrentPosition(function(message){
      getAtmCdmList(api, '', '', '', null).then(function (dataResponse) {
        onlyATM = sortByDistance(defaultLat, defaultLong, [...dataResponse]);
        onlyCDM = sortByDistance(defaultLat, defaultLong, [...dataResponse.filter(item => !!item.isCDM)]);
        // init items
        appendResults(onlyATM, onlyCDM, [], $(element).find('.search-result'), 'ATM', resultMessage, dataBranchLabel);
        // init map
        setMap($(element).find('#atm-map'), $(element).find('.search-result .atm-list').children().first());
        // autocomplete input
        autocompleteType('ATM', searchInput.find('input'), onlyATM, api, '', '', searchResult, null);

        // select search result for detail
        $(element).find('.show-list').on('click', '.item', function () {
          showDetail($(this));
        });
      });
      $.ajax({
        url: '/graphql/execute.json/techcombank/branchPhones', type: "GET", dataType: "json", success: function (response) {
          branchList = response.data.branchFragmentList.items.map(function(branch) {
            const { adrLine, ...rest } = branch;
            return {
              ...rest,
              pstlAdr : {
                adrLine : adrLine.plaintext
              },
            };
          });
          branchList = sortByDistanceToCurrentLocation(branchList,defaultLat, defaultLong);
          getBranchList(apiBranch, apiBrahchSuffix, '', defaultLat, defaultLong).then(dataResponse => {
            let QMSBranchList = dataResponse.branches ?? [];
            branchList = branchList.map((item) => {
              const foundQMSItem = QMSBranchList.find(
                (QMSitem) => QMSitem.branchCd === item.branchId
              );
              return foundQMSItem
                ? {
                    ...item,
                    branchIdNumber: foundQMSItem.branchId,
                    canBook: 'true',
                  }
                : item;
            });
            // append branch list
            $(element).find('.search-result .branch-list').find('.branch-list').empty();
            appendItems($(element).find('.search-result .branch-list'), dataBranchLabel, branchList);
          });
        }, error: function (error) {
          console.log(error);
        }
      });
    });


    getCityList(api).then(function (dataResponse) {
      CityList = dataResponse;
      const hanoi = CityList.find((item) => item.cityName === "Hà Nội");
      const hoChiMinh = CityList.find((item) => item.cityName === "Hồ Chí Minh");
      CityList = CityList.filter(
        (item) => item.cityName !== "Hà Nội" && item.cityName !== "Hồ Chí Minh"
      );
      CityList.sort((a, b) => {
        const nameA = a?.cityName?.normalize("NFD").toLowerCase().replace(/[\u0300-\u036f]/g, "").replace("đ","d");
        const nameB = b?.cityName?.normalize("NFD").toLowerCase().replace(/[\u0300-\u036f]/g, "").replace("đ","d");
        if (nameA < nameB) return -1;
        if (nameA > nameB) return 1;
        return 0;
      });
      CityList.unshift(hanoi);
      CityList.splice(1, 0, hoChiMinh);
      atmCdmCities = CityList;
      cityRendering(CityList);
      const nearestOption = cityOptions.find('li[value="nearest"]');
        if (nearestOption.length) {
          setTimeout(() => {
            nearestOption.click();
          }, 1000);
        }
    }).catch(function (error) {
      console.log(error);
    })

    // style focus search input
    searchInput.find('input').focus(function () {
      searchInput.find('.search').addClass('focus');
    });

    searchInput.find('input').focusout(function () {
      searchInput.find('.search').removeClass('focus');
    });

    // open city select-options
    citySelect.click(function () {
      if ($(this).hasClass('showed')) {
        $(this).removeClass('showed');
      } else {
        $(this).addClass('showed');
      }

      districtSelect.removeClass('showed');
    });

    // open district select-options
    districtSelect.click(function () {

      if ($(this).hasClass('showed')) {
        $(this).removeClass('showed');
      } else {
        $(this).addClass('showed');
      }
      citySelect.removeClass('showed');
    });

    function getDistrictByType() {
      const selectedCityIndex = cityOptions.find('li.selected').attr('city');
      if(selectedCityIndex) {
        getDistrictList(element, api, selectedCityIndex).then(function (dataResponse) {
          const districts = dataResponse;
          districts.sort((a, b) => {
            const nameA = a?.districtName?.normalize("NFD").toLowerCase().replace(/[\u0300-\u036f]/g, "").replace("đ","d");
            const nameB = b?.districtName?.normalize("NFD").toLowerCase().replace(/[\u0300-\u036f]/g, "").replace("đ","d");
            if (nameA < nameB) return -1;
            if (nameA > nameB) return 1;
            return 0;
          });
          districtRendering(districts);
        });
      } else {
        districtRendering(CityList);
      }
    }

    // close select-option box
    $(document).click(function (event) {
      if (
        !$(event.target).is(cityOptions) &&
        !$(event.target).parents().is(cityOptions) &&
        !$(event.target).is(citySelect) &&
        !$(event.target).is(districtSelect) &&
        !$(event.target).parents().is(citySelect) &&
        !$(event.target).parents().is(districtSelect)
      ) {
        citySelect.removeClass('showed');
        districtSelect.removeClass('showed');
      }
    });

    // select type of search results
    typeSearchMapBtn.click(function () {
      const searchValue = searchInput.find('input').val();
      const selectedDistricts = $(districtOptions).find('.selected').attr('district');
      typeSearchMapBtn.removeClass('clicked');
      $(this).addClass('clicked');
      const type = $(this).find('span').text();
      cityRendering(atmCdmCities);
            getDistrictByType();
            appendFilterResults(searchValue, selectedCityindex, selectedDistricts, searchResult, type, resultMessage, dataBranchLabel);
      selectedCityindex = $(cityOptions).find('.selected').attr('city');
      let btnBracnhValue = $(this).data().trackingClickInfoValue;
      if(btnBracnhValue) {
        let btnJsonStr = btnBracnhValue.replace(/'/g, '"');
        let btnJson = JSON.parse(btnJsonStr);
        let bracnhValue='';
        let branchValueStr='';
        let branchValueJson={};
        let updatedValues = '';
        $('.branches-radio-container').each((i,v)=>{
          bracnhValue = $(v).data().trackingClickInfoValue
          if(!bracnhValue) return;
          branchValueStr = bracnhValue.replace(/'/g, '"');
          branchValueJson = JSON.parse(branchValueStr);
          branchValueJson.branchSearchType = btnJson.branchSearchType;
          updatedValues = JSON.stringify(branchValueJson).replace(/"/g, "'");
          $(v).attr("data-tracking-click-info-value", updatedValues);
        });
      }
      // show search results
      if (type == 'ATM') {
        atmListItem.css('display', 'block');
        cdmListItem.css('display', 'none');
        branchListItem.css('display', 'none');
        expandCardsPhone.css('display', 'none');


        searchMapState = 1;
      } else if (type == 'CDM') {
        atmListItem.css('display', 'none');
        cdmListItem.css('display', 'block');
        branchListItem.css('display', 'none');
        expandCardsPhone.css('display', 'none');

        searchMapState = 2;
      } else {
        atmListItem.css('display', 'none');
        cdmListItem.css('display', 'none');
        branchListItem.css('display', 'block');
        expandCardsPhone.css('display', 'block');

        searchMapState = 3;
      }

      ItemSection.css('display', 'block');
      atmDetail.css('display', 'none');
    });

    // show detail item
    function showDetail(item) {

      ItemSection.css('display', 'none');
      atmDetail.css('display', 'block');

      // set item detail info
      const itemImageSrc = item.find('.image img').attr('src');
      const itemName = item.find('.name').text();
      const itemAddress = item.find('.address').text();
      const phone = item.find('.phone').text();
      const lat = item.find('.lattitude').val();
      const long = item.find('.longtitude').val();
      const currentBranchSelect = item.find('.atmBranchItemId').val();
      const canBook = item.find('.canBook').val();

      if (searchMapState == 3 && canBook === "true" ) {
        branchBtn.css('display', 'flex');
      } else {
        branchBtn.css('display', 'none');
      }

      itemImageDetail.attr('src', itemImageSrc);
      itemNameDetail.text(itemName);
      itemAddressDetail.text(itemAddress);
      atmDetail.find('.btn-contactus .cta-button').attr('currentBranchSelect', currentBranchSelect);
      if (phone != 'undefined' && phone != "null") {
        itemContactNumber.text(phone);
        expandCardsPhone.css('display', 'block');
      } else {
        expandCardsPhone.css('display', 'none');
      }

      // init map
      mapIframe.attr('src', srcGoogleMap + lat+','+long);
      direction.attr('href', googleHref + lat+','+long);
    }

    // comeback to list result
    comebackBtn.click(function () {
      atmDetail.css('display', 'none');
      ItemSection.css('display', 'block');
    });

    function districtRendering(districts) {
      const prevSelected = $(districtOptions).find('.selected').text();
      districtOptions.find('ul li:not([value="all"])').remove();
      const currentType = $(element).find('.search-type button.clicked').find('span').text();

      for (let item of districts) {
        const disTrictCode = item.districtCode ?? item.districtCd;
        const disTrictName = item.districtName ?? item.districtNm;
        if (disTrictCode && disTrictName) {
          let districtItemClass = "";
          if (prevSelected) {
            districtItemClass =
              changeStrToSearch(prevSelected) == changeStrToSearch(disTrictName)
                ? "selected"
                : "";
          }

          districtOptions
            .find("ul")
            .append(
              `<li district=${disTrictCode} class="${districtItemClass}" >${disTrictName}</li>`
            );
        }
      }

      // when select districts
      districtOptions.find('li').click(function () {
        districtSelect.removeClass('showed');
        districtSelect.find('input').attr('placeholder', $(this).text());

        const selectedDistrict = $(this).attr('district');

        const searchValue = searchInput.find('input').val();
        appendFilterResults(searchValue, selectedCityindex, selectedDistrict, searchResult, currentType, resultMessage, dataBranchLabel);

        districtOptions.find('li').removeClass('selected');
        $(this).addClass('selected');

        // reset map
        if (searchMapState == 1) {
          setMap(mapIframe, atmListItem.children().first());
        } else if (searchMapState == 2) {
          setMap(mapIframe, cdmListItem.children().first());
        } else {
          setMap(mapIframe, branchListItem.children().first());
        }

        comebackBtn.click();
      });
      const selectedCity = $(cityOptions).find('.selected').attr('city');
      const selectedDistricts = $(districtOptions).find('.selected').attr('district');
      const searchData = searchInput.find('input').val();
      appendFilterResults(searchData, selectedCity, selectedDistricts, searchResult, currentType, resultMessage, dataBranchLabel);
      const data = currentType === 'ATM' ? onlyATM : currentType === 'CDM' ? onlyCDM : branchList;
      autocompleteType(currentType, searchInput.find('input'), data, api, selectedCity, selectedDistricts, searchResult, currentType);
      const dataShow = currentType === 'ATM' ? atmListItem : currentType === 'CDM' ? cdmListItem : branchListItem;
      setMap(mapIframe, dataShow.children().first());
    }

    function cityRendering(CityList) {
      const prevSelected = $(cityOptions).find('.selected').text();
      cityOptions.find('ul li:not([value="all"]):not([value="nearest"])').remove();
      for (let city of CityList) {
        const cityCode = city.cityCode ?? city?.city?.cityCd;
        const cityName = city.cityName ?? city?.city?.cityNm;
        let selectedClassName = "";
        if (prevSelected) {
          selectedClassName =
            changeStrToSearch(prevSelected) == changeStrToSearch(cityName)
              ? "selected"
              : "";
        }
        if (cityCode && cityName) {
          $(element)
            .find(".search-filter .city .select-options ul")
            .append(
              `<li city='${cityCode}' class="${selectedClassName}">${cityName}</li>`
            );
        }

        // when select cities
        cityOptions.find('li').off('click').click(function () {
          citySelect.removeClass('showed');
          const city = $(this).text();
          selectedCityindex = $(this).attr('city');
          const currentType = $(element).find('.search-type button.clicked').find('span').text();
          const searchValue = searchInput.find('input').val();
          appendFilterResults(searchValue, selectedCityindex, '', searchResult, currentType, resultMessage, dataBranchLabel);
          // disable/enable district select-options
          if ($(this).attr('value') != 'all' && $(this).attr('value') != 'nearest') {
            districtFilter.removeClass('disable');
          } else {
            districtFilter.addClass('disable');
          }

          // reset selected district when re-select cities
          citySelect.find('input').attr('placeholder', city);
          districtSelect.find('input').attr('placeholder', placeHolder);

          cityOptions.find('li').removeClass('selected');
          districtOptions.find('li').removeClass('selected');

          $(this).addClass('selected');
          getDistrictByType();

          // reset map
          if (searchMapState == 1) {
            setMap(mapIframe, atmListItem.children().first());
          } else if (searchMapState == 2) {
            setMap(mapIframe, cdmListItem.children().first());
          } else {
            setMap(mapIframe, branchListItem.children().first());
          }

          // reset detail item
          comebackBtn.click();
        });
      }
    }
    // autocomplete
    function initAutocomplete(inp, arr, api, cityCode, districtCode, searchResultelement, typeSelected, isCDMApi) {
      inp.autocomplete({
        appendTo: '#atm-map-autocomplete',
        minLength: 0,
        source: function (request, response) {
          const userInput = changeStrToSearch(request.term);
          const filteredItems = arr.filter(tag =>
            changeStrToSearch(tag).includes(userInput)
          );
          if (filteredItems.length === 0) {
            response([
              {
                label: noOption,
                value: "",
                disabled: true,
              },
            ]);
          } else {
            response(filteredItems);
          }
        },

        select: function (event, ui) {
          if (ui.item.disabled) {
            event.preventDefault();
            return;
          }
          $(this).attr('selected-value', ui.item.label);
          const name = [ui.item.label];
          appendSelectedAutocompleteItem(name);
          // reset detail item
          comebackBtn.click();

          if (searchMapState == 1) {
            searchResult.find('.result-message .count').text(atmListItem.children().length);
          } else if (searchMapState == 2) {
            searchResult.find('.result-message .count').text(cdmListItem.children().length);
          } else {
            searchResult.find('.result-message .count').text(branchListItem.children().length);
          }
          searchInput.val(name[0]);
          const selectedCityindex = $(cityOptions).find('.selected').attr('city');
          const selectedDistrict = $(districtOptions).find('.selected').attr('district');
          const currentType = isCDMApi ?? $(element).find('.search-type button.clicked').find('span').text();
          appendFilterResults(name[0], selectedCityindex, selectedDistrict, searchResult, currentType, resultMessage, dataBranchLabel);
        },
        search: function (event, ui) {
          // reset detail item
          comebackBtn.click();
          const currentType = isCDMApi ?? $(element).find('.search-type button.clicked').find('span').text();
          appendFilterResults($(this).val(), cityCode, districtCode, searchResultelement, currentType, resultMessage, dataBranchLabel);
        },
        create: function () {
          $(this).data('ui-autocomplete')._renderItem = function (ul, item) {
            return $('<li>').append(item.label).append('</li>').appendTo(ul);
          };
        },

        open: function () {
          $('#atm-map-autocomplete > ul').css('top', '-20px');
        },
      });
    }

    function autocompleteType(type, inp, lstAtmCdmBranch, api, cityCode, districtCode, searchResultelement, isCDMApi) {
      const arr = getAutocompleteList(lstAtmCdmBranch)
      initAutocomplete(inp, arr, api, cityCode, districtCode, searchResultelement, type, isCDMApi);
    }

    function appendSelectedAutocompleteItem(name) {
      let arr;

      atmListItem.empty();
      cdmListItem.empty();
      branchListItem.empty();
      if (searchMapState == 1) {
        arr = searchAddress(name, onlyATM, 'name');
        appendItems(atmListItem, 'ATM', arr);
        setMap(mapIframe, atmListItem.children().first());
      } else if (searchMapState == 2) {
        arr = searchAddress(name, onlyCDM, 'name');
        appendItems(cdmListItem, 'CDM', arr);
        setMap(mapIframe, cdmListItem.children().first());
      } else {
        arr = searchAddress(name, branchList, 'name');
        appendItems(branchListItem, dataBranchLabel, arr);
        setMap(mapIframe, branchListItem.children().first());
      }
    }

    function getCurrentPosition(callBackFn) {
      navigator.geolocation.getCurrentPosition(function (position) {
        defaultLat = position.coords.latitude;
        defaultLong = position.coords.longitude;
        callBackFn('Allow get position');
      }, function(error) {
        callBackFn(error);
      });
    }

    function sortByDistance(lat, long, data) {
      return data.sort((a,b) => {
        const distanceA = calculateDistance(lat, long, a.atmLatitude, a.atmLongitude, "K");
        const distanceB = calculateDistance(lat, long, b.atmLatitude, b.atmLongitude, "K");
        return distanceA - distanceB
      })
    }
  });
});

function calculateDistance(lat1, lon1, lat2, lon2, unit) {
  const radlat1 = (Math.PI * lat1) / 180;
  const radlat2 = (Math.PI * lat2) / 180;
  const theta = lon1 - lon2;
  const radtheta = (Math.PI * theta) / 180;
  let dist =
    Math.sin(radlat1) * Math.sin(radlat2) +
    Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
  if (dist > 1) {
    dist = 1;
  }
  dist = Math.acos(dist);
  dist = (dist * 180) / Math.PI;
  dist = dist * 60 * 1.1515;
  if (unit == "K") {
    dist = dist * 1.609344;
  }
  if (unit == "N") {
    dist = dist * 0.8684;
  }
  return dist;
}

function sortByDistanceToCurrentLocation(locations, currentLat, currentLon) {
  return locations.sort((a,b) => {
    a.lat = typeof a.lat === 'string' ? a.lat?.split(',').join('') : a.lat;
    a.long = typeof a.long === 'string' ? a.long?.split(',').join('') : a.long;
    b.lat = typeof b.lat === 'string' ? b.lat?.split(',').join('') : b.lat;
    b.long = typeof b.long === 'string' ? b.long?.split(',').join('') : b.long;
    const distanceA = calculateDistance(currentLat, currentLon, a.lat, a.long, "K");
    const distanceB = calculateDistance(currentLat, currentLon, b.lat, b.long, "K");
    return distanceA - distanceB;
  })
}

const appendAddress = (CityList, branchList, selectedCityindex, selectedDistrictIndex, searchResult) => {
  let branchResults;
  if (selectedCityindex != -1) {
    const branchCityFilter = branchList.filter((branch) => branch.cityId == selectedCityindex);
    if (selectedDistrictIndex) {
      branchResults = branchList.filter((branch) => branch.cityId == selectedCityindex && branch.districtId == selectedDistrictIndex);
    } else {
      branchResults = [...branchCityFilter];
    }
  } else {
    branchResults = [...branchList];
  }
  searchResult.find('.branch .address .address-list').empty();
  for (let branch of branchResults) {
    const setTime = branch;
    searchResult.find('.branch .address .address-list').append(itemTimeSelect(setTime?.pstlAdr?.adrLine ?? setTime.atmAddress, '', setTime.branchId));
  }
};

//appointment-booking

$(document).ready(function () {
  const btnContactUsList = $(this).find('.btn-contactus');
  const popUpBookingContents = $(this).find('.booking');
  popUpBookingContents.each(function (popUpBookingIndex) {
    const popUpBookingContent = $(this);
    const id = popUpBookingContent.closest('.popup').attr('id');
    const btnContactUs = btnContactUsList.find('a[href="#' + id + '"]').closest('.btn-contactus');
    const apiBranch = $(this).attr("data-api-branch");
    const apiBrahchSuffix = $(this).attr("data-api-branch-suffix");
    const apiServices = $(this).attr("data-api-service");
    const apiBookingSlot = $(this).attr("data-api-booking-slot");
    const apiBookingConfirm = $(this).attr("data-api-booking-confirm");
    const serviceIdFromAtm = $(this).attr("data-serviceId");
    let CityList = [];
    let branchList = [];

    const btnSubmit = popUpBookingContent.find('.btn-submit');
    const searchFilter = popUpBookingContent.find('.search-filter');
    const cityFilter = searchFilter.find('.city');
    const districtFilter = searchFilter.find('.district');
    const citySelect = cityFilter.find('.option');
    const cityOptions = cityFilter.find('.select-options');
    const districtSelect = districtFilter.find('.option');
    const districtOptions = districtFilter.find('.select-options');
    const addresses = popUpBookingContent.find('.address-list');
    const scheduleBookingDate = popUpBookingContent.find('.schedule .date');
    const scheduleBookingTime = popUpBookingContent.find('.schedule .time');
    let isPersonal = true;
    let timeSelected = '';
    const todayLabel = popUpBookingContent.attr('data-today-label');
    const tomorrowLabel = popUpBookingContent.attr('data-tomorrow-label');
    const defaultLabelConfirmDate = popUpBookingContent.find('.confirm-appointment .date-appointment').text();
    let defaultLat = 21.***************;
    let defaultLong = 105.**************;
    let typeCustomerFromATM = undefined;
    let branchSelected = '';


    const displaySection = (className, isActive) => {
      if (isActive) {
        popUpBookingContent.find(className).addClass('active');
        popUpBookingContent.find(className).removeClass('hidden');
      } else {
        popUpBookingContent.find(className).addClass('hidden');
        popUpBookingContent.find(className).removeClass('active');
      }
    }

    if(btnContactUs) {
      const modal = $(btnContactUs).find('a.cta-button').attr('href');
      if(modal) {
        const popUpBooking = $("#"+modal.slice(1));
        const closeButton = popUpBooking.find('.close');
        btnContactUs.click(function (e) {
          popUpBooking.removeClass('hide');
          popUpBooking.addClass('show');
          popUpBooking.css('display','flex');
          branchSelected = $(e.currentTarget).find('.cta-button').attr('currentBranchSelect');
          popUpBookingContent.find('.booking-container').removeClass('active');
          popUpBookingContent.find('.confirm-appointment').removeClass('active');
          clearForm(popUpBookingContent);
          e.preventDefault();

          if(popUpBookingContent.find('.client').length > 0) {
            displaySection('.client', true);
            displaySection('.service', false);
            displaySection('.branch', false);
            displaySection('.schedule', false);
            displaySection('.form', false);
            displaySection('.confirm-appointment', false);
          } else {
            typeCustomerFromATM = $('input[name=youAre]:checked').val();
            isPersonal = typeCustomerFromATM === "11" ? true : false;
            const branchIdFromATM = btnContactUs.closest(".atm-map_container").find('.cta-button').attr('currentBranchSelect');
            popUpBookingContent.find('input[name="selectedBranch"]').val(branchIdFromATM);
            popUpBookingContent.find('input[name="selectedService"]').val(serviceIdFromAtm);
            showLoading();
            if(!($(e.currentTarget).closest(".atm-map_container").length > 0)) return;
            getServices(apiServices, isPersonal, branchIdFromATM).then((result) => {
              servicesRenderingATM(result.data);
            }).catch ( error => {
            }).finally(() => {
              displaySection('.client', false);
              displaySection('.service', true);
              hideLoading();
            });
          }
          $('body').css('overflow','hidden'); // prevent scroll
        });
        closeButton.click(function () {
          popUpBooking.removeClass('show');
          popUpBooking.addClass('hide');
          displaySection('.client', false);
          displaySection('.service', false);
          displaySection('.branch', false);
          displaySection('.schedule', false);
          displaySection('.form', false);
          displaySection('.confirm-appointment', false);
          $('body').css('overflow','');
        });
      }
    }

    function cityRendering(CityList) {
      citySelect.find('.dropdown-body').text('');
      citySelect.removeClass('selected');
      cityOptions.find('li').removeClass('selected');
      districtSelect.find('.dropdown-body').text('');
      districtSelect.removeClass('selected');
      $(popUpBookingContent)
          .find('.search-filter .city .select-options ul li').remove();
      for (let city of CityList) {
        const cityCode = city.cityCode ?? city?.city?.cityCd;
        const cityName = city.cityName ?? city?.city?.cityNm;
        if (cityCode != null) {
          $(popUpBookingContent)
          .find('.search-filter .city .select-options ul')
          .append('<li city=' + cityCode + '>' + cityName + '</li>');
        }
      }

       // open city select-options
      citySelect.click(function () {
        $(this).addClass('showed');
        districtSelect.removeClass('showed');
      });

      districtSelect.click(function () {
        $(this).addClass('showed');
        citySelect.removeClass('showed');

        const selectedCityIndex = cityOptions.find('li.selected').attr('city');
        disTrictRendering(filterDistrictsByCityCode(selectedCityIndex, CityList));
      });

      // when select cities
      cityOptions.find('li').click(function () {
        citySelect.removeClass('showed');
        const city = $(this).text();

        // reset selected district when re-select cities
        citySelect.addClass('selected');
        citySelect.find('.dropdown-body').text(city);
        districtSelect.find('.dropdown-body').text('');

        cityOptions.find('li').removeClass('selected');
        districtOptions.find('li').removeClass('selected');

        $(this).addClass('selected');

        // get filter results by selected city
        const selectedCityindex = $(this).attr('city');
        appendAddress(CityList, branchList, selectedCityindex, '', popUpBookingContent);
        registerAddressClick(addresses);
      });
    }

    function disTrictRendering(districts) {

      districtOptions.find('ul li:not([value="all"])').remove();
      for (let item of districts) {
        const disTrictCode = item.districtCode ?? item.districtCd;
        const disTrictName = item.districtName ?? item.districtNm;
        districtOptions.find('ul').append('<li district=' + disTrictCode + '>' + disTrictName + '</li>');
      }

      // when select districts
      districtOptions.find('li').click(function () {
        districtSelect.removeClass('showed');

        districtOptions.find('li').removeClass('selected');
        $(this).addClass('selected');

        // get filter results by selected district
        const selectedCityindex = cityOptions.find('li.selected').attr('city');
        const selectedDistrictIndex = $(this).attr('district');
        districtSelect.find('.dropdown-body').text($(this).text());
        appendAddress(CityList, branchList, selectedCityindex, selectedDistrictIndex, popUpBookingContent);
        registerAddressClick(addresses);
      });
    }
    const listClient = popUpBookingContent.find('.client-list .item');

    hideElementAfterAnimation(popUpBookingContent.find('.booking-container'));

    // close select-option box
    $(document).click(function (event) {
      if (
        !$(event.target).is(cityOptions) &&
        !$(event.target).parents().is(cityOptions) &&
        !$(event.target).is(citySelect) &&
        !$(event.target).is(districtSelect) &&
        !$(event.target).parents().is(citySelect) &&
        !$(event.target).parents().is(districtSelect)
      ) {
        citySelect.removeClass('showed');
        districtSelect.removeClass('showed');
      }
    });

    listClient.click(function () {
      popUpBookingContent.find('.booking-container').removeClass('active');
      isPersonal = $(this).find('.personal').length > 0 ? true : false;
      $(this).closest('.booking-container-wrapper').addClass(isPersonal ? 'selected-personal' : 'selected-bussiness');
      showLoading();
      getServices(apiServices, isPersonal,branchSelected).then((result) => {
        servicesRendering(result.data);
      }).catch ( error => {
      }).finally(() => {
        displaySection('.client', false);
        displaySection('.service', true);
        hideLoading();
      });
    });

    function servicesRenderingATM(services) {
      popUpBookingContent.find('input[name="selectedService"]').val('');
      popUpBookingContent.find('.service-list').empty();
      for (let item of services) {
        const serviceItems = item;
        popUpBookingContent.find('.service-list').append(itemTimeSelect(serviceItems.srvName, '', serviceItems.srvIdent));
      }

      const itemService = popUpBookingContent.find('.service-list .item');
      itemService.click(function () {
        popUpBookingContent.find('input[name="selectedService"]').val($(this).find('h3').attr('itemid'));
        popUpBookingContent.find('.booking-container').removeClass('active');
        displaySection('.service', false);
        popUpBookingContent.find('.schedule').addClass('active');
        showLoading();
        const branchIdFromATM = btnContactUs.find('.cta-button').attr('currentBranchSelect');
        const serviceId = popUpBookingContent.find('input[name="selectedService"]').val();
        getBookingSlot(apiBookingSlot, serviceId, branchIdFromATM).then((timeLots) => {
          bookingTimeRendering(timeLots.data);
            popUpBookingContent.find('.form').addClass('hidden');
            popUpBookingContent.find('.confirm-appointment').addClass('hidden');
            selectedDateCSS(scheduleBookingDate.find('div').first());
            initTimeSchedule(scheduleBookingDate.find('div').first());
            hideLoading();
          }).catch ( error => {
            hideLoading();
            displaySection('.schedule', true);
            displaySection('.form', false);
            displaySection('.confirm-appointment', false);
          });
      });
    }

    function servicesRendering(services) {
      popUpBookingContent.find('input[name="selectedService"]').val('');
      popUpBookingContent.find('.service-list').empty();
      for (let item of services) {
        const serviceItems = item;
        popUpBookingContent.find('.service-list').append(itemTimeSelect(serviceItems.srvName, '', serviceItems.srvIdent));
      }

      const itemService = popUpBookingContent.find('.service-list .item');
      itemService.click(function () {
        popUpBookingContent.find('input[name="selectedService"]').val($(this).find('h3').attr('itemid'));
        popUpBookingContent.find('.booking-container').removeClass('active');
        if(popUpBookingContent.find('.client').length > 0) {
          const serviceId = popUpBookingContent.find('input[name="selectedService"]').val();
          showLoading();
          getBranchList(apiBranch, apiBrahchSuffix,'',defaultLat, defaultLong, serviceId).then(function (dataResponse) {
            branchList = dataResponse.branches ?? [];
            CityList = dataResponse.cityList ?? [];
            appendAddress(CityList, branchList, -1, '', $(popUpBookingContent));
            registerAddressClick(addresses);
            cityRendering(CityList);
            hideLoading();
          }).catch ( error => {
            hideLoading();
          }).finally(() => {
            displaySection('.service', false);
            displaySection('.branch', true);
            popUpBookingContent.find('.branch .address .address-list').scrollTop(0);
          });
        }
      });
    }

    function bookingTimeRendering(timeList) {
      // set schedule booking
      const toDayAndTomorrow = getTodayAndTomorrow();
      $(popUpBookingContent).find('.schedule .time .time-list').empty();
      const todayDate = toDayAndTomorrow.today;
      const tomorrow = toDayAndTomorrow.tomorrow;
      $(popUpBookingContent)
        .find('.schedule .date .today')
        .html(todayLabel + ' ' + date.getDate().toString().padStart(2, '0') + '/' + (date.getMonth() + 1).toString().padStart(2, '0'));
      $(popUpBookingContent)
        .find('.schedule .date .tomorrow')
        .html(tomorrowLabel +' ' + (date.getDate() + 1).toString().padStart(2, '0') + '/' + (date.getMonth() + 1).toString().padStart(2, '0'));
      renderTimeByDate(todayDate, timeList);
      registerClickEvent();
      // click today/tomorrow schedule
      scheduleBookingDate.find('div').click(function () {
        $(popUpBookingContent).find('.schedule .time .time-list').empty();
        selectedDateCSS($(this));
        const todayEnable = scheduleBookingDate.find('.today.enable');
        if(todayEnable.length > 0 ) {
          renderTimeByDate(todayDate, timeList);
        }
        else {
          renderTimeByDate(tomorrow, timeList);
        }
        registerClickEvent();
      });
      setTimeout(() => {
        $(popUpBookingContent).find('.schedule .date .today').click();
      }, 100);
      if (date.getDay() == 7) {
        $(popUpBookingContent).find('.schedule .date .today').css('display', 'none');
        setTimeout(() => {
          $(popUpBookingContent).find('.schedule .date .tomorrow').click();
        }, 200);
      }
      if((date.getDay() + 1) == 7) {
        $(popUpBookingContent)
        .find('.schedule .date .tomorrow').css('display','none');
      }
      selectedDateCSS(scheduleBookingDate.find('div').first());
    }



    function registerClickEvent() {
       // enable clicking function for available time to select
       scheduleBookingTime.find('.time-list .item').click(function () {
        if ($(this).attr('class').includes('enable')) {
          popUpBookingContent.find('.booking-container').removeClass('active');
          timeSelected = $(this).text();

          let checkPersonalBaseOnBookingVisit = isPersonal;
          if(!popUpBookingContent.find('.client').length > 0) {
            checkPersonalBaseOnBookingVisit = !!(typeCustomerFromATM == '11');
          }

          if (!checkPersonalBaseOnBookingVisit) {
            popUpBookingContent.find('#' + TAXCODE).on('input', function () {
              validateTaxCode(popUpBookingContent);
            });
          }
          checkPersonalBaseOnBookingVisit ? popUpBookingContent.find('.taxt-code').css('display', 'none') : popUpBookingContent.find('.taxt-code').css('display', 'block');
          displaySection('.schedule', false);
          displaySection('.form', true);
        }
      });
    }
    function renderTimeByDate(seletedDate, dataRender) {
      const times = dataRender.filter((item) => item.slotDate == seletedDate)[0]?.slotData ?? [];
      timeList = times;
      for (let item of times) {
        const setTime = item;
        const enableTime = setTime.enable == 1? 'enable-time' : '';
        $(popUpBookingContent).find('.schedule .time .time-list').append(itemTimeSelect(setTime.slotTime, enableTime, ''));
      }
      changeTimeCSS();
    }

    btnSubmit.click(function (event) {

      let checkPersonalBaseOnBookingVisit = isPersonal;
      if(!popUpBookingContent.find('.client').length > 0) {
        checkPersonalBaseOnBookingVisit = !!(typeCustomerFromATM == '11');
      }

      if (!checkPersonalBaseOnBookingVisit) {
        validateTaxCode(popUpBookingContent);
      }
      validateEmail(popUpBookingContent);
      validatePhone(popUpBookingContent);
      recaptchaValidation(this); // changed by adobe
      const errors = popUpBookingContent.find('.form-error-message').filter(function(){
        return $(this).css("display") !== "none";
      });
      if (errors.length > 0) {
        event.preventDefault();
      } else {

       let type = '';
       if(popUpBookingContent.find('.client').length > 0) {
        type = isPersonal? "11" : "12";
       }else {
         type = typeCustomerFromATM
       }
       const name = popUpBookingContent.find('#name').val();
       const tax = popUpBookingContent.find('#' + TAXCODE).val();
       const email = popUpBookingContent.find('#' + EMAIL).val();
       const phone = popUpBookingContent.find('#' + PHONE).val();
       const todayAndTomorrow = getTodayAndTomorrow();
       let iDType = '';
       if(type == "11") {
         iDType = "4";
       }else {
         const taxLength = tax.replace(/-/g, '').length;
         iDType = (taxLength == 10 || taxLength == 13) ? "6" : "7";
       }
       const serviceId = popUpBookingContent.find('input[name="selectedService"]').val();
       const branchId = popUpBookingContent.find('input[name="selectedBranch"]').val();
       const todaySelect = popUpBookingContent.find('.today.enable');
       const bkDt = (todaySelect.length > 0)? todayAndTomorrow.today : todayAndTomorrow.tomorrow;
       const serviceName = popUpBookingContent.find('.service-list [itemid="' +serviceId +'"]').text();
       const captchakey = grecaptcha.enterprise.getResponse(popUpBookingIndex) || "";
       const requestObject = {
          "type": type,
          "iDType": iDType,
          "branchId": branchId,
          "bkDt":bkDt,
          "bkTm":timeSelected.trim(),
          "customerNm":name,
          "phneNb":phone,
          "accno":tax,
          "emailAdr": email,
          "captchakey" : captchakey,
          "servid": serviceId,
          "servnm": serviceName
      }
      showLoading();
      confirmSubmit(apiBookingConfirm, requestObject).then((res) => {
          popUpBookingContent.find('.booking-container').removeClass('active');
          displaySection('.form', false);
          displaySection('.confirm-appointment', true);

          if (!res?.data?.errorCode) {
            const confirmAppointment = popUpBookingContent.find('.confirm-appointment');
            const formatDate = (res?.data?.bkDt)? new Date(res?.data?.bkDt) :  undefined;
            let dateFormatByLang = '';

            if(formatDate) {
              if (document.documentElement.lang === 'en') {
                dateFormatByLang = moment(formatDate).format("DD MMM yyyy");
              } else {
                dateFormatByLang = moment(formatDate).format("DD/MM/YYYY");
              }
            }

            const confirmDateValue = defaultLabelConfirmDate + ' ' + dateFormatByLang + ', ' + (res?.data?.bkTm ?? '');
            confirmAppointment.find('.date-appointment').text(confirmDateValue);
            confirmAppointment.find('.confirm-ticket-number').text(res?.data?.apmtQr ?? '');
          } else {
            displaySection('.confirm-content-wrapper', false);
          }
        }).catch ( error => {
        }).finally(() => {
          hideLoading();
        });
        bookVisitAnalytics(); //book a visit analytics call
        event.preventDefault();
      }
    });

    popUpBookingContent.find('#' + EMAIL).on('input change', function () {
      validateEmail(popUpBookingContent);
    });
    popUpBookingContent.find('#' + PHONE).on('input change', function () {
      const currentInput = this.value.replace(/\D/g,'');
      if(/^\d+$/.test(this.value)) {
        validatePhone(popUpBookingContent);
      }else {
        this.value = currentInput;
      }
      if(!this.value) {
        validatePhone(popUpBookingContent);
      }
    });

    function bookVisitAnalytics() {
      //hashed Email converter
      let hashEmail = $("#emailAddress").val();
      if (hashEmail) {
         let hash = 0;
         for (let i = 0; i < hashEmail.length; i++) {
            const char = hashEmail.charCodeAt(i);
            hash = (hash << 5) - hash + char;
         }
         hashEmail = hash.toString(16);
      }
      //update analytics values
      if ($("#book-visit-analytics").length > 0) {
         const bookVisitValues = $("#book-visit-analytics").data().trackingFormInfoValue;
         const jsonBookVisitInfoStr = bookVisitValues.replace(/'/g, '"');
         const jsonBookVisitInfo = JSON.parse(jsonBookVisitInfoStr);
         jsonBookVisitInfo.hashedEmail = hashEmail;
         const updatedBookVisitValues = JSON.stringify(jsonBookVisitInfo).replace(/"/g, "'");
         $("#book-visit-analytics").attr("data-tracking-form-info-value", updatedBookVisitValues);
         $("#book-visit-analytics").trigger('click');
      }
    }

    function validateTaxCode(element) {
      const taxCode = element.find('#' + TAXCODE).val().trim();
      if (taxCode === '') {
        showError(TAXCODE+'-error');
      } else {
        hideError(TAXCODE);
      }
    }

    function validateEmail(element) {
      const emailValue = element.find('#' + EMAIL).val().trim();
      hideError(EMAIL);
      if (emailValue === '') {
        showError(EMAIL+'-error'+'-required');
      } else if (!isValidEmail(emailValue)) {
        showError(EMAIL+'-error'+'-invalid');
      } else {
        hideError(EMAIL);
      }
    }

    function validatePhone(element) {
      hideError(PHONE);
      const phoneValue = element.find('#' + PHONE).val().trim();
      if (phoneValue === '') {
        showError(PHONE+'-error');
      } else if(phoneValue.length < 10) {
        showError(PHONE+'-error'+'-invalid');
      }
      else {
        hideError(PHONE);
      }
    }

    function showError(inputId) {
      popUpBookingContent.find('#' + inputId).show();
      popUpBookingContent.find('#' + inputId).parent().addClass('error');
    }

    function hideError(inputId) {
      popUpBookingContent.find('#' + inputId).parent().removeClass('error');
      popUpBookingContent.find('#' + inputId).parent().find('.form-error-message').hide();
    }

    function isValidEmail(email) {
      const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      return emailRegex.test(email);
    }

    // change selected date css
    function selectedDateCSS(el) {
      scheduleBookingDate.find('div').css('background-color', 'unset');
      scheduleBookingDate.find('div').css('color', '#616161');
      scheduleBookingDate.find('div').removeClass('enable');

      el.css('background-color', '#212121');
      el.css('color', '#fff');
      el.addClass('enable');
    }

    // set time schedule css
    function initTimeSchedule(el) {
      switch (dayOfWeek) {
        case 0:
          if (el.index() == 0) {
            unavailableAllTime();
          } else {
            oriTimeCSS();
          }
          break;
        case 5:
          if (el.index() == 0) {
            changeTimeCSS();
          } else {
            unavailableAllTime();
          }
          break;
        case 6:
          unavailableAllTime();
          break;
        default:
          if (el.index() == 0) {
            changeTimeCSS();
          } else {
            oriTimeCSS();
          }
          break;
      }
    }

    // change css for available time to select
    function changeTimeCSS() {
      const itemsList = scheduleBookingTime.find('.time-list .item');
      itemsList.each(function(i, element) {
        if($(element).find('.enable-time').length > 0){
          // tim kiem class parrent and add class enable
          $(scheduleBookingTime.find('.time-list .item')[i]).addClass('enable');
          $(scheduleBookingTime.find('.time-list .item')[i]).css('color', '#616161');
          $(scheduleBookingTime.find('.time-list .item img')[i]).attr('src', '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-chevron-right.svg');
          $(scheduleBookingTime.find('.time-list .item svg path')[i]).attr('fill', '#ED1B24');
        } else {
          $(scheduleBookingTime.find('.time-list .item')[i]).css('color', '#a2a2a2');
          $(scheduleBookingTime.find('.time-list .item img')[i]).attr('src', '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/grey-cheron-right-large.svg');
          $(scheduleBookingTime.find('.time-list .item')[i]).removeClass('enable');
        }
      });
    }

    // original css for time to select
    function oriTimeCSS() {
      for (let i = 0; i < timeList.length; i++) {
        $(scheduleBookingTime.find('.time-list .item')[i]).css('color', '#616161');
        $(scheduleBookingTime.find('.time-list .item img')[i]).attr('src', '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-chevron-right.svg');
        $(scheduleBookingTime.find('.time-list .item')[i]).addClass('enable');
      }
    }

    // change css for unavailable time
    function unavailableAllTime() {
      $(scheduleBookingTime.find('.time-list .item')).css('color', '#a2a2a2');
      $(scheduleBookingTime.find('.time-list .item svg path')).attr('fill', '#a2a2a2');
      $(scheduleBookingTime.find('.time-list .item')).removeClass('enable');
    }

    function registerAddressClick(el) {
      popUpBookingContent.find('input[name="selectedBranch"]').val();
      el.find('.item').click(function () {
        popUpBookingContent.find('input[name="selectedBranch"]').val($(this).find('h3').attr('itemid'));
        const serviceId = popUpBookingContent.find('input[name="selectedService"]').val();
        const branchId = popUpBookingContent.find('input[name="selectedBranch"]').val();
        showLoading();
        getBookingSlot(apiBookingSlot, serviceId, branchId).then((timeLots) => {
          bookingTimeRendering(timeLots.data);
        }).catch ( error => {
        }).finally(() => {
          displaySection('.booking-container', false);
          displaySection('.client', false);
          displaySection('.service', false);
          displaySection('.branch', false);
          displaySection('.form', false);
          displaySection('.schedule', true);
          hideLoading();
        });
      });
    }

    function showLoading() {
      popUpBookingContent.find('.loading').css('display', 'inline-block');
      popUpBookingContent.find('.loading_backdrop').css('display', 'block');
    }

    function hideLoading() {
      popUpBookingContent.find('.loading').css('display', 'none');
      popUpBookingContent.find('.loading_backdrop').css('display', 'none');
    }
  });
});

function getCityList(api) {
  api += 'atmCdmCityList';
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: api,
      method: "get",
      success: function (response) {
        resolve(removeDuplicates(response?.data?.atmCdmFragmentList?.items, 'cityCode'));
      },
      error: function (xhr, status, error) {
        reject(error)
      },
    });
  });
}

function getDistrictList(element, api, cityCode) {
  const currentType = $(element).find('.search-type button.clicked').find('span').text();
  let requestAPI = '';
  if (!['ATM', 'CDM'].includes(currentType)) {
    requestAPI = `${api}districtList;cityCode=${cityCode}`;
  } else {
    requestAPI = `${api}atmCdmDistrictList;cityCode=${cityCode}`;
  }
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: requestAPI,
      method: 'get',
      success: function (response) {
        resolve(
          removeDuplicates(
            (response?.data?.atmCdmFragmentList || response?.data?.branchFragmentList)?.items,
            'districtCode',
          ),
        );
      },
      error: function (xhr, status, error) {
        reject(error);
      },
    });
  });
}

function generateAtmCdmApi(api) {
  return `${api}atmcdmSearch`;
}

function getAtmCdmList(api, name, cityCode, districtCode, isCDM) {
  const apiUpdated = generateAtmCdmApi(api);
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: apiUpdated,
      method: "get",
      success: function (response) {
        let dataResponse = response?.data?.atmCdmFragmentList?.items ?? [];
        dataResponse = filterNameOrAddress(name, dataResponse, 'name');
        resolve(dataResponse);
      },
      error: function (xhr, status, error) {
        reject(error)
      },
    });
  });
}

function getBranchList(api, apiSuffixes, search, lat, long, serviceId) {
  api += apiSuffixes ? apiSuffixes : '/_jcr_content.branchlist.json';
  const dataRequest = {
    "search": search ?? '',
    "lat": lat ?? 0,
    "long": long ?? 0,
    "srvIdent": serviceId ?? '',
  }
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: api,
      method: "POST",
      contentType: 'application/json',
      data: JSON.stringify(dataRequest),
      success: function (response) {
        let dataResponse = response?.branchList ?? [];
        dataResponse = filterNameOrAddress('', dataResponse, 'name');
        resolve({
          branches: dataResponse,
          cityList: response?.pstlAdr
        });
      },
      error: function (xhr, status, error) {
        reject(error)
      },
    });
  });
}

function getServices(api, isPersonal, branchId) {
  api +='/_jcr_content.servicelist.json';
  const requestData = {
    "type": isPersonal ? "11" : "12",
    "branchID": branchId ? `${branchId}` : ''
  }
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: api,
      method: "POST",
      contentType: 'application/json',
      data: JSON.stringify(requestData),
      success: function (response) {
          resolve({
            data: response? getAllSubServiceInf(response) : []
          });
      },
      error: function (xhr, status, error) {
        reject(error)
      },
    });
  });
}

function getBookingSlot(api, serviceId, branchId) {
  api +='/_jcr_content.branchslot.json';
  const requestData =
  {
      "branchID": `${branchId}`,
      "srvID": `${serviceId}`
  }
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: api,
      method: "POST",
      contentType: 'application/json',
      data: JSON.stringify(requestData),
      success: function (response) {
          resolve({
            data: response?.slotList ?? []
          });
      },
      error: function (xhr, status, error) {
        reject(error)
      },
    });
  });
}

function confirmSubmit(api, confirmInformation) {
  api +='/_jcr_content.confirmbooking.json';
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: api,
      method: "POST",
      contentType: 'application/json',
      data: JSON.stringify(confirmInformation),
      success: function (response) {
          resolve({
            data: response
          });
      },
      error: function (xhr, status, error) {
        reject(error)
      },
    });
  });
}

function filterDistrictsByCityCode(cityCd, branchAddress) {
  const cityData = branchAddress.find(item => item.city.cityCd === cityCd);
  return cityData? cityData.city.district : [];
}

function removeDuplicates(arr, prop) {
  if (!arr) {
    return [];
  }
  const objTmp = new Map();
  return arr.filter((item) => {
    const key = item[prop];
    if (!objTmp.has(key)) {
      objTmp.set(key, true);
      return true;
    }
    return false;
  });
}

function hideElementAfterAnimation(element) {
  element.each(function(index,item) {
    $(item).on('transitionend', function(event) {
      $(item).hide();
    })
  });
}

function clearForm(element) {
  const inputs = element.find('.input');
  inputs.each(function(index, item) {
    $(item).removeClass('error');
    $(item).find('input').val('');
    $(item).find('.form-error-message').hide();
  });

  const containers = document.querySelectorAll('.recaptcha');
    containers.forEach(function(container, index) {
      const recaptcha = container.querySelector('.g-recaptcha');
      if (recaptcha) {
      	grecaptcha.enterprise.reset(index);
     }
  });
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() +1).padStart(2,'0');
  const day = String(date.getDate()).padStart(2,'0');
  return `${year}-${month}-${day}`;
}

function getTodayAndTomorrow() {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() +1);
  return {
    today: formatDate(today),
    tomorrow: formatDate(tomorrow)
  }
}
function getAllSubServiceInf(dataInput) {
  if(dataInput.servicesList) {
    return dataInput.servicesList
    .flatMap(service => service.subServiceInf || [])
    .filter(subService => subService !== null && typeof subService === 'object');
  }else {
    return [];
  }
}
