// Horizontal Tab Bio
$(document).ready(function () {
    $('.management-team-panel').each(function () {
      $('.tab-horizontal-bio__button-see-more').click(function () {
        $(this).parent().find('.tab-horizontal-bio__card.display-none').removeClass('display-none');
        $(this).parent().find('.tab-horizontal-bio__card.display-none').addClass('display-block');
        $(this).hide();
      });

      const profileBtn = $(this).find('.tab-horizontal-bio__card-action-btn');
      const bioPopup = $(this).find('.bio-popup');
      const bioPopupContent = bioPopup.find('.bio-popup-content');
      const closePopupBtn = bioPopupContent.find('.close-btn');
      const loadMoreButton = $(this).find('.management-team-panel__load-more');
      const bioItems = $(this).find('.tab-horizontal-bio__card');
      let bioShowed = 6;
      let bioPerPage = 6;
      showHideBio();
      profileBtn.click(function () {
        const selectedBio = $(this).closest('.tab-horizontal-bio__card');
        const selectedBioRole = selectedBio.find('.tab-horizontal-bio__card-content-role');
        const selectedBioName = selectedBio.find('.tab-horizontal-bio__card-content-title');
        const selectedBioDetail = selectedBio.find('.tab-horizontal-bio__card-content-description');
        const selectedBioAva = selectedBio.find('img');
  
        bioPopupContent.find('.position').text(selectedBioRole.text());
        bioPopupContent.find('.name').text(selectedBioName.text());
        bioPopupContent.find('.detail-info').html(selectedBioDetail.html());
        bioPopupContent.find('.avatar img').attr('src', selectedBioAva.attr('src'));
  
        bioPopup.addClass('showed');
        $('body').addClass('modal-showing');
        $('.global-header').css("position", "relative");
        $('.scroll-to-top').removeClass('show');
      });
  
      closePopupBtn.click(function () {
        bioPopup.removeClass('showed');
        $('body').removeClass('modal-showing');
        $('.global-header').css("position", "sticky");
      });
      $(document).click(function (event) {
        if (bioPopup.hasClass('showed') &&
          !$(event.target).is(profileBtn) &&
          !$(event.target).is(bioPopupContent) &&
          !$(event.target).parents().is(bioPopupContent)
        ) {
          bioPopup.removeClass('showed');
          $('body').removeClass('modal-showing');
        }
      });

      loadMoreButton.click(function () {
        bioShowed += bioPerPage;
        bioItems.each(function (i) {
          if ($(this).is(':hidden') && i < bioShowed) {
            $(this).show();
          }
        });
      
        if(bioShowed > bioItems.length -1) {
          loadMoreButton.hide();
        }
      });

      $(window).on("resize", function() {
        showHideBio();
      });

      function showHideBio() {
        bioShowed = 6;
        bioPerPage = 6;
        if ($(window).width() < 768) {
          bioShowed = 4;
          bioPerPage = 4;
        }
        if (bioItems.length <= bioShowed) {
          loadMoreButton.hide();
        } else {
          loadMoreButton.show();
        }
  
        bioItems.each(function (i) {
          if (i >= bioPerPage) {
            $(this).hide();
          } else {
            $(this).show();
          }
        });
      }
    });
  });
  // End of Horizontal Tab Bio