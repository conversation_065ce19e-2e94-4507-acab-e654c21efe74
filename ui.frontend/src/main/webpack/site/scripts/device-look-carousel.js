import _ from 'lodash';

$(document).ready(function () {
  $('.device-look-carousel').each(function () {
    let $deviceLoopStep = $(this);
    let slidesToShowNum = $(this).attr('slides-to-show-num');
    if (!isNaN(slidesToShowNum)) slidesToShowNum = parseInt(slidesToShowNum);
    if ($(window).width() >= 992) {
      configImageSlider(slidesToShowNum);
    }
    $(window).on(
      'resize',
      _.debounce(
        () => {
          if ($(window).width() >= 992) {
            configImageSlider(slidesToShowNum);
          }
        },
        100,
        { trailing: true },
      ),
    );

    function configImageSlider(slidesToShowNum) {
      //Adobe: return if element not found
      if ($('.device-look-carousel__list-item').length === 0) return;
      $deviceLoopStep
        .find('.device-look-carousel__list-item')
        .not('.slick-initialized')
        .slick({
          slidesToShow: slidesToShowNum || 4,
          slidesToScroll: 1,
          swipe: true,
          swipeToSlide: true,
          autoplay: false,
          accessibility: true,
          arrows: true,
          infinite: false,
          responsive: [
            {
              breakpoint: 992,
              settings: 'unslick',
            },
          ],
        });
      $deviceLoopStep.find('.device-look-carousel__list-item .slick-next').text('');
      $deviceLoopStep.find('.device-look-carousel__list-item .slick-prev').text('');
      $deviceLoopStep.find('.device-look-carousel__list-item .slick-prev').css('cssText', 'display: none !important');
      $deviceLoopStep.find('.device-look-carousel__list-item .slick-next').css('cssText', 'display: block !important');
      $deviceLoopStep
        .find('.device-look-carousel__list-item')
        .on('afterChange', function (event, slick, currentSlide, nextSlide) {
          if ($(window).width() > 991) {
            if (currentSlide === 0) {
              $deviceLoopStep
                .find('.device-look-carousel__list-item .slick-prev')
                .css('cssText', 'display: none !important');
              $deviceLoopStep
                .find('.device-look-carousel__list-item .slick-next')
                .css('cssText', 'display: block !important');
            } else if (slick.$slides.last().hasClass('slick-active')) {
              $deviceLoopStep
                .find('.device-look-carousel__list-item .slick-prev')
                .css('cssText', 'display: block !important');
              $deviceLoopStep
                .find('.device-look-carousel__list-item .slick-next')
                .css('cssText', 'display: none !important');
            } else {
              $deviceLoopStep
                .find('.device-look-carousel__list-item .slick-prev')
                .css('cssText', 'display: block !important');
              $deviceLoopStep
                .find('.device-look-carousel__list-item .slick-next')
                .css('cssText', 'display: block !important');
            }
          }
        });
    }
  });

  $('.device-look-carousel').each(function () {
    let itembody = $(this).find('.device-look-carousel__item .device-look-carousel__item-body');
    const itemImg = $(this).find('.device-look-carousel__item .device-look-carousel__item-image-wrapper');

    function findMaxHeight(el) {
      let max = 0;
      el.each(function () {
        let elHeight = $(this).innerHeight();
        if (elHeight > max) {
          max = elHeight;
        }
      });
      return max;
    }

    function setItemHeight(body, img) {
      const bodyMaxHeight = findMaxHeight(body);
      const imgMaxHeight = findMaxHeight(img);
      body.each(function () {
        $(this).css("min-height", bodyMaxHeight);
      });
      img.each(function () {
        $(this).css("min-height", imgMaxHeight);
      });
    }

    setItemHeight(itembody, itemImg);

    $(window).on(
      'resize',
      _.debounce(
        () => {
          setItemHeight(itembody, itemImg);
        },
        100,
        { trailing: true },
      ),
    );
  });
});
