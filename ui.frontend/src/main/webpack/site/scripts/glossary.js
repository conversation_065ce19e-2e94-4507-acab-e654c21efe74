import { BaseComponent } from './base';

export class TcbGlossary extends BaseComponent {
  get glossaryAlphabet() {
    return this.querySelector(".glossary-alphabet");
  }
  get listAlphabet() {
    return this.glossaryAlphabet.querySelectorAll("a");
  }

  get glossaryContainer() {
    return this.querySelector(".glossary-container");
  }
  get keyWord() {
    return this.glossaryContainer.querySelector(".container-title");
  }
  get glossaryCard() {
    return this.glossaryContainer.querySelectorAll(".glossary-card");
  }

  get paginationSection() {
    return this.querySelector(".pagination");
  }
  get pageSize() {
    return this.querySelector(".glossary").getAttribute(
      "data-key-word-per-page"
    );
  }

  get language() {
    return this.querySelector(".glossary").getAttribute("data-lang");
  }

  pageLength = 0;
  pageNum = 0;
  selectedAlphabet = "";
  defaultPageNum = 1;
  defaultKeyWord = this.listAlphabet[0].innerText;
  url = new URL(window.location);
  prevLabel = this.language === "English" ? "Prev" : "Trang trước";
  nextLabel = this.language === "English" ? "Next" : "Trang sau";

  constructor() {
    super();
    this.onInit();
    this.initListAlphabet();
    window.addEventListener("resize", this.setTopAlphabetList.bind(this));
    window.addEventListener("scroll", this.setTopAlphabetList.bind(this));
  }

  //action click on alphabet list
  initListAlphabet() {
    this.listAlphabet.forEach((alphabet) => {
      alphabet.addEventListener("click", () => {
        $(".glossary-alphabet a.active").removeClass("active");
        alphabet.classList.add("active");
        this.selectedAlphabet = alphabet.innerText;
        this.keyWord.innerText = this.selectedAlphabet;
        this.url.searchParams.delete("term");
        window.history.replaceState({}, "", this.url);
        this.onClickAlphabet(this.selectedAlphabet);
        this.renderPagination(this.pageLength, 1);
        this.displayPageIndex(this.defaultPageNum);
      });
    });
  }

  onInit() {
    this.setTopAlphabetList();
    this.initGlossary();
  }

  onClickAlphabet(alphabet) {
    this.keyWord.value = alphabet;
    this.glossaryCard.forEach((card) => {
      const keyWordCard = card.getAttribute("data-key");
      const cardLength = card.getAttribute("data-page-length");
      const pageNumCard = card.getAttribute("data-page-index");
      if (alphabet === keyWordCard) {
        card.removeAttribute("hidden");
        this.pageLength = Math.ceil(cardLength / this.pageSize);
        this.pageNum = pageNumCard;
      } else {
        card.setAttribute("hidden", true);
      }
    });
  }

  //set top px for alphabet list when scroll down
  setTopAlphabetList() {
    const headerElement = $('header .header_layout');
    const heightOfHeader = headerElement?.outerHeight() ?? 0;
    this.glossaryAlphabet.style.setProperty("top", `${heightOfHeader}px`);
  }

  initGlossary() {
    const paramsURL = this.url.searchParams.get("term");
    let isEmptyObj = true;
    if (paramsURL) {
      //if has url params
      const objectCard = this.getAlphabetByTerm(paramsURL);
      isEmptyObj = $.isEmptyObject(objectCard);
      if (!isEmptyObj) {
        //if params is correct
        this.selectedAlphabet = objectCard.keyWord;
        this.setActiveClass(objectCard.keyWord);
        this.keyWord.innerText = objectCard.keyWord;
        const pageIndex = parseInt(objectCard.pageNum, 10) + 1;
        this.pageLength = Math.ceil(objectCard.cardLength / this.pageSize);
        this.renderPage(
          this.pageLength,
          pageIndex,
          objectCard.keyWord,
          pageIndex
        );

        this.scrollToView(objectCard.term);
      } else {
        //if params is not correct
        $(".glossary-alphabet a.active").removeClass("active");
        this.listAlphabet[0].classList.add("active");
        this.keyWord.innerText = this.defaultKeyWord;
        this.selectedAlphabet = this.defaultKeyWord;
        const firstKeyWordLength = parseInt(
          this.glossaryCard[0].getAttribute("data-page-length"),
          10
        );
        this.pageLength = Math.ceil(firstKeyWordLength / this.pageSize);
        this.url.searchParams.delete("term");
        window.history.replaceState({}, "", this.url);

        this.renderPage(this.pageLength, 1);
      }
    } else {
      //if have not params, set default keyword is A
      $(".glossary-alphabet a.active").removeClass("active");
      this.listAlphabet[0].classList.add("active");
      this.selectedAlphabet = this.defaultKeyWord;
      this.keyWord.innerText = this.defaultKeyWord;
      this.onClickAlphabet(this.selectedAlphabet);
      this.renderPage(this.pageLength, 1);
    }
  }

  renderPage(
    pageLength,
    selectedPage,
    selectedAlphabet = "A",
    defaultPageNum = 1
  ) {
    this.renderPagination(pageLength, selectedPage);
    this.displayPageIndex(defaultPageNum);
  }

  getAlphabetByTerm(term) {
    for (let card of this.glossaryCard) {
      let objectCard = {};
      const found = card.getAttribute("data-term");
      if (found === term) {
        const keyWordCard = card.getAttribute("data-key");
        const cardLength = card.getAttribute("data-page-length");
        const pageNumCard = card.getAttribute("data-page-index");
        objectCard = {
          keyWord: keyWordCard,
          cardLength: cardLength,
          pageNum: pageNumCard,
          term: found,
        };
        return objectCard;
      }
    }
  }

  //render pagination
  renderPagination(pageLength, selectedPage) {
    // remove old pagination
    this.paginationSection.innerHTML = "";

    // render prev button
    if (pageLength > 1) {
      this.renderPaginateButton("-1", this.prevLabel);
    }
    // render pagination number
    for (let index = 0; index < pageLength; index++) {
      this.renderPaginateButton(index + 1, index + 1);
    }
    // render next button
    if (pageLength > 1) {
      this.renderPaginateButton("+1", this.nextLabel);
    }

    this.querySelector(".pagination").setAttribute("actpage", selectedPage);
    let paginationPage = parseInt($(".pagination").attr("actpage"), 10);
    const pages = this.querySelectorAll(".pagination-index");
    for (let page of pages) {
      page.addEventListener(
        "click",
        function () {
          let go = page.getAttribute("data-href");
          switch (go) {
            case "+1":
              paginationPage++;
              break;
            case "-1":
              paginationPage--;
              break;
            default:
              paginationPage = parseInt(go, 10);
              break;
          }
          this.querySelector(".pagination").setAttribute(
            "actpage",
            paginationPage
          );
          this.displayPageIndex(paginationPage);
        }.bind(this)
      );
    }
  }

  //filter glossary card when click paginate number
  displayPageIndex(pageNum) {
    this.glossaryCard.forEach((card) => {
      const keyWordCard = card.getAttribute("data-key");
      const pageNumCard =
        parseInt(card.getAttribute("data-page-index"), 10) + 1;
      if (this.selectedAlphabet === keyWordCard && pageNumCard === pageNum) {
        card.removeAttribute("hidden");
      } else {
        card.setAttribute("hidden", true);
      }
    });
    this.removeLineBreak();
  }

  //set active class for alphabet
  setActiveClass(keyWord) {
    this.listAlphabet.forEach(function (alphabet) {
      if (alphabet.innerText === keyWord) {
        alphabet.classList.add("active");
      }
    });
  }

  // scroll to card query in url term
  scrollToView(term) {
    const alphabetListHeight = $(".glossary-alphabet").outerHeight();
    const scrollCard = this.findCard(term);
    const position = scrollCard.offsetTop;
    const offsetPosition = position - alphabetListHeight;

    window.onload = function () {
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    };
  }

  findCard(term) {
    let visibleCards = this.querySelectorAll(".glossary-card:not([hidden])");
    for (let cards of visibleCards) {
      if (cards.getAttribute("data-term") === term) {
        return cards;
      }
    }
  }

  removeLineBreak() {
    let visibleCard = this.querySelectorAll(".glossary-card:not([hidden])");
    if (visibleCard) {
      visibleCard[visibleCard.length - 1]
        .querySelector(".line-break")
        .classList.add("line-break-unset");
    }
  }

  renderPaginateButton(href, label) {
    const element = document.createElement("a");
    element.innerHTML = label;
    element.classList.add("pagination-index");
    element.setAttribute("data-href", href);
    this.paginationSection.appendChild(element);
  }
}
