import { TCB_ANALYTICS_EVENT, TCB_ANALYTICS_INTERACT_TYPE, addDataLayerObject, getTrackingPageName } from './analytics';
import { BaseComponent } from './base';
import { ObjectUtil } from './utils/object.util';
import { removeVietnameseAccent, replaceDoubleSpaceBySingleSpace, stringToSlug } from './utils/string.util';

export class TcbTracker extends BaseComponent {
  constructor() {
    super();

    this.initClickEvent();
  }

  get eventName(): string {
    return this.getAttribute('event');
  }

  get interactName(): string | undefined {
    return this.getAttribute('value') || undefined;
  }

  get download(): string | undefined {
    return this.getAttribute('download') || undefined;
  }

  get appDownload(): string | undefined {
    return this.getAttribute('app-download') || undefined;
  }

  get linkClick(): string | undefined {
    return this.getAttribute('link-click') || undefined;
  }

  get cta(): string | undefined {
    return this.getAttribute('cta') || undefined;
  }

  get faq(): string | undefined {
    return this.getAttribute('faq') || undefined;
  }

  get destinationPath(): string | undefined {
    return this.getAttribute('dest-path') || undefined;
  }

  get interactType(): string | undefined {
    return this.getAttribute('interact-type');
  }

  get videoName(): string | undefined {
    return this.getAttribute('video-name');
  }

  get contactUs(): string | undefined {
    return this.getAttribute('contact-us');
  }

  private initClickEvent(): void {
    this.addEventListener('click', this.track);
  }

  private track() {
    let linkClick = this.linkClick;
    const eventName = this.constructEventName();
    if (!linkClick && eventName === TCB_ANALYTICS_EVENT.LINK_CLICK) {
      linkClick = this.formattedInteractName();
    }

    const _techcombank = {
      clickInfo: {
        linkClick: this.addPrefixPageName(linkClick),
        download: this.download?.trim(),
        appDownload: this.appDownload?.trim(),
        faq: this.faq?.trim(),
        cta: this.addPrefixPageName(this.cta),
        videoName: this.videoName?.trim(),
        contactUs: this.addPrefixPageName(this.contactUs),
      },
    };

    ObjectUtil.deleteUndefinedField(_techcombank.clickInfo);

    const interaction = this.constructWebInteraction();
    addDataLayerObject(eventName, _techcombank, interaction);
  }

  private getDestionationPathInteractionType(): string {
    if (!this.destinationPath) {
      return '';
    }

    const path = this.destinationPath;

    if (path.startsWith('/content/techcombank/web/') || path.includes('techcombank.com')) {
      return TCB_ANALYTICS_INTERACT_TYPE.OTHER;
    } else if (path.endsWith('.pdf') || path.endsWith('.docx')) {
      return TCB_ANALYTICS_INTERACT_TYPE.DOWNLOAD;
    }
    return TCB_ANALYTICS_INTERACT_TYPE.EXIT;
  }

  private addPrefixPageName(value: any): string | undefined {
    return value
      ? removeVietnameseAccent(`${replaceDoubleSpaceBySingleSpace(getTrackingPageName())}|${value}`)
      : undefined;
  }

  private getDownloadInteractionType() {
    return this.download || this.appDownload ? TCB_ANALYTICS_INTERACT_TYPE.DOWNLOAD : '';
  }

  private constructEventName(): string {
    let eventName = '';
    if (this.download) {
      eventName = this.eventName || TCB_ANALYTICS_EVENT.DOWNLOAD;
    }
    if (this.appDownload) {
      eventName = this.eventName || TCB_ANALYTICS_EVENT.APP_DOWNLOAD;
    }
    if (this.cta) {
      eventName = this.eventName || TCB_ANALYTICS_EVENT.CTA;
    }
    if (this.contactUs) {
      eventName = this.eventName || TCB_ANALYTICS_EVENT.CONTACT_US;
    }

    if (!eventName) {
      eventName = this.eventName || TCB_ANALYTICS_EVENT.LINK_CLICK;
    }

    return eventName;
  }

  private constructWebInteraction(): { webInteractions: { name: string; type: string } } {
    return {
      webInteractions: {
        name: this.formattedInteractName(),
        type: this.constructInteractType(),
      },
    };
  }

  private constructInteractType(): string {
    return (
      this.interactType ||
      this.getDestionationPathInteractionType() ||
      this.getDownloadInteractionType() ||
      TCB_ANALYTICS_INTERACT_TYPE.OTHER
    );
  }

  private formattedInteractName(): string {
    return stringToSlug(this.interactName);
  }
}
