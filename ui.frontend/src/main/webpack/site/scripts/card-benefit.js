import $ from 'jquery';
import { BaseComponent } from './base';
import DOMPurify from 'dompurify';
import { screenSmMax } from './constants/common';

export class CardBenefitComponent extends BaseComponent {
  $benefitModal = $('.card-benefits-modal');

  constructor() {
    super();
    this.init();
  }

  init() {
    if (this.$benefitModal.length) {
      this.handleResponsiveImages();
      const { openModal } = this.setupModal();
      this.setupCardClick(openModal);
    }
  }

  handleResponsiveImages() {
    const isMobile = typeof window !== 'undefined' && window.innerWidth <= screenSmMax;
    if (isMobile) {
      this.$benefitModal.each(function () {
        const imageMb = $(this).data('image-mb');
        $(this).find('.card-benefits-item-image').attr('src', imageMb);
      });
    }
  }

  setupModal() {
    const modal = document.getElementById('modalCard');
    const closeBtn = document.querySelector('.close-btn');

    const openModal = () => {
      modal.classList.add('show');
      document.body.classList.add('show-popup');
    };

    const closeModal = () => {
      modal.classList.remove('show');
      document.body.classList.remove('show-popup');
    };

    closeBtn.addEventListener('click', closeModal);
    window.addEventListener('click', function (event) {
      if (event.target === modal) {
        closeModal();
      }
    });

    return { openModal, closeModal };
  }

  setupCardClick(openModal) {
    this.$benefitModal.on('click', function () {
      openModal();
      const $modal = $('#modalCard');
      const title = $(this).data('title') || '';
      const content = $(this).data('content') || '';
      const image = $(this).data('image') || '';
      const btnLabel = $(this).data('btn-label') || '';
      const btnType = $(this).data('btn-type') || 'button';
      const link = $(this).data('btn-link');
      const rel = $(this).data('btn-rel');
      const target = $(this).data('btn-target');

      $modal.find('.banner-modal-img').attr('src', image).attr('alt', title);
      $modal.find('.modal-card-benefits-content .content .content-inner').html(DOMPurify.sanitize(content));

      const $btnCard = $modal.find('.modal-card-benefits-content .btn-card');

      if (!link || !btnLabel || !$btnCard) {
        $btnCard.css('display', 'none');
        $btnCard.closest('.sticky-btn').css('display', 'none');
        return;
      }

      $btnCard.closest('.sticky-btn').css('display', '');
      $btnCard.css('display', 'inline-flex').attr('href', link).attr('rel', '').attr('target', '');

      if (rel) {
        $btnCard.attr('rel', 'nofollow');
      }
      if (target) {
        $btnCard.attr('target', '_blank');
      }

      $btnCard.find('span').html(DOMPurify.sanitize(btnLabel));

      if (btnType === 'button') {
        $btnCard.removeClass('bg-default').addClass('bg-black');
      } else {
        $btnCard.removeClass('bg-black').addClass('bg-default');
      }
    });
  }
}
