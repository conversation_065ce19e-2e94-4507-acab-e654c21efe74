import moment from 'moment';
import Currency<PERSON>elper from './currency-helper';
import { addDataLayerObject } from './analytics';

// Fixing rate
$(document).ready(function () {
  const fixingRate = $('.gold-rate');
  const timeSelect = fixingRate.find('.time-select');

  const tableContainer = fixingRate.find('.table-content-container');
  const emptyLabel = fixingRate.find('.exchange-rate__empty-label');
  const tableElements = fixingRate.find('.exchange-rate-table-content');
  const selectedDateElements = fixingRate.find('.calendar__input-field');
  const defaultGoldLabel = fixingRate.attr('data-label');
  const calendarElements = fixingRate.find('.calendar_real_estate');

  let currentDate = moment().format('DD/MM/yyyy');
  let currentTime = null;

  function generateTable(element, items) {
    element.empty();
    items.forEach((item, idx, array) => {
      const lastRow = idx === array.length - 1;
      element.append(
        `
      <div class="exchange-rate__table-records">
        <div class="table__first-column first-column ${lastRow ? 'last-row' : ''}">
          <p>${item.label ?? defaultGoldLabel ?? ''}</p>
        </div>
        <div class="table-records__data">
          <div class="table-records__data-content ${lastRow ? 'last-row' : ''}">
            <p>${CurrencyHelper.numberWithCommas(item.bidRate)}</p>
          </div>
          <div class="table-records__data-content ${lastRow ? 'last-row' : ''} last-column">
            <p>${CurrencyHelper.numberWithCommas(item.askRate)}</p>
          </div>
        </div>
      </div>
    `,
      );
    });
  }

  const trackingAA = () => {
    addDataLayerObject(
      'calculator',
      {
        clickInfo: {
          calculatorName: 'Gold Rate',
          calculatorFields: `${currentDate || ''}${currentDate && currentTime ? '|' : ''}${currentTime || ''}`,
        },
      },
      { webInteractions: { name: 'Calculators', type: 'other' } },
    );
  };

  selectedDateElements.each((idx, element) => {
    $(element).text(currentDate);
    $(element).on('updateCal', function () {
      if (currentDate === $(element).text()) {
        return;
      }
      currentDate = $(element).text();
      currentTime = null;
      updateData(currentDate, currentTime);
      trackingAA();
    });
  });

  calendarElements.each(function () {
    $(this).on('click', function () {
      const calendarPopup = $(this).next('.calendar-popup');
      const calendarInputHeight = $(this).outerHeight();
      if (calendarPopup?.length === 0) {
        return;
      }
      calendarPopup.css({ top: `${calendarInputHeight}px` });
      if (calendarPopup?.hasClass('active')) {
        calendarPopup?.removeClass('active');
      } else {
        calendarPopup?.addClass('active');
      }
    });
  });

  timeSelect.on('timeChanged', function (event, newValue) {
    currentTime = newValue;
    updateData(currentDate, currentTime);
    trackingAA();
  });

  updateData(currentDate, null);

  function updateData(date, time) {
    selectedDateElements.each((idx, element) => {
      $(element).text(currentDate);
    });

    const baseUrl = fixingRate.attr('data-url');
    if (baseUrl === undefined) {
      return;
    }
    const dateUrl = date ? `.${date.split('/').reverse().join('-')}` : '';
    const timeUrl = date && time ? `.${time.replaceAll(':', '-')}` : '';
    const url = `${baseUrl}/_jcr_content.gold-rates${dateUrl}${timeUrl}.integration.json`;

    $.ajax({
      url: url,
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        const goldRate = data?.goldRate;
        // If not available
        if (goldRate === null || goldRate.data.length === 0) {
          emptyLabel.removeClass('hidden');
          tableContainer.addClass('hidden');
          timeSelect.changeOptions(goldRate.updatedTimes, currentTime);
          return;
        }

        emptyLabel.addClass('hidden');
        tableContainer.removeClass('hidden');

        // generate table
        tableElements.each((idx, element) => {
          generateTable($(element), goldRate.data);
        });
        timeSelect.changeOptions(goldRate.updatedTimes, currentTime);
        if (currentTime === null && goldRate.updatedTimes.length > 0) {
          currentTime = goldRate.updatedTimes[0];
        }
      },
    });
  }
});
