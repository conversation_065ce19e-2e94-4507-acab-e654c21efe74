import { AA_ATTRIBUTE, TCB_ANALYTICS_EVENT, TCB_ANALYTICS_INTERACT_TYPE } from './analytics';
import { getElementText } from './utils';

// Card Product Filter component
$(document).ready(function () {
  let cardFilter = $('.filter-panel');
  let cardFilterContainer = cardFilter.find('.filter-panel__container');
  let cardFilterItems = cardFilterContainer.find('.filter-panel__items');
  let cardFilterButton = cardFilterItems.find('.filter-panel__button');

  updateBackground();
  cardFilterButton.each(function () {
    $(this).click(function () {
      if ($(this).hasClass('filter-selected')) {
        if (this.getAttribute('data-id') != 'all') {
          $(this).removeClass('filter-selected');
        }
        let hasSelected = false;
        for (let card = 1; card < cardFilterButton.length; card++) {
          if ($(cardFilterButton[card]).hasClass('filter-selected')) {
            hasSelected = true;
          }
        }
        if (!hasSelected) {
          $(cardFilterButton).first().addClass('filter-selected');
        }
      } else {
        $(this).addClass('filter-selected');
      }
      $('.filter-panel-container').hide();
      cardFilterButton.each((index, item) => {
        if (this.getAttribute('data-id') != 'all' && item.getAttribute('data-id') == 'all') {
          let hasSelected = false;
          for (let card = 1; card < cardFilterButton.length; card++) {
            if ($(cardFilterButton[card]).hasClass('filter-selected')) {
              hasSelected = true;
            }
          }
          if (!hasSelected) {
            $(cardFilterButton).first().addClass('filter-selected');
          } else {
            $(item).removeClass('filter-selected');
          }
        } else if (this.getAttribute('data-id') == 'all' && item.getAttribute('data-id') != 'all') {
          $(item).removeClass('filter-selected');
        }
        $('.filter-panel-container').each((i, element) => {
          if (item.getAttribute('data-id') == 'all' && $(item).hasClass('filter-selected')) {
            $(element).show();
          }
          if (element.getAttribute('data-id') == item.getAttribute('data-id') && $(item).hasClass('filter-selected')) {
            $(element).show();
          }
        });
      });
      if (!cardFilterItems.find('.filter-selected').length) {
        $('.filter-panel-container').show();
      }
      updateBackground();
    });
  });

  function updateBackground() {
    const allItems = $('.filter-panel-container');
    const visibleItems = allItems.filter(function () {
      return $(this).css('display') !== 'none';
    });
    visibleItems.each((index, element) => {
      if (index % 2 !== 0) {
        $(element).css('backgroundColor', '#fff');
      } else {
        $(element).css('backgroundColor', '#f5f6f8');
      }
    });
  }
});
// End Card Product Filter components

// Question Answer component
$(document).ready(function () {
  let questionAnswer = $('.enhancedfaqpanel');

  let questionShoweMore = questionAnswer.find('[data-showmore]').attr('data-showmore');
  let questionShow = parseInt(questionShoweMore);
  let cardExpendingMode = questionAnswer.find('.hide-faqs').attr('expandingMode');

  let faqPanel = $('.faq-panel');
  if (faqPanel.parent().hasClass('faqpanel')) {
    questionAnswer = faqPanel;
    questionShoweMore = questionAnswer.find('data-showmore');
  }

  questionAnswer.each(function () {
    let listQA = $(this).find('.enhancedfaq');
    if (faqPanel.parent().hasClass('faqpanel')) {
      listQA = $(this).find('.answer-question');
    }
    let questions = $(this).find('.question');
    questions.find('h3').addClass('text-base font-semibold');
    questions.each((_, question) => {
      const q = $(question);
      q.attr(AA_ATTRIBUTE.CLICK_EVENT, TCB_ANALYTICS_EVENT.FAQ_CLICK);
      q.attr(
        AA_ATTRIBUTE.CLICK_INFO_VALUE,
        JSON.stringify({
          faq: getElementText(q),
        }),
      );
      q.attr(
        AA_ATTRIBUTE.WEB_INTERACTION_VALUE,
        JSON.stringify({ webInteractions: { name: 'FAQ Panel', type: TCB_ANALYTICS_INTERACT_TYPE.OTHER } }),
      );
    });

    let questionAnswerBtn = $(this).find('.read-more');
    // function showhide awswer section
    for (let i = 0; i < questions.length; i++) {
      questions[i].addEventListener('click', function () {
        this.classList.toggle('active');
        let expand = this.lastElementChild;
        let answer = this.nextElementSibling;
        let answerHeight = answer.scrollHeight + 'px';
        if (answer.style.height === answerHeight) {
          answer.style.marginBottom = '0px';
          answer.style.height = '0px';
          expand.style.transform = 'rotate(0deg)';
        } else {
          answer.style.marginBottom = '24px';
          answer.style.height = answerHeight;
          expand.style.transform = 'rotate(-180deg)';
        }

        // case user can only open one faq at once
        if(cardExpendingMode == 'singleMode') {
          let clickedQuestion = $(this);
          questions.each(function () {
            if($(this).hasClass('active') && !$(this).is(clickedQuestion)) {
              this.classList.toggle('active');
              let expand = this.lastElementChild;
              let answer = this.nextElementSibling;
              answer.style.marginBottom = '0px';
              answer.style.height = '0px';
              expand.style.transform = 'rotate(0deg)';
            }
          })
        }
      });
    }

    let j = 5;
    let step = j + 4;

    if (listQA.length <= 5) {
      questionAnswerBtn.css('display', 'none');
    } else {
      $(listQA[4]).css('border', 'none');
    }

    questionAnswerBtn.click(function () {
      if (step >= listQA.length) {
        for (j; j < listQA.length; j++) {
          listQA[j].style.display = 'block';
        }

        questionAnswerBtn.css('display', 'none');
      } else {
        for (j; j <= step; j++) {
          listQA[j].style.display = 'block';
        }
      }
      questionShow += parseInt(questionShoweMore);
      if (questionShow === listQA.length) {
        questionAnswerBtn.css('display', 'none');
      }
      step += parseInt(questionShoweMore);
      listQA.each(function (i) {
        if (i < questionShow) {
          $(this).find('.question').removeClass('active');
          $(this).find('img').css('transform', 'rotate(0deg)');
          $(this).find('.answer').css('marginBottom', '0');
          $(this).css('border-bottom', '1px solid rgba(128, 128, 128, 0.363)');
        }
        if (i == questionShow - 1 || i == listQA.length - 1) {
          $(this).css('border', 'none');
        }
      });
    });
  });
});

$(document).ready(function () {
  const filterPanelElement = $('.filter-panel-container');

  if (filterPanelElement?.length === 0) {
    return;
  }

  filterPanelElement.each(function () {
    const columnContainerElement = $(this)?.find($('.column-container'));
    const viewAllFAQButtonElement = $(this)?.find($('.button.section'));
    const enhancedFAQPanelElement = $(this)?.find($('.enhancedfaqpanel.section'));
    const LARGE_BREAKPOINT = 992;
    const windowWidth = $(window)?.width();
    if (
      windowWidth >= LARGE_BREAKPOINT ||
      !(
        columnContainerElement?.length > 0 &&
        viewAllFAQButtonElement?.length > 0 &&
        enhancedFAQPanelElement?.length > 0
      )
    ) {
      return;
    }
    columnContainerElement.css({ position: 'relative' });
    viewAllFAQButtonElement.css({
      position: 'absolute',
      bottom: '-2rem',
      display: 'flex',
      'align-items': 'center',
      'justify-content': 'center',
      width: '100%',
    });
  });
});

// open faq tab on clicking on clicking nav equals panel links
$(document).ready( function () {
  let navEqualPanelLink = $('.nav-equal-panel a');
  navEqualPanelLink.click(function() {
    let navEqualPanelLinkHref = $(this).attr('href');
    if (navEqualPanelLinkHref) {
      if (navEqualPanelLinkHref.charAt(0) === "#") {
        let redirectId = navEqualPanelLinkHref.substring(1);
        let faqAnswerWithRediectId = $('.filter-panel-container .question .cmp-text[id=' + redirectId + ']');
        if(faqAnswerWithRediectId.length > 0) {
          faqAnswerWithRediectId.closest('.question').click();
          $('html,body').stop().animate({
            scrollTop: faqAnswerWithRediectId.closest('.filter-panel-container').get(0).offsetTop - $('header').height()
          }, 100);
        }
      } 
    }
  }) 
});