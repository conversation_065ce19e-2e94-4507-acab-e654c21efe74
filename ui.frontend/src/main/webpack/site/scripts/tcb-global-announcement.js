import DOMPurify from 'dompurify';
import { BaseComponent } from './base';
import { lgSemiBreakpoint } from './constants/common';
import { getSessionCountData, updateSessionCountData } from './count-user-sessions';

export const isGlobalAnnouncementExist = () => {
  const header = document.querySelector('header.global-header');
  const globalAnnouncement = header?.querySelector('.global-announcement');
  return !!(globalAnnouncement && !globalAnnouncement.classList.contains('hidden'));
};

export const reCalculateHeaderHeight = () => {
  const header = document.querySelector('header.global-header');
  const headerLayout = header?.querySelector('.header_layout');
  const navigationSub = header?.querySelector('.header-content .navigation_sub');
  const headerNavigation = header?.querySelector('.header-navigation');
  const globalAnnouncement = header?.querySelector('.global-announcement');
  const isGAExist = isGlobalAnnouncementExist();

  if (!header || !headerLayout || !navigationSub || !headerNavigation) {
    return;
  }

  if (window.innerWidth > lgSemiBreakpoint) {
    headerNavigation.style.paddingTop = '';
    header.style.height = `${headerLayout.offsetHeight}px`;
    navigationSub.style.top = isGAExist ? `${headerLayout.offsetHeight}px` : '';
    navigationSub.style.height = '';
  } else {
    headerNavigation.style.paddingTop = isGAExist ? `${globalAnnouncement.offsetHeight}px` : '';
    header.style.height = '';
    navigationSub.style.top = navigationSub.classList.contains('active') ? `${header.offsetHeight}px` : '-100%';
    navigationSub.style.height =
      navigationSub.classList.contains('active') ? `calc(100% - ${header.offsetHeight}px)` : '0';
  }
};

export class TcbGlobalAnnouncement extends BaseComponent {
  pathName = window.location.pathname;
  lang = document.documentElement.lang.toLowerCase() ?? 'vi';
  globalAnnouncementData;

  constructor() {
    super();
    this.getGlobalAnnouncement();
  }

  get globalAnnouncementElement() {
    return this.querySelector('.global-announcement');
  }

  get gaWrapperElement() {
    return this.globalAnnouncementElement.querySelector('.global-announcement__wrapper');
  }

  get gaTitleElement() {
    return this.gaWrapperElement.querySelector('.global-announcement__content--title');
  }

  get gaContentElement() {
    return this.gaWrapperElement.querySelector('.global-announcement__content--description');
  }

  get gaCloseButtonElement() {
    return this.gaWrapperElement.querySelector('.global-announcement__close-button');
  }

  isShowGA() {
    if (!this.globalAnnouncementData) {
      return false;
    }

    const { announcementId, highlyImportant, reopenAfterHowManySessions } = this.globalAnnouncementData;
    const sessionCountData = getSessionCountData(announcementId);
    const count = sessionCountData[announcementId].count;
    const isClosed = sessionCountData[announcementId].isClosed;

    if (/yes/.test(highlyImportant) || !isClosed) {
      return true;
    }

    if (reopenAfterHowManySessions === 0) {
      sessionCountData[announcementId].count = 0;
      updateSessionCountData(sessionCountData);
      return false;
    }

    if (count >= reopenAfterHowManySessions) {
      sessionCountData[announcementId].count = 0;
      sessionCountData[announcementId].isClosed = false;
      updateSessionCountData(sessionCountData);
      return true;
    }

    return false;
  }

  getGlobalAnnouncement() {
    const endpoint = `/bin/globalAnnouncement?lang=${this.lang}&path=${this.pathName}`;
    this.fetchData(endpoint)
      .then((response) => {
        this.globalAnnouncementData = response?.data?.items?.length > 0 ? response.data.items[0] : null;
      })
      .catch(() => {
        this.globalAnnouncementData = null;
      })
      .finally(() => {
        const isShowGlobalAnnouncement = this.isShowGA();
        if (isShowGlobalAnnouncement) {
          this.globalAnnouncementElement.classList.remove('hidden');
          this.onInit();
        }
      });
  }

  fetchData(url) {
    return new Promise(function (resolve, reject) {
      $.ajax({
        url: url,
        method: 'get',
        contentType: 'application/json',
        success: function (response) {
          resolve(response);
        },
        error: function (xhr, status, error) {
          reject(error);
        },
      });
    });
  }

  onInit() {
    this.renderGAContent();
    this.renderGAStyle();
    this.subscribeClickEvent();
    reCalculateHeaderHeight();
  }

  renderGAContent() {
    const { announcementTitle, announcementContent } = this.globalAnnouncementData;
    this.gaTitleElement.textContent = announcementTitle;
    this.gaContentElement.innerHTML = DOMPurify.sanitize(announcementContent, { USE_PROFILES: { html: true } });
  }

  renderGAStyle() {
    const { theme, announcementBarBackground } = this.globalAnnouncementData;
    switch (theme) {
      case 'dark':
        this.globalAnnouncementElement.classList.remove('light-theme');
        this.globalAnnouncementElement.classList.add('dark-theme');
        this.globalAnnouncementElement.style.background =
          announcementBarBackground ? `url(${announcementBarBackground})` : 'white';
        break;

      case 'light':
      default:
        this.globalAnnouncementElement.classList.remove('dark-theme');
        this.globalAnnouncementElement.classList.add('light-theme');
        this.globalAnnouncementElement.style.background =
          announcementBarBackground ? `url(${announcementBarBackground})` : 'black';
        break;
    }
  }

  subscribeClickEvent() {
    this.gaWrapperElement.addEventListener('click', this.globalAnnouncementClickListener.bind(this), false);
    this.gaCloseButtonElement.addEventListener('click', this.closeButtonClickListener.bind(this));
  }

  globalAnnouncementClickListener(event) {
    const target = event.target;
    if (target.classList.contains('ga-close-button')) {
      event.preventDefault();
      return;
    }

    const { ctaLink } = this.globalAnnouncementData;
    if (ctaLink) {
      window.open(ctaLink, '_blank');
    }
  }

  closeButtonClickListener() {
    this.globalAnnouncementElement.classList.add('hidden');
    const { announcementId, highlyImportant } = this.globalAnnouncementData;
    if (/no/.test(highlyImportant)) {
      const sessionCountData = getSessionCountData(announcementId);
      sessionCountData[announcementId].isClosed = true;
      updateSessionCountData(sessionCountData);
    }

    setTimeout(() => {
      reCalculateHeaderHeight();
    });
  }
}
