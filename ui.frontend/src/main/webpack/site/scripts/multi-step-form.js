$(document).ready(function () {
  $('.multi-step-form').each((_, formElement) => {
    const formId = formElement.id;

    // set error message container to overflow parrent section container and fit full device screen
    let multiStepFormErrorContainer =  $(formElement).find('.multi-step-form__error-container')
    setErrorContainerWidth(multiStepFormErrorContainer);
    window.addEventListener('resize', function() {
      multiStepFormErrorContainer.css('width', '100%');
      setErrorContainerWidth(multiStepFormErrorContainer);
    }, true);

    // add submit function name to multi-step-validation multi-step-callback attribute of form tag. When user submit form, submit function get called
    // read: form-submit.js
    $(formElement).closest('form').attr('multi-step-validation', 'c-cash');

    // remove default validation to avoid affecting custom validation 
    $(formElement).closest('form').attr('novalidate', 'novalidate');
    
    // if not last step, move to next step
    $(`a[id^=${formId}-step]`).each((_, actionElement) => {
      $(actionElement).on('click', () => {
        switchMultiStepFormStep(actionElement.id);
        window.scrollTo({ top: formElement.offsetTop, behavior: 'smooth' });
      });
    });

    // show/hide the unfilled questions list
    $(formElement).find('.multi-step-form__error-message').click(function() {
      $(formElement).find('.multi-step-form__unfilled-surveys-list').toggle();
      $(this).find('.toggle-button').toggleClass('closed');
    });
  });
});

// this function is called after form is submitted, displaying unfilled fields
export const submitMultiStepForm = (validationType, e) => {
  if (validationType === 'c-cash') {
    let validationResult = false;
    $('.multi-step-form').each((_, formElement) => {
      // validate forms and filling inputs that users haven't filled
      clearUnfilledSurveysList($(formElement));
      let validateInfo = validateInfoFields($(formElement));
      let validateMultiStep = validateMultiStepForm($(formElement));
      displayErrorMessage($(formElement), validateInfo && validateMultiStep);
      if (validateInfo && validateMultiStep) {
        validationResult = true;
        displayScoreboard($(formElement));
      } else {
        return false;
      }
    });
    return validationResult;
  }

  return false;
};

// validate required info fields
function validateInfoFields($formElement) {
  let formId = $formElement.attr('id');
  let infoSteps = $formElement.find('.multi-step-form__step:not(:has(input.input__radio))');
  let validationResult = true;
  infoSteps.each(function() {
    let requiredInfoFields = $(this).find('input[required], .multiple-choice__container, .checkbox');
    requiredInfoFields.each(function(surveyIndex) {
      if($(this).hasClass('cmp-form-text__text') && $(this).attr('required')) {
        if($(this).val() == '') {
          validationResult = false;
          let stepTitle = $(this).siblings('label').text();
          displayUnfilledSurveysList($formElement, `<li surveyStepId="${formId}-step-1" surveyId="${surveyIndex}">${stepTitle}</li>`);
        }
      }
      if($(this).hasClass('multiple-choice__container')) {
        let minimumNumberOfAnswers = $(this).find('.multiple-choice__container--answer').attr('data-minimum-of-answers');
        let numberOfSelectedAnswers = $(this).find('.answer__item--input:checked').length;
        if(minimumNumberOfAnswers > numberOfSelectedAnswers) {
          validationResult = false;
          let stepTitle = $(this).find('.multiple-choice__container--question .question--title').text();
          displayUnfilledSurveysList($formElement, `<li surveyStepId="${formId}-step-1" surveyId="${surveyIndex}">${stepTitle}</li>`);
        }
      }
      if($(this).hasClass('checkbox') && $(this).find('input[name="ignore_tnc"]').length > 0) {
        if($(this).find('input[name="ignore_tnc"]:checked').length < 1) {
          validationResult = false;
          let stepTitle = $(this).find('label.checkbox-label p').text();
          displayUnfilledSurveysList($formElement, `<li surveyStepId="${formId}-step-1" surveyId="${surveyIndex}">${stepTitle}</li>`);
        }
      }
    })
  })
  return validationResult;
}

// validate if all questions were filled
function validateMultiStepForm($formElement) {
  let formId = $formElement.attr('id');
  let surveySteps = $formElement.find('.multi-step-form__step:has(input.input__radio)');
  let validationResult = true;
  let surveyText = $formElement.find('.multi-step-form__unfilled-surveys-list').attr('surveyText');
  let surveyStepText = $formElement.find('.multi-step-form__unfilled-surveys-list').attr('surveyStepText');
  // loop through surveys, if a survey has an unanswered question => not showing scoreboard
  surveySteps.each(function(surveyStepIndex){
    let surveys = $(this).find('.form-survey__single-choice');
    let surveyStep = $(this);
    surveys.each(function(surveyIndex) {
      if($(this).find('input.input__radio:checked').length == 0) {
        validationResult = false;
        let surveyStepTitle = surveyStep.find('.title .tcb-title').text().replace(/^\s*(\d+|[MDCLXVI]+)\.\s*/, '');
        let errorMsgContent = `<li surveyStepId="${formId}-step-${surveyStepIndex + 2}" surveyId="${surveyIndex}">${surveyText} ${surveyIndex + 1}, ${surveyStepText} ${surveyStepIndex + 1}. ${surveyStepTitle}</li>`
        displayUnfilledSurveysList($formElement, errorMsgContent);
      }
    })
  })
  
  redirectToSurvey($formElement);
  return validationResult;
}

//switch to another step on user click on "next step" button or validation missing fields
function switchMultiStepFormStep(stepId) {
  $('.multi-step-form .multi-step-form__step').each((_, stepElement) => {
    stepElement.id === stepId
      ? stepElement.classList.add('active')
      : stepElement.classList.remove('active');
  });
}

// if submit successful, show the score board
function displayScoreboard(formElement) {
  let formId = formElement.attr('id');
  // get number of step
  let numberOfStep = $(formElement).attr('numberOfSteps');
  switchMultiStepFormStep(formId + '-step-' + numberOfStep);
  window.scrollTo({ top: formElement.offsetTop, behavior: 'smooth' });
}

// if validation's result is failed, display error message, else hide error message
function displayErrorMessage($formElement, validationResult) {
  let errorMsg = $formElement.find('.multi-step-form__error-message');
  errorMsg.toggleClass('active', !validationResult);
  window.scrollTo({ top: $formElement.get(0).offsetTop, behavior: 'smooth' });
}

// clear up unfilled surveys list
function clearUnfilledSurveysList($formElement) {
  let unfilledSurveysList = $formElement.find('.multi-step-form__unfilled-surveys-list');
  unfilledSurveysList.html('');
}

// display unfilled surveys list
function displayUnfilledSurveysList($formElement, errorMsgContent) {
  let unfilledSurveysList = $formElement.find('.multi-step-form__unfilled-surveys-list');
  unfilledSurveysList.append(errorMsgContent);
}

// redirect to unfilled question when user click on list
function redirectToSurvey($formElement) {
  $formElement.find('.multi-step-form__unfilled-surveys-list li').click(function() {
    let surveyStepId = $(this).attr('surveyStepId');
    let surveyId = $(this).attr('surveyId');
    switchMultiStepFormStep(surveyStepId);
    let selectedSurveyStep = $formElement.find('.multi-step-form__step.active');
    let selectedSurvey = selectedSurveyStep.find('.cmp-form-text__text, .formsurvey').eq(surveyId);
    // get the height of sticky error message and header to subtract when scorlling top
    let headerHeight = $('header').height();
    let errorMessageHeight = $formElement.find('.multi-step-form__error-container').height();
    window.scrollTo({ top: selectedSurvey.offset().top - (headerHeight + errorMessageHeight), behavior: 'smooth' });
  });
}

//setup width of error message and unfilled questions list container
function setErrorContainerWidth($container) {
  let containerWidth = $container.width();
  let screenWidthWithoutScrollbar = $('body').prop('clientWidth');
  let calcualtedMargin = (containerWidth - screenWidthWithoutScrollbar) /2;
  let containerPaddingTop = $container.closest('.tcb-content-container').css('padding-top');
  $container.css('width', screenWidthWithoutScrollbar);
  $container.css('margin-left', calcualtedMargin);
  $container.css('margin-right', calcualtedMargin);
  $container.find('.multi-step-form__error-message').css('margin-top', '-' + containerPaddingTop);
}