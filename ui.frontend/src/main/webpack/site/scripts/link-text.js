function loadLinkText(){
    // Set unique ids for link texts and view more buttons
    $(".link-text.link-text-component").each(function(index) {
        $(this).attr("id", index);
    });
    $(".link-text-btn").each(function(index, el) {
        $(this).attr("id", index);
    });

    // Show/hide view more button base on number of link texts
    let totalLinkTextComps = $(".link-text.link-text-component").length;
    for(let index = 0; index < totalLinkTextComps; index++){
        let cards = $(".link-text.link-text-component#" + index + " .ticket-item");
        let cardsNumber = $(".link-text.link-text-component#" + index + " .ticket-item").length;
        if(window.innerWidth >= 1024){
            if(cardsNumber <= 12){
                $("#" + index + ".link-text-btn").hide();
            } else {
                $("#" + index + ".link-text-btn").show();
            }
            cards.each(function(i) {
                if (i >= 12) {
                    $(this).hide();
                } else {
                    $(this).show();
                }
            });
        }
        else{
            if(cardsNumber <= 3){
                $("#" + index + ".link-text-btn").hide();
            } else {
                $("#" + index + ".link-text-btn").show();
            }
            cards.each(function(i) {
                if (i >= 3) {
                    $(this).hide();
                } else {
                    $(this).show();
                }
            });
        }
    }

    // Show hidden items on click
    $(".link-text-btn").click(function(){
        let linkTextId = $(this).attr("id");
        let cards = $(".link-text.link-text-component#" + linkTextId + " .ticket-item");
        cards.show();
        $(this).hide();
    })
}

$(document).ready(function () {
    loadLinkText();
    $(window).on("resize", function() {
        loadLinkText();
    })
    window.addEventListener("message", (e) => {
      if (e.data === "reloadLinkText") {
        loadLinkText();
      }
    }, false);
});