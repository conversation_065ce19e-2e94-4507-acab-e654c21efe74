import { BaseComponent } from './base';
import { renderDayExpired } from './utils/day-expired.util';
import { renderImage } from './utils/default-image.util';
import { filterCardsByTab, handleClickPdf, scrollText, startDayExpiredInterval } from './exclusive-helper';
import { LABEL_PDF_DOWNLOAD } from './constants/offer-common';

export class ExclusiveOffer extends BaseComponent {
  constructor() {
    super();
    this.previewPdf = {
      dataDownloadPdfLabel: this.dataset.previewDownloadLabel || LABEL_PDF_DOWNLOAD,
    };
    this.init();
  }

  init() {
    $(document).ready(() => {
      renderDayExpired();
      startDayExpiredInterval();
      filterCardsByTab();
      renderImage();
      handleClickPdf(undefined,this.previewPdf);
      scrollText();
    });
  }
}
