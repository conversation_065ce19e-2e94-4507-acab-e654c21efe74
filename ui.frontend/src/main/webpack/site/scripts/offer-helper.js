import { SessionStorageUtil } from "./utils/storage.util";
import { PREVIOUS_ARRAY_PAGE_KEY_NAME, PREVIOUS_PAGE_KEY_NAME } from "./constants/common";

export const fetchData = (url) => {
  return new Promise(function (resolve, reject) {
    $.ajax({
      url: url,
      method: "get",
      contentType: "application/json",
      success: function (response) {
        resolve(response);
      },
      error: function (xhr, status, error) {
        reject(error);
      },
    });
  });
};

export const removeHTMLTags = (html) => {
  return html.replace(/<[^>]*>?/gm, '').trim();
};

export const updateSearchParams = (params) => {
  const url = new URL(window.location);
  url.search = '';
  url.hash = '';
  for (const [key, value] of Object.entries(params)) {
    if (value === null) {
      url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, value);
    }
  }
  window.history.pushState({}, '', url);
};

export const clearSearchParams = (paramsName, isReplace = true) => {
  const url = new URL(window.location);
  url.search = '';
  url.hash = '';
  for (const key in paramsName) {
    url.searchParams.delete(paramsName[key]);
  }
  if (isReplace) {
    window.history.replaceState({}, "", url);
  }
  else {
    window.history.pushState({}, '', url);
  }
};

export const addCurrentUrlHistory = () => {
  if (SessionStorageUtil.get(PREVIOUS_ARRAY_PAGE_KEY_NAME)) {
    SessionStorageUtil.set(PREVIOUS_ARRAY_PAGE_KEY_NAME, [
      ...SessionStorageUtil.get(PREVIOUS_ARRAY_PAGE_KEY_NAME),
      window.location.href,
    ]);
  } else {
    SessionStorageUtil.set(PREVIOUS_ARRAY_PAGE_KEY_NAME, [
      window.location.href,
    ]);
  }
};

export const resetHistory = () => {
  SessionStorageUtil.set(PREVIOUS_ARRAY_PAGE_KEY_NAME, []);
  SessionStorageUtil.set(PREVIOUS_PAGE_KEY_NAME, '');
};

export const convertPxToRem = (px, rootFontSize = 16) => {
  if (!px) {
    return null;
  }
  return px / rootFontSize;
};

export const createSearchUrl = (baseUrl, params) => {
  if (!params) {
    return baseUrl;
  }
  const searchParams = new URLSearchParams(params);
  return `${baseUrl}?${searchParams.toString()}`;
};

export const handleResetDropdownLanguage = () => {
  const languageDropdownItems = $('header .language_dropdown a');
  languageDropdownItems.each((_, element) => {
    $(element).parent().show();
  });
};

export const listenerElement = (targetElement, functionCallback) => {
  if (!targetElement) {
    return;
  }
  const callback = (mutationsList) => {
    for (const mutation of mutationsList) {
      functionCallback(mutation);
    }
  };
  const observer = new MutationObserver(callback);
  const config = { childList: true, subtree: true, attributes: true, attributeFilter: ['class', 'style'] };
  observer.observe(targetElement, config);
};

export const disableBodyScroll = (disable = true) => {
  const bodyElement = $('body')[0];
  if (bodyElement && disable) {
    bodyElement.style.overflow = 'clip';
  } else {
    bodyElement.style.overflow = '';
  }
};
