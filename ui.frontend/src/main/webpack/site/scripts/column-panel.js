$(document).ready(function () {
  let insuranceOption = $('.column-panel-component');
  let insuranceReadMore = insuranceOption.find('.read-more');
  let insuranceReadMoreBtn = insuranceReadMore.find('button');
  let insuranceExpand = insuranceReadMore.find('.expand');
  let listInsurance = insuranceOption.find('.list-option');
  let listImageTitle = insuranceOption[0] ? [...insuranceOption[0].querySelectorAll('.image-title')] : [];
  let maxHeight = Math.max(...listImageTitle.map(item => item.clientHeight));
  let listDescription = insuranceOption.find('.content-description');

  let itemPanel = insuranceOption.find('.item-panel');
  let itemOptions0 = $(itemPanel[0]).find('.option');
  let itemOptions01 = $(itemPanel[1]).find('.option');
  let itemOptions02 = $(itemPanel[2]).find('.option');

  let numberOfitemOptions0 = itemOptions0.length;
  let numberOfitemOptions01 = itemOptions01.length;
  let numberOfitemOptions02 = itemOptions02.length;
  let numberOfOptions = numberOfitemOptions0 + numberOfitemOptions01 + numberOfitemOptions02;

  let viewMoreLabel = $(".read-more").data('viewmore'); // changed by adobe
  let viewLessLabel = $(".read-more").data('viewless'); // changed by adobe

  updateHeightTitle();
  updateToolTipText();


  function updateHeightTitle() {
    if($(window).width() > 767 ) {
      listImageTitle.forEach(item => {
        item.style.height = maxHeight + 'px';
      });
    } else {
      $(listImageTitle).removeAttr('style');
    }
  }

  function updateToolTipText() {
    updateToolTipTextTitle();
    updateToolTipTextDes();
  }

  function updateToolTipTextDes() {
    listDescription.each(function() {
      $(this).find('.tooltiptext').remove();
      if( ($(this)[0].offsetHeight < $(this)[0].scrollHeight) && !$(this).find('.tooltiptext').length ) {
        const toolTipText = $(`<span class="tooltiptext"></span>`);
        toolTipText.text($(this).text());
        $(this).append(toolTipText);
      }
    });
  }

  function updateToolTipTextTitle() {
    $(listImageTitle).each(function() {
      $(this).find('.tooltiptext').remove();
      if( ($(this)[0].offsetHeight < $(this)[0].scrollHeight) && !$(this).find('.tooltiptext').length ) {
        const toolTipText = $(`<span class="tooltiptext"></span>`);
        toolTipText.text($(this).text());
        $(this).append(toolTipText);
      }
    });
  }

  insuranceOption.each(function () {
    let showedItem = countShowedItem(itemOptions0) + countShowedItem(itemOptions01) + countShowedItem(itemOptions02);
    let i = getListStartPoint(listInsurance);
    let temp = showedItem;
    let step = [3, 3, 3];

    // hide read-more button if no-content to load
    if (numberOfitemOptions0 <= 2 && numberOfitemOptions01 <= 2 && numberOfitemOptions02 <= 2) {
      insuranceReadMore.css('display', 'none');
    }

    // click readmore button
    insuranceReadMore.click(function () {
      // function use in screen > 767px
      if ($(window).width() >= 767) {
        // back to original state when all items be showed
        if (checkAllItemBeShowed(listInsurance, i)) {
          for (let item of listInsurance) {
            let listOption = $(item).find('.option');
            let numberOfOption = listOption.length;

            for (let j = getStartPoint(listOption); j < numberOfOption; j++) {
              listOption[j].style.display = 'none';
            }
          }

          // change btn + set initial value
          insuranceReadMoreBtn.text(viewMoreLabel); // changed by adobe
          insuranceExpand.css('transform', 'rotate(360deg)');
          i = getListStartPoint(listInsurance);
          step = [3, 3, 3];
          showedItem = temp;
        } else {
          // load items
          for (let m = 0; m < listInsurance.length; m++) {
            let listOption = $(listInsurance[m]).find('.option');
            let numberOfOption = listOption.length;

            if (step[m] >= numberOfOption) {
              for (i[m]; i[m] < numberOfOption; i[m]++) {
                listOption[i[m]].style.display = 'flex';
                showedItem++;
              }
            } else {
              for (i[m]; i[m] <= step[m]; i[m]++) {
                listOption[i[m]].style.display = 'flex';
                showedItem++;
              }
            }

            step[m] = i[m] + 1;
          }

          changedBtn(showedItem, numberOfOptions);
        }
      }
      updateToolTipTextDes();
    });

    // change btn
    function changedBtn(showedItem, numberOfItem) {
      if (showedItem == numberOfItem) {
        insuranceReadMoreBtn.text(viewLessLabel); // changed by adobe
        insuranceExpand.css('transform', 'rotate(180deg)');
      }
    }

    // return number of showed item
    function countShowedItem(listOfOptions) {
      let count = 0;
      for (let item of listOfOptions) {
        let option = $(item);
        if (option.css('display') == 'flex') {
          count++;
        }
      }
      return count;
    }

    // get start point of every list to loop
    function getStartPoint(listOfOptions) {
      if (countShowedItem(listOfOptions) > 2) {
        return 2;
      }
      return countShowedItem(listOfOptions);
    }

    // return list of start point
    function getListStartPoint(listInsurance) {
      let i = [];
      for (let item of listInsurance) {
        let listOptions = $(item).find('.option');
        i.push(getStartPoint(listOptions));
      }
      return i;
    }

    // check if all item be showed
    function checkAllItemBeShowed(listInsurance, i) {
      let result = 0;
      for (let j = 0; j < listInsurance.length; j++) {
        let listOptions = $(listInsurance[j]).find('.option');
        if (i[j] == listOptions.length) {
          result++;
        }
      }

      return result == listInsurance.length;
    }
  });
  $(window).resize(()=> {
    updateHeightTitle();
    updateToolTipText();
  });
});