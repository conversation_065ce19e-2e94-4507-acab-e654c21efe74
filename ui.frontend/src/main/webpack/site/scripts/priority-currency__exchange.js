import 'slick-carousel';
import { addDataLayerObject } from './analytics';

$(document).ready(function () {
  const usdLabelCash = 'USD (1,2)';
  const usdLabelTransfer = 'USD (50,100)';

  $('.currency-converter').each((_, currencyConverter) => {
    const priorityExchangePanel = $(currencyConverter).find('.priority-exchange__panel');
    const panelItemExchangeSection = priorityExchangePanel.find('.panel-item__exchange-section');
    const exchangeSectionTransType = panelItemExchangeSection.find('.exchange-section__transaction-type-selector');
    const exchangeSectionCurSel = panelItemExchangeSection.find('.exchange-section__currency-selector');

    const exchangeSectionTabWrap = exchangeSectionTransType.find('.exchange-section__tab-wrapper');
    const exchangeSectionTabItem = exchangeSectionTabWrap.find('.exchange-section__tabs-item');
    const currencySelectorCurItem = exchangeSectionCurSel.find('.currency-selector__currency-item');

    const exchangeSectionInput = $(currencyConverter).find('.exchange-section__input')[0];
    const exchangeSectionInputField = $(currencyConverter).find('.exchange-section__input input')[0];
    const exchangeSectionNote = $(currencyConverter).find('.exchange-section__note');
    const exchangeSectionError = $(currencyConverter).find('.exchange-section__error');

    const outputMoney = $(currencyConverter).find('input.money__output-field')[0];
    const inputMoney = $(currencyConverter).find('input.money__input-field');
    const scrollConverter = $(currencyConverter).find('.scroll-currency-converter ');
    const scrollCurrencyConverter = $('.scroll-currency-converter');

    let previousValue;
    let typeTransaction;
    let matchingItems;
    let currentCurrencySource;
    let trackingTransactionTab = '';
    let trackingCurrencyTab = '';

    if (scrollCurrencyConverter.length) {
      scrollCurrencyConverter.each((_, item) => {
        try {
          new ScrollCurrencyConverter(item);
        } catch (e) {
          console.log(e);
        }
      });
    }

    initData();

    if ($('.analytics-transaction-tab .exchange-section__tabs-item').length > 0) {
      trackingTransactionTab = formatTabText(
        $('.analytics-transaction-tab .exchange-section__tabs-item').first().text(),
      );
      $('.analytics-transaction-tab .exchange-section__tabs-item').click(function () {
        trackingTransactionTab = formatTabText($(this).text());
        trackingAA();
      });
    }

    if ($('.analytics-currency-tab .exchange-section__tabs-item').length > 0) {
      trackingCurrencyTab = formatTabText($('.analytics-currency-tab .exchange-section__tabs-item').first().text());
      $('.analytics-currency-tab .exchange-section__tabs-item').click(function () {
        trackingCurrencyTab = formatTabText($(this).text());
        trackingAA();
      });
    }

    /**
     * Format tab text
     * @param {string} text
     */
    function formatTabText(text) {
      return text.replace(/\n/g, '').trim();
    }

    currencySelectorCurItem.each(function (index) {
      let exchangeSectionTabScroll = $(this).find('.exchange-section__tabs-scroller');
      if (exchangeSectionTabScroll[0].scrollWidth > exchangeSectionTabScroll[0].clientWidth) {
        if (index === 1) {
          exchangeSectionTabScroll.not('.slick-initialized').slick({
            infinite: false,
            speed: 300,
            slidesToShow: 1,
            slidesToScroll: 1,
          });
        } else {
          exchangeSectionTabScroll.not('.slick-initialized').slick({
            infinite: false,
            arrows: true,
            speed: 300,
            slidesToScroll: 1,
            variableWidth: true,
          });
        }
        exchangeSectionTabScroll.on('afterChange', function () {
          const lastItemRight = $(this).find('.slick-slide').last().get(0).getBoundingClientRect().right;
          const divRight = $(this).get(0).getBoundingClientRect().right;
          if (divRight > lastItemRight) {
            scrollConverter.removeClass('can-next');
          } else {
            scrollConverter.addClass('can-next');
          }
        });
      }
    });

    function trackingAA() {
      addDataLayerObject(
        'calculator',
        {
          clickInfo: {
            calculatorName: 'Currency Exchange',
            calculatorFields: `${trackingTransactionTab}|${trackingCurrencyTab}`,
          },
        },
        { webInteractions: { name: 'Calculators', type: 'other' } },
      );
    }

    function disableInput(element) {
      element.addClass('not-found');
      currencySelectorCurItem.addClass('not-found');
      exchangeSectionError.css('display', 'block');
      exchangeSectionNote.css('display', 'none');
      $(exchangeSectionInputField).attr('disabled', 'disabled');
      $(exchangeSectionInput).addClass('active');
      previousValue = commasNumber(exchangeSectionInputField.value) || previousValue;
      exchangeSectionInputField.value = '';
    }

    function enableInput(element) {
      element.removeClass('not-found');
      currencySelectorCurItem.removeClass('not-found');
      exchangeSectionError.css('display', 'none');
      exchangeSectionNote.css('display', 'block');
      exchangeSectionInputField.removeAttribute('disabled');
      $(exchangeSectionInput).removeClass('active');
      previousValue = exchangeSectionInputField.value ? commasNumber(exchangeSectionInputField.value) : previousValue;
      exchangeSectionInputField.value = numberWithCommas(previousValue) || exchangeSectionInputField.value;
    }

    function initData() {
      const url = currencyConverter.getAttribute('data-url');

      const displayCurrencyInput = (element, response, currentCurrencySource) => {
        typeTransaction = $(element).attr('data-type');
        matchingItems = getCurrencyWithLabel(currentCurrencySource, response?.exchangeRate.data, typeTransaction);
        if (!matchingItems) {
          disableInput($(element));
        } else {
          enableInput($(element));
        }
        let result = formatMoney(parseInt(previousValue) * matchingItems?.[typeTransaction]) || '';
        outputMoney.value = result;
      };

      $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function (response) {
          exchangeSectionTabItem.each(function () {
            if ($(this).hasClass('tab-active')) {
              displayCurrencyInput(this, response, currentCurrencySource);
            }
            $(this).click(function () {
              exchangeSectionTabItem.removeClass('tab-active');
              $(this).addClass('tab-active');
              displayCurrencyInput(this, response, currentCurrencySource);
            });
          });
          currencySelectorCurItem.each(function () {
            let currencySelector = $(this);
            let exchangeSectionTabScroll = $(this).find('.exchange-section__tabs-scroller.scroller');
            let currencyItemTabItem = exchangeSectionTabScroll.find('.exchange-section__tabs-item');
            currencyItemTabItem.each(function () {
              let tabItem = $(this);
              if (tabItem.hasClass('tab-active')) {
                currentCurrencySource = tabItem.text().trim();
                matchingItems = getCurrencyWithLabel(
                  tabItem.text().trim(),
                  response?.exchangeRate.data,
                  typeTransaction,
                );

                if (!matchingItems || $(this).hasClass('not-found')) {
                  disableInput(currencySelector);
                } else {
                  enableInput(currencySelector);
                }

                let result = formatMoney(parseInt(previousValue) * matchingItems?.[typeTransaction]) || '';
                outputMoney.value = result;
              }
              $(this).click(function () {
                currencyItemTabItem.removeClass('tab-active');
                currentCurrencySource = tabItem.text().trim();
                matchingItems = getCurrencyWithLabel(
                  tabItem.text().trim(),
                  response?.exchangeRate.data,
                  typeTransaction,
                );
                $(this).addClass('tab-active');
                if (!matchingItems || $(this).hasClass('not-found')) {
                  disableInput(currencySelector);
                } else {
                  enableInput(currencySelector);
                }

                let result = formatMoney(parseInt(previousValue) * matchingItems?.[typeTransaction]) || '';
                outputMoney.value = result;
              });
            });
          });
          inputMoney.on('input', function (event) {
            const amount = $(this).val().replaceAll(',', '');
            const digitRegExp = /^\d+$/;
            const inputValue = event?.originalEvent?.data ?? '';
            const validAmount = digitRegExp.test(amount) ? amount : amount.replace(inputValue, '');
            $(this).val(numberWithCommas(validAmount));
            let result = formatMoney(parseInt(commasNumber($(this).val())) * matchingItems?.[typeTransaction]) || '';
            outputMoney.value = result;
          });
        },
        error: function (error) {
          console.log(error);
        },
      });
    }
  });

  function commasNumber(x) {
    return x.toString().replaceAll(',', '');
  }

  function numberWithDot(x) {
    if (x) {
      return x
        .toString()
        .replaceAll('.', '')
        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
    }
    return '';
  }

  function numberWithCommas(x) {
    return numberWithDot(x).replaceAll('.', ',');
  }

  function formatMoney(number) {
    const formattedNumber = Number(number).toFixed(3);
    const parts = formattedNumber.split('.');
    const integerPart = parts[0];
    let decimalPart = parts[1];
    const integerWithCommas = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (number) {
      for (let i = decimalPart.length - 1; i >= 0; i--) {
        if (decimalPart[i] == '0') {
          decimalPart = decimalPart.slice(0, -1);
        } else {
          break;
        }
      }
      const result = decimalPart ? integerWithCommas + '.' + decimalPart : integerWithCommas;
      return result;
    } else {
      return null;
    }
  }

  function getCurrencyWithLabel(currencyLabel, data, typeTransaction) {
    let item = null;
    if (!currencyLabel) {
      return null;
    }

    const currency = currencyLabel?.toLowerCase();

    if (currency?.startsWith('usd')) {
      if (currency === 'usd') {
        item = getUSDExchangeRates(typeTransaction, data);
      } else {
        item = data.filter((element) => element.label.toLowerCase() == currency && element[typeTransaction]);
      }
    } else {
      item = data.filter((element) => element.sourceCurrency.toLowerCase() == currency && element[typeTransaction]);
    }
    item.sort(function (a, b) {
      return a < b;
    });
    if (item.length > 0) {
      return item[0];
    }
    return null;
  }

  function getUSDExchangeRates(typeTransaction, data) {
    let exchangeRates = null;
    switch (typeTransaction) {
      case 'bidRateCK':
      case 'askRate':
        exchangeRates = data.filter(
          (element) => element.label == usdLabelTransfer && element[typeTransaction]
        );
        break;
      case 'bidRateTM':
      case 'askRateTM':
        exchangeRates = data.filter((element) => element.label == usdLabelCash && element[typeTransaction]);
        break;
      default:
        break;
    }
    return exchangeRates;
  }
});
