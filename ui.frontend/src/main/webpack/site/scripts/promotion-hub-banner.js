import 'slick-carousel';
import { BaseComponent } from './base';
import { handleClickPdf } from './exclusive-helper';
import { SELECTOR_DETAIL_LINK, LABEL_PDF_DOWNLOAD } from './constants/offer-common';
export class TcbPromotionBannerCarousel extends BaseComponent {
  selectorPdfLink = `tcb-promotion-banner-carousel a.promotion-detail-link[href]:not(${SELECTOR_DETAIL_LINK})`;

  constructor() {
    super();
    this.previewPdf = {
      dataDownloadPdfLabel: this.dataset.previewDownloadLabel || LABEL_PDF_DOWNLOAD,
    };
    this.autoPlaySpeed = this.dataset.autoplaySpeed || false;
    this.init();
  }

  init() {
    this.initCarousel();
    handleClickPdf(this.selectorPdfLink, this.previewPdf);
  }

  initCarousel() {
    $(this.querySelector('.promotion-banner-slick')).slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplay: this.autoPlaySpeed,
      dots: true,
      arrows: false,
      customPaging: function (slider, i) {
        if (i < 4) {
          return '<button>' + (i + 1) + '</button>';
        } else {
          return '';
        }
      },
    });
  }
}
