import { addDataLayerObject } from './analytics';

$(document).ready(function () {
  function numberWithDot(x) {
    return x
      .toString()
      .replaceAll('.', '')
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
  }

  function numberWithCommas(x) {
    return numberWithDot(x).replaceAll('.', ',');
  }

  function toInt(x) {
    if (!x) return 0;
    return parseInt(x.replaceAll('.', ''));
  }

  function enforceMinMax(el) {
    if (el.value != '') {
      if (parseInt(el.value) < parseInt(el.min)) {
        el.value = el.min;
      }
      if (parseInt(el.value) > parseInt(el.max)) {
        el.value = el.max;
      }
    }
  }

  const sliders = $('.tcb-input--save-calc');
  if (sliders.length > 0) {
    sliders.each(function () {
      const slider = $(this);
      const saveCalculation = slider.closest('.save-calculation');
      const moneyInput = saveCalculation.find("[name='depositMoney']");
      const monthInput = saveCalculation.find("[name='depositMonth']");
      let allowtrackAnalytics = false;
      moneyInput.forceNumericOnly();
      monthInput.forceNumericOnly();

      function trackAnalytics() {
        let sliderValue = slider.find('.tcb-input-range_inline-value')[0];
        let save = moneyInput.val().replaceAll(',', '');
        let percent = toInt(sliderValue.innerText);
        let month = toInt(monthInput.val());

        if (allowtrackAnalytics && save && percent && month) {
          addDataLayerObject(
            'calculator',
            {
              clickInfo: {
                calculatorName: 'Debt Panel',
                calculatorFields: `${save}|${percent}%|${month}`,
              },
            },
            { webInteractions: { name: 'Debt Tabs Panel', type: 'other' } },
          );
          allowtrackAnalytics = false;
        }
      }

      function calcSaveMoney() {
        let sliderValue = slider.find('.tcb-input-range_inline-value')[0];
        let save = moneyInput.val().replaceAll(',', '');
        let percent = toInt(sliderValue.innerText);
        let month = toInt(monthInput.val());
        if (save > 0) {
          let profit = Math.round(save * month * (percent / 100 / 12));
          let total = Number(save) + Number(profit);
          saveCalculation.find('#profit-value--save-calc').html(numberWithCommas(profit) + ' VND');
          saveCalculation.find('#total-value--save-calc').html(numberWithCommas(total) + ' VND');

          allowtrackAnalytics = true;
        } else {
          saveCalculation.find('#profit-value--save-calc').html(0 + ' VND');
          saveCalculation.find('#total-value--save-calc').html(0 + ' VND');
        }
      }

      moneyInput.keypress(function (event) {
        const selectionStart = $(this)[0].selectionStart;
        const selectionEnd = $(this)[0].selectionEnd;
        const zeroKeycode = 48;
        if (
          ($(this).val().length > 14 && selectionStart === selectionEnd) ||
          ($(this).val() === '' && event.keyCode === zeroKeycode)
        ) {
          event.preventDefault();
        }
        const charCode = event.which ? event.which : event.keyCode;
        if (String.fromCharCode(charCode).match(/\D/g)) {
          return false;
        }
      });

      moneyInput.keyup(function () {
        if ($(this).val() === '') {
          saveCalculation.find('#profit-value--save-calc').html(0 + ' VND');
          saveCalculation.find('#total-value--save-calc').html(0 + ' VND');
          return;
        }
        const maxNumber = 500000000000;
        const currentInput = $(this).val().replaceAll(',', '');
        if (isNaN(currentInput)) {
          $(this).val('');
          return;
        }
        const updateValue = currentInput > maxNumber ? maxNumber : currentInput;

        $(this).val(numberWithCommas(updateValue));

        calcSaveMoney();
      });

      moneyInput.on('focusout', () => {
        trackAnalytics();
      });

      moneyInput.on('input', function (event) {
        const amount = $(this).val().replaceAll(',', '');
        const digitRegExp = /^\d+$/;
        const inputValue = event?.originalEvent?.data ?? '';
        const validAmount = digitRegExp.test(amount) ? amount : amount.replace(inputValue, '');
        $(this).val(validAmount);
      });

      monthInput.on('input', function () {
        const amount = $(this).val().replaceAll('.', '');
        const digitRegExp = /^\d+$/;
        $(this).val(digitRegExp.test(amount) ? numberWithDot(amount) : '');
      });

      monthInput.keyup(function () {
        if ($(this).val() === '') {
          saveCalculation.find('#profit-value--save-calc').html(0 + ' VND');
          saveCalculation.find('#total-value--save-calc').html(0 + ' VND');
          return;
        }
        enforceMinMax(this);
        $(this).val(numberWithDot(toInt($(this).val())));
        calcSaveMoney();
      });

      monthInput.on('focusout', () => {
        trackAnalytics();
      });

      slider[0].addEventListener('setvaluemouseup', () => {
        calcSaveMoney();
        trackAnalytics();
      });
    });
  }
});
