import { SELECTOR_DETAIL_LINK } from './constants/offer-common';
import { renderDayExpired } from './utils/day-expired.util';

let intervalId;
const selectorPdfLinkOrigin = `.card .group_card__content .card__button a[href]:not(${SELECTOR_DETAIL_LINK})`;

export const updateVisibility = ($cardItems, $loadMoreBtn, selectedType, itemsToShowInitially) => {
  $cardItems.hide();
  const $matchingCards = $cardItems.filter(function () {
    const cardTypes = ($(this).data('tab') || '').toString().split(',');
    return $.inArray(selectedType, cardTypes) !== -1;
  });

  $cardItems.not($matchingCards).hide();
  $matchingCards.slice(0, itemsToShowInitially).show();
  if ($matchingCards.length > itemsToShowInitially) {
    $loadMoreBtn.show();
  } else {
    $loadMoreBtn.hide();
  }
};

export function startDayExpiredInterval() {
  if (intervalId) {
    clearInterval(intervalId);
  }
  return setInterval(() => {
    renderDayExpired();
  }, 1000);
}

export function filterCardsByTab() {
  const $tabsContainer = $('.tab-card');
  const $tabItems = $tabsContainer.find('.tab-card__item a');
  const $contentContainer = $('.tab-content');
  const $cardItems = $contentContainer.find('.card');
  const $loadMoreBtn = $('.card-see-more');

  const itemsToShowInitially = 4;
  const itemsToShowOnClick = $loadMoreBtn.data('see-more') || 8;

  function handleTabClick(e) {
    e.preventDefault();
    const selectedType = $(this).data('tab');
    $tabItems.removeClass('active');
    $(this).addClass('active');
    updateVisibility($cardItems, $loadMoreBtn, selectedType, itemsToShowInitially);
    scrollText();
  }

  function handleLoadMoreClick() {
    const $activeTab = $tabItems.filter('.active');
    if (!$activeTab.length) {
      return;
    }

    const selectedType = $activeTab.data('tab');
    const $matchingCards = $cardItems.filter(function () {
      const cardTypes = ($(this).data('tab') || '').toString().split(',');
      return $.inArray(selectedType, cardTypes) !== -1;
    });

    const $hiddenMatchingCards = $matchingCards.filter(':hidden');
    $hiddenMatchingCards.slice(0, itemsToShowOnClick).show();

    if ($matchingCards.filter(':hidden').length > 0) {
      $loadMoreBtn.show();
    } else {
      $loadMoreBtn.hide();
    }
  }

  $tabItems.on('click', handleTabClick);
  $loadMoreBtn.on('click', handleLoadMoreClick);

  const initialTabType = $tabItems.filter('.active').data('tab') || 'popular';
  updateVisibility($cardItems, $loadMoreBtn, initialTabType, itemsToShowInitially);
}

export function showPreviewModalListener(event, option = {}) {
  let { dataDownloadPdfLabel, previewTitle, previewLoadingLabel } = option;
  const target = event.currentTarget;
  if (!dataDownloadPdfLabel) {
    dataDownloadPdfLabel = $(target).data('download-pdf-label');
  }
  if (!previewTitle) {
    previewTitle = 'Preview';
  }
  if (!previewLoadingLabel) {
    previewLoadingLabel = 'Loading PDF...';
  }
  const previewURL = $(target)?.attr('href');

  if (previewURL && previewURL.includes('.pdf')) {
    event.preventDefault();
    triggerShowPreviewModal(previewURL, previewTitle, previewLoadingLabel, dataDownloadPdfLabel);
  }
}

export function triggerShowPreviewModal(url, title, loadingLabel, downloadLabel) {
  const previewModalElement = $('.popup-download').eq(0);
  previewModalElement.trigger('show-preview-modal', [
    {
      url,
      documentType: 'pdf',
      title,
      loadingLabel,
      downloadLabel,
    },
  ]);
}

export function handleClickPdf(selectorPdfLink = selectorPdfLinkOrigin, option = {}) {
  // Unbind previous event listeners to avoid duplication
  $(document).off('click', selectorPdfLink);
  $(document).on('click', selectorPdfLink, (e) => showPreviewModalListener(e, option));
}

export function scrollText() {
  $('.card').each(function () {
    const $container = $(this);
    const $text = $container.find('.card__title');
    if ($text.length && $text[0].scrollWidth > $container.innerWidth()) {
      $text.addClass('marquee');
    }
  });
}
