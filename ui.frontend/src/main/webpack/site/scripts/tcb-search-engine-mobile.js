import { BaseComponent } from "./base";

export class TcbSearchEngineMobile extends BaseComponent {
  lastScroll = 0;

  constructor() {
    super();
    this.onInit();
  }

  onInit() {
    const searchEngine = $(".header_layout").find("tcb-search-engine-mobile");
    window.addEventListener("scroll", () => {
      if (window.scrollY > this.lastScroll) {
        $(searchEngine).addClass("search-engine-hidden");
        $(searchEngine).removeClass("search-engine-show");
      } else if (window.scrollY < this.lastScroll) {
        $(searchEngine).addClass("search-engine-show");
        $(searchEngine).removeClass("search-engine-hidden");
      }
      this.lastScroll = window.scrollY <= 0 ? 0 : window.scrollY;
    });
  }
}

