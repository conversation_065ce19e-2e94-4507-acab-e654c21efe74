import { readPdfFile, renderYoutube } from "./service-fee";

$(document).ready(function () {
  const bodyElement = $("body");
  const popupDownloadElements = $(".popup-download");
  if (popupDownloadElements?.length === 0) {
    return;
  }

  popupDownloadElements.each(function () {
    const popupDownloadElement = $(this);
    const closeIcon =
      "/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-red.svg";
    const downloadIcon =
      "/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-pdf-download.svg";

    let url = "";
    let title = "";
    let loadingLabel = "";
    let downloadLabel = "";
    let popupContentElement;
    let pdfName = "";

    popupDownloadElement.on("show-preview-modal", function (event, params) {
      if (!params?.url) {
        return;
      }

      url = params.url;
      title = params?.title || "Preview";
      loadingLabel = params?.loadingLabel || "Loading PDF ...";
      downloadLabel = params?.downloadLabel || "Download";
      pdfName = url.split("/").pop(); 

      switch (params.documentType) {
        case "pdf":
          renderPDFPreviewModal();
          break;

        case "media":
          renderMediaPreviewModal();
          break;

        default:
          window.open(url, "_blank");
          break;
      }
    });

    function renderPDFPreviewModal() {
      renderPreviewModal();
      const downloadButtonElement = $(
        popupContentElement.querySelector(".foot a"),
      );
      readPdfFile(popupDownloadElement, downloadButtonElement, url, title);
    }

    function renderMediaPreviewModal() {
      renderPreviewModal();
      renderYoutube(popupDownloadElement, title, url);
    }

    function renderPreviewModal() {
      popupDownloadElement.empty();
      renderPreviewModalWrapper();
      renderPreviewModalHeader();
      renderPreviewModalBody();
      renderPreviewModalFooter();
      showPreviewModal();
    }

    function renderPreviewModalWrapper() {
      popupContentElement = document.createElement("div");
      popupContentElement.classList.add("popup-content");
      popupContentElement.addEventListener("click", function (event) {
        event.stopPropagation();
      });
      popupDownloadElement.append(popupContentElement);
    }

    function renderPreviewModalHeader() {
      /* <img src="${closeIcon}"> */
      const buttonIconElement = document.createElement("img");
      buttonIconElement.src = closeIcon;

      /* <div class="button-label"></div> */
      const buttonLabelElement = document.createElement("div");
      buttonLabelElement.classList.add("button-label");
      buttonLabelElement.append(buttonIconElement);

      /* <button class="close-btn"></button> */
      const closeButtonElement = document.createElement("button");
      closeButtonElement.classList.add("close-btn");
      closeButtonElement.append(buttonLabelElement);
      closeButtonElement.addEventListener("click", function () {
        hidePreviewModal();
      });

      /* <span class="title"></span> */
      const titleElement = document.createElement("span");
      titleElement.classList.add("title");

      /* <div class="head"></div> */
      const headElement = document.createElement("div");
      headElement.classList.add("head");
      headElement.append(titleElement);
      headElement.append(closeButtonElement);

      popupContentElement.append(headElement);
    }

    function renderPreviewModalBody() {
      /* <div class="loading">${loadingLabel}</div> */
      const loadingElement = document.createElement("div");
      const loadingText = document.createTextNode(loadingLabel);
      loadingElement.classList.add("loading");
      loadingElement.append(loadingText);

      /* <div class="file-content"></div> */
      const embedElement = document.createElement("div");
      embedElement.classList.add("file-content");

      popupContentElement.append(loadingElement);
      popupContentElement.append(embedElement);
    }

    function renderPreviewModalFooter() {
      /* <span class="default-bold">${downloadLabel}</span> */
      const downloadLabelElement = document.createElement("span");
      const downloadLabelText = document.createTextNode(downloadLabel);
      downloadLabelElement.classList.add("default-bold");
      downloadLabelElement.append(downloadLabelText);

      /* <img src=${downloadIcon}> */
      const downloadIconElement = document.createElement("img");
      downloadIconElement.src = downloadIcon;

      /* <a target="_blank"></a> */
      const anchorElement = document.createElement("a");
      anchorElement.target = "_blank";
      anchorElement.append(downloadLabelElement);
      anchorElement.append(downloadIconElement);
      anchorElement.addEventListener("click", function () {
        hidePreviewModal();
      });

      //tcb-tracker
      const downloadTracker = document.createElement("tcb-tracker");
      downloadTracker.setAttribute('download',pdfName);
      downloadTracker.setAttribute('value',downloadLabel);
      downloadTracker.appendChild(anchorElement);

      /* <div class="foot"></div> */
      const footElement = document.createElement("div");
      footElement.classList.add("foot");
      footElement.append(downloadTracker);
      popupContentElement.append(footElement);
    }

    function hidePreviewModal() {
      popupDownloadElement.empty();
      popupDownloadElement.css("display", "none");
      bodyElement.removeClass("show-popup");
    }

    function showPreviewModal() {
      popupDownloadElement.css("display", "flex");
      bodyElement.addClass("show-popup");
    }

    /* Hide modal when click outside */
    $(document).click(function (event) {
      if ($(event.target).is(popupDownloadElement)) {
        hidePreviewModal();
      }
    });

    /* Hide modal when press escape */
    $(document).keyup(function (event) {
      if (event.keycode === 27) {
        hidePreviewModal();
      }
    });
  });
});
