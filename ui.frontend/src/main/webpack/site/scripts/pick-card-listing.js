import { BaseComponent } from './base';

export class CardPicker extends BaseComponent {
  constructor() {
    super();
    this.init();
  }
  checkIconSvg = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/checked-icon.svg';
  addIconSvg = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/add-icon.svg';

  contentBTNCompareAdded = `
        Added <img alt="${this.checkIconSvg}" class="icon-svg-checked" src="${this.checkIconSvg}">
      `;
  contentBTNCompare = `
        Add to compare <img alt="icon-arrow" src="${this.addIconSvg}">
      `;

  creditCardListingCompare = $('.credit-card-listing-compare');
  urlParams = new URLSearchParams(location.search);
  cardIdsGlobal = this.urlParams.getAll('cardId');
  listDataImageCompare = [];
  listDataFilter = {};
  listDataFilterPickerGlobal = {};

  initDataAndUpdateParamURLSearch(cardIdsParam) {
    const cardIds = cardIdsParam || [];
    const newCardIds = [...new Set(cardIds)].slice(0, 3);
    if (!newCardIds.length) {
      return;
    }
    const newParams = new URLSearchParams();

    newCardIds.map((item) => {
      newParams.append('cardId', item);
    });

    const newUrl = `${window.location.pathname}?${newParams.toString()}`;
    window.history.pushState({}, '', newUrl);
    this.cardIdsGlobal = newCardIds;

    const listButtonCompare = $('.button-item-compare');
    if (listButtonCompare.length && newCardIds.length) {
      listButtonCompare.each((_, buttonCompare) => {
        let cardId = '';
        if ($(buttonCompare).data('id')) {
          cardId = $(buttonCompare).data('id').toString();
        }
        const cardImage = $(buttonCompare).data('image');

        if (cardIds.includes(cardId)) {
          const index = this.listDataImageCompare.findIndex((item) => item.id === cardId);
          if (index >= 0) {
            return;
          }
          this.listDataImageCompare.push({
            id: cardId,
            img: cardImage,
          });
        }
      });
      this.handleRenderComponentCompare();
    }
  }

  handleGetLabelButtonSelectCompare() {
    const buttonCompares = $('.button-item-compare');
    if (buttonCompares.length) {
      const buttonCompare = buttonCompares[0];
      const addLabel = $(buttonCompare).data('add-label');
      const addedLabel = $(buttonCompare).data('added-label');

      this.contentBTNCompareAdded = `${addedLabel} <img alt="${this.checkIconSvg}" 
      class="icon-svg-checked" src="${this.checkIconSvg}">`;
      this.contentBTNCompare = `${addLabel} <img alt="icon-arrow" src="${this.addIconSvg}">`;
    }
  }

  handleRenderDataFilter() {
    const listPickCard = $('.pick-card-container');
    if (listPickCard) {
      const listDataFilterPicker = this.listDataFilterPickerGlobal;
      listPickCard.each(function (_, pickCardElement) {
        let dataTypeList = [];
        if ($(pickCardElement).data('type')) {
          dataTypeList = $(pickCardElement).data('type').split(',');
        }
        if (dataTypeList.length) {
          dataTypeList.map((item) => {
            listDataFilterPicker[item] = item;
          });
        }
      });

      let wrapperContentFilter = null;
      if (this.creditCardListingCompare.length) {
        wrapperContentFilter = this.creditCardListingCompare.find('.filter__item');
      }

      if (wrapperContentFilter) {
        wrapperContentFilter.each((index, itemFilter) => {
          const button = $(itemFilter).find('.credit-card-listing__button');
          const cardType = button.data('card-type');
          if (!listDataFilterPicker[cardType]) {
            $(itemFilter).css('display', 'none');
          }
        });

        wrapperContentFilter.each((index, element) => {
          $(element).on('click', () => {
            const button = $(element).find('.credit-card-listing__button');
            const cardType = button.data('card-type');
            if (this.listDataFilter[cardType]) {
              delete this.listDataFilter[cardType];
              button.removeClass('filter-selected');
            } else {
              this.listDataFilter[cardType] = cardType;
              button.addClass('filter-selected');
            }
            this.handleRenderPickCardForFilter();
          });
        });
      }
      this.listDataFilterPickerGlobal = listDataFilterPicker;
    }
  }

  handleRenderPickCardForFilter() {
    const listingPickCardContainer = $('.listing-pick-card-container');

    if (!listingPickCardContainer.length) {
      return;
    }
    const isFilterEmpty = Object.keys(this.listDataFilter).length === 0;
    listingPickCardContainer.each((_, pickCardListing) => {
      const $listing = $(pickCardListing);
      const pickCards = $listing.find('.pick-card-container');

      if (isFilterEmpty) {
        pickCards.css('display', 'flex');
        $listing.css('display', 'flex');
        return;
      }

      let visibleCount = 0;
      pickCards.each((_, pickCardElement) => {
        const $pickCard = $(pickCardElement);
        const dataTypeList = ($pickCard.data('type') || '').split(',');
        const isVisible = dataTypeList.some((type) => this.listDataFilter[type]);

        if (isVisible) {
          visibleCount++;
          $pickCard.css('display', 'flex');
        }
        if (!isVisible) {
          $pickCard.css('display', 'none');
        }
      });

      if (visibleCount) {
        $listing.css('display', 'flex');
      }
      if (!visibleCount) {
        $listing.css('display', 'none');
      }
    });
  }

  handleRenderComponentCompare() {
    let componentCompare = null;
    if (this.creditCardListingCompare.length) {
      componentCompare = this.creditCardListingCompare?.find('.compare-choosing');
    }

    if (!componentCompare) {
      return;
    }

    const lenDataImageCompare = this.listDataImageCompare.length;

    this.toggleCompareButtonState(lenDataImageCompare, componentCompare);

    this.toggleCompareComponentVisibility(lenDataImageCompare, componentCompare);

    const listItemCompare = $('.list-card__item');
    if (listItemCompare && lenDataImageCompare) {
      this.renderCompareContent();
    }
  }

  handleRenderCompareCard() {
    const listPickCard = $('.pick-card-container');
    if (!listPickCard) {
      return;
    }

    const isMaxCompare = this.cardIdsGlobal.length === 3;

    listPickCard.each((_, pickCardElement) => {
      const $pickCard = $(pickCardElement);
      const cardId = $pickCard.data('id')?.toString();
      const $buttonCompare = $pickCard.find('.button-item-compare');

      if (!$buttonCompare.length) {
        return;
      }

      $buttonCompare.html('');
      const isAdded = this.cardIdsGlobal.includes(cardId);

      if (isAdded) {
        $buttonCompare
          .addClass('button-item-compare-added')
          .removeClass('button-item-disabled')
          .append(this.contentBTNCompareAdded);
      } else {
        $buttonCompare.removeClass('button-item-compare-added').append(this.contentBTNCompare);

        if (isMaxCompare) {
          $buttonCompare.addClass('button-item-disabled');
        } else {
          $buttonCompare.removeClass('button-item-disabled');
        }
      }
    });
  }

  toggleCompareButtonState(lenDataImageCompare) {
    const compareButtonLink = $('.compare-button__link');

    if (compareButtonLink) {
      if (lenDataImageCompare > 1) {
        $(compareButtonLink).removeAttr('disabled');
        this.updateCompareButtonLink(compareButtonLink);
      } else {
        $(compareButtonLink).attr('disabled', '');
      }
    }
  }

  updateCompareButtonLink(compareButtonLink) {
    if (compareButtonLink) {
      const href = $(compareButtonLink).attr('href') || '';
      if (href) {
        const url = new URL(href, window.location.origin);
        url.search = '';
        this.cardIdsGlobal.forEach((id) => {
          url.searchParams.append('cardId', id);
        });
        $(compareButtonLink).attr('href', url.toString());
      }
    }
  }

  toggleCompareComponentVisibility = (lenDataImageCompare, componentCompare) => {
    $(componentCompare).toggleClass('hidden', lenDataImageCompare === 0);
  };

  renderCompareContent() {
    const listItemCompare = $('.list-card__item');
    if (!listItemCompare.length) {
      return;
    }
    listItemCompare.each((index, itemCompare) => {
      $(itemCompare).removeClass('has-card');
      const imgCard = $(itemCompare).find('.img-card-compare');
      if (imgCard) {
        $(imgCard).attr('src', '');
      }
      const btnRemove = $(itemCompare).find('.remove-button');
      $(btnRemove).attr('data-id', '').off('click');
    });

    this.listDataImageCompare.forEach((item, index) => {
      const { id, img } = item;
      const itemCompare = listItemCompare[index];

      $(itemCompare).addClass('has-card');
      $(itemCompare).find('.img-card-compare').attr('src', img);
      const btnRemove = $(itemCompare).find('.remove-button');
      $(btnRemove)
        .attr('data-id', id)
        .on('click', () => this.handleUpdateParamsUrl(id, '', true));
    });
  }

  handleUpdateParamsUrl(params, image, isRemove) {
    if (!params) {
      return;
    }

    if (!isRemove) {
      this.urlParams.append('cardId', params);
      this.listDataImageCompare.push({ id: params, img: image });
    } else {
      const newParams = new URLSearchParams();
      this.cardIdsGlobal.filter((id) => id !== params).forEach((id) => newParams.append('cardId', id));
      this.urlParams = newParams;
      this.listDataImageCompare = this.listDataImageCompare.filter((item) => item.id !== params);
    }

    const urlParamsString = this.urlParams.toString();
    let newUrl = window.location.pathname;
    if (urlParamsString) {
      newUrl = `${window.location.pathname}?${this.urlParams.toString()}`;
    }
    window.history.pushState({}, '', newUrl);

    this.urlParams = new URLSearchParams(location.search);
    this.cardIdsGlobal = this.urlParams.getAll('cardId');

    this.handleRenderCompareCard();
    this.handleRenderComponentCompare();
  }

  handleEventClickButtonCompareCard() {
    $('.button-item-compare').each((_, button) => {
      const $button = $(button);
      $button.off('click').on('click', () => {
        if ($button.hasClass('button-item-compare-added') || $button.hasClass('button-item-disabled')) {
          return;
        }

        const cardId = $button.data('id')?.toString();
        const cardImage = $button.data('image')?.toString();
        if (cardId && cardImage) {
          this.handleUpdateParamsUrl(cardId, cardImage, false);
        }
      });
    });
  }

  init() {
    this.handleGetLabelButtonSelectCompare();
    this.handleRenderDataFilter();
    this.initDataAndUpdateParamURLSearch(this.cardIdsGlobal);
    this.handleRenderCompareCard();
    this.handleEventClickButtonCompareCard();
  }
}
