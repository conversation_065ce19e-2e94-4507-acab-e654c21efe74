window.HeroBanner = class HeroBanner {
  wrapper;
  menu;
  nextButton;
  prevButton;

  constructor(banner) {
    this.wrapper = banner;
    if (this.wrapper) {
      this.initVariable();
      this.init();
    }
  }

  init() {
    if (!this.menu) {
      return;
    }

    this.pageResizeListener();
    this.pageScrollListener();
    this.buttonClickListener();
    this.setActiveTab();
  }

  initVariable() {
    this.menu = this.wrapper.querySelector('.page-menu');
    this.nextButton = this.wrapper.querySelector('.tcb-hero-banner_control--next');
    this.prevButton = this.wrapper.querySelector('.tcb-hero-banner_control--prev');
  }

  pageResizeListener() {
    window.addEventListener('resize', () => {
      this.menu.scrollLeft = 0;
      this.nextButton.style.display = this.menu.scrollWidth < window.innerWidth ? 'none' : 'block';
    });
  }

  pageScrollListener() {
    this.menu.addEventListener('scroll', () => {
      const atEnd = this.menu.scrollLeft >= this.menu.scrollWidth - this.menu.offsetWidth - 1;
      this.prevButton.style.display = this.menu.scrollLeft === 0 ? 'none' : 'block';
      this.nextButton.style.display = atEnd ? 'none' : 'block';
    });
  }

  buttonClickListener() {
    const screenWidth = window.innerWidth;
    const menuScrollWidth = this.menu.scrollWidth;

    if (menuScrollWidth < screenWidth) {
      this.nextButton.style.display = 'none';
    }

    this.nextButton?.addEventListener('click', () => {
      this.menu.scrollLeft = menuScrollWidth;
    });

    this.prevButton?.addEventListener('click', () => {
      this.menu.scrollLeft = 0;
    });
  }

  setActiveTab() {
    const tabs = this.menu.querySelectorAll('.page-menu_item');
    if (tabs.length === 0) {
      return;
    }

    const parentTab = this.menu.querySelector('.page-menu_item.page-menu_item--parent');
    const highlightTab = this.menu.querySelector('.page-menu_item.has-hightlight');
    const currentTab = this.menu.querySelector('.page-menu_item.page-menu_item--current');
    let activeTab;

    if (currentTab) {
      activeTab = currentTab;
    } else if (parentTab) {
      activeTab = parentTab;
    } else if (highlightTab) {
      activeTab = highlightTab;
    } else {
      activeTab = tabs[0];
    }

    activeTab.classList.add('page-menu_item--active');
  }
};

//Apply active class on hero banner tabs if active class is not there
$(document).ready(function () {
  const sliderElements = document.querySelectorAll('.tcb-hero-banner');
  sliderElements.forEach((item) => {
    try {
      new HeroBanner(item);
    } catch (error) {
      console.log(error);
    }
  });
});
