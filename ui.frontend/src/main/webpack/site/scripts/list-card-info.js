$(document).ready(function () {
  let listCardInfo = $('.list-card-info');
  let listCardInfoListItem = listCardInfo.find('.list-card-info__list-item');
  let numberOfCardsShownDefault = listCardInfoListItem.length;
  const defaultSlide = 4;
  const isCardSlider = $('.list-card-info-slider').data("card-slider");
  const displayVariation = isCardSlider ? $('.list-card-info-slider').data("display-variation").slice(-1) : defaultSlide;
  const slidesToShow = Number(displayVariation);
  // switch to carousel on smaller device  
  slickInitial();
  // show and hide in view more
  let viewAllFlag = false;
  showHideCardsInitials();

  $(window).on('resize', function () {
    slickInitial();
    showHideCardsInitials();
  });

  function slickInitial() {

    $('.list-card-info').each(function () {
      if ($(this).closest(".card-stackable").length <= 0) {
        const carouselSlicklist = $(this).find('.list-card-info__list-item');
        const ctaLink = carouselSlicklist.find('.list-card-info__item-action');
      if(ctaLink.length > 0 && !$(carouselSlicklist).hasClass('overlay-card-list-item')){
        carouselSlicklist.addClass('stack-view');
      }

      if(isCardSlider) {
        listCardInfo
        .find('.list-card-info__list-item-slider')
        .not('.slick-initialized')
        .slick({
          slidesToShow: slidesToShow || 4,
          slidesToScroll: 1,
          swipe: true,
          swipeToSlide: true,
          autoplay: false,
          accessibility: true,
          arrows: true,
          infinite: false,
          prevArrow: '<button type="button" class="slick-prev"></button>',
          nextArrow: '<button type="button" class="slick-next"></button>',
          responsive: [
            {
              breakpoint: 431,
              settings: {
                slidesToShow: 1,
              },
            },
            {
              breakpoint: 769,
              settings: {
                slidesToShow: 2,
              },
            },
            {
              breakpoint: 1025,
              settings: {
                slidesToShow: 3,
              },
            },
          ],
        });
      }

      if ($(window).width() <= 767 && !isCardSlider) {
        carouselSlicklist.not('.list-card-info__item-action').not('.slick-initialized').slick({
          slidesToShow: 1.08,
          slidesToScroll: 1,
          autoplay: false,
          autoplaySpeed: 2000,
          arrows: false,
          dots: true,
          infinite: false,
        });
        let viewMoreBtn = $('.list-card-info .list-card-info__view-more-btn');
        viewMoreBtn.css('display', 'none');
      } else {
        carouselSlicklist.filter('.slick-initialized').slick('unslick');
        let viewMoreBtn = $('.list-card-info .list-card-info__view-more-btn');
        viewMoreBtn.css('display', 'flex');
      }
    }
    }); 
  }
  function showHideCardsInitials() {
    listCardInfoListItem.each(function () {
      //view more view less functionalities
      if($(this).hasClass('view-more')) {
        //get cols/rows to show number
        let screenWidth = window.innerWidth;
        if(screenWidth > 991) {
          if($(this).hasClass('column-4')) {
            numberOfCardsShownDefault = 4;
          }
          if($(this).hasClass('column-3')) {
            numberOfCardsShownDefault = 3;
          }
          if($(this).hasClass('column-2')) {
            numberOfCardsShownDefault = 2;
          }
        }
        else if(screenWidth > 767) {
          numberOfCardsShownDefault = 4;
        }
        else {
          numberOfCardsShownDefault = 3;
        }
        //hide all rows, only show first rows
        if(viewAllFlag) {
          showHideCards($(this), numberOfCardsShownDefault, 'show');
        }
        else {
          showHideCards($(this), numberOfCardsShownDefault, 'hide');
        }
      }
    });
  }

  // view more view less on click
  listCardInfo.each(function () {
    let viewMoreBtnContainer = $(this).find('.list-card-info__view-more-btn');
    const listCardInfoListItem = $(this).find('.list-card-info__list-item');
    let viewMoreBtn = viewMoreBtnContainer.find('.cta-button');
    viewMoreBtn.click(function() {
      if(viewAllFlag) {
        viewMoreBtn.addClass('view-more');
        let viewMoreText = viewMoreBtnContainer.attr('viewMorelabel');
        $(this).find('.cmp-button__text').text(viewMoreText);
        showHideCards(listCardInfoListItem, numberOfCardsShownDefault, 'hide');
        viewAllFlag = false;
      }
      else {
        viewMoreBtn.removeClass('view-more');
        let viewLessText = viewMoreBtnContainer.attr('viewLesslabel');
        $(this).find('.cmp-button__text').text(viewLessText);
        showHideCards(listCardInfoListItem, numberOfCardsShownDefault, 'show');
        viewAllFlag = true;
      }
    })
  }); 

  function showHideCards(listCardInfoListItem, numberOfCardsShownDefault, action) {
    let listCardInfoItem = listCardInfoListItem.find('.list-card-info__item');
    listCardInfoListItem.find('[style*="display: none"]').css('display', 'flex');
    if(action == 'hide') {
      listCardInfoItem.each(function(index) {
        if(index >= numberOfCardsShownDefault) {
          $(this).css('display', 'none');
        }
      })
    }
  }
});
$(document).ready(function () {
  slickInitialOvelay();

  $(window).on('resize', function () {
    slickInitialOvelay();
  });

  function slickInitialOvelay() {
    $('.overlay-card-list').each(function () {
      const carouselSlicklist = $(this).find('.list-card-info__list-item');
      if ($(window).width() <= 767) {
        carouselSlicklist.not('.slick-initialized').slick({
          slidesToShow: 1,
          slidesToScroll: 1,
          autoplay: false,
          autoplaySpeed: 2000,
          arrows: true,
          prevArrow:
            '<div data-role="none" class="slick-arrow slick-prev"></div>',
          nextArrow:
            '<div data-role="none" class="slick-arrow slick-next"></div>',
          dots: false,
          infinite: false,
        });
        let viewMoreBtn = $('.list-card-info .list-card-info__view-more-btn');
        viewMoreBtn.css('display', 'none');
      } else {
        carouselSlicklist.filter('.slick-initialized').slick('unslick');
        let viewMoreBtn = $('.list-card-info .list-card-info__view-more-btn');
        viewMoreBtn.css('display', 'flex');
      }
    });
  }
});