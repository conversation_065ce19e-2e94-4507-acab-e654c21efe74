import { BaseComponent } from './base';
import { renderDayExpired } from './utils/day-expired.util';
import { renderImage } from './utils/default-image.util';
import { scrollText, handleClickPdf } from './exclusive-helper';
import {
  SELECTOR_DETAIL_LINK,
  LABEL_PDF_PREVIEW,
  LABEL_PDF_DOWNLOAD,
  LABEL_PDF_LOADING_MESSAGE,
} from './constants/offer-common';

export class TcbPromotionCard extends BaseComponent {
  selectorPdfLink = `tcb-promotion-card .card a.tcb-card__link[href]:not(${SELECTOR_DETAIL_LINK})`;

  constructor() {
    super();
    this.intervalId = null;
    this.renderInterval = 1000;
    this.previewPdf = {
      dataDownloadPdfLabel: this.dataset.previewDownloadLabel || LABEL_PDF_DOWNLOAD,
      previewTitle: this.dataset.defaultTitle || LABEL_PDF_PREVIEW,
      previewLoadingLabel: this.dataset.previewLoadingLabel || LABEL_PDF_LOADING_MESSAGE,
    };
    this.init();
  }

  init() {
    renderDayExpired();
    renderImage();
    scrollText();

    handleClickPdf(this.selectorPdfLink, this.previewPdf);

    this.intervalId = setInterval(() => {
      renderDayExpired();
    }, this.renderInterval);
  }

  destroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}
