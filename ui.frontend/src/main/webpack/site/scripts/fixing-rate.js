import moment from 'moment';
import CurrencyHelper from './currency-helper';
import { addDataLayerObject } from './analytics';

// Fixing rate
$(document).ready(function () {
  const fixingRate = $('.fixing-rate');
  const timeSelect = fixingRate.find('.time-select');
  const tableContainer = fixingRate.find('.table-content-container');
  const emptyLabel = fixingRate.find('.exchange-rate__empty-label');
  const tableElements = fixingRate.find('.exchange-rate-table-content');
  const selectedDateElements = fixingRate.find('.calendar__input-field');
  const calendarElements = fixingRate.find('.calendar_real_estate');

  let currentDate = moment().format('DD/MM/yyyy');
  let currentTime = null;

  function generateTable(element, items) {
    element.empty();
    items.forEach((item, idx, array) => {
      const lastRow = idx === array.length - 1;
      element.append(
        `
      <div class="exchange-rate__table-records">
        <div class="table__first-column first-column ${lastRow ? 'last-row' : ''}">
          <p>${item.sourceCurrency}/${item.targetCurrency}</p>
        </div>
        <div class="table-records__data">
          <div class="table-records__data-content ${lastRow ? 'last-row' : ''}">
            <p>${CurrencyHelper.numberWithCommas(item.vndLoan)}</p>
          </div>
          <div class="table-records__data-content ${lastRow ? 'last-row' : ''} last-column">
            <p>${item.inputTime ?? ''}</p>
          </div>
        </div>
      </div>
    `,
      );
    });
  }

  const trackingAA = () => {
    addDataLayerObject(
      'calculator',
      {
        clickInfo: {
          calculatorName: 'Fixing Rate',
          calculatorFields: `${currentDate || ''}${currentDate && currentTime ? '|' : ''}${currentTime || ''}`,
        },
      },
      { webInteractions: { name: 'Calculators', type: 'other' } },
    );
  };

  selectedDateElements.each((idx, element) => {
    $(element).text(currentDate);
    $(element).on('updateCal', function () {
      if (currentDate === $(element).text()) {
        return;
      }
      currentDate = $(element).text();
      currentTime = null;
      updateData(currentDate, currentTime);
      trackingAA();
    });
  });

  calendarElements.each(function () {
    $(this).on('click', function () {
      const calendarPopup = $(this).next('.calendar-popup');
      const calendarInputHeight = $(this).outerHeight();
      if (calendarPopup?.length === 0) {
        return;
      }
      calendarPopup.css({ top: `${calendarInputHeight}px` });
      if (calendarPopup?.hasClass('active')) {
        calendarPopup?.removeClass('active');
      } else {
        calendarPopup?.addClass('active');
      }
    });
  });

  timeSelect.on('timeChanged', function (event, newValue) {
    currentTime = newValue;
    updateData(currentDate, currentTime);
    trackingAA();
  });

  updateData(currentDate, null);

  function updateData(date, time) {
    selectedDateElements.each((idx, element) => {
      $(element).text(currentDate);
    });

    const baseUrl = fixingRate.attr('data-url');
    if (baseUrl === undefined) {
      return;
    }
    const dateUrl = date ? `.${date.split('/').reverse().join('-')}` : '';
    const timeUrl = date && time ? `.${time.replaceAll(':', '-')}` : '';
    const url = `${baseUrl}/_jcr_content.fixing-rates${dateUrl}${timeUrl}.integration.json`;

    $.ajax({
      url: url,
      type: 'GET',
      dataType: 'json',
      success: function (data) {
        const fixingRate = data?.fixingRate;
        // If not available
        if (fixingRate === null || fixingRate.data.length === 0) {
          emptyLabel.removeClass('hidden');
          tableContainer.addClass('hidden');
          timeSelect.changeOptions(fixingRate.updatedTimes, currentTime);
          return;
        }

        emptyLabel.addClass('hidden');
        tableContainer.removeClass('hidden');

        // generate table
        tableElements.each((idx, element) => {
          generateTable($(element), fixingRate.data);
        });
        timeSelect.changeOptions(fixingRate.updatedTimes, currentTime);
        if (currentTime === null && fixingRate.updatedTimes.length > 0) {
          currentTime = fixingRate.updatedTimes[0];
        }
      },
    });
  }
});
