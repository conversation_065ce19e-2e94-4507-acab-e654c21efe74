import moment from 'moment';
$(document).ready(function () {
    let creditRanking = $('.credit-ranking-component');

    creditRanking.each(function () {
        let selectOptions = $(this).find('.select-options');
        let showedOptions;
        let selectAllOptionText = selectOptions.find('.option.select-all').text();
        let creditList = $(this).find('.credits-list');
        let creditReportList = $(this).find('.credit');
        let menu = $(this).find('.menu');
        let menuItemList = $(this).find('.menu-item');
        let tabList = $(this).find('.tab');

        creditList.not(creditList.first()).css('display', 'none'); // hidden other list credits/other years
        showedOptions = sortFilterOptions($(this).find('.filter-options'));
        setNumberOfColumns(creditReportList);

        creditList.each(function () {
            let credits = $(this).find('.credit');

            credits.each(function () {
                let dateMenu = $(this).find('.menu-item');
                let tabItem = $(this).find('.tab');

                tabItem.not(tabItem.first()).addClass('display-none');

                // click tabs
                dateMenu.click(function () {
                    selectTab(dateMenu, $(this), tabItem, credits);
                });

                // Add year attribute to menu and tab items
                let formatDate = 'DD/MM/YYYY';
                if(document.documentElement.lang === 'en') {
                    formatDate = 'DD MMM yyyy';
                }
                dateMenu.each(function () {
                    //process date time and add year attribute to item
                    let tabLabel = $(this).attr('tabLabel');
                    if (tabLabel !== null) {
                        let tabYear = tabLabel.split('-')[0];
                        $(this).attr('tabYear', tabYear);
                    }
                    let date = new Date(tabLabel);
                    let localizeDateString = moment(date, "DD/MM/YYYY").format(formatDate);
                    $(this).text(localizeDateString);
                    setDataTrackingValue($(this), localizeDateString);
                });
                tabItem.each(function () {
                    //process date time and add year attribute to item
                    let tabLabel = $(this).attr('tabLabel');
                    if (tabLabel !== null) {
                        let tabYear = tabLabel.split('-')[0];
                        $(this).attr('tabYear', tabYear);
                    }
                });
            });
        });

        // automatically select 1st filter with data, if there are no year with data, choose select all filter
        showedOptions.each(function() {
            if($(this).attr('value') !== selectAllOptionText) {
                if(menu.find('.menu-item[tabyear="' + $(this).attr('value') +'"]').length > 0) {
                    selectFilterOption($(this));
                    selectOptions.find('.select-current-option').text($(this).attr('value'));
                    return false;
                }
            }
        })

        // select an option in the filter to filter reports
        showedOptions.click(function () {
            selectFilterOption($(this));
        });

        $(window).resize(function () {
            if ($(window).width() < 992) {
                creditList.find('.tab .content').css('height', 'unset');
            }
        });

        function selectFilterOption(selectedFilter) {
            //filter tabs by the year user selected
            if (selectedFilter.attr('value') == selectAllOptionText) {
                //set eventual select title to select all text
                setTimeout(() => {
                    selectOptions.find('.select-current-option').text(selectAllOptionText);
                }, 100); 
                menuItemList.each(function () {
                    $(this).removeClass('filtered');
                })
                tabList.each(function () {
                    $(this).removeClass('filtered');
                })
            } else {
                let filterYear = selectedFilter.attr('value');
                menuItemList.each(function () {
                    if ($(this).attr('tabyear') === filterYear) {
                        $(this).removeClass('filtered');
                    } else {
                        $(this).addClass('filtered');
                    }
                })
                tabList.each(function () {
                    if ($(this).attr('tabyear') === filterYear) {
                        $(this).removeClass('filtered');
                    } else {
                        $(this).addClass('filtered');
                    }
                })
            }

            creditReportList.each(function () {
                // hide report panels with all tabs filtered
                if ($(this).find('.menu-item:not(.filtered)').length == 0) {
                    $(this).addClass('filtered');
                } else {
                    $(this).removeClass('filtered');
                    //after filtering, auto select 1st tab not filtered
                    let menuItems = $(this).find('.menu-item');
                    let menuItemSelected = $(this).find('.menu-item:not(.filtered):first');
                    let tabItems = $(this).find('.tab');
                    if (menuItemSelected.length > 0) {
                        selectTab(menuItems, menuItemSelected, tabItems, creditReportList);
                    }
                }
            })
            // re-set layout (2 columns/3 columns) after some report has been filtered out
            let filteredCreditReportList = creditReportList.not('.filtered');
            setNumberOfColumns(filteredCreditReportList);
        }

        //highlight an item when user click
        function selectTab(dateMenu, dateMenuSelected, tabItem, credits) {
            dateMenu.css('color', '#a2a2a2');
            dateMenu.css('font-weight', 'unset');
            dateMenu.css('border-bottom', 'unset');

            dateMenuSelected.css('border-bottom', '4px solid rgb(237, 27, 36)');
            dateMenuSelected.css('color', '#000000');
            dateMenuSelected.css('font-weight', '600');

            tabItem.addClass('display-none');
            // Because there is a previous arrow, must subtract 1 to tab index
            $(tabItem[dateMenuSelected.index()]).removeClass('display-none');
        }

        //sort options in filter by year
        function sortFilterOptions($wrapper) {
            $wrapper.find('.option[value!="' + selectAllOptionText + '"]').sort(function (a, b) {
                return +b.value - +a.value;
            }).appendTo($wrapper);
            return selectOptions.find('.option');
        }

        //switching between 2 columns and 3 columns layouts
        function setNumberOfColumns(creditReportList) {
            if(creditReportList.length > 2) {
                creditList.removeClass('grid-2-col');
                creditList.addClass('grid-3-col');
            }
            else {
                creditList.removeClass('grid-3-col');
                creditList.addClass('grid-2-col');
            }
        }

        //scrolling menu
        creditReportList.each(function () {
            let mouseDown = false;
            let startX, scrollLeft;
            let slider = $(this).find('.menu')[0];

            let startDragging = (e) => {
                mouseDown = true;
                startX = e.pageX - slider.offsetLeft;
                scrollLeft = slider.scrollLeft;
            }

            let stopDragging = (e) => {
                mouseDown = false;
            }

            let move = (e) => {
                e.preventDefault();
                if (!mouseDown) { return; }
                let x = e.pageX - slider.offsetLeft;
                let scroll = x - startX;
                slider.scrollLeft = scrollLeft - scroll;
            }

            // Add the event listeners
            slider.addEventListener('mousemove', move, false);
            slider.addEventListener('mousedown', startDragging, false);
            slider.addEventListener('mouseup', stopDragging, false);
            slider.addEventListener('mouseleave', stopDragging, false);
        })

        //AA intergration
        function setDataTrackingValue(cta, ctaName) {
            cta.attr("data-tracking-click-event", "linkClick");
            cta.attr("data-tracking-click-info-value", "{'linkClick' : '" + ctaName + "'}");
            cta.attr(
                "data-tracking-web-interaction-value", "{'webInteractions': {'name': 'Credit Rating','type': 'other'}}"
            );
        }
    });
});