$(document).ready(function () {
  if ($('.cmp-custom-embed').length === 0) {
    return;
  }

  onInit();

  /**
   * FUNCS
   */

  function onInit() {
    // This code loads the IFrame Player API code asynchronously.
    const youtubeIframeApiScript = document.createElement('script');
    youtubeIframeApiScript.src = 'https://www.youtube.com/iframe_api';
    youtubeIframeApiScript.addEventListener('load', () => {
      window.YT.ready(function () {
        onYouTubeIframeAPIReady();
      });
    });

    document.head.append(youtubeIframeApiScript);
  }

  function youtubeParser(url) {
    const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[7].length == 11 ? match[7] : false;
  }

  function matchYoutubeUrl(url) {
    const p =
      /^(?:https?:\/\/)?(?:m\.|www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/;
    return url.match(p);
  }

  function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (err) {
      return false;
    }
  }

  // This function creates an <iframe> (and YouTube player) after the API code downloads.
  function onYouTubeIframeAPIReady() {
    const slides = $('.cmp-custom-embed');
    for (let i = 0; i < slides.length; i++) {
      let parentClass = $('.cmp-custom-embed')[i];
      let videoUrl = $(parentClass).find('#embed_url').val();
      let videoType = $(parentClass).find('#embed_type').val();
      let iframeUrl = $(parentClass).find('iframe').attr('src');
      let id = $(parentClass).find('#embed_id').val();

      const { height, width } = getVideoSize(parentClass);

      if (videoType == 'EMBEDDABLE') {
        initYtPlayer(id, $(parentClass).find('#embed_video_id').val(), height, width);
      } else if (videoType == 'URL') {
        if (matchYoutubeUrl(videoUrl) === true) {
          initYtPlayer(id, youtubeParser(videoUrl));
        }
      } else if (isValidUrl(iframeUrl) === true && videoType == 'HTML') {
        if (matchYoutubeUrl(iframeUrl) === true) {
          initYtPlayer(id, youtubeParser(iframeUrl));
        }
      }
    }
  }

  function getVideoSize(parentClass) {
    const layout = $(parentClass).find('.embed_video_layout').val();

    let height;
    let width;

    switch (layout) {
      case 'fixed':
        height = $(parentClass).find('#embed_video_height').val();
        width = $(parentClass).find('#embed_video_width').val();
        break;
      case 'responsive':
        const aspectRatio = $(parentClass).find('#embed_video_aspect_ratio').val();
        height = (width * aspectRatio) / 100;
        width = $('.embed').width();
        break;
      default:
        break;
    }

    return { height, width };
  }

  function initYtPlayer(id, videoId, height, width) {
    new YT.Player(id, {
      videoId,
      height,
      width,
      playerVars: {
        playsinline: 1,
      },
      events: {
        onStateChange: onPlayerStateChange,
      },
    });
  }

  // The API calls this function when the player's state changes.
  // The function indicates that when playing a video (state=1)
  function onPlayerStateChange(event) {
    const startStatuses = [YT.PlayerState.PLAYING];
    const stopStatuses = [YT.PlayerState.ENDED];

    if (startStatuses.includes(event.data)) {
      const videoTitle = event.target.getVideoData().title.replaceAll("'", '');

      const tcbTrackerVideoStart = $('#tcb-tracker-video-start');
      tcbTrackerVideoStart.attr('video-name', videoTitle);
      tcbTrackerVideoStart.attr('value', 'btn_start');
      tcbTrackerVideoStart.trigger('click');
    } else if (stopStatuses.includes(event.data)) {
      const videoTitle = event.target.getVideoData().title.replaceAll("'", '');

      const tcbTrackerVideoComplete = $('#tcb-tracker-video-complete');
      tcbTrackerVideoComplete.attr('video-name', videoTitle);
      tcbTrackerVideoComplete.attr('value', 'btn_complete');
      tcbTrackerVideoComplete.trigger('click');
    }
  }
});
