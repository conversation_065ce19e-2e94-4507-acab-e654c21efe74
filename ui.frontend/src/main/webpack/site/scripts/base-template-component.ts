import { BaseComponent } from './base';

export class BaseTemplateComponent extends BaseComponent {
  constructor(templateId: string) {
    super();

    const shadowRoot = this.attachShadow({ mode: 'open' });
    const styleSheets = Array.from(document.styleSheets)
      .map((styleSheet) => {
        try {
          return Array.from(styleSheet.cssRules)
            .map((rule) => rule.cssText)
            .join('');
        } catch (e) {
          return '* { margin: 0 }';
        }
      })
      .join('\n');

    const styleElement = document.createElement('style');
    styleElement.textContent = styleSheets;
    shadowRoot.appendChild(styleElement);

    const template = document.getElementById(templateId) as HTMLTemplateElement;
    if (template) {
      const templateContent = template.content.cloneNode(true);
      shadowRoot.appendChild(templateContent);
    } else {
      console.error(`Template ${templateId} not found`);
    }
  }
}
