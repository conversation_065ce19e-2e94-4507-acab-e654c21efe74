$(document).ready(function () {
    $('.tcb-teaser').each(function () {
        let container = $(this).closest(".tcb-sectionContainer");
        if (container) {
            let margin = 0;
            let marginBGSmall = container.find(".tcb-smallImage").length > 0 ? container.find(".tcb-smallImage").css("padding-bottom") : 0;
            let marginBG = container.find(".tcb-bgImage").length > 0 ? container.find(".tcb-bgImage").css("padding-bottom") : 0;
            let marginColor = container.find(".tcb-bgColor").length > 0 ? container.find(".tcb-bgColor").css("padding-bottom") : 0;
            if (marginBGSmall + marginBG + marginColor == 0) {
                margin = parseInt(container.find(".tcb-content-container").length > 0 ? container.find(".tcb-content-container").css("padding-bottom") : 0);
            } else {
                margin = parseInt(marginBGSmall) + parseInt(marginBG) + parseInt(marginColor);
            }
            if (margin > 48) {
                margin = 48;
            }
            $(this).find(".app-preview-image").css("margin-top", margin);
            $(this).find(".app-preview-image").css("margin-bottom", 0 - margin);
        }
    });
});