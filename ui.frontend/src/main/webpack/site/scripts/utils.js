/**
 * Get plain string
 * @param {string} str
 * @returns {string}
 */
export const formatString = (str) => {
  if (str) {
    str = str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    str = str
      .replace(/(\r\n|\n|\r)/gm, '')
      .replace(/ĩ/g, 'i')
      .replace(/ø/g, 'o')
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
    str = str.trim();
  }

  return str;
};

/**
 * Get jQ elem plain text
 * @param {JQueryElement} elem
 * @returns {string}
 */
export const getElementText = (elem) => {
  if (!elem) {
    return '';
  }
  return formatString(elem.text()).trim();
};

/**
 * Parse string to JSON
 * @param {string} value
 * @returns {JSON}
 */
export const parseStringToJson = (value) => {
  if (value && typeof value === 'string') {
    return JSON.parse(value.replaceAll("'", '"'));
  }
};

export const reloadYTSession = (iframe) => {
  if (iframe) {
    const src = iframe.getAttribute('src');
    iframe.classList.add('hidden');
    iframe.setAttribute('src', '');
    iframe.setAttribute('src', src);
    setTimeout(() => {
      iframe.classList.remove('hidden');
    }, 800);
  }
};
