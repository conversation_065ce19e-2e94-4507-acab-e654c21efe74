import moment from 'moment';
import { orderBy } from 'lodash';
import { LocationUtil } from './utils/location.util';

let dateFormated = 'DD/MM/YYYY';
const year = "year";
const quarter = "quarter";
const defaultSelectedQuarter = ['Q1', 'Q2', 'Q3', 'Q4'];

function sortDocuments(tabItem) {
  const documents = tabItem.find('.row').toArray();
  const sortedDocs = orderBy(
    documents,
    (o) => {
      return moment($(o).find('.date span').text(), dateFormated).format('YYYYMMDD');
    },
    ['desc'],
  );
  tabItem.empty();
  tabItem.append(sortedDocs);
}

function getYearRange(listViewElement) {
  const yearCheckboxs = listViewElement.find('.checkbox-list .year ul');
  const dates = listViewElement
    .find('.row .date span')
    .map(function () {
      return $(this).text();
    })
    .get();
  const years = dates.map((e) => moment(e, dateFormated).get('year')).filter((e) => !isNaN(e));
  const minYear = Math.min(...years);
  const maxYear = Math.max(...years);
  const arr = Array.from({ length: maxYear - minYear + 1 }, (_, i) => maxYear - i);
  yearCheckboxs.empty();
  arr.forEach((e) => {
    yearCheckboxs.append(`
    <li>
      <label>
        <input type="checkbox" value="${e}" />
        <div>${e}</div>
      </label>
    </li>
    `);
  });
}

function regisSearchBoxes($searchBox, $searchBtn) {
  $searchBox.keypress(function (e) {
    let key = e.which;
    if (key == 13) {
      // the enter key code
      // trigger show/hide LVD Items by Filter
      showHideByFilter($searchBox.val());
    }
  });
  $searchBtn.on('click', function () {
    showHideByFilter($searchBox.val());
  });
}

function showHideByFilter(filterTxt) {
  // search across all LVDs on the page
  let $lvds = $('.list-view-documents');
  $lvds.each(function () {
    let $lvd = $(this);
    let $lvItems = $lvd.find('.list-items .row');
    $lvItems.each(function () {
      let $lvItem = $(this);
      let itemContent = $lvItem.find('.content').length > 0 ? $lvItem.find('.content')[0].textContent.toLowerCase() : '';
      if (!filterTxt || (filterTxt && itemContent && itemContent.indexOf(filterTxt.toLowerCase()) > -1)) {
        $lvItem.removeClass('filter-hide');
      } else {
        $lvItem.addClass('filter-hide');
      }
    });
  });
}

function checkValidQuarter(objParam) {
  return !(!objParam[quarter] ||
    !Number(objParam[quarter]) ||
    !defaultSelectedQuarter.includes(`Q${objParam[quarter]}`) ||
    (objParam[quarter] && !objParam[year]));
}

function checkValidYear(objParam, availableYears) {
  return !(!objParam[year] ||
    !Number(objParam[year]) ||
    !availableYears.includes(objParam[year]));
}

function queryParameters(element) {
  const availableYears = element
    .find('.year input')
    .map(function () {
      return $(this).val();
    }).get();
  const currentUrlParamObj = LocationUtil.getUrlParamObj();
  const isValidQuarter = checkValidQuarter(currentUrlParamObj);
  const isValidYear = checkValidYear(currentUrlParamObj, availableYears);

  if (currentUrlParamObj[quarter] && currentUrlParamObj[year] && (!isValidQuarter || !isValidYear)) {
    currentUrlParamObj[quarter] = defaultSelectedQuarter;
    currentUrlParamObj[year] = availableYears.slice(0, 2);
  }

  if (!isValidQuarter) {
    currentUrlParamObj[quarter] = defaultSelectedQuarter;
  }

  if (!isValidYear) {
    currentUrlParamObj[year] = availableYears.slice(0, 2);
  }

  return currentUrlParamObj;
}

function initSelectedForFilter(element, selected, isQuarter) {
  const initData = queryParameters(element)[selected];
  let result = [];
  if (Array.isArray(initData)) {
    result = initData;
  } else {
    isQuarter ? result.push(`Q${initData}`) : result.push(initData);
  }
  return result;
}

function setupFilter(element, placeHolderText) {
  let filterSelect = element.find('.select-checkbox-filter');
  let selectBtn = element.find('.select');
  const selectedYearLabel = element.find('.select__selected-year');
  let placeHolder = element.find('.select-year-filter .select span');
  // desktop options
  let showedCheckbox = element.find('.checkbox-list');
  let options = filterSelect.find('.checkbox .type input');

  // mobile options
  let popupContent = filterSelect.find('.popup-content');
  let mobileOptions = popupContent.find('.type input');
  let mobileFilterContent = popupContent.find('.content');
  let applyButton = popupContent.find('.btn button');
  // variables
  let selectedQuaters = initSelectedForFilter(showedCheckbox, quarter, true);
  let selectedYears = initSelectedForFilter(showedCheckbox, year, false);
  let tmpYears = selectedYears;
  let tmpQuaters = selectedQuaters;
  selectedYearLabel.text(selectedYears);

  const onOptionsChanged = (_options, oldYears) => {
    let yearOptions = _options.filter(function () {
      return $(this).parents('.year').length > 0;
    });
    let quarterOptions = _options.filter(function () {
      return $(this).parents('.quarter').length > 0;
    });

    if (oldYears.length == 0) {
      quarterOptions.prop('checked', true);
      quarterOptions.prop('disabled', false);
    }
    let newSelectedYears = yearOptions
      .filter(':checkbox:checked')
      .map(function () {
        return $(this).val();
      })
      .get();
    if (newSelectedYears.length == 0) {
      quarterOptions.prop('checked', false);
      quarterOptions.prop('disabled', true);
    }
    let newSelectedQuarters = quarterOptions
      .filter(':checkbox:checked')
      .map(function () {
        return $(this).val();
      })
      .get();
    return [newSelectedYears, newSelectedQuarters];
  };

  const cloneToMobile = () => {
    mobileFilterContent.empty();
    showedCheckbox.clone().appendTo(mobileFilterContent);
    mobileOptions = $(popupContent[0]).find('.type input');
  };

  const updateFilters = () => {
    let allOptions = options.add(mobileOptions);
    allOptions
      .filter(function () {
        return $(this).parents('.year').length > 0;
      })
      .each(function () {
        $(this).prop('checked', false);
        if (selectedYears.includes($(this).val())) {
          $(this).prop('checked', true);
        }
      });
    allOptions
      .filter(function () {
        return $(this).parents('.quarter').length > 0;
      })
      .each(function () {
        $(this).prop('checked', false);
        if (selectedQuaters.includes($(this).val())) {
          $(this).prop('checked', true);
        }
      });
    if (selectedYears.length == 0) {
      placeHolder.text(placeHolderText);
    } else {
      placeHolder.text(selectedYears.join(','));
    }
    let selectedCategoryLabel = element.find('.select-category-filter .select span');
    let selectedCategory = element.find('.popup-content .checkbox-list .category input:checked');
    let selectCategoryText = [];
    selectedCategory.each(function () {
      selectCategoryText.push($(this).val());
    });
    if(selectCategoryText.length > 0) {
      selectedCategoryLabel.text(selectCategoryText.join(','));

      let listViewDocument = selectedCategoryLabel.closest('.list-view-documents');
      showHideByFilter(selectCategoryText, listViewDocument);
    }
  };

  function showHideByFilter(filterTxt, listViewDocument) {
    let $lvItems = listViewDocument.find('.list-items .row');
    let viewMore = $(listViewDocument).find('.view-more');
    if (filterTxt.length == 0) {
      $lvItems.addClass('filter-hide');
      viewMore.css('display', 'none');
      return;
    }
    $lvItems.each(function () {
      let $lvItem = $(this);
      if (filterTxt.length > 0) {
        let itemContent = $lvItem.find('.content-category span').html();
        let isContain = filterTxt.some(function (keyword) {
          return !keyword || (keyword && itemContent && itemContent.indexOf(keyword) > -1);
        });
        if (isContain) {
          $lvItem.removeClass('filter-hide');
        } else {
          $lvItem.addClass('filter-hide');
        }
      } else {
        $lvItem.removeClass('filter-hide');
      }
    });
  }

  updateFilters();
  cloneToMobile();
  // Desktop change options
  options.on('click', function () {
    let [years, quarters] = onOptionsChanged(options, selectedYears);
    selectedYears = years;
    selectedYearLabel.text(selectedYears.join(', '));
    selectedQuaters = quarters;
    element.trigger('updateFilter', [selectedYears, selectedQuaters]);
    if (selectedYears.length == 0) {
      placeHolder.text(placeHolderText);
    } else {
      placeHolder.text(selectedYears.join(','));
    }
  });

  // Mobile filter clicked
  selectBtn.on('click', function (e) {
    e.stopPropagation();

    if ($(window).width() > 767) {
      if ($(this).hasClass("showed")) {
        $(this).toggleClass('showed');
      } else {
        selectBtn.removeClass("showed");
        $(this).toggleClass('showed');
      }
    } else {
      $(this).parent().toggleClass('mobile');
      $('body').addClass('overflow-hidden');

      updateFilters();
      tmpYears = selectedYears;
      tmpQuaters = selectedQuaters;
    }
  });

  $(document).on('click', function (event) {
    if (!$(event.target).is(showedCheckbox) && !$(event.target).parents().is(showedCheckbox)) {
      selectBtn.removeClass('showed');
    }

    if (!$(event.target).is(popupContent) && !$(event.target).parents().is(popupContent)) {
      selectBtn.parent().removeClass('mobile');
      $('body').removeClass('overflow-hidden');
    }
  });

  popupContent.on('click', (event) => {
    event.stopPropagation();
  });

  mobileOptions.on('click', function () {
    let [years, quarters] = onOptionsChanged(mobileOptions, tmpYears);
    tmpYears = years;
    tmpQuaters = quarters;
  });

  applyButton.on('click', function () {
    selectBtn.parent().removeClass('mobile');
    $('body').removeClass('overflow-hidden');
    selectedYears = tmpYears;
    selectedQuaters = tmpQuaters;
    selectedYearLabel.text(selectedYears.join(', '));
    updateFilters();
    element.trigger('updateFilter', [selectedYears, selectedQuaters]);
  });

  element.trigger('updateFilter', [selectedYears, selectedQuaters]);
}

$(document).ready(function () {
  if (document.documentElement.lang === 'en') {
    dateFormated = 'DD MMM yyyy';
    let dateSpan = $('.row .date span');
    dateSpan.each(function () {
      const englishDate = moment($(this).html(), 'DD/MM/yyyy').format(dateFormated);
      $(this).html(englishDate);
    });
  }
  let serviceFee = $('.list-view-documents');

  let mastheadBtn = $('.mastheadsimplebanner .looking-for_input-options-content a');

  serviceFee.each(function () {
    let menuService = $(this).find('.menu');
    let tabServiceFeeItem = $(this).find('.list-items .tab');
    let viewMore = $(this).find('.view-more');
    let viewMoreBtn = viewMore.find('.cta-button');
    const viewMoreNumber = Number($(this).attr('data-viewmore-number')) ?? 10;
    const filterPlaceholder = $(this).attr('data-filter-placeholder') ?? 'Select';
    let tabServiceState = 0;
    let selectedYears = null;
    let selectedQuaters = null;

    getYearRange($(this));
    tabServiceFeeItem.each(function () {
      sortDocuments($(this));
    });

    window.addEventListener('load', function () {
      let paramID = window.location.href.split('#')[1];
      if (paramID) {
        let element = document.getElementById(paramID);
        if (element) {
          let listView = $(element).closest('.listviewdocumentv2');
          if (listView.length > 0) {
            element.click();
            window.scrollTo({ top: listView.offset().top - 60, behavior: 'smooth' });
          }
        }
      }
    });

    mastheadBtn.on('click', function (e) {
      let href = $(this).attr('href');
      let currentURL = window.location.href;
      let id = $(this).attr('href').split('#')[1];
      if (currentURL.includes(href.split('?')[0]) && href.includes('?category=#')) {
        e.preventDefault();
        window.history.pushState({}, '', currentURL.split('?')[0] + '?category=#' + id);
        let listView = $('#' + id).closest('.listviewdocumentv2');
        if (listView.length > 0) {
          $('#' + id).click();
          window.scrollTo({ top: listView.offset().top - 60, behavior: 'smooth', });
        }
      }
    });

    // click tab title
    menuService.on('click', '.tcb-tabs_item', function () {
      tabServiceState = $(this).data('tabindex');

      tabServiceFeeItem.css('display', 'none');
      $(tabServiceFeeItem[tabServiceState]).css('display', 'block');
      if ($(tabServiceFeeItem[tabServiceState]).find('.row[data-hidden="false"]:hidden').length > 0) {
        viewMore.css('display', 'flex');
      } else {
        viewMore.css('display', 'none');
      }
    });

    setTimeout(() => {
      menuService.find('.tcb-tabs_item').addClass('md-ripples');
    });

    // add even listener

    let downloadBtn = $(this).find('.file-download .btn .link');
    let serviceFeePopup = $(this).find('.popup-download');

    downloadBtn.click(function (e) {
      $(this).siblings('input[type="hidden"]').click();
      e.preventDefault();
      let url = $(this).data('file-link');
      let type = $(this).data('file-type');
      const previewLoadingLabel = serviceFeePopup?.attr("data-preview-loading-label");
      const previewDownloadLabel = serviceFeePopup?.attr("data-preview-download-label");
      switch (type) {
        case 'pdf':
          e.preventDefault();
          serviceFeePopup.trigger("show-preview-modal", [
            {
              url: url,
              documentType: "pdf",
              loadingLabel: previewLoadingLabel,
              downloadLabel: previewDownloadLabel
            },
          ]);
          break;
        case 'seeMore':
          url = $(this).prop('href');
          const openNewTab = $(this).data('open-new-tab');
          if(!openNewTab) {
            window.open(url, '_self');
          } else {
            window.open(url, '_blank');
          }
          break;
        case 'youtube':
          let title = $(this).parents('.row').find('.content p').text();
          e.preventDefault();
          serviceFeePopup.trigger("show-preview-modal", [
            {
              url: url,
              documentType: "media",
              title: title,
              loadingLabel: previewLoadingLabel,
              downloadLabel: previewDownloadLabel
            },
          ]);
          break;
        default:
          window.open(url, '_blank');
          break;
      }
    });

    let showedItem = [];
    let step = [];

    $(this).on('updateFilter', function (event, years, quarters) {
      selectedQuaters = quarters;
      selectedYears = years;
      initialSetup();
    });

    if ($(this).find('.select-options').length > 0) {
      setupFilter($(this), filterPlaceholder);
    } else {
      initialSetup();
    }

    // initial set up
    function initialSetup() {
      tabServiceFeeItem.each(function () {
        showedItem.push(viewMoreNumber);
        step.push(2 * viewMoreNumber - 1);
      });
      if (selectedYears && selectedQuaters) {
        tabServiceFeeItem.find('.row').each(function () {
          let date = moment($(this).find('.date span').text(), dateFormated);
          if (selectedYears.includes(`${date.year()}`) && selectedQuaters.includes(`Q${date.quarter()}`)) {
            $(this).attr('data-hidden', false);
          } else {
            $(this).attr('data-hidden', true);
          }
        });
      } else {
        tabServiceFeeItem.find('.row').attr('data-hidden', false);
      }

      tabServiceFeeItem.each(function () {
        $(this).find('.row[data-hidden="true"]').css('display', 'none');
        $(this)
          .find('.row[data-hidden="false"]')
          .each(function (i, e) {
            if (i < viewMoreNumber) {
              $(e).css('display', 'flex');
            } else {
              $(e).css('display', 'none');
            }
          });

        if ($(tabServiceFeeItem[tabServiceState]).find('.row[data-hidden="false"]:hidden').length > 0) {
          viewMore.css('display', 'flex');
        } else {
          viewMore.css('display', 'none');
        }
      });
    }

    // view more btn
    viewMoreBtn.click(function () {
      let rowItem = $(tabServiceFeeItem[tabServiceState]).find('.row[data-hidden="false"]');

      // Get the showedItem value of the current tab
      let tabShowedItem = 0;
      if (tabServiceState < showedItem.length) {
        tabShowedItem = showedItem[tabServiceState];
      }

      // Get the step value of the current tab
      let tabStep = 0;
      if (tabServiceState < step.length) {
        tabStep = step[tabServiceState];
      }

      if (tabStep >= rowItem.length) {
        for (tabShowedItem; tabShowedItem <= rowItem.length; tabShowedItem++) {
          $(rowItem[tabShowedItem]).css('display', 'flex');
        }

        viewMore.css('display', 'none');
      } else {
        for (tabShowedItem; tabShowedItem <= tabStep; tabShowedItem++) {
          $(rowItem[tabShowedItem]).css('display', 'flex');
        }
      }

      step[tabServiceState] = tabShowedItem + viewMoreNumber - 1;
    });
  });

  // register for search box
  regisSearchBoxes(
    $('input[trigger-search-to="list-view-document"]'),
    $('button[trigger-search-to="list-view-document"]'),
  );
});
