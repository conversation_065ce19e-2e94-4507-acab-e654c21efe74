import moment from 'moment';
jQuery(function () {
  $('.list-view-documents').each(function () {
    const rootEndpoint = '/graphql/execute.json/techcombank/viewDocumentList';
    const listViewDocument = $(this);
    const listItem = listViewDocument.find('.list-items .tab');
    const elementCategoryFilter = listViewDocument.find('.checkbox-list .category');
    const selectCategoryFilter = listViewDocument.find('.select-category-filter');
    const placeholderCategory = selectCategoryFilter.find('span');
    const filterCategoryPlaceholder = $(this).attr('data-category-filter-placeholder') ?? 'Select';
    let viewMore = $(this).find('.view-more');

    listItem.each(function () {
      let cfPath = $(this).data('cf-root-path');
      if (cfPath) {
        // Appending '/' at the end for GraphQL Query Optimisation
        if (!cfPath.endsWith("/")) {
          cfPath += "/";
        }
        let endpoint = rootEndpoint + '%3BcfPath%3D' + cfPath;
        executeQuery(endpoint, $(this));
      }
    });
    renderCategoryFilter();

    function executeQuery(url, tab) {
      $.get({
        url: url,
        success: function (data) {
          renderListView(data, tab);
        },
        async: false,
      });
    }

    function renderListView(data, tab) {
      const dataViewDocuments = data.data.listViewDocumentFragmentList.items;

      const rowTemplate = tab.find('.row.row-template');
      let hasCategory = false;

      if (dataViewDocuments.length > 0) {
        dataViewDocuments.forEach((dataItem) => {
          if (dataItem.category != null && dataItem.category != '') {
            hasCategory = true;
          }
        });
        if (!hasCategory) {
          rowTemplate.find('.content-category').remove();
        }
        dataViewDocuments.forEach((dataItem) => {
          let rowElement = rowTemplate.clone();
          rowElement.removeClass('row-template');

          rowElement.find('.date span').text(formatDate(dataItem.date));
          rowElement.find('.content-category .text-base').text(dataItem.category);
          rowElement.find('.content .text-base').text(dataItem.categoryTitle.plaintext);
          const fileDownload = rowElement.find('.file-download');

          if (dataItem.documentItems.length == 0) {
            const noneDocumentItemsElement = fileDownload.find('.have-file-name.file-item-template');
            const cloneItem = noneDocumentItemsElement.clone();
            cloneItem.removeClass('file-item-template');
            fileDownload.append(renderDocumentItems(dataItem, cloneItem, false));
          } else {
            const documentItemElement = fileDownload.find('.have-file-name.file-item-template');
            dataItem.documentItems.forEach((docItem) => {
              const cloneItem = documentItemElement.clone();
              cloneItem.removeClass('file-item-template');
              const itemDoc = renderDocumentItems(docItem, cloneItem, true);
              fileDownload.append(itemDoc);
            });
          }
          tab.prepend(rowElement);
          tab.find(rowTemplate).remove();
        });
      }
    }

    function renderDocumentItems(dataItem, noneDocumentItemsElement, isItem) {
      noneDocumentItemsElement.addClass('show-document');
      noneDocumentItemsElement.removeClass('hide-document');
      if (isItem) {
        noneDocumentItemsElement.find('.file-name').text(dataItem.documentTitle.plaintext);
      }
      if (dataItem.typeOfOutput != 'seeMore') {
        const typeNoneSeemore = noneDocumentItemsElement.find('.type-output-none-seemore');
        typeNoneSeemore.removeClass('hide-document');
        typeNoneSeemore.addClass('show-document');

        typeNoneSeemore.find('a.link').attr('data-file-type', dataItem.typeOfOutput);
        if (dataItem.linkIcon?._path) {
          renderPictureSource(dataItem.linkIcon._path, typeNoneSeemore);
          typeNoneSeemore.find('a img').prop('src', dataItem.linkIcon._path);
        }
        if (dataItem.linkLabel) {
          typeNoneSeemore.find('a span').text(dataItem.linkLabel);
          typeNoneSeemore.find('tcb-tracker').attr('value', dataItem.linkLabel);
        }
        if (dataItem.documentPath?._path) {
          typeNoneSeemore.find('tcb-tracker').attr('download', dataItem.documentPath._path.split('/')?.pop());
          typeNoneSeemore.find('a.link').attr('data-file-link', dataItem.documentPath._path);
          typeNoneSeemore.find('a.link').prop('href', dataItem.documentPath._path);
        } else if (dataItem.externalDocumentPath) {
          typeNoneSeemore.find('tcb-tracker').attr('download', dataItem.externalDocumentPath.split('/')?.pop());
          typeNoneSeemore.find('a.link').attr('data-file-link', dataItem.externalDocumentPath);
          typeNoneSeemore.find('a.link').prop('href', dataItem.externalDocumentPath);
        }
      } else {
        //handle for case open page
        const typeSeemore = noneDocumentItemsElement.find('.type-output-seemore');
        typeSeemore.removeClass('hide-document');
        typeSeemore.addClass('show-document');

        typeSeemore.find('a.link').attr('data-file-type', dataItem.typeOfOutput);
        typeSeemore.find('a.link').attr('data-open-new-tab', dataItem.openNewTab);
        if (dataItem.linkIcon?._path) {
          renderPictureSource(dataItem.linkIcon._path, typeSeemore);
          typeSeemore.find('a img').prop('src', dataItem.linkIcon._path);
        }
        if (dataItem.linkLabel) {
          typeSeemore.find('tcb-tracker').attr('value', dataItem.linkLabel);
          typeSeemore.find('a span').text(dataItem.linkLabel);
        }
        if (dataItem.documentPath?._publishUrl) {
          typeSeemore.find('a.link').prop('href', dataItem.documentPath._publishUrl);
          typeSeemore.find('tcb-tracker').attr('dest-path', dataItem.documentPath._publishUrl);
        } else if (dataItem.externalDocumentPath) {
          typeSeemore.find('a.link').prop('href', dataItem.externalDocumentPath);
          typeSeemore.find('tcb-tracker').attr('dest-path', dataItem.externalDocumentPath);
        }
      }
      return noneDocumentItemsElement;
    }

    function renderPictureSource(path, element) {
      element.find('a source').eq(0).prop('media', '(max-width: 360px)');
      element.find('a source').eq(0).prop('srcset', getMobileImagePath(path));

      element.find('a source').eq(1).prop('media', '(max-width: 576px)');
      element.find('a source').eq(1).prop('srcset', getMobileImagePath(path));

      element.find('a source').eq(2).prop('media', '(min-width: 1080px)');
      element.find('a source').eq(2).prop('srcset', getWebImagePath(path));

      element.find('a source').eq(3).prop('media', '(min-width: 1200px)');
      element.find('a source').eq(3).prop('srcset', getWebImagePath(path));
    }

    function getWebImagePath(imagePath) {
      if (!imagePath) {
        return '';
      }

      if (imagePath.toLowerCase().endsWith('.svg')) {
        return imagePath;
      } else if (imagePath.toLowerCase().endsWith('.png')) {
        return imagePath + '.rendition/cq5dam.web.1280.1280.png';
      } else {
        return imagePath + '.rendition/cq5dam.web.1280.1280.jpeg';
      }
    }

    function getMobileImagePath(imagePath) {
      if (!imagePath) {
        return '';
      }

      if (imagePath.toLowerCase().endsWith('.svg')) {
        return imagePath;
      } else if (imagePath.toLowerCase().endsWith('.png')) {
        return imagePath + '.rendition/cq5dam.web.640.640.png';
      } else {
        return imagePath + '.rendition/cq5dam.web.640.640.jpeg';
      }
    }

    function renderCategoryFilter() {
      const categoryCheckboxs = elementCategoryFilter.find('ul');
      const categories = listViewDocument
        .find('.row .content-category span')
        .map(function () {
          return $(this).text();
        })
        .get();
      const categoryDuplicate = [];
      for (let item of categories) {
        if (item != '' && categoryDuplicate.indexOf(item) === -1) {
          categoryDuplicate.push(item);
        }
      }
      categoryDuplicate.forEach((e) => {
        categoryCheckboxs.append(`
                <li>
                  <label>
                    <input type="checkbox" value="${e}" />
                    <div>${e}</div>
                  </label>
                </li>
                `);
      });

      categoryCheckboxs.find("input[type='checkbox']").eq(0).prop('checked', true);
      categoryCheckboxs.find("input[type='checkbox']").eq(1).prop('checked', true);

      fillPlaceholderCategory();

      let checkbox = elementCategoryFilter.find("input[type='checkbox']");
      checkbox.on('change', function () {
        let listVal = elementCategoryFilter
          .find("input[type='checkbox']:checked")
          .map(function () {
            return $(this).val();
          })
          .get();
        showHideByFilter(listVal);
        fillPlaceholderCategory();
      });
    }

    function showHideByFilter(filterTxt) {
      let $lvItems = listViewDocument.find('.list-items .row');
      if (filterTxt.length == 0) {
        $lvItems.addClass('filter-hide');
        viewMore.css('display', 'none');
        return;
      }
      $lvItems.each(function () {
        let $lvItem = $(this);
        if (filterTxt.length > 0) {
          let itemContent = $lvItem.find('.content-category span').html();
          let isContain = filterTxt.some(function (keyword) {
            return !keyword || (keyword && itemContent && itemContent.indexOf(keyword) > -1);
          });
          if (isContain) {
            $lvItem.removeClass('filter-hide');
          } else {
            $lvItem.addClass('filter-hide');
          }
        } else {
          $lvItem.removeClass('filter-hide');
        }
      });
    }

    function formatDate(value) {
      let date = new Date(value);
      if (date.getTimezoneOffset() > 0) {
        date.setDate(date.getDate() + 1);
      }
      return moment(date).format('DD/MM/YYYY');
    }

    function fillPlaceholderCategory() {
      let listVal = elementCategoryFilter
        .find("input[type='checkbox']:checked")
        .map(function () {
          return $(this).val();
        })
        .get();
      if (listVal.length == 0) {
        placeholderCategory.text(filterCategoryPlaceholder);
      } else {
        placeholderCategory.text(listVal.join(','));
      }
    }
  });
});
