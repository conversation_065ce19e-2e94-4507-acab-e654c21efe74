import { submitMultiStepForm } from './multi-step-form.js';

let formTitle = $("body").attr("data-tracking-page-name");
$(document).ready(function () {
  $(".tcbformcontainer form.cmp-form").submit(function (event) {
    let currentObject = $(this);
    let formSubmitBtn = $(currentObject).find(".cmp-form-button");

    //form validation check
    event.preventDefault();
    if (!validateForm(event)) {
      errorAnalytics(currentObject);
      $("#form-error-analytics").trigger("click");
      return;
    }else{
      $(formSubmitBtn).prop("disabled", true);
    }

    // capture form success analytics
    processAnalyticsFields(currentObject);


    // handle navigate COC web when value form button is coc web
    let buttonValue = formSubmitBtn.val();
    if (buttonValue && (buttonValue.toString().includes('origination/vi/sign-up') || buttonValue.toString().includes('origination/en/sign-up'))) {
      let params = new URLSearchParams(window.location.search);
      let url = new URL(`${buttonValue}${params}`);
      window.location.href = url;
      return;
    }

    //form submit ajax call : start
    let form = $(this);
    let ajaxPage = $(this).attr("action");
    let modalTargetId = $(form).attr("data-success-modalId");
    let failureModalTargetId = $(form).attr("data-failure-modalId");
    let multiStepValidation = $(form).attr("multi-step-validation");
    let callbackFunction = $(form).attr("data-success-callback");
    let targetModalId;
    if (multiStepValidation) {
      let validationResult = submitMultiStepForm(multiStepValidation);
      if(!validationResult) {
        $(formSubmitBtn).prop("disabled", false);
        return false;
      }
    }
    $.ajax({
      url: ajaxPage,
      method: "POST",
      data: $(form).serialize(), // Serialize form data
      success: function (data) {
        let returnedData = JSON.parse(JSON.stringify(data));
        if (returnedData.Success == 200) {
          showModal(modalTargetId);
          if (callbackFunction) {
            eval(callbackFunction);
          }
        }
        resetForm();
        $(formSubmitBtn).prop("disabled", false);
      },
      error: function (xhr, status, error) {
        showModal(failureModalTargetId);
        $(formSubmitBtn).prop("disabled", false);
      },
    });

    //form reset function
    function resetForm() {
      $(form).trigger("reset");
      $(form)
        .find(".dropdown-wrapper .dropdown-inputbase .dropdown-viewValue")
        .text("")
        .addClass("hidden");
      $(form)
        .find(".dropdown-wrapper .dropdown-inputbase .dropdown-placeholder")
        .removeClass("hidden");
      let siteKeyElement = document.getElementById("siteKey");
      if (siteKeyElement !== null && siteKeyElement.value !== "") {
        grecaptcha.enterprise.reset();
        const checkedCaptcha = document.querySelector('.captcha-checked');
        if (checkedCaptcha) {
          checkedCaptcha.classList.remove('captcha-checked');
        }
      }
    }

    //form show modal box
    function showModal(modalId) {
      targetModalId = modalId;
      $(".modal .popup").each(function () {
        let modalID = $(this).attr("id");
        if (targetModalId === "#" + modalID) {
          // Check if the current div's class matches the class of the first div
          $(this).css("display", "flex");
        }
      });
    }
  });
  //form submit ajax call : end

  //dateClick function
  function dateClickAnalytics() {
    if ($(".tcbformcontainer form.cmp-form input").length > 0) {
      $(".tcbformcontainer form.cmp-form input").eq(0).trigger("click");
    }
  }

  // analytics for start filling the form : start
  $(".tcbformcontainer form.cmp-form").on("change", "input:not(button.cmp-form-button, [type='radio'], [type='checkbox'], .dropdown-wrapper)", function () {
    const value = $(this).val().trim();
    if (value){
      inputClickEvent.call(this);
    }
  });

  $(".tcbformcontainer form.cmp-form").on("click", "[type='radio'], [type='checkbox'], .dropdown-item", function () {
      inputClickEvent.call(this);
  });

  function inputClickEvent(){
    if ($(this).hasClass("datepicker-input")) {
      dateClickAnalytics();
    }
    let formStartDataValues = $(this)
      .parents(".tcb-form-wrapper")
      .find("#form-start-analytics")
      .data().trackingClickInfoValue;
    let submitBtn = $(this)
      .parents(".tcb-form-wrapper")
      .find(".cmp-form-button")
      .text()
      .trim();
    let webInteractionDataValues = $(this)
      .parents(".tcb-form-wrapper")
      .find("#form-start-analytics")
      .data().trackingWebInteractionValue;
    if (formStartDataValues) {
      let jsonStr = formStartDataValues.replace(/'/g, '"');
      let json = JSON.parse(jsonStr);
      json.formName = formTitle;
      let updatedValues = JSON.stringify(json).replace(/"/g, "'");
      let webInteractionStr = webInteractionDataValues.replace(/'/g, '"');
      let webInteractionjson = JSON.parse(webInteractionStr);
      webInteractionjson.webInteractions.name = submitBtn;
      let updatedWebInteractionValues = JSON.stringify(
        webInteractionjson
      ).replace(/"/g, "'");
      $(this)
        .parents(".tcb-form-wrapper")
        .find("#form-start-analytics")
        .attr("data-tracking-click-info-value", updatedValues);
      $(this)
        .parents(".tcb-form-wrapper")
        .find("#form-start-analytics")
        .attr(
          "data-tracking-web-interaction-value",
          updatedWebInteractionValues
        );
      $(this)
        .parents(".tcb-form-wrapper")
        .find("#form-start-analytics")
        .data("trackingWebInteractionValue", updatedWebInteractionValues);
    }
    $(this)
    .parents(".tcb-form-wrapper")
    .find("#form-start-analytics")
    .trigger("click");
  }
  // analytics for start filling the form : end
});

// process Analytics Fields function
function processAnalyticsFields(currentObject) {
  let analyticsFields = [];
  let hasedEmailId = "";
  $(currentObject)
    .find(".analytics-form-field input")
    .each(function () {
      let value = $(this).val().trim();
      if (this.type === "radio" || this.type === "checkbox") {
        if (this.checked) {
          if (value !== "") {
            analyticsFields.push(value);
          }
        }
      } else if ($(this).data("type") === "email") {
        if (value !== "") {
          let emailVal = value;
          const hashEmail = hashEmailValue(emailVal);
          value = hashEmail;
          hasedEmailId = hashEmail;
          analyticsFields.push(value);
        }
      } else {
        if (value !== "") {
          analyticsFields.push(value);
        }
      }
    });

  let analyticsFieldsString = analyticsFields.join(" | ");
  // Call the function to update the dataLayer object
  updateDataLayerObject(currentObject, analyticsFieldsString, hasedEmailId);
}

//update DataLayerObject
function updateDataLayerObject(
  currentObject,
  analyticsFieldsString,
  hasedEmailId
) {
  let submitBtn = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find(".cmp-form-button")
    .text()
    .trim();
  let formBranchName = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find(".analytics-form-field .branch-list .dropdown-viewValue")
    .text()
    .trim();
  let formSubmitDataValues = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find("#form-submit-analytics")
    .data().trackingClickInfoValue;
  let webInteractionDataValues = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find("#form-submit-analytics")
    .data().trackingWebInteractionValue;

  if (formSubmitDataValues) {
    let jsonStr = formSubmitDataValues.replace(/'/g, '"');
    let json = JSON.parse(jsonStr);
    json.formName = formTitle;
    json.formDetails = analyticsFieldsString;
    json.hashedEmail = hasedEmailId;
    if (hasedEmailId !== "") {
      json.hashedEmail = hasedEmailId;
    } else {
      delete json.hashedEmail;
    }
    if (!$(".branch-list").hasClass("disabled") && formBranchName !== "") {
      json.branchLocation = formBranchName;
    } else {
      delete json.branchLocation;
    }
    let updatedValues = JSON.stringify(json).replace(/"/g, "'");

    let webInteractionStr = webInteractionDataValues.replace(/'/g, '"');
    let webInteractionjson = JSON.parse(webInteractionStr);
    webInteractionjson.webInteractions.name = submitBtn;
    let updatedWebInteractionValues = JSON.stringify(
      webInteractionjson
    ).replace(/"/g, "'");
    $(currentObject)
      .parents(".tcb-form-wrapper")
      .find("#form-submit-analytics")
      .attr("data-tracking-click-info-value", updatedValues);
    $(currentObject)
      .parents(".tcb-form-wrapper")
      .find("#form-submit-analytics")
      .attr("data-tracking-web-interaction-value", updatedWebInteractionValues);
    $(currentObject)
      .parents(".tcb-form-wrapper")
      .find("#form-submit-analytics")
      .data("trackingWebInteractionValue", updatedWebInteractionValues);
  }
  $(currentObject)
    .parents(".tcb-form-wrapper")
    .find("#form-submit-analytics")
    .trigger("click");
}

// covert hash email value for analytics
function hashEmailValue(emailVal) {
  let hashEmail = "";
  if (emailVal) {
    let hash = 0;
    for (let i = 0; i < emailVal.length; i++) {
      const char = emailVal.charCodeAt(i);
      hash = (hash << 5) - hash + char;
    }
    hashEmail = hash.toString(16);
  }
  return hashEmail;
}

// capture form error analytics
function errorAnalytics(currentObject) {
  let analyticsErrorFields = [];
  let analyticsDataErrorFields = [];
  let hashErrorEmailID;
  let radioErrorProcessed = false;
  let checkboxErrorProcessed = false;
  let formBranchName = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find(".analytics-form-field .branch-list .dropdown-viewValue")
    .text()
    .trim();
  $(currentObject)
    .find(".analytics-form-field input")
    .each(function () {
      let radioValue = $(this)
        .parents(".analytics-form-field")
        .find(".form-error-message")
        .text()
        .trim();
      let value = $(this).val().trim();
      if (this.type === "radio") {
        if (!this.checked && !radioErrorProcessed) {
          analyticsErrorFields.push(radioValue);
          radioErrorProcessed = true;
        } else if (this.checked) {
          if (value !== "") {
            analyticsDataErrorFields.push(value);
          }
        }
      } else if (this.type === "checkbox") {
        if (!this.checked && !checkboxErrorProcessed) {
          analyticsErrorFields.push(radioValue);
          checkboxErrorProcessed = true;
        } else if (this.checked) {
          if (value !== "") {
            analyticsDataErrorFields.push(value);
          }
        }
      } else if ($(this).data("type") === "email") {
        if (value !== "") {
          let emailVal = value;
          const hashErrorEmail = hashEmailValue(emailVal);
          value = hashErrorEmail;
          hashErrorEmailID = hashErrorEmail;
        } else {
          if (radioValue !== "") {
            analyticsErrorFields.push(radioValue);
          }
          hashErrorEmailID = "";
        }
      } else {
        analyticsErrorFields.push(radioValue);
        if (value !== "") {
          analyticsDataErrorFields.push(value);
        }
      }
    });

  let analyticsErrorFieldsString = analyticsErrorFields
    .map((error) => error.replace(/\*/g, ""))
    .filter((error) => error !== "")
    .join(" | ");

  let analyticsDataErrorFieldsString = analyticsDataErrorFields.join(" | ");

  // update form error datalayer object
  let formErrorDataValues = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find("#form-error-analytics")
    .data().trackingClickInfoValue;
  let formInfoValues = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find("#form-error-analytics")
    .data().trackingFormInfoValue;
  let userInfoValues = $(currentObject)
    .parents(".tcb-form-wrapper")
    .find("#form-error-analytics")
    .data().trackingUserInfoValue;
  if (formErrorDataValues) {
    let jsonStr = formErrorDataValues.replace(/'/g, '"');
    let jsonFormInfoStr = formInfoValues.replace(/'/g, '"');
    let jsonUserInfoStr = userInfoValues.replace(/'/g, '"');
    let json = JSON.parse(jsonStr);
    let jsonFormInfo = JSON.parse(jsonFormInfoStr);
    let jsonUserInfo = JSON.parse(jsonUserInfoStr);
    json.errorMessage = analyticsErrorFieldsString;
    jsonFormInfo.formName = formTitle;
    jsonFormInfo.formDetails = analyticsDataErrorFieldsString;
    jsonFormInfo.hashedEmail = hashErrorEmailID;
    jsonFormInfo.branchLocation = formBranchName;
    jsonUserInfo.gaid = "test";
    let updatedValues = JSON.stringify(json).replace(/"/g, "'");
    let updatedFormValues = JSON.stringify(jsonFormInfo).replace(/"/g, "'");
    let updatedUserValues = JSON.stringify(jsonUserInfo).replace(/"/g, "'");
    $(currentObject)
      .parents(".tcb-form-wrapper")
      .find("#form-error-analytics")
      .attr("data-tracking-click-info-value", updatedValues);
    $(currentObject)
      .parents(".tcb-form-wrapper")
      .find("#form-error-analytics")
      .attr("data-tracking-form-info-value", updatedFormValues);
    $(currentObject)
      .parents(".tcb-form-wrapper")
      .find("#form-error-analytics")
      .attr("data-tracking-user-info", updatedUserValues);
  }
}
