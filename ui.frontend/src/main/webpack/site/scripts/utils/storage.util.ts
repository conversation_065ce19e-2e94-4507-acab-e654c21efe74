export class StorageUtils {
  public static get(key: string) {
    const storedValue = localStorage.getItem(key);
    return storedValue ? JSON.parse(storedValue) : storedValue;
  }

  public static set(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value));
  }

  public static remove(key: string) {
    localStorage.removeItem(key);
  }
}

export class SessionStorageUtil {
  public static get(key: string) {
    const storedValue = sessionStorage.getItem(key);
    return storedValue ? JSON.parse(storedValue) : storedValue;
  }

  public static set(key: string, value: any) {
    sessionStorage.setItem(key, JSON.stringify(value));
  }

  public static remove(key: string) {
    sessionStorage.removeItem(key);
  }
}
