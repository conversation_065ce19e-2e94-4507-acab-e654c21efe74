export const removeVietnameseAccent = (input = ''): string => {
  if (!input) {
    return input;
  }

  const stringContainAccent = 'àáãảạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệđùúủũụưừứửữựòóỏõọôồốổỗộơờớởỡợìíỉĩịäëïîöüûñçýỳỹỵỷ';
  const stringSlug = 'aaaaaaaaaaaaaaaaaeeeeeeeeeeeduuuuuuuuuuuoooooooooooooooooiiiiiaeiiouuncyyyyy';

  let result = input.trim().toLowerCase();
  for (let i = 0, l = stringContainAccent.length; i < l; i++) {
    result = result.replace(RegExp(stringContainAccent[i], 'gi'), stringSlug[i]);
  }

  return result;
};

export const replaceDoubleSpaceBySingleSpace = (input = ''): string => {
  return input.replace(/\s\s+/g, ' ');
};

export const stringToSlug = (input = ''): string => {
  if (!input) {
    return input;
  }

  return removeVietnameseAccent(replaceDoubleSpaceBySingleSpace(input))
    .replace(/ /g, '_')
    .replace(/[^a-z0-9_]/g, '_')
    .replace(/_+/g, '_');
};

/**
 * Get plain string
 * @param {string} str
 * @returns {string}
 */
export const formatString = (str) => {
  if (str) {
    str = str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    str = str
      .replace(/(\r\n|\n|\r)/gm, '')
      .replace(/ĩ/g, 'i')
      .replace(/ø/g, 'o')
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
    str = str.trim();
  }

  return str;
};

/**
 * Get jQ elem plain text
 * @param {JQueryElement} elem
 * @returns {string}
 */
export const getElementText = (elem) => {
  if (!elem) {
    return '';
  }
  return formatString(elem.text()).trim();
};

/**
 * Parse string to JSON
 * @param {string} value
 * @returns {JSON}
 */
export const parseStringToJson = (value: string) => {
  if (value && typeof value === 'string') {
    return JSON.parse(value.replace(/'/g, '"'));
  }
};
