import { LocationUtil } from './location.util';

export const TARGET_MAP_PARAM = {
  category: ['types'],
  product: ['products', 'card-types'],
};

const PARAM_NAMES = {
  detail: 'detail',
  detailName: 'name',
  cardTypes: 'card-types',
  sort: 'sort',
  products: 'products',
  memberships: 'memberships',
  types: 'types',
  partner: 'partner',
  limit: 'limit',
  offset: 'offset',
  searchText: 'q',
  location: 'location',
  membership: 'membership',
  target: 'target',
  commonKey: 'search-with-merchant',
};

const PAYLOAD_NAMES = {
  limit: 'limit',
  offset: 'offset',
  sort: 'sort',
  isSearch: 'isSearch',
  searchText: 'searchText',
  cardTypes: 'card-types',
  products: 'products',
  types: 'types',
  partner: 'partner',
  location: 'location',
  memberships: 'memberships',
  commonKey: 'searchWithMerchant',
};

export function getParamNames() {
  return PARAM_NAMES;
}

export function getPayloadNames() {
  return PAYLOAD_NAMES;
}

export function getBaseEndpoint(dataUrl) {
  return `${dataUrl}.getpromotions.json?`;
}

export function getCardTypeByParentKey() {
  const value = {};

  const $cardTypeParents = $('.offer-filter__checkbox-wrapper');
  if ($cardTypeParents.length) {
    $cardTypeParents.each((_, parentElement) => {
      const $parent = $(parentElement);
      const parentKey = $parent.data('card-type');
      const $children = $parent.find('.input__checkbox');
      const allChildValues = [];

      if ($children.length) {
        $children.each((_, childElement) => {
          const childKey = $(childElement).val().toLowerCase();
          allChildValues.push(`${parentKey}/${childKey}`);
        });
      }
      value[parentKey] = allChildValues;
    });
  }

  return value;
}

export function getCardTypeChildrenKey(urlParamObj) {
  let outputParam = [];
  const allCard = getCardTypeByParentKey();

  let cardTypesParams = urlParamObj[PARAM_NAMES.cardTypes] || '';
  cardTypesParams = cardTypesParams.split(',');

  if (cardTypesParams.length) {
    cardTypesParams.forEach((param) => {
      const childKeys = allCard[param] || (param && [param]);
      if (childKeys.length) {
        outputParam = outputParam.concat(childKeys);
      }
    });
  }

  return outputParam;
}

export function getTarget(urlParamObj) {
  const targetValue = urlParamObj[PARAM_NAMES.target];
  const targetEntries = TARGET_MAP_PARAM[targetValue];
  if (targetEntries && targetEntries.length) {
    const filters = {};
    targetEntries.forEach((param) => {
      const paramValue = urlParamObj[param];
      if (paramValue) {
        filters[param] = paramValue;
      }
    });
    return filters;
  }
  return {};
}

export function getEndpoint(dataUrl, options) {
  const { limit, offset, sort } = options || {};

  const endpoint = getBaseEndpoint(dataUrl);
  const urlParams = new URLSearchParams();
  const urlParamObj = LocationUtil.getUrlParamObj() || {};
  const targetMap = getTarget(urlParamObj) || {};

  if (limit != null) {
    urlParams.append(PAYLOAD_NAMES.limit, limit);
  }
  if (offset != null) {
    urlParams.append(PARAM_NAMES.offset, offset);
  }
  if (sort) {
    urlParams.append(PARAM_NAMES.sort, sort);
  }

  const searchText = urlParamObj[PARAM_NAMES.searchText];
  if (searchText) {
    appendSearchParams(urlParams, urlParamObj, targetMap);
  } else {
    appendFilterParams(urlParams, urlParamObj);
  }

  urlParams.append(PAYLOAD_NAMES.memberships, 'danh-cho-tat-ca-khach-hang');

  return endpoint + urlParams.toString();
}

function appendSearchParams(urlParams, urlParamObj, targetMap) {
  urlParams.append(PAYLOAD_NAMES.isSearch, 'true');
  urlParams.append(PAYLOAD_NAMES.searchText, urlParamObj[PARAM_NAMES.searchText]);

  const commonValue = urlParamObj[PARAM_NAMES.commonKey];
  if (commonValue) {
    urlParams.append(PAYLOAD_NAMES.commonKey, encodeURIComponent(commonValue));
  }

  Object.entries(targetMap).forEach(([paramKey, targetValue]) => {
    urlParams.append(paramKey, targetValue);
  });
}

function appendFilterParams(urlParams, urlParamObj) {
  Object.entries(urlParamObj).forEach(([paramKey, targetValue]) => {
    urlParams.append(paramKey, targetValue);
  });

  const cardTypeParams = getCardTypeChildrenKey(urlParamObj);
  if (cardTypeParams.length) {
    urlParams.set(PARAM_NAMES.cardTypes, cardTypeParams.join(','));
  }
}
