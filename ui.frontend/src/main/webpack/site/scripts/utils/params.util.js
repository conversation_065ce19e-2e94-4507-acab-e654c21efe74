const urlBase = () => {
  return new URL(window.location);
};

export function getAllUrlParams() {
  const searchPattern = /[?&]+([^=&]+)=([^&]*)/gi;
  const params = {};
  window.location.search.replace(searchPattern, function (_, key, value) {
    params[key] = decodeURIComponent(value);
  });
  return params;
}

export function getQueryParam(name) {
  const pattern = '[?&]' + name + '=([^&#]*)';
  const regex = new RegExp(pattern);
  const results = regex.exec(window.location.search);

  if (results && results[1]) {
    return decodeURIComponent(results[1].replace(/\+/g, ' '));
  }

  return null;
}

export function setURLParams(name, value, replaceState) {
  const url = urlBase();
  url.searchParams.set(name, value);
  if (replaceState) {
    window.history.replaceState({}, '', url.toString());
  }
}

export function removeURLParams(name, replaceState) {
  const url = urlBase();
  url.searchParams.delete(name);
  if (replaceState) {
    window.history.replaceState({}, '', url.toString());
  }
}
