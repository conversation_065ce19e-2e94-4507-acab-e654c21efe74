export class LocationUtil {
  public static getUrlParamObj(location?: string): { [x: string]: string } {
    const urlParam = this.getPageUrlParams(location);
    return urlParam
      ? JSON.parse('{"' + urlParam.toLowerCase().replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) =>
          key === '' ? value : decodeURIComponent(value),
        )
      : {};
  }

  public static getPageUrlParams(location?: string): string {
    let pageURLParam = '';
    if (location) {
      pageURLParam = location.split('?')[1] || '';
    } else {
      pageURLParam = window.location.search || '';
    }
    return decodeURIComponent(pageURLParam).replace(/^\?/, '');
  }
}
