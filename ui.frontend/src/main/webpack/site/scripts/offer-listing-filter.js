// import {
//   updateSearchParams,
//   addCurrentUrlHistory,
//   resetHistory,
//   handleResetDropdownLanguage,
//   convertPxToRem,
//   listenerElement,
//   disableBodyScroll,
// } from './offer-helper';
// import { LocationUtil } from './utils/location.util';
// import { HEADER_HEIGHT_VARIABLE, SELECTOR_DETAIL_LINK } from './constants/offer-common';
// import { LOCALE_VI, LOCALE_EN, SLASH } from './constants/common';
// import moment from 'moment';

// $(function () {
//   const offerListingComponent = $('.offer-listing-filter__body');
//   if (offerListingComponent.length === 0) {
//     return;
//   }

//   let promotionData;
//   let url = new URL(window.location);
//   let totalPage;
//   let totalItem;
//   let dataSearchCount = '';
//   let dataViewMore = '';
//   let dataURL = '';
//   let dataDay = '';
//   let dayLabel = '';
//   let dataExpiryLabel = '';
//   let dataComponentName = '';
//   let dataLanguageLabel = '';
//   let dataEmptyPromotion = '';
//   let dataDownloadPdfLabel = '';
//   let currentPage = 1;
//   let activeFilterType = '';
//   const MOBILE_SIZE = 768;
//   const UP_ARROW_KEY = 38;
//   const DOWN_ARROW_KEY = 40;
//   const DEFAULT_SORT_PARAMS = 'most popular';
//   const paramsName = {
//     detail: 'detail',
//     detailName: 'name',
//     cardTypes: 'card-types',
//     sort: 'sort',
//     products: 'products',
//     memberships: 'memberships',
//     types: 'types',
//     partner: 'partner',
//     limit: 'limit',
//     offset: 'offset',
//     searchText: 'q',
//     location: 'location',
//     membership: 'membership',
//   };
//   const paramsValue = {
//     limit: 12,
//     offset: 0,
//     sort: DEFAULT_SORT_PARAMS,
//     products: '',
//     memberships: '',
//     types: '',
//     searchText: '',
//     location: '',
//     cardTypes: '',
//   };

//   const dataElement = offerListingComponent.find('.offer-listing-promotions');
//   const detailPlaceholderElement = $('#detail-container')?.[0];
//   const headerElement = $('header')?.[0];

//   const sortDesktopElement = $('.offer-listing-promotions__wrapper input.input__radio');
//   const sortDropdownDesktopElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__dropdown.sort-desktop-element',
//   );
//   const productDesktopElement = $('.offer-listing-promotions__wrapper div.offer-filter__checkbox.product-checkbox');
//   const cardTypeDesktopElement = $('.card-checkbox .checkbox-child');
//   const categoryDesktopElement = $('.offer-listing-promotions__wrapper div.offer-filter__checkbox.category-checkbox');
//   const membershipDesktopElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__checkbox.membership-checkbox',
//   );
//   const merchantDesktopElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__dropdown.merchant-desktop-element',
//   );
//   const locationDesktopElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__dropdown.location-desktop-element',
//   );

//   const sortMobileElement = $('.offer-listing-promotions__wrapper div.offer-filter__dropdown.sort-mobile-element');
//   const membershipMobileElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__checkbox.membership-checkbox-mobile',
//   );
//   const cardTypeMobileElement = $('.card-checkbox.card-checkbox-mobile .checkbox-child');
//   const productMobileElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__checkbox.product-checkbox-mobile',
//   );
//   const categoryMobileElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__checkbox.category-checkbox-mobile',
//   );
//   const merchantMobileElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__dropdown.merchant-mobile-element',
//   );
//   const locationMobileElement = $(
//     '.offer-listing-promotions__wrapper div.offer-filter__dropdown.location-mobile-element',
//   );

//   const searchTextElement = $('.offer-listing-promotions__wrapper .search-area .promotions-filter .load-more__button');

//   const searchTextInputElement = $('.offer-listing-promotions__wrapper .search-input');

//   const inputDropdownElement = $('.offer-listing-promotions__wrapper .input-dropdown ul');

//   const searchBoxElement = $('.offer-listing-promotions__wrapper .search-box');

//   let textInputSearch = '';

//   let promotionResultsNoText;

//   let dialogDetailData;

//   initDataType();
//   initSearchParams();
//   displayElementByScreen();
//   handlePromotionFilterOnMobile();
//   handleClosePromotionFilterOnMobile();
//   getPromotions();
//   getAllPromotions();
//   initDialogDetail();
//   handleBackBrowserDetail();
//   handleSetHeaderHeight();

//   $(window).resize(displayElementByScreen);

//   function toggleFilterSections(currDataFilterType, mobileFilterSections) {
//     if (currDataFilterType) {
//       activeFilterType = currDataFilterType;
//       $('.offer-filter__title-collapse').hide();
//       mobileFilterSections.forEach((mobileFilterSection) => {
//         if ($(mobileFilterSection).attr('data-filter-type') === currDataFilterType) {
//           $(mobileFilterSection).show();
//           $(mobileFilterSection).find('.offer-filter__title-close').show();
//           const collapsedElementSelector = $(mobileFilterSection)
//             .find('.offer-filter__title-collapse')
//             .attr('data-collapsed-element');
//           const collapsedWrapperElement = $(collapsedElementSelector).parent();
//           collapsedWrapperElement.css('height', 'fit-content');
//           $('.tcb-modal.offer-listing-enhance-mobile > .tcb-modal__wrapper').addClass('is-single-filter');
//         } else {
//           $(mobileFilterSection).hide();
//         }
//       });
//     } else {
//       $('.tcb-modal.offer-listing-enhance-mobile > .tcb-modal__wrapper').removeClass('is-single-filter');
//       $('.offer-filter__title-collapse').show();
//       $('.offer-filter__title-close').hide();
//       $('.offer-filter__title-close').first().show();
//       mobileFilterSections.forEach((mobileFilterSection) => {
//         $(mobileFilterSection).show();
//       });
//     }
//   }

//   function handlePromotionFilterOnMobile() {
//     if (dataElement.length > 0) {
//       let btnOpenFilter = dataElement.find('.btn-open-filter');
//       let buttonFilter = dataElement.find('.tcb-apply-filter-button');
//       btnOpenFilter.on('click', function () {
//         const currDataFilterType = $(this).attr('data-filter-type');
//         const mobileFilterSections = $('.mobile-filter-section');
//         toggleFilterSections(currDataFilterType, mobileFilterSections);
//         dataElement.addClass('open');
//         $('body').addClass('modal-showing');
//       });
//       buttonFilter.on('click', function () {
//         dataElement.removeClass('open');
//         $('body').removeClass('modal-showing');
//         $('.offer-listing-filter__button').show();
//         activeFilterType = '';
//       });
//     }
//   }

//   function handleClosePromotionFilterOnMobile() {
//     if (dataElement.length > 0) {
//       let buttonCloseFilter = dataElement.find('.tcb-close-filter-button');
//       buttonCloseFilter.on('click', function () {
//         if (activeFilterType) {
//           if (activeFilterType === paramsName.products) {
//             removeURLParams(paramsName.products, true);
//             removeURLParams(paramsName.cardTypes, true);
//           } else {
//             removeURLParams(activeFilterType, true);
//           }
//         } else {
//           Object.keys(paramsName).forEach((paramNameKey) => removeURLParams(paramsName[paramNameKey], true));
//         }
//         if (activeFilterType) {
//           resetActiceMobileFilterInput(activeFilterType);
//         } else {
//           Object.keys(paramsName).forEach((param) => resetActiceMobileFilterInput(paramsName[param]));
//         }
//         activeFilterType = '';
//         updateActiveFilterPramsButton();
//         paramsValue.offset = 0;
//         currentPage = 1;
//         getPromotions();
//       });
//     }
//   }

//   function resetActiceMobileFilterInput(filterType) {
//     if (filterType === paramsName.products) {
//       const inputElementProduct = productMobileElement?.find($('input.input__checkbox'));
//       inputElementProduct.each(function () {
//         $(this).prop('checked', false);
//       });

//       const inputElementCardType = cardTypeMobileElement?.find($('input.input__checkbox'));
//       inputElementCardType.each(function () {
//         $(this).prop('checked', false);
//         updateCheckboxStates();
//       });
//     }
//     if (filterType === paramsName.types) {
//       const inputElement = categoryMobileElement?.find($('input.input__checkbox'));
//       inputElement.each(function () {
//         $(this).prop('checked', false);
//       });
//     }

//     if (filterType === paramsName.memberships) {
//       const inputElement = membershipMobileElement?.find($('input.input__checkbox'));
//       inputElement.each(function () {
//         $(this).prop('checked', false);
//       });
//     }

//     if (filterType === paramsName.partner) {
//       const placeholderLabel = merchantMobileElement?.find($('span.display__text'))?.attr('data-placeholder');
//       merchantMobileElement?.find($('span.display__text'))?.text(placeholderLabel);
//     }

//     if (filterType === paramsName.location) {
//       const placeholderLabel = locationMobileElement?.find($('span.display__text'))?.attr('data-placeholder');
//       locationMobileElement?.find($('span.display__text'))?.text(placeholderLabel);
//     }
//   }

//   function initDialogDetail() {
//     const offerDetailElement = $('offerlistingdetail');
//     if (!offerDetailElement.length) {
//       return;
//     }

//     const detailParam = url.searchParams.get(paramsName.detail);
//     let parseText = offerDetailElement?.attr('data-init');
//     dialogDetailData = JSON.parse(parseText);

//     if (detailParam) {
//       resetHistory();
//       openDetailModal(detailParam);
//     }
//   }

//   function displayElementByScreen() {
//     let width = $(window).width();
//     if (width < MOBILE_SIZE) {
//       $('.offer-listing-promotions__wrapper .offer-listing-filter__button').show();
//       $('.offer-listing-promotions__wrapper .offer-filter__container').hide();
//       $('.offer-listing-promotions__wrapper .offer-cards__container .offer-cards__wrapper .search-area').hide();
//     } else {
//       $('.offer-listing-promotions__wrapper .offer-listing-filter__button').hide();
//       $('.offer-listing-promotions__wrapper .offer-filter__container').show();
//       $('.offer-listing-promotions__wrapper .offer-cards__container .offer-cards__wrapper .search-area').show();
//     }
//   }

//   function initDataType() {
//     dataURL = dataElement?.attr('data-url');
//     dataSearchCount = dataElement?.attr('data-search-count');
//     dataViewMore = dataElement?.attr('data-view-more');
//     dataDay = dataElement?.attr('data-day-label');
//     dayLabel = dataElement?.attr('data-day');
//     dataExpiryLabel = dataElement?.attr('data-expiry-label');
//     dataEmptyPromotion = dataElement?.attr('data-empty-promotion-label');
//     dataLanguageLabel = dataElement?.attr('data-language-label');
//     dataComponentName = dataElement?.attr('data-component-name');
//     dataDownloadPdfLabel = dataElement?.attr('data-download-pdf-label');
//   }

//   function triggerFilterByElement(element, paramsName) {
//     if (!element) {
//       return;
//     }
//     let value = url.searchParams.get(paramsName)?.replaceAll(' ', '-');
//     value = value?.replaceAll('+', '-');
//     const inputElement = element?.find($('input.input__checkbox'));
//     if (inputElement?.length) {
//       inputElement.each(function () {
//         const elementValue = $(this).val();
//         if (value.includes(elementValue?.toLowerCase())) {
//           $(this).prop('checked', true);
//         }
//       });
//     }
//   }

//   function initSearchParams() {
//     if (url.searchParams.size === 0) {
//       paramsValue.sort = DEFAULT_SORT_PARAMS;
//     } else {
//       if (url.searchParams.has(paramsName.searchText)) {
//         paramsValue.searchText = url.searchParams.get(paramsName.searchText);
//         searchTextInputElement.val(decodeURI(paramsValue.searchText).replace(/\+/g, ' '));
//       }

//       if ($(window).width() > MOBILE_SIZE) {
//         if (url.searchParams.has(paramsName.sort)) {
//           let sort = url.searchParams.get(paramsName.sort).replaceAll(' ', '-');
//           sort = sort.replaceAll('+', '-');
//           sortDesktopElement.each(function () {
//             let elementValue = $(this).attr('data-value');
//             if (sort.toLowerCase() === elementValue.toLowerCase()) {
//               $(this).attr('checked', 'checked');
//             }
//           });

//           const label = getValueLabel('sort', sort, false);
//           setTimeout(() => {
//             sortDropdownDesktopElement?.find($('span.display__text'))?.text(label);
//           }, 300);
//         } else {
//           paramsValue.sort = DEFAULT_SORT_PARAMS;
//         }

//         if (url.searchParams.has(paramsName.products)) {
//           let products = url.searchParams.get(paramsName.products).replaceAll(' ', '-');
//           products = products.replaceAll('+', '-');
//           const inputElement = productDesktopElement?.find($('input.input__checkbox'));
//           inputElement.each(function () {
//             const elementValue = $(this).val();
//             if (products.includes(elementValue.toLowerCase())) {
//               $(this).prop('checked', true);
//             }
//           });
//         }

//         if (url.searchParams.has(paramsName.memberships)) {
//           triggerFilterByElement(membershipDesktopElement, paramsName.memberships);
//         }

//         if (url.searchParams.has(paramsName.cardTypes)) {
//           const cardTypeParams = getCardTypeChildrenKey();
//           const inputElement = cardTypeDesktopElement?.find($('input.input__checkbox'));
//           inputElement.each(function () {
//             const prefixParentTag = $(this)
//               .closest('.offer-filter__checkbox-wrapper')
//               .find('.checkbox-parent .input__checkbox')
//               .val();
//             const elementValue = $(this).val();
//             if (cardTypeParams.includes(prefixParentTag + '/' + elementValue.toLowerCase())) {
//               $(this).prop('checked', true);
//             }
//             updateCheckboxStates();
//           });
//         }

//         if (url.searchParams.has(paramsName.types)) {
//           let types = url.searchParams.get(paramsName.types).replaceAll(' ', '-');
//           types = types.replaceAll('+', '-');
//           const inputElement = categoryDesktopElement?.find($('input.input__checkbox'));
//           inputElement.each(function () {
//             const elementValue = $(this).val();
//             if (types.includes(elementValue.toLowerCase())) {
//               $(this).prop('checked', true);
//             }
//           });
//         }

//         if (url.searchParams.has(paramsName.partner)) {
//           const partner = url.searchParams.get(paramsName.partner).replaceAll(' ', '-').replaceAll('+', '-');
//           const label = getValueLabel('merchant', partner, false);
//           merchantDesktopElement?.find($('span.display__text'))?.text(label);
//         }

//         if (url.searchParams.has(paramsName.location)) {
//           const location = url.searchParams.get(paramsName.location).replaceAll(' ', '-').replaceAll('+', '-');
//           const label = getValueLabel('location', location, false);
//           locationDesktopElement?.find($('span.display__text'))?.text(label);
//         }
//       } else {
//         if (url.searchParams.has(paramsName.sort)) {
//           let sort = url.searchParams.get(paramsName.sort).replaceAll(' ', '-');
//           sort = sort.replaceAll('+', '-');
//           setTimeout(() => {
//             const label = getValueLabel('sort', sort, false);
//             sortMobileElement?.find($('span.display__text'))?.text(label);
//             sortDropdownDesktopElement?.find($('span.display__text'))?.text(label);
//           }, 500);
//         } else {
//           paramsValue.sort = DEFAULT_SORT_PARAMS;
//         }

//         if (url.searchParams.has(paramsName.cardTypes)) {
//           const cardTypeParams = getCardTypeChildrenKey();
//           const inputElement = cardTypeMobileElement?.find($('input.input__checkbox'));
//           inputElement.each(function () {
//             const prefixParentTag = $(this)
//               .closest('.offer-filter__checkbox-wrapper')
//               .find('.checkbox-parent .input__checkbox')
//               .val();
//             const elementValue = $(this).val();
//             if (cardTypeParams.includes(prefixParentTag + '/' + elementValue.toLowerCase())) {
//               $(this).prop('checked', true);
//             }
//             updateCheckboxStates();
//           });
//         }

//         if (url.searchParams.has(paramsName.products)) {
//           let products = url.searchParams.get(paramsName.products).replaceAll(' ', '-');
//           products = products.replaceAll('+', '-');
//           const inputElement = productMobileElement?.find($('input.input__checkbox'));
//           inputElement.each(function () {
//             const elementValue = $(this).val();
//             if (products.includes(elementValue.toLowerCase())) {
//               $(this).prop('checked', true);
//             }
//           });
//         }

//         if (url.searchParams.has(paramsName.memberships)) {
//           triggerFilterByElement(membershipMobileElement, paramsName.memberships);
//         }

//         if (url.searchParams.has(paramsName.types)) {
//           let types = url.searchParams.get(paramsName.types).replaceAll(' ', '-');
//           types = types.replaceAll('+', '-');
//           const inputElement = categoryMobileElement?.find($('input.input__checkbox'));
//           inputElement.each(function () {
//             const elementValue = $(this).val();
//             if (types.includes(elementValue.toLowerCase())) {
//               $(this).prop('checked', true);
//             }
//           });
//         }

//         if (url.searchParams.has(paramsName.partner)) {
//           const partner = url.searchParams.get(paramsName.partner);
//           merchantMobileElement?.find($('span.display__text'))?.text(capitalizeFirstLetter(partner));
//         }

//         if (url.searchParams.has(paramsName.location)) {
//           const location = url.searchParams.get(paramsName.location);
//           locationMobileElement?.find($('span.display__text'))?.text(capitalizeFirstLetter(location));
//         }
//       }
//     }
//   }

//   function capitalizeFirstLetter(text) {
//     return `${text.charAt(0).toUpperCase()}${text.slice(1)}`;
//   }

//   function getEndpoint() {
//     let endpoint = `${dataURL}.offerlisting.json?${paramsName.limit}=${paramsValue.limit}&${paramsName.offset}=${paramsValue.offset}`;
//     if (url.searchParams.has(paramsName.sort)) {
//       endpoint += `&${paramsName.sort}=${url.searchParams.get(paramsName.sort).toString()}`;
//     } else if (paramsValue.sort) {
//       endpoint += `&${paramsName.sort}=${paramsValue.sort.split(' ').join('+')}`;
//     }

//     if (url.searchParams.has(paramsName.products)) {
//       endpoint += `&${paramsName.products}=${url.searchParams.get(paramsName.products).toString()}`;
//     } else if (paramsValue.products) {
//       endpoint += `&${paramsName.products}=${paramsValue.products}`;
//     }

//     if (url.searchParams.has(paramsName.memberships)) {
//       endpoint += `&${paramsName.memberships}=${url.searchParams.get(paramsName.memberships)}`;
//     } else if (paramsValue.products) {
//       endpoint += `&${paramsName.memberships}=${paramsValue.memberships}`;
//     }

//     if (url.searchParams.has(paramsName.cardTypes)) {
//       const cardTypeParams = getCardTypeChildrenKey();
//       endpoint += `&${paramsName.cardTypes}=${cardTypeParams?.join(',')}`;
//     } else if (paramsValue.products) {
//       endpoint += `&${paramsName.cardTypes}=${paramsValue.cardTypes}`;
//     }

//     if (url.searchParams.has(paramsName.types)) {
//       endpoint += `&${paramsName.types}=${url.searchParams.get(paramsName.types).toString()}`;
//     } else if (paramsValue.types) {
//       endpoint += `&${paramsName.types}=${paramsValue.types}`;
//     }

//     if (url.searchParams.has(paramsName.partner)) {
//       endpoint += `&${paramsName.partner}=${url.searchParams.get(paramsName.partner).toString()}`;
//     }

//     if (url.searchParams.has(paramsName.location)) {
//       endpoint += `&${paramsName.location}=${url.searchParams.get(paramsName.location).toString()}`;
//     }

//     if (url.searchParams.has(paramsName.searchText)) {
//       const searchText = url.searchParams.get(paramsName.searchText).toString();
//       endpoint += `&searchText=${encodeURIComponent(searchText)}&isSearch=true`;
//     } else if (paramsValue.types) {
//       endpoint += `&searchText=${encodeURIComponent(paramsValue.searchText)}&isSearch=true`;
//     }

//     return endpoint;
//   }

//   function getAllPromotions() {
//     const endpoint = `${dataURL}.offerlisting.json?${paramsName.limit}=0&${paramsName.offset}=0&${paramsName.sort}=${DEFAULT_SORT_PARAMS}`;
//     fetchData(endpoint).then((response) => {
//       promotionResultsNoText = response.results;
//     });
//   }

//   function getActiveFilterNames(url) {
//     const urlParamObj = LocationUtil.getUrlParamObj();
//     if (Object.keys(urlParamObj).length === 0) {
//       return [];
//     }
//     const includeParams = [
//       paramsName.products,
//       paramsName.memberships,
//       paramsName.types,
//       paramsName.partner,
//       paramsName.location,
//       paramsName.membership,
//       paramsName.cardTypes,
//     ];
//     return Object.keys(urlParamObj).filter((paramName) => includeParams.includes(paramName));
//   }
//   function updateActiveFilterPramsButton() {
//     const apiUrl = getEndpoint();
//     const btnOpenFilterTotal = dataElement.find('.btn-open-filter__total');
//     const activeFilterNames = getActiveFilterNames(apiUrl);
//     if (activeFilterNames.length > 0) {
//       btnOpenFilterTotal.show();
//       btnOpenFilterTotal.text(activeFilterNames.length);
//     } else {
//       btnOpenFilterTotal.hide();
//     }
//     $('.btn-open-filter[data-filter-type]').removeClass('active');
//     $('.btn-open-filter[data-filter-type]').each(function () {
//       $(this).find('.tcb-filter-button-title').html($(this).attr('data-filter-name'));
//     });

//     let buttonLabels = getFilterParams(activeFilterNames);

//     activeFilterNames.forEach((activeFilterName) => {
//       let selectorButton = activeFilterName;

//       if (activeFilterName === 'card-types') {
//         selectorButton = 'products';
//       }

//       const activeFilterNameSelector = $(`.btn-open-filter[data-filter-type="${selectorButton}"]`);
//       activeFilterNameSelector.addClass('active');

//       const buttonTitle = activeFilterNameSelector.find('.tcb-filter-button-title');
//       if (Object.keys(buttonLabels)?.length) {
//         const label = getFilterButtonLabel(buttonLabels[selectorButton]);
//         buttonTitle.html(label);
//       } else {
//         buttonTitle.html(activeFilterNameSelector.attr('data-filter-name'));
//       }
//     });
//   }

//   function getFilterParams(activeParams) {
//     let buttons = {};

//     activeParams.forEach((activeParam) => {
//       const searchParams = url.searchParams.get(activeParam);
//       const params = searchParams.split(',');
//       let labels = [];
//       let dataFieldName = activeParam;
//       let buttonField = activeParam;

//       if (activeParam === 'card-types') {
//         dataFieldName = 'cards';
//         buttonField = 'products';
//       }
//       if (activeParam === 'location') {
//         dataFieldName = 'locations';
//       }
//       if (activeParam === 'location') {
//         dataFieldName = 'locations';
//       }
//       if (activeParam === 'partner') {
//         dataFieldName = 'partners';
//       }

//       params.forEach((param) => {
//         let value = param?.replaceAll(' ', '-');
//         value = value?.replaceAll('+', '-');
//         labels.push(getValueLabel(dataFieldName, value));
//       });

//       buttons = {
//         ...buttons,
//         [buttonField]: [...(buttons[buttonField] || []), ...labels],
//       };
//     });

//     return buttons;
//   }

//   function getFilterButtonLabel(labels) {
//     return labels.length > 1 ? `${labels[0]}, +${labels.length - 1}` : `${labels[0]}`;
//   }

//   function getValueLabel(paramName, preValue, removeLastChar = true) {
//     let value = preValue.split('/').pop();
//     let singleName = paramName;

//     if (removeLastChar) {
//       singleName = paramName?.substr(0, paramName?.length - 1);
//     }

//     const attrName = `data-${singleName}`;
//     const inputElement = document.querySelector(`[${attrName}][value='${value}']`);

//     let label = inputElement?.getAttribute(attrName);
//     return label;
//   }

//   function getPromotions() {
//     const api = getEndpoint();
//     if (dataElement.length > 0 && $(window).width() < MOBILE_SIZE) {
//       updateActiveFilterPramsButton();
//     }
//     fetchData(api).then((response) => {
//       promotionData = response;
//       if (promotionData.total && Number(promotionData.total) > 0) {
//         const sort = url.searchParams.get(paramsName.sort);
//         let promotions = [];

//         if (!sort && promotionData.results.length > 6) {
//           let favoriteItem = promotionData.results.splice(0, 6);
//           const nonFavoriteItemIndex = promotionData.results.findIndex((item) => /false/.test(item?.favourite));
//           const nonFavoriteItem = promotionData.results.splice(nonFavoriteItemIndex, 1)[0];
//           promotionData.results.unshift(nonFavoriteItem);
//           favoriteItem = favoriteItem.map((item) => ({ ...item, isFavorite: true }));
//           promotions = [...favoriteItem, ...promotionData.results];
//         } else {
//           promotions = promotionData.results;
//         }

//         totalItem = promotionData.total;
//         totalPage = Math.ceil(totalItem / paramsValue.limit);

//         if (promotions.length > 0) {
//           renderPromotionCard(promotions);
//         } else {
//           renderEmptyPromotionCard();
//         }
//       } else {
//         renderEmptyPromotionCard();
//       }
//     });
//   }

//   function fetchData(url) {
//     return new Promise(function (resolve, reject) {
//       $.ajax({
//         url: url,
//         method: 'get',
//         contentType: 'application/json',
//         success: function (response) {
//           resolve(response);
//         },
//         error: function (xhr, status, error) {
//           reject(error);
//         },
//       });
//     });
//   }

//   function renderPromotionCard(response) {
//     const cardPromotionList = $('.card-promotion__list');
//     cardPromotionList.empty();
//     response.forEach((item) => {
//       const element = renderPromotionCardItem(item);
//       cardPromotionList.append(element);
//     });
//     handleClickDetail();
//     handleClickPdf();
//     renderExpiryDate();
//     renderPromotionCount();
//     renderPagination();
//     renderProductTypes();
//   }

//   function handleClickPdf() {
//     const selectorPdfLink = `.card-promotion__item-wrapper .card-content__link a[href]:not(${SELECTOR_DETAIL_LINK})`;
//     $(selectorPdfLink).click(showPreviewModalListener);
//   }

//   function showPreviewModalListener(event) {
//     const target = event.currentTarget;
//     const previewURL = $(target)?.attr('href');
//     const previewTitle = 'Preview';
//     const previewLoadingLabel = 'Loading PDF...';
//     if (previewURL && previewURL.includes('.pdf')) {
//       event.preventDefault();
//       const previewModalElement = $('.popup-download');
//       previewModalElement.trigger('show-preview-modal', [
//         {
//           url: previewURL,
//           documentType: 'pdf',
//           title: previewTitle,
//           loadingLabel: previewLoadingLabel,
//           downloadLabel: dataDownloadPdfLabel,
//         },
//       ]);
//     }
//   }

//   function renderPromotionCardItem(item) {
//     const isDetailView = item.isDetailView;
//     let offerLink = item.url || '';
//     if (isDetailView === 'true') {
//       offerLink = `?${paramsName.detail}=${item.name}`;
//     }
//     return `
//             <div class="card-promotion__item-wrapper">
//                 <div class="card-promotion__item">
//                     <input type="hidden" data-product-types="${item?.products?.join() || ''}" />
//                     <div class="card-promotion__item-product-types"></div>
//                     <div class="card-promotion__item-body">
//                       <div class="card-offer__image">
//                           <span class="card-offer__image--wrapper">
//                               <span class="image-card-offer">
//                                   <img alt="default-thumbnail" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27620%27%20height=%27348%27/%3e">
//                               </span>
//                               <img class="card-offer__image--thumbnail" src="${item?.thumbnail || ''}" alt="card-image"/>
//                           </span>
//                       </div>
//                       <div class="card-offer__content">
//                         <span class="card-content__label">
//                             ${item?.products?.join(', ') || ''}
//                         </span>
//                         <span class="card-content__title">
//                             ${item?.partner?.join(', ') || ''}
//                         </span>
//                         <span class="card-content__description">
//                             ${item?.description || ''}
//                         </span>
//                         <div class="expiry-date-section-wrapper">
//                           <div class="expiry-date-section" data-expiry="${item?.expiryDate}"></div>
//                           <div class="card-content__favorite-promo ${item?.favourite === 'true' && item?.isFavorite ? 'display-unset' : 'display-none'}">
//                               <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/fav-icon.png"
//                                   alt="fav-icon"
//                               />
//                           </div>
//                         </div>
//                         <div class="card-content__link">
//                             <a href="${offerLink}"
//                                 target="${item?.openInNewTab === 'true' ? '_blank' : '_self'}" rel="noopener noreferrer"
//                                 class="card-content__link--button" data-tracking-click-event="viewOfferCTA"
//                                 data-preview-title="Preview"
//                                 data-name="${item.name}"
//                                 data-preview-loading-label="Loading PDF..."
//                                 data-preview-download-label="${dataDownloadPdfLabel}"
//                                 data-tracking-offer-info-value="{'viewOfferTitle':'${item?.partner?.join()} ${item?.products?.join()} ${item?.memberships?.join()}'}"
//                                 data-tracking-web-interaction-value="{'webInteractions': {'name': '${dataComponentName} | ${dataViewMore}','type': '${item?.interactionType}'}}">
//                                     <span class="button--text">${dataViewMore || ''}</span>
//                                     <div class="button--icon">
//                                         <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow.svg"
//                                             alt="red-arrow"/>
//                                     </div>
//                             </a>
//                         </div>
//                     </div>
//                   </div>
//                 </div>
//             </div>
//         `;
//   }

//   function renderEmptyPromotionCard() {
//     const promotionTotalCount = $('.promotion-total-count');
//     const offer = $('.offer-listing-promotions');
//     const countElement =
//       offer && offer.attr('data-search-count') ? offer.attr('data-search-count').replace('%count', '<b>0</b>') : '';
//     promotionTotalCount.empty();
//     promotionTotalCount.append(countElement);

//     const cardPromotionList = $('.card-promotion__list');
//     const emptyElement = `
//             <div class="card-promotion__item-wrapper card-promotion__item-empty">
//                 <span>${dataEmptyPromotion}</span>
//             </div>
//         `;
//     cardPromotionList.empty();
//     cardPromotionList.append(emptyElement);

//     const pagination = $('.pagination');
//     pagination.empty();
//   }

//   function renderExpiryDate(isRelated = false, element = null) {
//     const expiryDateElement = isRelated ? element.find('.expiry-date-section') : $('.expiry-date-section');
//     expiryDateElement.each(function () {
//       const dataExpiryDate = $(this).attr('data-expiry');
//       let expiredDate;
//       if (dataLanguageLabel === LOCALE_VI) {
//         const dateParts = dataExpiryDate.split(SLASH);
//         expiredDate = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0], 23, 59, 59);
//       } else if (dataLanguageLabel === LOCALE_EN) {
//         const dateFormated = 'DD MMM YYYY';
//         const endOfDate = moment(dataExpiryDate, dateFormated).add('days', 1);
//         expiredDate = endOfDate.toDate();
//         $(this).attr('data-expiry-time', endOfDate.toISOString());
//       }
//       const duration = expiredDate.getTime() - Date.now();
//       const durationOf3Days = 259_200_000;
//       const showExpiryCountdownBar = duration >= 0 && duration < durationOf3Days;
//       if (showExpiryCountdownBar) {
//         const intervalID = setInterval(() => {
//           const remainingTime = expiredDate.getTime() - Date.now();
//           const days = Math.floor(remainingTime / (24 * 60 * 60 * 1000));
//           const daysms = remainingTime % (24 * 60 * 60 * 1000);
//           const hours = Math.floor(daysms / (60 * 60 * 1000));
//           const hoursms = remainingTime % (60 * 60 * 1000);
//           const minutes = Math.floor(hoursms / (60 * 1000));
//           const minutesms = remainingTime % (60 * 1000);
//           const seconds = Math.floor(minutesms / 1000);
//           const remainingData = {
//             day: days,
//             time: `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`,
//           };
//           const dayNumber = remainingData.day > 0 ? ` ${remainingData.day} ${dayLabel} ` : ' ';
//           const remainingText = `${dataDay}${dayNumber}<span class="card-content__expired-date--time">${remainingData.time}</span>`;
//           const progressBarWidth = Number(100 - (remainingTime / durationOf3Days) * 100).toFixed();
//           const countdownElement = `
//             <div class="card-content__expired-date-count-down">
//               <span class="card-content__expired-date--date-time">${remainingText}</span>
//               <div class="progress-bar">
//                 <div class="progress-bar__inner" style="width: ${progressBarWidth}%"></div>
//               </div>
//             </div>
//           `;
//           if (remainingTime <= 0) {
//             clearInterval(intervalID);
//           }
//           $(this).empty();
//           $(this).append(countdownElement);
//         }, 1000);
//       } else {
//         const expiredDateElement = `
//             <div class="card-content__expired-date">
//                 <span class="card-content__expired-date--date-time">${dataExpiryLabel} ${dataExpiryDate || ''}</span>
//             </div>
//         `;
//         $(this).empty();
//         $(this).append(expiredDateElement);
//       }
//     });
//   }

//   function renderProductTypes() {
//     const productTypesElements = $('.card-promotion__item-product-types');
//     productTypesElements.each(function () {
//       $(this).empty();
//       const dataProductTypes = (
//         $(this).prev('input[type="hidden"][data-product-types]').attr('data-product-types') || ''
//       )
//         .split(',')
//         .filter((type) => type.length > 0);
//       if (dataProductTypes.length > 0) {
//         dataProductTypes.forEach((dataProductTypeItem) => {
//           const newProductTypeDiv = document.createElement('div');
//           newProductTypeDiv.className = 'card-promotion__item-product-type';
//           newProductTypeDiv.innerText = dataProductTypeItem;
//           $(this).append(newProductTypeDiv);
//         });
//       } else {
//         $(this).hide();
//       }
//     });
//   }

//   function renderPromotionCount() {
//     const promotionTotalCount = $('.promotion-total-count');
//     promotionTotalCount.empty();
//     const searchCountLabel = dataSearchCount.replace('%count', `<b>${totalItem}</b>`);
//     promotionTotalCount.append(searchCountLabel);
//   }

//   function updatePagination() {
//     const previousElement = $('.pag-container--previous');
//     const nextElement = $('.pag-container--next');
//     const numberWrapperElements = $('.pag-number');

//     const numberLength = 6;
//     const firstPosition = 0;
//     const lastPosition = totalPage - 1;

//     if (totalPage <= numberLength) {
//       previousElement.remove();
//       nextElement.remove();
//       return;
//     }

//     if (previousElement && currentPage === firstPosition + 1) {
//       previousElement.addClass('disable');
//     } else if (nextElement && currentPage === lastPosition + 1) {
//       nextElement.addClass('disable');
//     }

//     numberWrapperElements.addClass('pag-scroll');
//   }

//   function renderPagination() {
//     const pagination = $('.pagination');

//     if (totalPage <= 1) {
//       pagination.hide();
//       return;
//     }
//     pagination.show();

//     const backDisableIcon = `<img class="prev"
//       src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-arrow-left-disable.svg" />`;
//     const nextDisableIcon = `<img class="next"
//       src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-arrow-left-disable.svg" />`;
//     const backIcon = `<img class="prev"
//       src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-arrow-left.svg" />`;
//     const nextIcon = `<img class="next"
//       src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-arrow-left.svg" />`;

//     const previous = `<label class="pag-container--previous">${backDisableIcon}${backIcon}</label>`;
//     const next = `<label class="pag-container--next">${nextDisableIcon}${nextIcon}</label>`;
//     pagination.empty();

//     const pagContainer = '<div class="pag-container"></div>';
//     pagination.append(pagContainer);

//     const pagContainerElement = $('.pagination .pag-container');
//     pagContainerElement.append(previous);

//     const pageNumberElement = document.createElement('div');
//     pageNumberElement.setAttribute('class', 'pag-number');

//     let activeElement = null;

//     for (let i = 1; i <= totalPage; i++) {
//       const element = document.createElement('a');
//       element.setAttribute('class', `${i === currentPage ? 'active' : ''}`);
//       const span = document.createElement('span');
//       span.textContent = i;
//       element.appendChild(span);
//       pageNumberElement.appendChild(element);

//       if (i === currentPage) {
//         activeElement = element;
//       }
//     }

//     pagContainerElement.append(pageNumberElement);
//     pagContainerElement.append(next);

//     setTimeout(() => {
//       const containerOffsetLeft = pageNumberElement.offsetLeft;
//       pageNumberElement.scrollLeft = activeElement.offsetLeft - containerOffsetLeft;
//     }, 100);

//     updatePagination();

//     const aTagElement = $('.pagination .pag-container a');
//     const previousElement = $('.pagination .pag-container label.pag-container--previous');
//     const nextElement = $('.pagination .pag-container label.pag-container--next');

//     aTagElement.click(function () {
//       if ($(this).length > 0) {
//         currentPage = Number($(this).text());
//         onPageChange(currentPage, true);
//       }
//     });

//     previousElement.click(function () {
//       if ($(this).length > 0) {
//         if (currentPage && Number(currentPage) > 1) {
//           currentPage -= 1;
//           onPageChange(currentPage, true);
//         }
//       }
//     });

//     nextElement.click(function () {
//       if ($(this).length > 0) {
//         if (currentPage && Number(currentPage) < totalPage) {
//           currentPage += 1;
//           onPageChange(currentPage, true);
//         }
//       }
//     });
//   }

//   function getDetailEndpoint(detailName) {
//     let endpoint = `${dataURL}.offerdetail.json?${paramsName.detailName}=${detailName}`;
//     return endpoint;
//   }

//   $(document).on('click', '.offer-listing-detail__wrapper .offer-info__link', function (event) {
//     event.preventDefault();

//     const previewURL = $(this)?.attr('href');
//     const previewTitle = $(this)?.attr('data-preview-title');
//     const previewLoadingLabel = $(this)?.attr('data-preview-loading-label');
//     const previewDownloadLabel = $(this)?.attr('data-preview-download-label');

//     if (previewURL && previewURL.includes('.pdf')) {
//       const previewModalElement = $('.popup-download');

//       previewModalElement.trigger('show-preview-modal', [
//         {
//           url: previewURL,
//           documentType: 'pdf',
//           title: previewTitle,
//           loadingLabel: previewLoadingLabel,
//           downloadLabel: previewDownloadLabel,
//         },
//       ]);
//     }
//   });

//   function lockHtmlScroll(isUnLock = false) {
//     const htmlElement = $('html')[0];
//     if (htmlElement && !isUnLock) {
//       htmlElement.style.overflow = 'hidden';
//     } else {
//       htmlElement.style.overflow = '';
//     }
//   }

//   function openDetailModal(detailName) {
//     fetchData(getDetailEndpoint(detailName)).then((response) => {
//       const { detail: detailData, locales } = response;
//       if (detailData) {
//         offerListingComponent.fadeOut(300);

//         const existDetailPopup = document.getElementsByTagName('tcb-offer-listing-detail')?.[0];
//         if (existDetailPopup) {
//           existDetailPopup.remove();
//         }
//         const detailPopup = document.createElement('tcb-offer-listing-detail');
//         detailPopup.addContent(dialogDetailData, detailData, dataURL, dataLanguageLabel, {
//           renderPromotionCardItem: renderPromotionCardItem,
//           renderExpiryDate: renderExpiryDate,
//           showPreviewModalListener: showPreviewModalListener,
//         });

//         disableBodyScroll();

//         const headerHeight = headerElement?.offsetHeight || 0;
//         const detailPlaceholder = document.getElementById('detail-container');
//         if (detailPlaceholder) {
//           detailPlaceholder.setAttribute('data-locales', locales?.join(','));
//           detailPlaceholder.classList.add('open');
//           detailPlaceholder.appendChild(detailPopup);
//           detailPlaceholder.scrollTop = headerHeight * -1;
//         }
//       }
//     });
//   }

//   function setVariableHeaderHeight(headerElementChanged) {
//     const headerHeight = convertPxToRem(headerElementChanged?.offsetHeight) || 0;
//     detailPlaceholderElement.style.setProperty(HEADER_HEIGHT_VARIABLE, headerHeight + 'rem');
//   }

//   function handleSetHeaderHeight() {
//     if (headerElement && detailPlaceholderElement) {
//       const headerDelayTime = 300;
//       setTimeout(() => {
//         setVariableHeaderHeight(headerElement);
//         listenerElement(headerElement, (_) => {
//           const headerElementRefreshed = $('header')?.[0];
//           setVariableHeaderHeight(headerElementRefreshed);
//         });
//       }, headerDelayTime);
//     }
//   }

//   function handleBackBrowserDetail() {
//     window.addEventListener('popstate', (_) => {
//       const detailPlaceholder = $('#detail-container');
//       if (detailPlaceholder?.length && detailPlaceholder.hasClass('open')) {
//         detailPlaceholder.removeClass('open');
//         detailPlaceholder.empty();
//         $('.offer-listing-filter__body').fadeIn(400);
//         disableBodyScroll(false);
//         handleResetDropdownLanguage();
//       }
//     });
//   }

//   function handleClickDetail() {
//     const selectorLink = `.card-promotion__item-wrapper .card-content__link a${SELECTOR_DETAIL_LINK}`;
//     $(selectorLink).click((event) => {
//       event.preventDefault();
//       const detailName = $(event.currentTarget).data('name');

//       addCurrentUrlHistory();
//       updateSearchParams({ [paramsName.detail]: detailName });
//       openDetailModal(detailName);
//     });
//   }

//   function onPageChange(changedPage, scrollToList = false) {
//     paramsValue.offset = (changedPage - 1) * paramsValue.limit;

//     const promotionList = $('.card-promotion__list');
//     if (scrollToList && promotionList?.length) {
//       let width = $(window).width();
//       let offset = 250;
//       if (width < MOBILE_SIZE) {
//         offset = 280;
//       }
//       $('html, body').animate(
//         {
//           scrollTop: promotionList.offset().top - offset,
//         },
//         500,
//       );
//     }

//     updatePagination();
//     getPromotions();
//   }

//   function getSelectedCheckbox(element, paramName) {
//     const selectedElement = element.find($('input.input__checkbox:checked'));
//     if (selectedElement.length === 0) {
//       removeURLParams(paramName, true);
//       paramsValue[paramName] = '';
//       return;
//     }

//     const selectedItem = selectedElement
//       .map(function () {
//         return $(this)?.val()?.toLowerCase()?.split('-')?.join(' ');
//       })
//       .get();

//     if (selectedItem.length === element.children().length) {
//       removeURLParams(paramName, true);
//       paramsValue[paramName] = selectedItem.join().split('-').join('+').split(',').join('%2C');
//     } else {
//       setURLParams(paramName, selectedItem.join(), true);
//       paramsValue[paramName] = '';
//     }
//   }

//   function setURLParams(name, params, replaceState) {
//     url = new URL(window.location);
//     url.searchParams.set(name, params);
//     url.searchParams.sort();
//     if (replaceState) {
//       window.history.replaceState({}, '', url);
//     }
//   }

//   function removeURLParams(name, replaceState) {
//     url.searchParams.delete(name);
//     url.searchParams.sort();
//     if (replaceState) {
//       window.history.replaceState({}, '', url);
//     }
//   }

//   function selectKeyword(direction, parentElement) {
//     const inputDropdownItemElements = $(parentElement).find('.input-dropdown ul li');
//     textInputSearch = searchTextInputElement.val();
//     if (!inputDropdownItemElements.hasClass('input-dropdown__item--active')) {
//       inputDropdownItemElements.first().addClass('input-dropdown__item--active');
//       searchTextInputElement.val(inputDropdownItemElements.first().text());
//       return;
//     }
//     for (let i = 0; i < inputDropdownItemElements.length; i++) {
//       if (inputDropdownItemElements.eq(i).hasClass('input-dropdown__item--active')) {
//         let newIndex = i + direction;
//         if (newIndex < 0) {
//           newIndex = inputDropdownItemElements.length - 1;
//         }
//         if (newIndex > inputDropdownItemElements.length - 1) {
//           newIndex = 0;
//         }
//         inputDropdownItemElements.eq(i).removeClass('input-dropdown__item--active');
//         inputDropdownItemElements.eq(newIndex).addClass('input-dropdown__item--active');
//         searchTextInputElement.val(inputDropdownItemElements.eq(newIndex).text());
//         break;
//       }
//     }
//   }

//   function sortOffers(element, selector) {
//     if ($(element).length === 0) {
//       return;
//     }

//     const selectedItem = $(element)?.find($(selector))?.val()?.toLowerCase();

//     if (selectedItem === 'most-popular') {
//       removeURLParams(paramsName.sort, true);
//       paramsValue.sort = DEFAULT_SORT_PARAMS;
//     } else {
//       setURLParams(paramsName.sort, selectedItem?.split('-')?.join(' '), true);
//       paramsValue.sort = '';
//     }

//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   }

//   sortDesktopElement.click(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     const selectedItem = $(this)?.attr('data-value');
//     if (selectedItem === 'most-popular') {
//       removeURLParams(paramsName.sort, true);
//       paramsValue.sort = DEFAULT_SORT_PARAMS;
//     } else {
//       setURLParams(paramsName.sort, selectedItem?.split('-')?.join(' '), true);
//       paramsValue.sort = '';
//     }

//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   function filterByLocation(element, selector) {
//     if ($(element).length === 0) {
//       return;
//     }

//     const selectedItem = $(element)?.find($(selector))?.val()?.toLowerCase()?.split('-')?.join(' ');

//     if (selectedItem === 'all') {
//       removeURLParams(paramsName.location, true);
//     } else {
//       setURLParams(paramsName.location, selectedItem, true);
//     }

//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   }

//   function handleOnChangeNestedCheckbox() {
//     updateCheckboxStates();
//     getSelectedCardCheckbox($(this).closest('.card-checkbox'), paramsName.cardTypes);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   }

//   productDesktopElement.click(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     getSelectedCheckbox($(this), paramsName.products);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   categoryDesktopElement.click(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     getSelectedCheckbox($(this), paramsName.types);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   membershipDesktopElement.click(function () {
//     if (!$(this).length) {
//       return;
//     }

//     getSelectedCheckbox($(this), paramsName.memberships);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   merchantDesktopElement.change(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     const selectedItem = $(this)
//       ?.find($('input.dropdown-selected-value.merchant-desktop-element'))
//       ?.val()
//       ?.toLowerCase()
//       ?.split('-')
//       ?.join(' ');

//     if (selectedItem === 'all') {
//       removeURLParams(paramsName.partner, true);
//     } else {
//       setURLParams(paramsName.partner, selectedItem, true);
//     }

//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   sortMobileElement.change(function () {
//     sortOffers(this, 'input.dropdown-selected-value.sort-mobile-element');
//   });

//   sortDropdownDesktopElement.change(function () {
//     sortOffers(this, 'input.dropdown-selected-value.sort-desktop-element');
//   });

//   productMobileElement.click(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     getSelectedCheckbox($(this), paramsName.products);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   membershipMobileElement.click(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     getSelectedCheckbox($(this), paramsName.memberships);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   categoryMobileElement.click(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     getSelectedCheckbox($(this), paramsName.types);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   merchantMobileElement.change(function () {
//     if ($(this).length === 0) {
//       return;
//     }

//     const selectedItem = $(this)
//       ?.find($('input.dropdown-selected-value.merchant-mobile-element'))
//       ?.val()
//       ?.toLowerCase()
//       ?.split('-')
//       ?.join(' ');

//     if (selectedItem === 'all') {
//       removeURLParams(paramsName.partner, true);
//     } else {
//       setURLParams(paramsName.partner, selectedItem, true);
//     }

//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   locationMobileElement.change(function () {
//     filterByLocation(this, 'input.dropdown-selected-value.location-mobile-element');
//   });

//   locationDesktopElement.change(function () {
//     filterByLocation(this, 'input.dropdown-selected-value.location-desktop-element');
//   });

//   function normalizeString(str) {
//     return str
//       ? str
//         .normalize('NFD')
//         .replace(/[\u0300-\u036f]/g, '')
//         .toLowerCase()
//       : '';
//   }

//   searchTextInputElement.each(function (i, inputElement) {
//     inputElement.addEventListener('input', function (event) {
//       const currentValue = event.target.value;
//       searchTextInputElement.each(function (i, otherInputElement) {
//         if (otherInputElement !== inputElement) {
//           otherInputElement.value = currentValue;
//         }
//       });
//     });
//   });

//   searchTextElement.click(function () {
//     paramsValue.offset = 0;
//     textInputSearch = searchTextInputElement.val();
//     setURLParams(paramsName.searchText, textInputSearch, true);
//     url.searchParams.set('isSearch', 'true');
//     if (textInputSearch.length < 1) {
//       removeURLParams(paramsName.searchText, true);
//       removeURLParams('isSearch', true);
//     }
//     getPromotions();
//   });

//   searchTextInputElement.on('input', function () {
//     const textInput = normalizeString(this.value);
//     let listPartner = [];
//     inputDropdownElement.empty();
//     if (textInput.length > 0) {
//       $.each(promotionResultsNoText, function (index, e) {
//         const descriptionNormalized = normalizeString(e.description || '');
//         const partnerNormalized = normalizeString(e?.partner?.join(' ') || '');
//         if (
//           (descriptionNormalized.includes(textInput) || partnerNormalized.includes(textInput)) &&
//           !listPartner.includes(partnerNormalized)
//         ) {
//           listPartner.push(partnerNormalized);
//           inputDropdownElement.append($('<li>').text(e.partner).addClass('input-dropdown-item'));
//         }
//       });
//     }
//     if (inputDropdownElement.children().length > 0) {
//       inputDropdownElement.show();
//     } else {
//       inputDropdownElement.hide();
//     }
//     if (!textInput.length) {
//       removeURLParams(paramsName.searchText, true);
//       removeURLParams('isSearch', true);
//       paramsValue.offset = 0;
//       currentPage = 1;
//       getPromotions();
//     }
//   });

//   searchTextInputElement.on('keydown', function (e) {
//     if (e.keyCode === 13) {
//       paramsValue.offset = 0;
//       textInputSearch = searchTextInputElement.val();
//       setURLParams(paramsName.searchText, textInputSearch, true);
//       url.searchParams.set('isSearch', 'true');
//       inputDropdownElement.hide();
//       searchTextInputElement.blur();
//       if (textInputSearch.length < 1) {
//         removeURLParams(paramsName.searchText, true);
//         removeURLParams('isSearch', true);
//       }
//       getPromotions();
//     }
//   });

//   searchTextInputElement.on('focus', function () {
//     if (inputDropdownElement.children().length > 0) {
//       inputDropdownElement.show();
//     }
//   });

//   searchTextInputElement.on('focusout', function () {
//     inputDropdownElement.hide();
//   });

//   inputDropdownElement.on('mousedown', function (e) {
//     if ($(e.target).hasClass('input-dropdown-item')) {
//       e.stopPropagation();
//       searchTextInputElement.val($(e.target).text());
//       inputDropdownElement.empty();
//       textInputSearch = normalizeString(searchTextInputElement.val());
//       setURLParams(paramsName.searchText, textInputSearch, true);
//       url.searchParams.set('isSearch', 'true');
//       if (textInputSearch.length < 1) {
//         removeURLParams(paramsName.searchText, true);
//         removeURLParams('isSearch', true);
//       }
//       getPromotions();
//     }
//   });

//   searchBoxElement.on('keydown', function (e) {
//     if ((e.keyCode !== UP_ARROW_KEY && e.keyCode !== DOWN_ARROW_KEY) || !$(this).find('.input-dropdown ul li').length) {
//       return;
//     }
//     e.preventDefault();
//     switch (e.keyCode) {
//       case UP_ARROW_KEY:
//         selectKeyword(-1, this);
//         break;
//       case DOWN_ARROW_KEY:
//         selectKeyword(1, this);
//         break;
//     }
//   });

//   function updateCheckboxStates() {
//     $('.offer-filter__checkbox-wrapper').each(function () {
//       let parentCheckbox = $(this).find('.checkbox-parent input[type="checkbox"]');
//       let childCheckboxes = $(this).find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]');
//       let anyChecked = childCheckboxes.filter(':checked').length > 0;
//       let allChecked = childCheckboxes.length === childCheckboxes.filter(':checked').length;

//       parentCheckbox.prop('checked', anyChecked);

//       if (allChecked) {
//         parentCheckbox.addClass('all-checked');
//       } else {
//         parentCheckbox.removeClass('all-checked');
//       }
//     });
//   }

//   function getCardTypeByParentKey(isIncludeOneLevel = false) {
//     let value = {};
//     let cardTypeParents = $('.card-checkbox:not(.card-checkbox-mobile) .checkbox-parent');
//     cardTypeParents.each((_, parentElement) => {
//       const parent = $(parentElement);
//       const parentKey = parent.find('.input__checkbox')?.val()?.toLowerCase();
//       const children = parent.next().find('.input__checkbox');
//       let allChildValues = [];
//       children.each((_, childElement) => {
//         const childKey = $(childElement).val()?.toLowerCase();
//         if (parentKey !== childKey || isIncludeOneLevel) {
//           allChildValues.push(`${parentKey}/${childKey}`);
//         }
//       });
//       value[parentKey] = allChildValues;
//     });
//     return value;
//   }

//   function getCardTypeChildrenKey() {
//     const allCard = getCardTypeByParentKey();
//     const cardTypesParams = url.searchParams.get(paramsName.cardTypes).split(',');
//     let outputParam = [];
//     cardTypesParams?.forEach((param) => {
//       const children = allCard[param];
//       const childKeys = children?.length ? children : [param];
//       if (childKeys?.length) {
//         outputParam = outputParam.concat(childKeys);
//       }
//     });
//     return outputParam;
//   }

//   function getSelectedCardCheckbox(element, paramName) {
//     const selectedElement = element.find($('.checkbox-child input.input__checkbox:checked'));

//     if (selectedElement.length === 0) {
//       removeURLParams(paramName, true);
//       paramsValue[paramName] = '';
//       return;
//     }

//     const selectedKeys = selectedElement
//       .map(function () {
//         let prefixParentTag = $(this)
//           .closest('.offer-filter__checkbox-wrapper')
//           .find('.checkbox-parent .input__checkbox')
//           .val();
//         return prefixParentTag + SLASH + $(this)?.val()?.toLowerCase();
//       })
//       .get();

//     let outputParam = [...selectedKeys];

//     const cardTypeKeys = getCardTypeByParentKey(true);
//     Object.keys(cardTypeKeys)?.forEach((cardTypeParentKey) => {
//       const allChildValues = cardTypeKeys[cardTypeParentKey];
//       let includeValues = [];
//       let otherChildValues = [];

//       outputParam.forEach((selectedKey) => {
//         if (allChildValues.includes(selectedKey)) {
//           includeValues.push(selectedKey);
//         } else if (!otherChildValues.includes(selectedKey)) {
//           otherChildValues.push(selectedKey);
//         }
//       });

//       if (includeValues?.length && includeValues.length === allChildValues.length) {
//         outputParam = [...otherChildValues, cardTypeParentKey];
//       }
//     });

//     setURLParams(paramName, outputParam.join(), true);
//     paramsValue[paramName] = '';
//   }

//   $('.offer-filter__title-collapse').click(function (event) {
//     event.preventDefault();
//     event.stopPropagation();
//     const collapsedElementSelector = $(this).attr('data-collapsed-element');
//     if (!collapsedElementSelector) {
//       return;
//     }
//     const collapsedElement = $(collapsedElementSelector);
//     const collapsedWrapperElement = $(collapsedElementSelector).parent();
//     if (collapsedWrapperElement.hasClass('offer-filter__collapse-wrapper--open')) {
//       collapsedWrapperElement.removeClass('offer-filter__collapse-wrapper--open');
//       collapsedWrapperElement.height(0);
//     } else {
//       collapsedWrapperElement.addClass('offer-filter__collapse-wrapper--open');
//       collapsedWrapperElement.height(collapsedElement.outerHeight(true) + ($(window).width() < MOBILE_SIZE ? 24 : 0));
//     }
//   });

//   $('.offer-filter__title-close').click(function (event) {
//     event.preventDefault();
//     event.stopPropagation();
//     closeMobileModal();
//   });

//   function closeMobileModal() {
//     dataElement.removeClass('open');
//     $('body').removeClass('modal-showing');
//     $('.offer-listing-filter__button').show();
//   }

//   $(document).on('click', '.tcb-modal.offer-listing-enhance-mobile', function (event) {
//     const modalContent = document.getElementsByClassName('tcb-modal__wrapper')[0];
//     if (!modalContent.contains(event.target)) {
//       closeMobileModal();
//     }
//   });

//   $('.checkbox-parent input[type="checkbox"]').on('change', function (event) {
//     const target = event.currentTarget;
//     event.preventDefault();
//     let isChecked = $(this).is(':checked');
//     if (isChecked) {
//       $(this).prop('checked');
//       $(this)
//         .closest('.offer-filter__checkbox-wrapper')
//         .find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]')
//         .prop('checked', true);
//     } else {
//       const selectedItems = $(this)
//         .closest('.offer-filter__checkbox-wrapper')
//         .find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]:checked').length;

//       const allItems = $(this)
//         .closest('.offer-filter__checkbox-wrapper')
//         .find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]').length;

//       if (selectedItems === allItems) {
//         $(this).prop('checked', false);
//         $(this)
//           .closest('.offer-filter__checkbox-wrapper')
//           .find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]')
//           .prop('checked', false);
//       } else if (selectedItems > 0) {
//         $(this).prop('checked');
//         $(this)
//           .closest('.offer-filter__checkbox-wrapper')
//           .find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]')
//           .prop('checked', true);
//       }
//     }

//     let childWrapper = $(this)
//       .closest('.offer-filter__checkbox-wrapper')
//       .find('.offer-filter__checkbox-child-wrapper:not(.no-child)');
//     let isVisible = childWrapper.is(':visible');
//     let svgIcons = $(target).closest('.offer-filter__checkbox-wrapper').find('img');

//     if (!isVisible) {
//       childWrapper && childWrapper.slideToggle();
//       svgIcons.each(function () {
//         $(this).toggleClass('expanded');
//       });
//     }
//     updateCheckboxStates();

//     getSelectedCardCheckbox($(this).closest('.card-checkbox'), paramsName.cardTypes);
//     paramsValue.offset = 0;
//     currentPage = 1;
//     getPromotions();
//   });

//   $('.offer-filter__checkbox-child-wrapper input[type="checkbox"]').on('change', handleOnChangeNestedCheckbox);

//   $('.checkbox-parent > span').on('click', function (event) {
//     const target = event.currentTarget;
//     let childWrapper = $(target)
//       .closest('.offer-filter__checkbox-wrapper')
//       .find('.offer-filter__checkbox-child-wrapper');
//     childWrapper && childWrapper.slideToggle();
//     let svgIcons = $(target).find('img');
//     svgIcons.each(function () {
//       $(this).toggleClass('expanded');
//     });
//   });
// });

// $(document).ready(function () {
//   let sortText = $('.analytics-ol-sort-mobile .dropdown__list').find('li:first-child').text();
//   $('.analytics-ol-sort-mobile .display__text').text(sortText);
//   $('.analytics-ol-sort-mobile input').val(sortText);
//   let sortDesktopText = $('.analytics-ol-sort-desktop .dropdown__list').find('li:first-child').text();
//   $('.analytics-ol-sort-desktop .display__text').text(sortDesktopText);
//   $('.analytics-ol-sort-desktop input').val(sortDesktopText);
//   $('.offer-filter__container.enhanced-offer-filter__container .offer-filter__checkbox input').click(function () {
//     if ($(this).is(':checked')) {
//       captureOLFilters();
//       $('.analyticsLayer.enhanced-offer-filter__container').trigger('click');
//     }
//   });

//   $('.analytics-ol-merchant ul, .analytics-ol-merchant-mobile ul').click(function () {
//     captureOLFilters();
//     $('.analyticsLayer.enhanced-offer-filter__container').trigger('click');
//   });

//   $('.offer-listing-enhance-mobile .offer-filter__checkbox-item-mobile input').click(function () {
//     if ($(this).is(':checked')) {
//       captureOLFilters();
//       $('.analyticsLayer.enhanced-offer-filter__container').trigger('click');
//     }
//   });
// });

// //analytics : start
// let sortedOfferListCategories;
// let sortedOfferListProduct;
// let sortedOfferListMerchant;
// let sortedOfferListSort;
// let updatedOfferListFilters = '';
// let merchantPlaceholder = '';

// //capturing Categories
// function captureOLCategories() {
//   let updatedOLCategories = [];
//   if ($(window).width() > 767) {
//     $('.analytics-ol-categories .offer-filter__checkbox-item input[type="checkbox"]:checked').each(function () {
//       updatedOLCategories.push($(this).next('span').text());
//     });
//     sortedOfferListCategories =
//       updatedOLCategories && updatedOLCategories.length > 0 ? updatedOLCategories.join(' | ') : '';
//   } else {
//     $(
//       '.offer-listing-enhance-mobile .category_filter .offer-filter__checkbox-item-mobile input[type="checkbox"]:checked',
//     ).each(function () {
//       updatedOLCategories.push($(this).next('span').text());
//     });
//     sortedOfferListCategories =
//       updatedOLCategories && updatedOLCategories.length > 0 ? updatedOLCategories.join(' | ') : '';
//   }
// }

// function captureProductFilter() {
//   let updatedProductFilter = [];
//   if ($(window).width() > 767) {
//     $('.analytics-product-type .offer-filter__checkbox-item input[type="checkbox"]:checked').each(function () {
//       updatedProductFilter.push($(this).next('span').text());
//     });
//     sortedOfferListProduct =
//       updatedProductFilter && updatedProductFilter.length > 0 ? updatedProductFilter.join(' | ') : '';
//   } else {
//     $(
//       '.offer-listing-enhance-mobile .product_filter .offer-filter__checkbox-item-mobile input[type="checkbox"]:checked',
//     ).each(function () {
//       updatedProductFilter.push($(this).next('span').text());
//     });
//     sortedOfferListProduct =
//       updatedProductFilter && updatedProductFilter.length > 0 ? updatedProductFilter.join(' | ') : '';
//   }
// }

// // capturing Sort
// function captureOLSort() {
//   let updatedOLSort = [];
//   if ($(window).width() > 767) {
//     $('.analytics-ol-sort .offer-filter__checkbox-item .checkbox-item__wrapper input[type="radio"]:checked').each(
//       function () {
//         updatedOLSort.push($(this).next('div').text());
//       },
//     );
//     sortedOfferListSort = updatedOLSort && updatedOLSort.length > 0 ? updatedOLSort.join(' | ') : '';
//   } else {
//     let updatedOLSortMobile = $('.analytics-ol-sort-mobile input').val();
//     sortedOfferListSort = updatedOLSortMobile ? updatedOLSortMobile : '';
//   }
// }
// // capturing Merchant
// function captureOLMerchant() {
//   merchantPlaceholder = $('.analytics-ol-merchant .display__text').attr('data-placeholder');
//   if ($(window).width() > 767) {
//     let updatedOLMerchant = '';
//     if (updatedOLMerchant != '-1' && merchantPlaceholder == $('.analytics-ol-merchant .display__text').text()) {
//       updatedOLMerchant = $('.analytics-ol-merchant input').val();
//     } else {
//       updatedOLMerchant = $('.analytics-ol-merchant .display__text').text();
//     }
//     sortedOfferListMerchant = updatedOLMerchant && updatedOLMerchant.length > 0 ? updatedOLMerchant : '';
//   } else {
//     let updatedOLMerchant = $('.analytics-ol-merchant-mobile input').val();
//     sortedOfferListMerchant = updatedOLMerchant && updatedOLMerchant.length > 0 ? updatedOLMerchant : '';
//   }
// }

// function captureOLFilters() {
//   captureOLSort();
//   captureProductFilter();
//   captureOLCategories();
//   captureOLMerchant();

//   let categoriesClick = sortedOfferListCategories;
//   let merchantClick = sortedOfferListMerchant;
//   let SortClick = sortedOfferListSort;
//   let cardFilterClick = sortedOfferListProduct;
//   let updatedOfferListFilters = '';

//   if (SortClick && SortClick.trim().length > 0) {
//     if (updatedOfferListFilters.length > 0) {
//       updatedOfferListFilters += ' | ' + SortClick.trim();
//     } else {
//       updatedOfferListFilters += SortClick.trim();
//     }
//   }
//   if (cardFilterClick && cardFilterClick.trim().length > 0) {
//     if (updatedOfferListFilters.length > 0) {
//       updatedOfferListFilters += ' | ' + cardFilterClick.trim();
//     } else {
//       updatedOfferListFilters += cardFilterClick.trim();
//     }
//   }
//   if (categoriesClick && categoriesClick.trim().length > 0) {
//     if (updatedOfferListFilters.length > 0) {
//       updatedOfferListFilters += ' | ' + categoriesClick.trim();
//     } else {
//       updatedOfferListFilters += categoriesClick.trim();
//     }
//   }
//   if (merchantClick && merchantClick.trim().length > 0 && merchantClick != '-1') {
//     if (updatedOfferListFilters.length > 0) {
//       updatedOfferListFilters += ' | ' + merchantClick.trim();
//     } else {
//       updatedOfferListFilters += merchantClick.trim();
//     }
//   }
//   //updated dataLayerObject
//   const calendarEventsDataValues = $('.enhanced-offer-filter__container').data().trackingClickInfoValue;
//   if (calendarEventsDataValues) {
//     const jsonStr = calendarEventsDataValues.replace(/'/g, '"');
//     const json = JSON.parse(jsonStr);
//     json.articleFilter = updatedOfferListFilters;
//     const updatedValues = JSON.stringify(json).replace(/"/g, "'");
//     $('.enhanced-offer-filter__container').attr('data-tracking-click-info-value', updatedValues);
//   }
// }

// //analytics : end
