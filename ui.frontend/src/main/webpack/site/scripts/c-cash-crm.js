$(document).ready(function () {
    // in ccash form survey, combine answers and add to hidden fields to integrate to CRM
    let multiStepForm = $('.multistepform');

    multiStepForm.each(function () {
        // build description
        let descriptionInput = multiStepForm.find('input[type="hidden"][name="description"]');
        if(descriptionInput.length == 1) {
            let companyScale = '';
            let companyTopCriterias = [];
            let companyOtherCriterias = '';
            multiStepForm.find('.dropdown-wrapper:has(input[name="company_scale"])').click(function () {
                companyScale = $(this).find('.dropdown-viewValue').text();
                fillDescription(descriptionInput, companyScale, companyTopCriterias, companyOtherCriterias);
            });
            multiStepForm.find('.input__checkbox[name="company_top_criterias"]').change(function () {
                if(!companyTopCriterias.includes(this.value)) {     
                    companyTopCriterias.push(this.value);
                }
                else {
                    companyTopCriterias.splice(companyTopCriterias.indexOf(this.value), 1);
                }
                fillDescription(descriptionInput, companyScale, companyTopCriterias, companyOtherCriterias);
            });
            multiStepForm.find('.cmp-form-text__text[name="company_other_criterias"]').change(function () {
                companyOtherCriterias = this.value;
                fillDescription(descriptionInput, companyScale, companyTopCriterias, companyOtherCriterias);
            });
        }

        // build additional_information
        let additionalInformationInput = multiStepForm.find('input[type="hidden"][name="additional_information"]');
        if(additionalInformationInput.length == 1) {
            let surveyAnswers = [];
            multiStepForm.find('.form-survey__single-choice').each(function(surveyIndex) {
                $(this).find('input.input__radio').click(function() {
                    surveyAnswers[surveyIndex] = $(this).siblings('.answer__item--text').text();
                    fillAdditionalInfomation(additionalInformationInput, surveyAnswers);
                })
            })
        }
    });

    function fillDescription(descriptionInput, companyScale, companyTopCriterias, companyOtherCriterias) {
        let description = `${companyScale},${companyTopCriterias.toString()},${companyOtherCriterias}`;
        descriptionInput.val(limitStringLength(description));
    }

    function fillAdditionalInfomation(additionalInformationInput, surveyAnswers) {
        let additionalInfomation = `${surveyAnswers.toString()}`;
        additionalInformationInput.val(limitStringLength(additionalInfomation));
    }

    // CRM can only take string with length < 32000
    function limitStringLength(crmString) {
        if(crmString.length > 32000) {
            crmString = crmString.slice(0, 32000);
        }
        return crmString
    }
});