import { BaseComponent } from './base';
import { domainWhiteList, PREVIOUS_PAGE_KEY_NAME } from './constants/common';
import { SessionStorageUtil } from './utils/storage.util';

export class TcbButton extends BaseComponent {
	get ctaElement() {
		return this.querySelector('a.cta-button');
	}

	get dataLinkToPreviousPage() {
		return this.getAttribute('data-link-to-previous-page') || 'no';
	}

	constructor() {
		super();
		if (this.ctaElement) {
			this.handlePreviousListener();
		}
	}

	handlePreviousListener() {
		if (this.dataLinkToPreviousPage === 'yes') {
			const referrer = document.referrer;
			const storedPreviousPage = SessionStorageUtil.get(PREVIOUS_PAGE_KEY_NAME);

			if (!referrer && !storedPreviousPage) {
				this.ctaElement.classList.add('hidden');
				return;
			}

			const currentURL = new URL(window.location.href);
			const currentHref = currentURL.href;
			const previousURL = new URL(referrer || storedPreviousPage);
			const previousHref = previousURL.href;
			const previousHostName = previousURL.hostname;

			if (domainWhiteList.includes(previousHostName) && previousHref !== currentHref) {
				this.ctaElement.href = previousHref;
			} else {
				this.ctaElement.classList.add('hidden');
			}
		}
	}
}
