import { AA_ATTRIBUTE, TCB_ANALYTICS_EVENT } from './analytics';
import { getElementText } from './utils';

$(function () {
  $('.articlecardcross a.card-center-carditem').each((_, item) => {
    const cardItem = $(item);
    const title = getElementText(cardItem.find('.cardcenter-carditem-content h3'));
    const desc = getElementText(cardItem.find('.cardcenter-carditem-content div'));
    cardItem.attr(AA_ATTRIBUTE.CLICK_INFO_VALUE, JSON.stringify({ [TCB_ANALYTICS_EVENT.LINK_CLICK]: title || desc }));
  });

  $('.card-center-listcard').each(function () {
    const cardItems = $(this).find('.card-center-carditem');
    const numberItems = cardItems.length;
    if (numberItems == 0) {
      return;
    }
    const perPage = parseInt($(this).attr('mobile-items') ?? -1);
    const column = $(this).attr('data-column');
    let numberCardPerRow = 0;
    const viewMore = $(this).parent().find('.view-more');
    const viewMoreBtn = viewMore.find('.load-more__button');
    let currentItems = perPage;

    function setup() {
      if ($(window).width() >= 768) {
        if (column != null) {
          numberCardPerRow = parseInt(column.replace('column-', ''));
        }

        if (numberItems <= 2 * numberCardPerRow) {
          viewMore.hide();
        }

        cardItems.each((i, card) => {
          if (i >= 2 * numberCardPerRow) {
            $(card).hide();
          } else {
            $(card).show();
          }
        });
      }

      if ($(window).width() < 768) {
        viewMore.hide();
        if (perPage == -1) {
          $(cardItems).show();
        }
        if (perPage == 0) {
          $(cardItems).hide();
        }
        if (perPage != -1 && perPage != 0) {
          cardItems.each(function (i) {
            if (i >= perPage) {
              $(this).hide();
            }
          });
          if (numberItems > perPage) {
            viewMore.show();
          }
        }
      }
    }

    const updateItems = () => {
      if ($(window).width() >= 768) {
        $(this).find('.card-center-carditem').show();
        viewMore.hide();
      } else {
        $(cardItems)
          .slice(currentItems, currentItems + perPage)
          .show();
        currentItems += perPage;
        if (currentItems >= numberItems) {
          viewMore.hide();
        }
      }
    };

    setup();

    viewMoreBtn.on('click', function () {
      updateItems();
    });

    $(window).resize(function () {
      setup();
    });
  });
});
