import { BaseComponent } from './base';

export class RecommendCategory extends BaseComponent {
  constructor() {
    super();
    this.init();
  }

  init() {
    this.bindEvents();
  }

  bindEvents() {
    const $tabItems = $('.promotion-detail .promotion-hub_tab-hub-item');
    if (!$tabItems.length) {
      return;
    }

    $tabItems.on('click', (event) => {
      event.preventDefault();
      const $item = $(event.currentTarget);
      const dataType = $item.data('value');

      if (typeof dataType === 'string' && dataType !== '') {
        this.setParam('type', dataType);
      }
    });
  }

  setParam(param, value) {
    const url = new URL(window.location.href);
    url.searchParams.set(param, value);
    window.history.replaceState({}, '', url);
  }
}
