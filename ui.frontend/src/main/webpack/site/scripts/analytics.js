import { parseStringToJson } from './utils';
import { ObjectUtil } from './utils/object.util';
import { LocationUtil } from './utils/location.util';

const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
const businessUnit = 'tcb landing page';
const platform = 'web';
const trackingCodeParams = ['utm_medium', 'utm_source', 'utm_campaign', 'utm_content'];
const hostEnvs = {
  'aem-sit.techcombank.com': 'sit',
  'aem-uat.techcombank.com': 'stg',
  'techcombank.com': 'prod',
  'preview.techcombank.com': 'preview',
  'aem-qa.techcombank.com': 'sit'
};
const userInfo = {
  status: 'not logged-in',
};
const environment = hostEnvs[location.hostname] || 'dev';
export const AA_ATTRIBUTE = {
  PAGE_NAME: 'data-tracking-page-name',
  PRODUCT_NAME: 'data-tracking-product-name',
  JOURNEY: 'data-tracking-journey',
  COUNTRY: 'data-tracking-country',
  EVENT: 'data-tracking-event',
  CLICK_EVENT: 'data-tracking-click-event',
  WEB_INTERACTION_VALUE: 'data-tracking-web-interaction-value',
  CLICK_INFO_KEY: 'data-tracking-click-info-key',
  CLICK_INFO_VALUE: 'data-tracking-click-info-value',
  FORM_INFO_KEY: 'data-tracking-form-info-key',
  FORM_INFO_VALUE: 'data-tracking-form-info-value',
  OFFER_INFO_KEY: 'data-tracking-offer-info-key',
  OFFER_INFO_VALUE: 'data-tracking-offer-info-value',
};
const AA_DATA = {
  PAGE_NAME: 'trackingPageName',
  PRODUCT_NAME: 'trackingProductName',
  JOURNEY: 'trackingJourney',
  COUNTRY: 'trackingCountry',
  EVENT: 'trackingEvent',
  CLICK_EVENT: 'trackingClickEvent',
  WEB_INTERACTION_VALUE: 'trackingWebInteractionValue',
  CLICK_INFO_KEY: 'trackingClickInfoKey',
  CLICK_INFO_VALUE: 'trackingClickInfoValue',
  FORM_INFO_KEY: 'trackingFormInfoKey',
  FORM_INFO_VALUE: 'trackingFormInfoValue',
  OFFER_INFO_KEY: 'trackingOfferInfoKey',
  OFFER_INFO_VALUE: 'trackingOfferInfoValue',
};
export const TCB_ANALYTICS_EVENT = {
  PAGE_LOADED: 'pageLoaded',
  LINK_CLICK: 'linkClick',
  FAQ_CLICK: 'faq',
  APP_DOWNLOAD: 'appDownload',
  DOWNLOAD: 'download',
  ERROR: 'error',
  PRODUCT_JOURNEY_START: 'productJourneyStart',
  GLOBAL_NAVIGATION: 'globalNavigation',
  CONTACT_US: 'contactUs',
  NOTIFICATION_OPEN: 'notificationOpen',
  NOTIFICATION_CLICK_TO_ACTION: 'notificationClickToAction',
  NOTIFICATION_GOT_IT: 'notificationGotit',
  NOTIFICATION_CLOSE: 'notificationClose',
  NOTIFICATION_CLICK: 'notificationClick',
  BANNER_CLICK: 'bannerClick',
};
export const TCB_ANALYTICS_INTERACT_TYPE = {
  OTHER: 'other',
  DOWNLOAD: 'download',
  EXIT: 'exit',
};

/**
 * Push AA DataLayer
 * @param {string} eventName
 * @param {TcbAnalyticsData} techcombank
 * @param {WebInteractions} webInteract
 */
export const addDataLayerObject = (
  eventName,
  techcombank,
  webInteract, // web inter data
) => {
  const currentDate = new Date();
  const dayOfWeek = days[currentDate.getDay()];
  const timestamp = new Date(currentDate.getTime() + 7 * 60 * 60000).toISOString().substring(0, 19) + 'Z';

  const _techcombank = {
    pageName: getTrackingPageName(),
    pageURL: window.location.href,
    pageURLParam: LocationUtil.getPageUrlParams(),
    siteSection: location.hash.split('#')[1] || location.href.split(location.hostname + '/')[1],
    language: trackingLanguage,
    country: trackingCountry,
    environment,
    businessUnit,
    platform,
    productName: trackingProduct != undefined ? trackingProduct : '',
    journey: location.pathname,
    dayOfWeek,
    timestamp,
    cookiesEnabled: navigator.cookieEnabled ? 'y' : 'n',
    defaultBrowserLanguage: navigator.language || navigator.userLanguage,
    userInfo,
    ...techcombank,
  };

  ObjectUtil.deleteUndefinedField(_techcombank);

  const dataLayerObject = {
    event: eventName,
    _techcombank,
  };

  if (webInteract) {
    dataLayerObject.web = webInteract;
  }

  const trackingCode = getTrackingCode();
  if (trackingCode) {
    dataLayerObject.marketing = { trackingCode };
  }

  window.adobeDataLayer = window.adobeDataLayer || [];
  window.adobeDataLayer.push(dataLayerObject);
};

/**
 * Method to get the tracking code from the URL params
 * @returns {string}
 */
const getTrackingCode = () => {
  const urlParamObj = LocationUtil.getUrlParamObj();
  const markingCodeArr = trackingCodeParams.map((param) => urlParamObj[param]);
  if (!markingCodeArr.join('')) {
    return;
  }

  return markingCodeArr.join(':');
};

/**
 * Format string
 * @param {string} str
 * @returns {string}
 */
const formatString = (str) => {
  if (str && typeof str === 'string') {
    str = str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    str = str.replace(/ĩ/g, 'i').replace(/ø/g, 'o').replace(/đ/g, 'd').replace(/Đ/g, 'D');
    str = str.trim();
  }

  return str;
};

/**
 * Get element data | attr
 * @param {jQueryElement} element
 * @param {string} dataKey AA_DATA
 * @param {string} attributeKey AA_ATTRIBUTE
 * @returns
 */
const getElementDataAttribute = (element, dataKey, attributeKey) => {
  return element.attr(attributeKey) || element.data(dataKey) || '';
};

const mainElement = $('body');
const pageName = getElementDataAttribute(mainElement, AA_DATA.PAGE_NAME, AA_ATTRIBUTE.PAGE_NAME) || undefined;
const trackingProduct =
  getElementDataAttribute(mainElement, AA_DATA.PRODUCT_NAME, AA_ATTRIBUTE.PRODUCT_NAME) || undefined;
const trackingCountry = getElementDataAttribute(mainElement, AA_DATA.COUNTRY, AA_ATTRIBUTE.COUNTRY) || undefined;
const trackingLanguage = $('html').attr('lang');
const trackingEvent = getElementDataAttribute(mainElement, AA_DATA.EVENT, AA_ATTRIBUTE.EVENT);

if (trackingEvent) {
  addDataLayerObject(trackingEvent);
}

// Event click
mainElement.on('click', `[${AA_ATTRIBUTE.CLICK_EVENT}]`, (elem) => {
  const element = $(elem.currentTarget);

  const eventName = formatString(getElementDataAttribute(element, AA_DATA.CLICK_EVENT, AA_ATTRIBUTE.CLICK_EVENT));
  const trackingWebInteractionValue = formatString(
    getElementDataAttribute(element, AA_DATA.WEB_INTERACTION_VALUE, AA_ATTRIBUTE.WEB_INTERACTION_VALUE),
  );

  const trackingClickInfoKey =
    getElementDataAttribute(element, AA_DATA.CLICK_INFO_KEY, AA_ATTRIBUTE.CLICK_INFO_KEY) || 'clickInfo';
  const trackingClickInfoValue = formatString(
    getElementDataAttribute(element, AA_DATA.CLICK_INFO_VALUE, AA_ATTRIBUTE.CLICK_INFO_VALUE),
  );

  const trackingFormInfoKey =
    getElementDataAttribute(element, AA_DATA.FORM_INFO_KEY, AA_ATTRIBUTE.FORM_INFO_KEY) || 'formInfo';
  const trackingFormInfoValue = formatString(
    getElementDataAttribute(element, AA_DATA.FORM_INFO_VALUE, AA_ATTRIBUTE.FORM_INFO_VALUE),
  );

  const trackingOfferInfoKey =
    getElementDataAttribute(element, AA_DATA.OFFER_INFO_KEY, AA_ATTRIBUTE.OFFER_INFO_KEY) || 'offer';
  const trackingOfferInfoValue = formatString(
    getElementDataAttribute(element, AA_DATA.OFFER_INFO_VALUE, AA_ATTRIBUTE.OFFER_INFO_VALUE),
  );

  const techcombank = {};
  if (trackingClickInfoValue) {
    techcombank[trackingClickInfoKey] = parseStringToJson(trackingClickInfoValue);
  }
  if (trackingFormInfoValue) {
    techcombank[trackingFormInfoKey] = parseStringToJson(trackingFormInfoValue);
  }
  if (trackingOfferInfoValue) {
    techcombank[trackingOfferInfoKey] = parseStringToJson(trackingOfferInfoValue);
  }

  const webInteract = parseStringToJson(trackingWebInteractionValue);

  addDataLayerObject(eventName, techcombank, webInteract);
});

/**
 * Get tracking page name businessUnit:platform:pageName
 * @returns string
 */
export function getTrackingPageName() {
  const pageTitle = document.querySelector('body').getAttribute(AA_ATTRIBUTE.PAGE_NAME);

  return `${businessUnit}:${platform}:${pageTitle}`;
}
