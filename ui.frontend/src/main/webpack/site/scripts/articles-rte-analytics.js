function articleRteAnalytics() {
    $(".article-content .article-text a").each(function() {
        const href = $(this).attr('href');
        const linkText = $(this).text();
        let type;

        if (href){
            // Check if it's an external link
            if (href.startsWith('http') && !href.includes('techcombank.com')) {
                type = 'External';
            } 
            // Check if it's a PDF link
            else if (href.endsWith('.pdf')) {
                type = 'download';
            } 
            // Check if it's an internal link
            else if (href.startsWith('/') || href.includes('techcombank.com')) {
                type = 'other';
            } 
            // Default to 'other' type
            else {
                type = 'other';
            }

            // Apply attributes
            $(this).attr('data-tracking-click-event', "linkClick");
            $(this).attr('data-tracking-web-interaction-value', "{'webInteractions': {'name': '" + linkText + "','type': '" + type + "'}}");
            $(this).attr('data-tracking-click-info-value', "{'linkClick' : '" + linkText + "'}");
        }
    });
}


$(document).ready(function() {
    articleRteAnalytics();
});
