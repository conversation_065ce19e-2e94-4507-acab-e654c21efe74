$(document).ready(function () {
  const prefixOneLink = 'https://tcbmobile.onelink.me';
  const oneLinkElements = document.querySelectorAll(`a[href*='${prefixOneLink}']`);
  if (!oneLinkElements || !oneLinkElements.length) {
    return;
  }

  const getUrlParam = (url) => {
    return url.split('?')[1] || '';
  };

  const getUrlParamObject = (url) => {
    const urlParam = getUrlParam(url);
    return urlParam
      ? JSON.parse('{"' + urlParam.toLowerCase().replace(/&/g, '","').replace(/=/g, '":"') + '"}', (key, value) =>
          key === '' ? value : decodeURIComponent(value),
        )
      : {};
  };

  const mappingParams = {
    pid: 'utm_source',
    c: 'utm_campaign',
    af_channel: 'utm_medium',
    af_ad: 'utm_content',
    af_sub1: 'utm_term',
  };
  const addKeys = {
    gclid: 'af_sub2',
    utm_source: 'utm_source',
    utm_campaign: 'utm_campaign',
    utm_medium: 'utm_medium',
  };

  const windowLocationUrlParams = getUrlParamObject(window.location.search);

  for (let element of oneLinkElements) {
    const onlinkUrlParams = getUrlParamObject(element.href);
    Object.keys(onlinkUrlParams).forEach((key) => {
      if (windowLocationUrlParams[mappingParams[key]]) {
        if (key === 'pid') {
          onlinkUrlParams[key] = `${windowLocationUrlParams[mappingParams[key]]}-web`;
          return;
        }
        onlinkUrlParams[key] = windowLocationUrlParams[mappingParams[key]];
      }
    });
    Object.keys(addKeys).forEach((key) => {
      if (windowLocationUrlParams[key]) {
        onlinkUrlParams[addKeys[key]] = windowLocationUrlParams[key];
      }
    });
    const url = new URL(element.href.split('?')[0]);
    Object.keys(onlinkUrlParams).forEach((key) => {
      url.searchParams.set(key, onlinkUrlParams[key]);
    });

    element.href = url.href;
  }
});
