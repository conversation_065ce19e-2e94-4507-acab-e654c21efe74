
import CurrencyHelper from './currency-helper';

$(document).ready(function () {
  const fastAccessContents = $('.quick-access');

  fastAccessContents.each(function () {
    const fastAccessContent = $(this);
    const fastAccessTable = fastAccessContent.find('.quick-access__table-desktop');
    const fastAccessTableHeaderItems = fastAccessTable.find('.table-header__items');
    const headerButton = fastAccessTableHeaderItems.find('.header__button');
    const tableRecordsInner = fastAccessTable.find('.table-records__inner');
    const headerItemMobile = $(this).find('.header-item');
    const currencyPopup = $(this).find('.pop-up');
    const currencyPopupContent = currencyPopup.find('.pop-up-content');
    const currencyPopUpTitle = currencyPopup.find('.title p');
    const altBtn = $('.alternate-button-mobile');
    const currencyValues = fastAccessContents.find('.currency-value-format span');

    currencyValues.each(function (i) {
      const currencyValue = $(this).text();
      $(this).text(CurrencyHelper.numberWithCommas(currencyValue));
    });

    headerButton.each(function (i) {
      const itemTemp = $(this);
      if (i == 0) {
        $(this).addClass('header__selected');
        tableRecordsInner.each(function (j) {
          if (j == i) {
            $(this).addClass('record__show');
          }
        });
      }
      itemTemp.click(function () {
        headerButton.removeClass('header__selected');
        $(this).addClass('header__selected');
        headerButton.scrollCenter('.header__selected', 300);
        tableRecordsInner.each(function (j) {
          $(this).removeClass('record__show');
          if (j == i) {
            $(this).addClass('record__show');
          }

          headerButton.css('border-bottom', 'unset');
          $(itemTemp).css('border-bottom', '4px solid #ed1c24');
        });
      });
    });

    if ($(window).width() <= 767) {
      headerItemMobile.click(function (e) {
        e.stopPropagation();
        showCurExPopUp($(this));
        if (altBtn.length > 0) {
          altBtn.css("display", "none");
        }
        $('body').addClass('overflow-hidden');
      });
    }

    $(window).resize(function () {
      if ($(window).width() <= 767) {
        headerItemMobile.click(function (e) {
          e.stopPropagation();
          if (altBtn.length > 0) {
            altBtn.css("display", "none");
          }
          showCurExPopUp($(this));
          $('body').addClass('overflow-hidden');
        });
      }
    });

    function showCurExPopUp(element) {
      // remove old content
      const oldContent = currencyPopupContent.find('.table-records__inner');
      oldContent.remove();

      // add new content
      currencyPopup.css('display', 'block');
      $('.scroll-to-top').removeClass('show');
      currencyPopUpTitle.text(element.find('span').text());
      $(tableRecordsInner[element.index()]).clone().appendTo(currencyPopupContent);
    }

    $(document).click(function (event) {
      if (!$(event.target).is(currencyPopupContent) && !$(event.target).parents().is(currencyPopupContent)) {
        if (altBtn.length > 0 && $(window).width() <= 767) {
          altBtn.css("display", "");
        }
        currencyPopup.css('display', 'none');
        $('body').removeClass('overflow-hidden');
      }
    });

    jQuery.fn.scrollCenter = function (elem, speed) {
      const active = $(this).parent().find(elem); // find the active element
      const activeWidth = active.width() / 2; // get active width center

      let pos = active.position().left + activeWidth; //get left position of active li + center position
      const elpos = active.scrollLeft(); // get current scroll position
      const elW = active.width(); //get div width

      pos = pos + elpos - elW / 2; // for center position if you want adjust then change this

      $(this).parent().animate({ scrollLeft: pos }, speed);

      return pos;
    };
  });
});