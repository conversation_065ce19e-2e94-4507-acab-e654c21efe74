import 'slick-carousel';
import { getQueryParam } from './utils/params.util';
import { BaseComponent } from './base';
import { screenSmMax } from './constants/common';

export class CardTypeFilterComponent extends BaseComponent {
  constructor() {
    super();
    this.init();
    this.initCarousel();
  }

  init() {
    this.$cardType = $('[data-promotion-hub-card-type]');
    const cardTypesParam = getQueryParam('card-types');
    const typesParam = getQueryParam('types');

    if ((!cardTypesParam && !typesParam) || !this.$cardType.length) {
      this.checkHubVisibility();
      return;
    }

    let cardTypesSet = new Set();
    if (cardTypesParam) {
      cardTypesSet = new Set(cardTypesParam.split(','));
    }

    let typesSet = new Set();
    if (typesParam) {
      typesSet = new Set(typesParam.split(','));
    }

    this.filterCards(cardTypesSet, typesSet);
    this.checkHubVisibility();
  }

  filterCards(cardTypesSet, typesSet) {
    this.$cardType.each(function () {
      const $card = $(this);
      const types = $card.data('promotion-hub-card-type');

      if (!types) {
        $card.remove();
        return;
      }

      const typeArr = types.split(',');
      let isMatched = false;

      // Check if we have both card-types and types parameters
      if (cardTypesSet.size > 0 && typesSet.size > 0) {
        // Both parameters must match (AND logic)
        const cardTypeMatched = typeArr.some(
          (slug) =>
            cardTypesSet.has(`${slug}`) ||
            cardTypesSet.has(`the-tin-dung/${slug}`) ||
            cardTypesSet.has(`the-thanh-toan/${slug}`)
        );

        const typeMatched = typeArr.some(
          (slug) =>
            typesSet.has(`${slug}`) ||
            typesSet.has(`the-tin-dung/${slug}`) ||
            typesSet.has(`the-thanh-toan/${slug}`)
        );

        isMatched = cardTypeMatched && typeMatched;
      } else if (cardTypesSet.size > 0) {
        // Only card-types parameter
        isMatched = typeArr.some(
          (slug) =>
            cardTypesSet.has(`${slug}`) ||
            cardTypesSet.has(`the-tin-dung/${slug}`) ||
            cardTypesSet.has(`the-thanh-toan/${slug}`)
        );
      } else if (typesSet.size > 0) {
        // Only types parameter
        isMatched = typeArr.some(
          (slug) =>
            typesSet.has(`${slug}`) ||
            typesSet.has(`the-tin-dung/${slug}`) ||
            typesSet.has(`the-thanh-toan/${slug}`)
        );
      }

      if (!isMatched) {
        $card.remove();
      } else {
        $card.css('display', 'flex');
      }
    });
  }

  checkHubVisibility() {
    const $visibleCards = $('.promotion-hub_credit-card').filter(function() {
      return $(this).css('display') !== 'none' && $(this).is(':visible');
    });

    const $hubElement = $('tcb-credit-card-hub');

    if ($visibleCards.length === 0) {
      $hubElement.hide();
    } else {
      $hubElement.show();
    }
  }

  initCarousel() {
    // Initialize carousel on page load if needed
    this.configCarousel();

    // Handle window resize
    $(window).on('resize', () => {
      this.configCarousel();
    });
  }

  configCarousel() {
    const $carouselContainer = $('.promotion-hub_group-credit-card');

    if ($carouselContainer.length === 0) {
      return;
    }

    if ($(window).width() <= screenSmMax) {
      // Initialize carousel for mobile
      if (!$carouselContainer.hasClass('slick-initialized')) {
        $carouselContainer.slick({
          slidesToShow: 1.1,
          slidesToScroll: 1,
          dots: true,
          arrows: false,
          infinite: false,
          mobileFirst: true,
          adaptiveHeight: true,
          responsive: [
            {
              breakpoint: 768,
              settings: 'unslick'
            }
          ]
        });
      }
    } else {
      // Destroy carousel for desktop
      if ($carouselContainer.hasClass('slick-initialized')) {
        $carouselContainer.slick('unslick');
      }
    }
  }
}
