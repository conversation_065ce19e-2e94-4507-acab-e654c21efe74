import { BaseComponent } from './base';
import 'slick-carousel';

export class SlidePromotion extends BaseComponent {
  constructor() {
    super();
    this.init();
  }

  slickCardBtn(){
    const slickCard = $('.slick-text');
    const autoPlaySpeed = slickCard.data('autoplay-speed');
    slickCard.not('.slick-initialized').slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      infinite: true,
      autoplay: true,
      arrows: false,
      autoplaySpeed: autoPlaySpeed || 1000,
      centerPadding: '0',
      draggable: false,
    });
  }

  init() {
    this.slickCardBtn();
  }
}
