import 'slick-carousel';

$(document).ready(function () {
  const sectionFastAccess = $('section.section__fast-access');

  sectionFastAccess.each(function () {
    const fastAccessContent = $(this).find('.fast-access__contents');
    const fastAccessTable = fastAccessContent.find('.fast-access__table');
    const fastAccessTableHeaderItems = fastAccessTable.find('.table-header__items');
    const headerButton = fastAccessTableHeaderItems.find('.header__button');
    const tableRecordsInner = fastAccessTable.find('.table-records__inner');
    const headerItemMobile = $(this).find('.header-item');
    const currencyPopup = $(this).find('.pop-up');
    const currencyPopupContent = currencyPopup.find('.pop-up-content');
    const currencyPopUpTitle = currencyPopup.find('.title h6');

    headerButton.each(function (i) {
      const itemTemp = $(this);
      if (i == 0) {
        $(this).addClass('header__selected');
        tableRecordsInner.each(function (j) {
          if (j == i) {
            $(this).addClass('record__show');
          }
        });
      }
      itemTemp.click(function () {
        headerButton.removeClass('header__selected');
        $(this).addClass('header__selected');
        headerButton.scrollCenter('.header__selected', 300);
        tableRecordsInner.each(function (j) {
          $(this).removeClass('record__show');
          if (j == i) {
            $(this).addClass('record__show');
          }

          headerButton.css('border-bottom', 'unset');
          $(itemTemp).css('border-bottom', '4px solid #ed1c24');
        });
      });
    });

    if ($(window).width() <= 767) {
      headerItemMobile.click(function (e) {
        e.stopPropagation();
        showCurExPopUp($(this));
        $('body').addClass('overflow-hidden');
      });
    }

    $(window).resize(function () {
      if ($(window).width() <= 767) {
        headerItemMobile.click(function (e) {
          e.stopPropagation();
          showCurExPopUp($(this));
          $('body').addClass('overflow-hidden');
        });
      }
    });

    function showCurExPopUp(element) {
      // remove old content
      const oldContent = currencyPopupContent.find('.table-records__inner');
      oldContent.remove();

      // add new content
      currencyPopup.css('display', 'block');
      $('.scroll-to-top').removeClass('show');
      currencyPopUpTitle.text(element.find('span').text());
      $(tableRecordsInner[element.index()]).clone().appendTo(currencyPopupContent);
    }

    $(document).click(function (event) {
      if (!$(event.target).is(currencyPopupContent) && !$(event.target).parents().is(currencyPopupContent)) {
        currencyPopup.css('display', 'none');
        $('body').removeClass('overflow-hidden');
      }
    });

    jQuery.fn.scrollCenter = function (elem, speed) {
      const active = $(this).parent().find(elem); // find the active element
      const activeWidth = active.width() / 2; // get active width center

      let pos = active.position().left + activeWidth; //get left position of active li + center position
      const elpos = active.scrollLeft(); // get current scroll position
      const elW = active.width(); //get div width

      pos = pos + elpos - elW / 2; // for center position if you want adjust then change this

      $(this).parent().animate({ scrollLeft: pos }, speed);

      return pos;
    };
  });
});

// List Card
$(document).ready(function () {
  $('.list-card').each(function () {
    if ($(window).width() <= 767) {
      $('.list-card__list-item').not('.slick-initialized').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: false,
        autoplaySpeed: 2000,
        arrows: false,
        dots: true,
        infinite: false,
      });
    } else {
      $('.list-card__list-item').filter('.slick-initialized').slick('unslick');
    }
    $(window).on('resize', function () {
      if ($(window).width() <= 767) {
        $('.list-card__list-item').not('.slick-initialized').slick({
          slidesToShow: 1,
          slidesToScroll: 1,
          autoplay: false,
          autoplaySpeed: 2000,
          arrows: false,
          dots: true,
          infinite: false,
        });
      } else {
        $('.list-card__list-item').filter('.slick-initialized').slick('unslick');
      }
    });
  });
});
// End of List Card

// End Card Product Filter component

//Document download slider
$(document).ready(function () {
  $('.list-document-download').each(function () {
    if ($(window).width() <= 1334) {
      $('.list-item-document-download__body')
        .not('.slick-initialized')
        .slick({
          slidesToShow: 3,
          slidesToScroll: 1,
          autoplay: false,
          autoplaySpeed: 2000,
          arrows: true,
          dots: false,
          infinite: false,
          centerMode: false,
          responsive: [
            {
              breakpoint: 992,
              settings: {
                slidesToShow: 2,
                arrows: false,
              },
            },
          ],
        });
    } else {
      $('.list-item-document-download__body').filter('.slick-initialized').slick('unslick');
    }
    $(window).on('resize', function () {
      if ($(window).width() <= 1334) {
        $('.list-item-document-download__body')
          .not('.slick-initialized')
          .slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: false,
            autoplaySpeed: 2000,
            arrows: true,
            dots: false,
            infinite: false,
            centerMode: false,
            responsive: [
              {
                breakpoint: 992,
                settings: {
                  slidesToShow: 2,
                  arrows: false,
                },
              },
            ],
          });
      } else {
        $('.list-item-document-download__body').filter('.slick-initialized').slick('unslick');
      }
    });
    $('.list-document-download .list-document-download__container .list-item-document-download__body .slick-track').css(
      'cssText',
      'margin-left: -20px;'
    );
    $('.list-item-document-download__body').on('afterChange', function (event, slick, currentSlide) {
      if (slick.$slides.length - 1 == currentSlide) {
        $(
          '.list-document-download .list-document-download__container .list-item-document-download__body .slick-track'
        ).css('cssText', 'margin-left: 0;');
      } else {
        $(
          '.list-document-download .list-document-download__container .list-item-document-download__body .slick-track'
        ).css('cssText', 'margin-left: -20px;');
      }
    });

    $('.list-item-document-download__body .item-document-download_content')
      .first()
      .addClass('radius-top-bottom-left-8');
    $('.list-item-document-download__body .item-document-download_content')
      .last()
      .addClass('radius-top-bottom-right-8');

    $('.list-item-document-download__body .slick-next').text('');
    $('.list-item-document-download__body .slick-prev').text('');

    $('.list-item-document-download__body .slick-prev').css('cssText', 'display: none !important');
    $('.list-item-document-download__body .slick-next').css('cssText', 'display: block !important');

    $('.list-item-document-download__body').on('afterChange', function (event, slick, currentSlide, nextSlide) {
      if ($(window).width() <= 1334 && $(window).width() >= 992) {
        if (currentSlide == 0) {
          $('.list-item-document-download__body .slick-prev').css('cssText', 'display: none !important');
          $('.list-item-document-download__body .slick-next').css('cssText', 'display: block !important');
        } else if (slick.$slides.length - 3 == currentSlide) {
          $('.list-item-document-download__body .slick-prev').css('cssText', 'display: block !important');
          $('.list-item-document-download__body .slick-next').css('cssText', 'display: none !important');
        } else {
          $('.list-item-document-download__body .slick-prev').css('cssText', 'display: block !important');
          $('.list-item-document-download__body .slick-next').css('cssText', 'display: block !important');
        }
      }
    });
  });
});

//end document download slider

// tab-vertical-qa Component
$(document).ready(function () {
  $('.tab-vertical-qa').each(function () {
    $(this)
      .find('.tab-vertical-qa__tab-control-btn')
      .click(function () {
        if ($(window).width() >= 992) {
          activeTab(this);
        } else {
          showpopup(this);
        }
      });
    if ($(window).width() < 992) {
      $('.tab-vertical-qa__tab-control-btn').removeClass('tab-active');
      $('.tab-vertical-qa .tab-vertical-qa__article-content').css('display', 'none');
    } else {
      activeTab($('.tab-vertical-qa__tab-control-btn:first-child'));
    }
    $(window).resize(function () {
      if ($(window).width() < 992) {
        $('.tab-vertical-qa__tab-control-btn').removeClass('tab-active');
        $('.tab-vertical-qa .tab-vertical-qa__article-content').css('display', 'none');
        $('.tab-vertical-qa .tab-vertical-qa__tab-content').css('display', 'none');
      } else {
        activeTab($('.tab-vertical-qa__tab-control-btn:first-child'));
        $('.tab-vertical-qa .tab-vertical-qa__tab-content').css('display', 'block');
      }
    });
    $(this)
      .find('.tab-vertical-qa__tab-content')
      .click(function () {
        $('.tab-vertical-qa__tab-content').hide();
      });
    $(this)
      .find('.tab-vertical-qa__modal-close')
      .click(function () {
        $('.tab-vertical-qa__article-content').hide();
        $('.tab-vertical-qa__tab').css('cssText', 'background-color:unset');
      });
    function activeTab(obj) {
      $('.tab-vertical-qa .tab-vertical-qa__tab-control-btn').removeClass('tab-active');
      $(obj).addClass('tab-active');
      const id = $(obj).data('tab-control');
      $('.tab-vertical-qa__article-content').css('display', 'none');
      $('.tab-vertical-qa__article-content').each(function () {
        if ($(this).data('tab-content') == id) {
          $(this).css('display', 'block');
        }
      });
    }
    function showpopup(obj) {
      $('.tab-vertical-qa .tab-vertical-qa__tab-control-btn').removeClass('tab-active');
      $(obj).addClass('tab-active');
      const id = $(obj).data('tab-control');
      $('.tab-vertical-qa__article-content').hide();
      $('.tab-vertical-qa__tab-content').hide();
      $('.tab-vertical-qa__article-content').each(function () {
        if ($(this).data('tab-content') == id) {
          $(this).show();
          $('.tab-vertical-qa__tab-content').show();
          $('.tab-vertical-qa__tab-content').css(
            'cssText',
            'background: #00000069; position: absolute; width: 100vw; height: 100vh; top: 0; left: 0; z-index: 2; max-width: unset;'
          );
        }
      });
    }
  });
});

// End tab-vertical-qa Component

//Tab Horizontal Report Component
$(document).ready(function () {
  $('.tab-horizontal-report').each(function () {
    $(this)
      .find('.tab-horizontal-report__tab-control-btn')
      .click(function () {
        activeTabHorizontalReport(this);
      });
    activeTabHorizontalReport($(this).find('.tab-horizontal-report__tab-control-btn:first-child'));

    function activeTabHorizontalReport(obj) {
      $(obj)
        .parent()
        .parent()
        .parent()
        .parent()
        .find('.tab-horizontal-report__tab-control-btn')
        .removeClass('tab-horizontal-report__tab-active');
      $(obj).addClass('tab-horizontal-report__tab-active');
      const id = $(obj).data('tab-control');
      $(obj)
        .parent()
        .parent()
        .parent()
        .parent()
        .find('.tab-horizontal-report__tab-content-card')
        .css('display', 'none');
      $('.tab-horizontal-report__tab-content-card').each(function () {
        if ($(this).data('tab-content') == id) {
          $(this).css('display', 'flex');
          if ($(this).find('.tab-horizontal__tab-content-item').length > 4) {
            $('.tab-horizontal__tab-content-list-item').filter('.slick-initialized').slick('unslick');
            $(this)
              .find('.tab-horizontal__tab-content-list-item')
              .not('.slick-initialized')
              .slick({
                slidesToShow: 4,
                slidesToScroll: 1,
                autoplay: false,
                accessibility: true,
                arrows: true,
                infinite: false,
                nextArrow: '<button class="slick-next slick-arrow slick-disabled"></button>',
                prevArrow: '<button class="slick-prev slick-arrow"></button>',
                responsive: [
                  {
                    breakpoint: 1335,
                    settings: {
                      slidesToShow: 3,
                      slidesToScroll: 1,
                    },
                  },
                  {
                    breakpoint: 992,
                    settings: {
                      slidesToShow: 2,
                      slidesToScroll: 1,
                      arrows: false,
                    },
                  },
                ],
              });
            $('.tab-horizontal .slick-prev').css('cssText', 'display: none !important');
            $('.tab-horizontal .slick-next').css('cssText', 'display: block !important');
            $('.tab-horizontal__tab-content-list-item').on(
              'afterChange',
              function (event, slick, currentSlide, nextSlide) {
                if ($(window).width() <= 1334) {
                  if (currentSlide == 0) {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: none !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: block !important');
                  } else if (currentSlide == 2) {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: block !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: none !important');
                  } else {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: block !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: block !important');
                  }
                } else {
                  if (currentSlide == 0) {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: none !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: block !important');
                  } else if (currentSlide == 1) {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: block !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: none !important');
                  }
                }
              }
            );
          } else if ($(this).find('.tab-horizontal__tab-content-item').length == 4) {
            $('.tab-horizontal__tab-content-list-item').filter('.slick-initialized').slick('unslick');
            $('.tab-horizontal__tab-content-list-item').filter('.slick-initialized').slick('unslick');
            $(this)
              .find('.tab-horizontal__tab-content-list-item')
              .not('.slick-initialized')
              .slick({
                slidesToShow: 4,
                slidesToScroll: 1,
                autoplay: false,
                accessibility: true,
                arrows: true,
                infinite: false,
                nextArrow: '<button class="slick-next slick-arrow slick-disabled"></button>',
                prevArrow: '<button class="slick-prev slick-arrow"></button>',
                responsive: [
                  {
                    breakpoint: 1335,
                    settings: {
                      slidesToShow: 3,
                      slidesToScroll: 1,
                    },
                  },
                  {
                    breakpoint: 992,
                    settings: {
                      slidesToShow: 2,
                      slidesToScroll: 1,
                      arrows: false,
                    },
                  },
                ],
              });
            $('.tab-horizontal .slick-prev').css('cssText', 'display: none !important');
            $('.tab-horizontal .slick-next').css('cssText', 'display: block !important');
            $('.tab-horizontal__tab-content-list-item').on(
              'afterChange',
              function (event, slick, currentSlide, nextSlide) {
                if ($(window).width() <= 1334) {
                  if (currentSlide == 0) {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: none !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: block !important');
                  } else if (currentSlide == 1) {
                    $('.tab-horizontal .slick-prev').css('cssText', 'display: block !important');
                    $('.tab-horizontal .slick-next').css('cssText', 'display: none !important');
                  }
                }
              }
            );
          } else if ($(this).find('.tab-horizontal__tab-content-item').length == 3) {
            $('.tab-horizontal__tab-content-list-item').filter('.slick-initialized').slick('unslick');
            $(this)
              .find('.tab-horizontal__tab-content-list-item')
              .not('.slick-initialized')
              .slick({
                slidesToShow: 3,
                slidesToScroll: 1,
                autoplay: false,
                accessibility: true,
                arrows: false,
                infinite: false,
                responsive: [
                  {
                    breakpoint: 992,
                    settings: {
                      slidesToShow: 2,
                      slidesToScroll: 1,
                    },
                  },
                ],
              });
          }
        }
      });
    }
  });
});

// Tab Slider component

//Card list Component
$(document).ready(function () {
  $('.card-list').each(function () {
    const cardListContainer = $(this).find('.card-list__container');
    const cardListItem = cardListContainer.find('.card-list__list-item');
    if ($(window).width() < 767 && !$(this).hasClass('card-list-stack') && !cardListItem.hasClass('digital')) {
      slickCardlist();
    } else if ($(window).width() < 767 && cardListItem.hasClass('digital')) {
      cardListItem.filter('.slick-initialized').slick('unslick');
    } else {
      $('.card-list__list-item').filter('.slick-initialized').slick('unslick');
    }
    $(window).resize(function () {
      if ($(window).width() < 767 && !$('.card-list').hasClass('card-list-stack')) {
        slickCardlist();
      } else {
        $('.card-list__list-item').filter('.slick-initialized').slick('unslick');
      }
    });

    $(window).resize(function () {
      if ($(window).width() < 767 && cardListItem.hasClass('digital')) {
        cardListItem.filter('.slick-initialized').slick('unslick');
      }
    });

    function slickCardlist() {
      $('.card-list__list-item').not('.slick-initialized').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: false,
        arrows: false,
        dots: true,
        infinite: false,
      });
    }
  });
});
// End of Card list Component

// Tab Vertical Features component
$(document).ready(function () {
  const tabVerticalFeature = $('.tab-vertical-features-component');

  tabVerticalFeature.each(function () {
    const menuItem = $(this).find('.menu .menu-item');
    const menuItemH3 = menuItem.find('h3');
    const tabItem = $(this).find('.list-items .tab');
    const viewMoreTabVertical = $(this).find('.view-more');
    let tabVerticalState = 0;

    let i = 3;
    let step = i + 2;
    // set up first state
    initialSetup();

    // click menu
    menuItem.click(function () {
      if ($(window).width() >= 992) {
        menuItem.css('border-bottom', '1px solid rgb(222, 222, 222)');
      } else {
        menuItem.css('border-bottom', 'none');
      }
      menuItemH3.css('color', 'rgb(97, 97, 97)');
      menuItemH3.css('font-weight', '400');

      $(this).css('border-bottom', '4px solid rgb(237, 27, 36)');
      $(this).find('h3').css('color', 'black');
      $(this).find('h3').css('font-weight', 'bolder');

      tabItem.css('display', 'none');
      if ($(window).width() >= 992) {
        $(tabItem[$(this).index()]).css('display', 'grid');
      } else {
        $(tabItem[$(this).index()]).css('display', 'flex');
      }

      tabVerticalState = $(this).index();

      initialSetup();

      if ($(window).width() < 992) {
        menuItem.scrollCenter($(this), 300);
      }
    });

    // ripple clicked effect
    function ripple(e) {
      // Setup
      const posX = this.offsetLeft;
      const posY = this.offsetTop;
      let buttonWidth = this.offsetWidth;
      let buttonHeight = this.offsetHeight;

      // Add the element
      const ripple = document.createElement('span');
      ripple.classList.add('ripple-btn-tab-vertical');
      this.appendChild(ripple);

      // Make it round!
      if (buttonWidth >= buttonHeight) {
        buttonHeight = buttonWidth;
      } else {
        buttonWidth = buttonHeight;
      }

      // Get the center of the element
      const x = e.pageX - posX - buttonWidth / 2;
      const y = e.pageY - posY - buttonHeight / 2;

      ripple.style.width = `${buttonWidth}px`;
      ripple.style.height = `${buttonHeight}px`;
      ripple.style.top = `${y}px`;
      ripple.style.left = `${x}px`;

      if ($(window).width() >= 992) {
        ripple.classList.add('ripple-animation-tab-vertical');
      }
      setTimeout(() => {
        this.removeChild(ripple);
      }, 1000);
    }

    // add even listener
    menuItem.on('click', ripple);

    // initial set up
    function initialSetup() {
      i = 3;
      step = i + 2;

      tabItem.each(function () {
        if ($(window).width() < 992) {
          $(this).find('.item:nth-child(n+4)').css('display', 'none');

          if ($(tabItem[tabVerticalState]).find('.item').length > 3) {
            viewMoreTabVertical.css('display', 'block');
          } else {
            viewMoreTabVertical.css('display', 'none');
          }
        } else {
          $(this).find('.item').css('display', 'flex');
          viewMoreTabVertical.css('display', 'none');
        }
      });

      // resize
      $(window).resize(function () {
        tabItem.each(function () {
          if ($(window).width() < 992) {
            $(tabItem[tabVerticalState]).css('display', 'flex');
            $(this).find('.item:nth-child(n+4)').css('display', 'none');

            if ($(tabItem[tabVerticalState]).find('.item').length > 3) {
              viewMoreTabVertical.css('display', 'block');
            } else {
              viewMoreTabVertical.css('display', 'none');
            }
          } else {
            $(this).find('.item').css('display', 'flex');
            viewMoreTabVertical.css('display', 'none');
            $(tabItem[tabVerticalState]).css('display', 'grid');
          }
        });
      });
    }

    // view more btn
    viewMoreTabVertical.click(function () {
      const listItem = $(tabItem[tabVerticalState]).find('.item');

      if (step >= listItem.length) {
        for (i; i < listItem.length; i++) {
          listItem[i].style.display = 'flex';

          if (i === listItem.length) {
            viewMoreTabVertical.css('display', 'none');
          }
        }
      } else {
        for (i; i <= step; i++) {
          listItem[i].style.display = 'flex';
        }
      }
      step = i + 2;
    });

    // scroll center function
    jQuery.fn.scrollCenter = function (elem, speed) {
      const activeWidth = elem.width() / 2; // get active width center

      let pos = elem.position().left + activeWidth; //get left position of active li + center position
      const elpos = elem.scrollLeft(); // get current scroll position
      const elW = elem.width(); //get div width

      pos = pos + elpos - elW / 2; // for center position if you want adjust then change this

      $(this).parent().animate({ scrollLeft: pos }, speed);

      return pos;
    };
  });
});

// card-center-container mid long term loan
$(document).ready(function () {
  const cardCenterContainer = $('section.card-center-container');
  const cardCenterContainerLarge = cardCenterContainer.find('.large');
  const cardCenterListCard = cardCenterContainerLarge.find('.card-center-listcard');

  //Adobe: return if element not found
  if (cardCenterListCard.length == 0) return;

  if (cardCenterListCard.hasClass('no-slick-mobile')) {
    cardCenterListCard.not('.slick-initialized').slick({
      slidesToShow: 3,
      slidesToScroll: 1,
      autoplay: false,
      arrows: true,
      dots: true,
      infinite: false,
      prevArrow:
        '<div data-role="none" class="slick-arrow slick-prev"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
      nextArrow:
        '<div data-role="none" class="slick-arrow slick-next"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
      responsive: [
        {
          breakpoint: 1201,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 1,
          },
        },
        {
          breakpoint: 768,
          settings: 'unslick',
        },
      ],
    });
  } else {
    cardCenterListCard.not('.slick-initialized').slick({
      slidesToShow: 3,
      slidesToScroll: 1,
      autoplay: false,
      arrows: true,
      dots: true,
      infinite: false,
      prevArrow:
        '<div data-role="none" class="slick-arrow slick-prev"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
      nextArrow:
        '<div data-role="none" class="slick-arrow slick-next"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
      responsive: [
        {
          breakpoint: 1201,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 1,
          },
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
          },
        },
      ],
    });
  }
  $(window).resize(function () {

    //Adobe: return if element not found
    if (cardCenterListCard.length == 0) return;

    if (cardCenterListCard.hasClass('no-slick-mobile')) {
      cardCenterListCard.not('.slick-initialized').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: false,
        arrows: true,
        dots: true,
        infinite: false,
        prevArrow:
          '<div data-role="none" class="slick-arrow slick-prev"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
        nextArrow:
          '<div data-role="none" class="slick-arrow slick-next"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
        responsive: [
          {
            breakpoint: 1201,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 1,
            },
          },
          {
            breakpoint: 768,
            settings: 'unslick',
          },
        ],
      });
    } else {
      cardCenterListCard.not('.slick-initialized').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: false,
        arrows: true,
        dots: true,
        infinite: false,
        prevArrow:
          '<div data-role="none" class="slick-arrow slick-prev"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
        nextArrow:
          '<div data-role="none" class="slick-arrow slick-next"><svg focusable="false" viewBox="0 0 24 24" aria-hidden="true"><path d="M5.88 4.12L13.76 12l-7.88 7.88L8 22l10-10L8 2z"></path></svg></div>',
        responsive: [
          {
            breakpoint: 1201,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 1,
            },
          },
          {
            breakpoint: 768,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
              arrows: false,
            },
          },
        ],
      });
    }
  });
});

// table component
$(document).ready(function () {
  const tableCMP = $('.table-container');

  if ($(window).width() <= 767) {
    tableMoveContent();
  } else {
    tableCMP.find('.moved-content').remove();
  }

  $(window).resize(function () {
    if ($(window).width() <= 767) {
      tableMoveContent();
    } else {
      tableCMP.find('.moved-content').remove();
    }
  });

  function tableMoveContent() {
    tableCMP.each(function () {
      const movedContent = $(this)
        .find('.no-scroll .table-body-row:first-child .table-cell-innercontent p')
        .last()
        .text();
      $(this).find('.moved-content').remove();
      $(this)
        .find('.no-scroll .table-cell-innercontent>div>div:last-child p')
        .before('<p class="moved-content">' + movedContent + '</p>');
    });
  }
});

// ripple clicked effect
function ripple(e) {
  // Setup
  const posX = this.offsetLeft;
  const posY = this.offsetTop;
  let buttonWidth = this.offsetWidth;
  let buttonHeight = this.offsetHeight;

  // Add the element
  const ripple = document.createElement('span');
  ripple.classList.add('ripple-btn-service-fee');
  this.appendChild(ripple);

  // Make it round!
  if (buttonWidth >= buttonHeight) {
    buttonHeight = buttonWidth;
  } else {
    buttonWidth = buttonHeight;
  }

  // Get the center of the element
  const x = e.pageX - posX - buttonWidth / 2;
  const y = e.pageY - posY - buttonHeight / 2;

  ripple.style.width = `${buttonWidth}px`;
  ripple.style.height = `${buttonHeight}px`;
  ripple.style.top = `${y}px`;
  ripple.style.left = `${x}px`;
  ripple.classList.add('ripple-animation-service-fee');
  setTimeout(() => {
    this.removeChild(ripple);
  }, 1000);
}

// Select Filter
$(document).ready(function () {
  const selectFilter = $('.select-filter');

  selectFilter.each(function () {
    const select = $(this).find('.select');
    const options = $(this).find('.options li');
    const oriSelectedText = select.find('span').text();
    const targetOption = $(this).find('.options>div');

    // show options to select
    select.click(function (e) {
      e.stopPropagation();
      $(this).toggleClass('showed');
    });

    // change placeholder
    options.click(function () {
      select.removeClass('showed');
      options.removeClass('selected');
      $(this).addClass('selected');

      if ($(this).index() == 0) {
        select.find('span').text(oriSelectedText);
      } else {
        select.find('span').text($(this).text());
      }
    });

    $(document).click(function (event) {
      if (!$(event.target).is(targetOption) && !$(event.target).parents().is(targetOption)) {
        select.removeClass('showed');
      }
    });
  });
});

// Floating Banner
$(document).ready(function () {
  $('.floating-banner').each(function () {
    $(this)
      .find('.floating-banner__btn-close')
      .click(function () {
        $(this).parent().parent().parent().remove();
      });
    setTimeout(function () {
      $('.floating-banner').remove();
    }, $(this).find('.floating-banner__container').attr('auto-hide') * 1000);
  });
});
// End of Floating Banner

// Scroll to top
$(document).ready(function () {
  const scrollToTopButton = $('.scroll-to-top');
  let hideButtonTimeout;

  function handleScroll() {
    if (window.scrollY > 300) {
      scrollToTopButton.addClass('show');
    } else {
      scrollToTopButton.removeClass('show');
    }
  }

  function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    $(window).off('scroll', handleScroll);
    clearTimeout(hideButtonTimeout);
    hideButtonTimeout = setTimeout(function () {
      if (window.scrollY <= 300) {
        scrollToTopButton.removeClass('show');
      }
      $(window).on('scroll', handleScroll);
    }, 5000);
  }

  scrollToTopButton.on('click', scrollToTop);
  $(window).on('scroll', handleScroll);
});
// End of Scroll to top
// End of

// calendar and real-estate-calc general code
$(document).ready(function () {
  $('body').on('click', function (event) {
    // if you click somewhere else not calendar-popup or not calendar-icon, it will hide the calendar-popup
    // if you click somewhere else not time__dropdown or not time-select, it will hide the time__dropdown
    // if you click somewhere else not modal active or not button-link, it will hide the modal
    if (
      $(event.target).parents('.calendar-popup').length == 0 &&
      $(event.target).parents('.ui-datepicker-header').length == 0 &&
      $(event.target).parents('.date-time-wrapper__input-extra').length == 0 &&
      !$(event.target).is('.calulator-analytics') &&
      !$(event.target).is('.calendar__input-field') &&
      $(event.target).parents('.calendar__input-field').length == 0
    ) {
      $('body').removeClass('calendar-showing');
      $('.calendar-popup.active').removeClass('active');
      $('.calendar_real_estate').removeClass('no-pointer');
    }
    if ($(event.target).parents('.time__dropdown').length == 0 && $(event.target).parents('.time-select').length == 0) {
      $('body').removeClass('time-showing');
      $('.time__dropdown.active').removeClass('active');
      $('.time-select .header-select__data-input').removeClass('no-pointer');
    }
    if (
      !$(event.target).is('.content-button__link.loan-calc') &&
      $(event.target).parents('.modal.active').length == 0
    ) {
      $('body').removeClass('overflow-hidden');
      $('.modal.active').removeClass('active');
    }
  });
});

const filterButton = $('.news_open-filter-button');
if (filterButton.length) {
  $(document.body).on('scroll', () => {
    if (filterButton.offset().top < 0 ) {
      filterButton.addClass('sticky');
    } else {
      filterButton.removeClass('sticky');
    }
  });
}

// accordion inspire
$(document).ready(function () {
  const accordionInspire = $('.accordion-inspire-component');

  accordionInspire.each(function () {
    const accordion = $(this);
    const itemTitle = accordion.find('.item-title');

    itemTitle.each(function () {
      $(this).click(function () {
        const itemDesc = $(this).parent();

        accordion.find('.item').not(itemDesc).addClass('hide');
        accordion.find('.item').not(itemDesc).removeClass('show');
        itemDesc.toggleClass('hide');
        itemDesc.toggleClass('show');
      });
    });

  });
});

// all-awards.html
let currentSelectedTab = 0;
$(`.tab-item`).addClass('hidden', true);
const handleTabClick = (tabIndex, prevTab, nextTab, element) => {
  const tabs = $(element).closest(".tabs-control-wrapper").find(".tab-item");
  if (tabs?.length > 0) {
    if (nextTab === prevTab) {
      return;
    }
    let prevTabElement;
    let nextTabElement;
    tabs.each(function () {
      if ($(this).attr('data-year') === prevTab) {
        prevTabElement = $(this);
      } else if ($(this).attr('data-year') === nextTab) {
        nextTabElement = $(this);
      }
    });
    prevTabElement.addClass('hidden');
    nextTabElement.removeClass('hidden');
  } else if ($(`.tab-item`).length) {
    $(`[data-year="${nextTab}"]`).removeClass('hidden');
  }
  else if ($('.tab-horizontal-report__tab-content-card').length) {
    const tabs = $('.tab-horizontal-report__tab-content-card');
    tabs[currentSelectedTab].classList.add('hidden');
    currentSelectedTab = tabIndex;
    tabs[currentSelectedTab].classList.remove('hidden');
  }
};

const tcbTabs = $('.tcb-tabs');
if (tcbTabs.length) {
  for (let tab of tcbTabs) {
    try {
      new TcbTab(tab, handleTabClick);
    } catch (e) {
      console.log(e);
    }
  }
}

$('input[name="totalPrice"]').on('paste', function (event) {
  if (event.originalEvent.clipboardData.getData('Text').match(/[^\d]/)) {
    event.preventDefault();
  }
});

const typeSection = $('#newsTypes');
const typeSectionOther = $('#newsTypes-1');
const checkboxes = typeSection.find('input[type="checkbox"]');
const checkboxesOther = typeSectionOther.find('input[type="checkbox"]');
const checkboxAll = $('#allTypePost');
const checkboxAllOther = $('#allTypePost-1');

window.toggleAll = (event) => {
  const checked = event.target.checked;
  checkboxes.prop('checked', checked);
  checkboxesOther.prop('checked', checked);
};

window.typeCheckboxChange = (event) => {
  const checked = event.target.checked;
  if (!checked) {
    checkboxAll.prop('checked', false);
  } else {
    let all = true;
    checkboxes.each((index, element) => {
      if (index !== 0 && !element.checked) {
        all = false;
      }
    });
    checkboxAll.prop('checked', all);
    checkboxAllOther.prop('checked', all);
  }
  checkboxesOther.each((i, e) => {
    if(event.target.name == e.name) {
      e.checked = event.target.checked;
    }
  });
};

window.typeCheckboxChangeOther = (event) => {
  const checked = event.target.checked;
  if (!checked) {
    checkboxAllOther.prop('checked', false);
  } else {
    let all = true;
    checkboxesOther.each((index, element) => {
      if (index !== 0 && !element.checked) {
        all = false;
      }
    });
    checkboxAll.prop('checked', all);
    checkboxAllOther.prop('checked', all);
  }
  checkboxes.each((i, e) => {
    if(event.target.name == e.name) {
      e.checked = event.target.checked;
    }
  });
};

window.toggleRadios = (groupName,selectedRadio) => {
  const radios = document.querySelectorAll('input[name="' + groupName + '"]');
  for (let radio of radios) {
    if (selectedRadio.value === radio.value) {
      radio.checked = true;
    }
  }
};


// TCB modal for grid pagination

try {
  new GridPagination($('.tcb-grid-card_list'), '.tcb-grid-card_item');
} catch (e) {
  console.log(e);
}
$(document).ready(function() {
  $('a.cta-button').each(function() {
    const currentHref = $(this).attr('href');
    if (currentHref && currentHref.startsWith('tel')) {
      $(this).attr('href', decodeURIComponent(currentHref));
    }
  });
});
