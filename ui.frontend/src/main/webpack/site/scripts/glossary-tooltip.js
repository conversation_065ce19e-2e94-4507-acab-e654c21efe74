// Glossary Tooltip
import { BaseComponent } from './base';
import { TRANSLATIONS, LANG_EN, LANG_VI } from './translations';
import DOMPurify from 'dompurify';

const arrowIcon = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow-icon.svg';
const isEn = `${location.pathname}/`.includes(`/${LANG_EN}/`);
const lang = isEn ? LANG_EN : LANG_VI;
const glossaryUrlVi = '/tu-khoa';
const glossaryUrlEng = `/${lang}/glossary`;

$(document).ready(function () {
  const glossaryTooltip = $(".glossary-tooltip");

  glossaryTooltip.each(function () {
    try {
      new TcbGlossaryTooltip($(this));
    } catch(e) {
      console.log(e);
    }
  });

  $(window).on('scroll', function() {
    if($('.glossary-tooltip-text-content').hasClass("show-glossary-tooltip")) {
      $('.glossary-tooltip-text-content').removeClass("show-glossary-tooltip");
		  $('.glossary-tooltip-text-content').addClass("hide-glossary-tooltip");
    }
  });

  $('body').on('click mouseover', function (event) {
    if ($(event.target).parents('.glossary-tooltip-text').length == 0) {
      const content = $('.glossary-tooltip-text-content');
      hideTooltip(content);
    }
  });

  function hideTooltip(element) {
    element.removeClass("show-glossary-tooltip");
    element.addClass("hide-glossary-tooltip");
  }
});

export class TcbGlossaryTooltip extends BaseComponent {
  element;

  constructor(element) {
    super();
    this.element = element;
    this.initTooltip();
  }

  initTooltip() {
    const termSlug = $(this.element).attr('data-term-slug');
    const tooltipTextElement = document.createElement('div');
    tooltipTextElement.className = 'glossary-tooltip-text';
    if ($('body').find('.glossary-tooltip-text').length == 0) {
      $('body').append(tooltipTextElement);
    }
    const tooltip = $('body').find('.glossary-tooltip-text');
    this.element.on('mouseover click', (event) => {
      const dataSlug = tooltip.attr('data-term-slug');
      if(dataSlug !== termSlug) {
        const tooltipTextContentElement = document.createElement('span');
        tooltipTextContentElement.className = 'glossary-tooltip-text-content box-shadow';
        tooltip.empty();
        tooltip.attr('data-term-slug', termSlug);
        tooltip.append(tooltipTextContentElement);

        this.getTooltipContent(tooltip, termSlug);
      }
      this.calPosition(tooltip);
      event.stopPropagation();
    });
  }

  calPosition(tooltip) {
    const content = tooltip.find('.glossary-tooltip-text-content');
    const contentWidth = 270;
    const contentHeight = content.outerHeight();

    let positionContent = this.element?.get(0).getBoundingClientRect()?.top - contentHeight;

    if (this.element?.get(0).getBoundingClientRect()?.top <= contentHeight) {
      positionContent = this.element?.get(0).getBoundingClientRect()?.bottom;
    }

    let tmpLeft = this.element?.get(0).getBoundingClientRect()?.left - contentWidth / 3;
    let tmpRight = $(window).width() - tmpLeft - contentWidth;
    let minMargin = 24;
    if (tmpRight < minMargin) {
      tmpRight = minMargin;
      tmpLeft = 'auto';
    } else if (tmpLeft < minMargin) {
      tmpLeft = minMargin;
      tmpRight = 'auto';
    }
    content.css({
      position: 'fixed',
      top: positionContent,
      left: tmpLeft,
      right: tmpRight,
    });

    this.showTooltip(content);
  }

  getTooltipContent(tooltip, slug) {
    if (!slug) {
      tooltip.empty();
      return;
    }

    const htmlRegExp = /^<.*?>$/;
    const urlGlossary = `${isEn ? glossaryUrlEng : glossaryUrlVi}${slug}`;

    $.ajax({
      url: `/graphql/execute.json/techcombank/termBySlug;slug=${slug.split('=')[1]}`,
      type: 'GET',
      dataType: 'json',
      success: function (response) {
        const arrowIconElement = document.createElement('img');
        arrowIconElement.src = arrowIcon;

        const viewMoreElement = document.createElement('a');
        viewMoreElement.target = '_blank';
        viewMoreElement.href = urlGlossary;
        viewMoreElement.className = 'view-more';
        viewMoreElement.textContent = TRANSLATIONS[lang]?.VIEW_DETAIL;
        viewMoreElement.append(arrowIconElement);

        const content = tooltip.find('.glossary-tooltip-text-content');
        const tooltipContent =
          response.data.vi.items.length != 0
            ? response.data.vi.items[0].preview.html
            : response.data.en.items[0].preview.html;
        if (!htmlRegExp.test(tooltipContent.replace(/\n$/, '').trim())) {
          const tooltipContentElement = document.createElement('p');
          tooltipContentElement.innerHTML = DOMPurify.sanitize(tooltipContent, { USE_PROFILES: { html: true } });
          content.append(tooltipContentElement);
        } else {
          content.append(tooltipContent);
        }
        content.append(viewMoreElement);
      },
      error: function () {
        tooltip.empty();
      },
      async: false,
    });
  }

  showTooltip(element) {
    element.removeClass("hide-glossary-tooltip");
    element.addClass("show-glossary-tooltip");
  }
}
