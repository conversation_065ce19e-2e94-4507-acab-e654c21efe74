import { BaseTemplateComponent } from './base-template-component';
import { screenSmMax } from './constants/common';
import { ACCORDION_OPEN_STYLE } from './constants/offer-common';
import { convertPxToRem } from './offer-helper';

export class OfferAccordionElement extends BaseTemplateComponent {
  constructor() {
    super('offer-detail-accordion-template');

    this.init();
  }

  get openDefault() {
    const dataOpenDefault = this.getAttribute('data-open-default');
    return /true/.test(dataOpenDefault);
  }

  get isLocation() {
    const dataLocation = this.getAttribute('data-location');
    return /true/.test(dataLocation);
  }

  toggleContent(element, wrapper, isLocation, isOpen = false) {
    const width = $(window).width();
    element.classList.toggle('active');
    const expand = element.lastElementChild;
    const answer = element.nextElementSibling;
    if (!answer) {
      return;
    }
    const answerHeight = convertPxToRem(answer.scrollHeight) + 'rem';
    const isOpenAnswer = answer.style.height !== answerHeight || isOpen;

    if (isOpenAnswer) {
      Object.assign(element?.style, {
        borderBottom: '0.0625rem solid #DEDEDE',
        paddingBottom: '1.25rem',
        marginBottom: '1rem',
      });
      answer.style.height = answerHeight;
      answer.style.marginBottom = '0';
      expand.style.transform = 'rotate(-180deg)';
      if (isLocation && width > screenSmMax) {
        wrapper.css('padding', '48px 0px');
      } else if (isLocation) {
        wrapper.css('padding', '40px 16px');
      }

      const imgBgThumbnail = wrapper.attr('data-bg-img');
      const imgBgColor = wrapper.attr('data-bg-color');

      if (imgBgThumbnail) {
        wrapper.css('background-image', imgBgThumbnail);
      } else if (imgBgColor) {
        wrapper.css('background-color', imgBgColor);
      }
    } else {
      Object.assign(element?.style, ACCORDION_OPEN_STYLE);
      answer.style.height = '0px';
      answer.style.marginBottom = '0';
      expand.style.transform = 'rotate(0deg)';
      wrapper?.css('background-image', 'none');
      wrapper?.css('background-color', '#ffffff');

      if (isLocation && width > screenSmMax) {
        wrapper.css('padding', '0px');
      } else if (isLocation) {
        wrapper.css('padding', '0px 16px');
      }
    }
  }

  init() {
    const accordion = $(this.shadowRoot);
    const questionAnswer = accordion.find('.enhancedfaqpanel');
    const wrapper = $(this).closest('.offer-detail-container');
    questionAnswer.each( (_, question) => {
      let questions = $(question).find('.question');
      questions.find('h3').addClass('text-base font-semibold');
      questions.each((_, question) => {
        if (this.openDefault) {
          this.toggleContent(question, wrapper, this.isLocation, true);
        }
        question.addEventListener('click', (event) => {
          this.toggleContent(event.currentTarget, wrapper, this.isLocation);
        });
      });
    });
  }
}
