import CurrencyHelper from './currency-helper';
import { addDataLayerObject } from './analytics';

$(document).ready(function () {
  $('.insurance-gain').each(function () {
    let insuarnceGain = $(this);
    let insuranceValue = [];
    let allowtrackAnalytics = false;

    try {
      insuranceValue = eval(insuarnceGain.attr('data-insurance-gain')) ?? [];
    } catch {}

    let ageValidate,
      defaulAgeValidation = {
        insuranceSL: { min: 0, max: 69 },
        insuranceAG: { min: 18, max: 50 },
        insuranceBL: { min: 0, max: 65 },
      };
    try {
      ageValidate = eval(insuarnceGain.attr('data-age-validate')) ?? defaulAgeValidation;
    } catch {
      ageValidate = defaulAgeValidation;
    }

    const currentYear = new Date().getFullYear();
    let _age = '';
    let _money = '';

    function numberWithComma(x) {
      return x
        .toString()
        .replaceAll(',', '')
        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    }

    insuarnceGain.find('.date-time-wrapper__input').keypress(function (e) {
      let charCode = e.which ? e.which : e.keyCode;
      if (String.fromCharCode(charCode).match(/\D/g)) {
        return false;
      }
      let preValue = e.target.value;
      let currentValue = String.fromCharCode(charCode);
      let finalValue = preValue + currentValue;
      if (e.target.getAttribute('name') == 'day' && finalValue > 31 && finalValue.length < 3) {
        return false;
      }
      if (e.target.getAttribute('name') == 'month' && finalValue > 12 && finalValue.length < 3) {
        return false;
      }
      if (
        e.target.getAttribute('name') == 'year' &&
        finalValue > currentYear &&
        (e.target.selectionStart !== 0) &&
        finalValue.length < 5
      ) {
        return false;
      }
    });

    function calcAge() {
      let day = insuarnceGain.find(".date-time-wrapper__input[name='day']").val();
      let month = insuarnceGain.find(".date-time-wrapper__input[name='month']").val();
      let year = insuarnceGain.find(".date-time-wrapper__input[name='year']").val();
      let d = new Date(year, month - 1, day);
      let today = new Date();
      let age = -1;
      if (
        d.getDate() == parseInt(day) &&
        d.getMonth() + 1 == parseInt(month) &&
        year == d.getFullYear() &&
        year <= new Date().getFullYear() &&
        year > 999 &&
        today > d
      ) {
        age = Math.floor((today - d) / (365.25 * 24 * 60 * 60 * 1000));
        insuarnceGain.find('.input-fields__error-msg').text('');
        insuarnceGain.find('.date-time-wrapper__input:not(.money__input-field)').css('border-color', '');
      } else {
        insuarnceGain.find('.input-fields__error-msg').text(insuarnceGain.attr('data-invalid-date'));
        insuarnceGain.find('.date-time-wrapper__input:not(.money__input-field)').css('border-color', '#f44336');
        insuarnceGain.find('.insurance-fee').html(0 + ` ${insuarnceGain.attr('data-currency-input')}`);
      }
      return age;
    }

    function trackAnalytics() {
      const trackDate = `${insuarnceGain.find('input[name=day]').val()}-${insuarnceGain
        .find('input[name=month]')
        .val()}-${insuarnceGain.find('input[name=year]').val()}`;

      const trackGender = insuarnceGain.find('.input-fields__drop-down .select-option .gender.selected').text();

      const money = _money ? parseInt(_money.replaceAll(',', '')) : 0;

      if (allowtrackAnalytics && trackDate && trackGender && money) {
        addDataLayerObject(
          'calculator',
          {
            clickInfo: {
              calculatorName: 'Insurance Gain',
              calculatorFields: `${trackDate} | ${trackGender} | ${money}`,
            },
          },
          { webInteractions: { name: 'Insurance Gain', type: 'other' } },
        );
        allowtrackAnalytics = false;
      }
    }

    function calcMoney() {
      const money = _money ? parseInt(_money.replaceAll(',', '')) : 0;
      if (_age > -1 && money > 0) {
        const gender = insuarnceGain.find('.option.selected.gender').attr('value') === '0' ? 0 : 1;
        const inputId = insuarnceGain.find('.date-time-wrapper__input.money__input-field').attr('id');
        let ageValidation = ageValidate?.[inputId] || { min: 0, max: 0 };
        if (_age >= ageValidation.min && _age <= ageValidation.max) {
          const insuranceItem = insuranceValue.find((x) => x.age === _age);
          if (!insuranceItem) {
            return;
          }
          const premiumIndex = insuranceItem?.[inputId]?.[gender] || 0;
          const newResult = Math.round((premiumIndex * money) / 1000 / 1000) * 1000;
          insuarnceGain
            .find('.insurance-fee')
            .html(`${CurrencyHelper.numberWithCommas(newResult)} ${insuarnceGain.attr('data-currency-input')}`);
          allowtrackAnalytics = true;
        } else {
          insuarnceGain.find('.input-fields__error-msg').text(insuarnceGain.attr('data-invalid-age'));
          insuarnceGain.find('.date-time-wrapper__input:not(.money__input-field)').css('border-color', '#f44336');
        }
      }
    }

    insuarnceGain.find("[name='moneyValue']").keyup(function () {
      _money = insuarnceGain.find("[name='moneyValue']").val();
      if (_money) {
        insuarnceGain.find('.date-time-wrapper__input.money__input-field').val(numberWithComma(_money));
        calcMoney();
      }
    });

    insuarnceGain.find("[name='year'], [name='month'], [name='day']").keyup(function () {
      _age = calcAge();
      calcMoney();
    });

    insuarnceGain.find("[name='year'], [name='month'], [name='day'], [name='moneyValue']").on('focusout', () => {
      trackAnalytics();
    });

    insuarnceGain.find("[name='moneyValue']").on('paste', function (event) {
      if (event.originalEvent.clipboardData.getData('Text').match(/[^\d\.\,]/)) {
        event.preventDefault();
      }
    });

    insuarnceGain.find('.drop-down__controls').click(function () {
      let option = $(this).next('.select-option');
      $(option).toggleClass('opened');
    });

    insuarnceGain.find('.select-option').click(function (e) {
      let selectOption = $(this);
      if ($(e.target).hasClass('option')) {
        let select = $(this).prev('.drop-down__controls');
        let selectcontrol = $(select).find('.drop-down__select');
        $(selectcontrol).html($(e.target).html());
        selectOption.find('.option').each((index, item) => {
          $(item).removeClass('selected');
          if ($(item).html() == $(e.target).html()) {
            $(item).addClass('selected');
          }
        });
        calcMoney();
        allowtrackAnalytics = true;
        trackAnalytics();
      }
      selectOption.removeClass('opened');
    });

    $(window).on('click', function (e) {
      if (
        !$(e.target).hasClass('drop-down__container') &&
        !$(e.target).hasClass('drop-down__controls') &&
        !$(e.target).hasClass('drop-down__select') &&
        !$(e.target).parents().hasClass('drop-down__controls')
      ) {
        $('.insurance-gain').find('.select-option').removeClass('opened');
      }
    });
  });
});
