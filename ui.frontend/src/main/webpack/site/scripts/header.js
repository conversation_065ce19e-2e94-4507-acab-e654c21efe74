$(document).ready(function () {
    $('.header_layout').each(function () {
        const header = $(this);
        const altButton = header.find(".navigation-secondary_actions .link_component-link.alt-link");
        const loginButton = header.find(".navigation-secondary_actions #login-btn");
        const secondaryMenu = header.find(".navigation_secondary .navigation-secondary_menu");
        const primaryMenu = header.find(".navigation_primary.content-wrapper");
        const hasSearchEngine = header.find("tcb-search-engine-mobile").hasClass("search-engine-show");

        let lastScrollTop = 0;
        let heigthMashead = document.querySelector('.tcb-hero-banner')?.clientHeight || 300;
        const heightHeader = header.height();

        if (altButton.length > 0 && secondaryMenu.length > 0) {
            if ($(window).width() > 1024) {
                $('.global-header').css("height", heightHeader);
                $('.global-header').css("position", "relative");
                $(this).css("position", "fixed");
                $(this).css("width", "100%");
                $(window).on("scroll", handleScroll);
            }

            adjustFooter();

            $(window).on("resize", function() {
                adjustFooter();
            })
        }

        function handleScroll() {
            if ($(window).scrollTop() > heigthMashead) {
                checkShowSticky(true)
            } else {
                checkShowSticky(false);
            }
        }

        function checkShowSticky(isShow) {
            if ($(window).scrollTop() > heigthMashead) {
                altButton.css("display", "flex");
                loginButton.hide();
                secondaryMenu.hide();
            } else {
                altButton.hide();
                loginButton.css("display", "flex");
                secondaryMenu.css("display", "flex");

            }
            const windowScroll = $(window).scrollTop();
            if (isShow) {
                if (windowScroll > lastScrollTop) {
                    primaryMenu.addClass("hide-nav");
                    primaryMenu.removeClass("show-nav");
                } else {
                    primaryMenu.removeClass("hide-nav");
                    primaryMenu.addClass("show-nav");
                }
                if (Math.abs(lastScrollTop - windowScroll) > 150) {
                    lastScrollTop = windowScroll;
                }
            } else {
                primaryMenu.removeClass("hide-nav");
                primaryMenu.addClass("show-nav");
            }
        }

        function adjustFooter() {
            if ($(window).width() <= 1024) {
                $(window).unbind("scroll");
                $('.global-header').css("height", hasSearchEngine ? "138px" : "65px");
                $('.global-header').css("position", "sticky");
                $(header).css("width", "100%");
                altButton.hide();
                loginButton.css("display", "flex");
                secondaryMenu.hide();
                let id = setInterval(frame, 100);
                function frame() {
                    let menu_mobile = header.find(".mobile-menu-items .navigation-secondary_menu");
                    if (menu_mobile.length > 0) {
                        clearInterval(id);
                        menu_mobile.css("display", "flex");
                    }
                }

                let buttonMobile = header.find(".alternate-button-mobile");
                if (buttonMobile) {
                    $("footer").css("margin-bottom", buttonMobile.outerHeight());
                }
            } else {

                $('.global-header').css("height", heightHeader);
                $('.global-header').css("position", "relative");
                $(header).css("position", "fixed");
                $(header).css("width", "100%");

                if ($(window).scrollTop() > heigthMashead) {
                    altButton.css("display", "flex");
                    loginButton.hide();
                    secondaryMenu.hide();
                    primaryMenu.addClass("hide-nav");
                    primaryMenu.removeClass("show-nav");
                } else {
                    altButton.hide();
                    loginButton.css("display", "flex");
                    secondaryMenu.css("display", "flex");
                    primaryMenu.removeClass("hide-nav");
                    primaryMenu.addClass("show-nav");
                }

                lastScrollTop = 0;
            }
        }
    });
});