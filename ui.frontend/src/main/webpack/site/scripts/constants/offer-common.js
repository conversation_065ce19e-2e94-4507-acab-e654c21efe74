export const HEADER_HEIGHT_VARIABLE = '--header-height';
export const ACCORDION_OPEN_STYLE = {
  borderBottom: '0.0625rem solid #DEDEDE',
  paddingBottom: '1.25rem',
  marginBottom: '1rem',
};
export const SLASH = '/';
export const SELECTOR_DETAIL_LINK = '[href^="?detail"]';

export const TYPE_CARD_THE_TIN_DUNG = 'the-tin-dung';
export const TYPE_CARD_THE_THANH_TOAN = 'the-thanh-toan';

export const ID_PROMOTION_SEARCH = 'promotion-hub-search-result';
export const ID_PROMOTION_FILTER = 'promotion-hub-filter-result';
export const TYPE_PROMOTION_SEARCH = 'searchPromotion';
export const TYPE_PROMOTION_FILTER = 'filterPromotion';
export const CLASS_HIDDEN_ELEMENT = 'hidden-forced';
export const KEY_RECENT_SEARCH = 'recentSearch';
export const DEFAULT_SORT_PARAMS = 'most popular';

export const LABEL_PDF_PREVIEW = 'Preview';
export const LABEL_PDF_DOWNLOAD = 'Download';
export const LABEL_PDF_LOADING_MESSAGE = 'Loading PDF...';

export const SELECTOR_MEMBERSHIP_LIST = 'tcb-result-page-membership[data-type="3"]';
export const SELECTOR_MEMBERSHIP_SEARCH = 'tcb-result-page-membership[data-type="4"]';
export const PARAM_ALL_PRODUCT = 'all-product';
