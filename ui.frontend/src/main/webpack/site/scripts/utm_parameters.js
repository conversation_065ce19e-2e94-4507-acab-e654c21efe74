import { LocationUtil } from './utils/location.util';
import { utmUrlParams } from './constants/common';

$(document).ready(function () {
  const origin = document.location.origin;

  // check if currnet url has utm_* parameters
  const currentUrlParamObj = LocationUtil.getUrlParamObj();
  const isContainedUTMParam = utmUrlParams.findIndex((param) => Object.keys(currentUrlParamObj).includes(param)) !== -1;
  if (!isContainedUTMParam) {
    return;
  }

  // get all link elements
  const linkElements = document.querySelectorAll(`a[href*='${origin}']`);
  if (!linkElements || !linkElements.length) {
    return;
  }

  linkElements.forEach((linkElement) => {
    const linkUrlParamObject = LocationUtil.getUrlParamObj(linkElement.href);
    utmUrlParams.forEach((param) => {
      // only add utm_* incase current element doesnt have it yet.
      if (!linkUrlParamObject[param] && currentUrlParamObj[param]) {
        linkUrlParamObject[param] = currentUrlParamObj[param];
      }
    });

    const url = new URL(linkElement.href.split('?')[0]);
    Object.keys(linkUrlParamObject).forEach((key) => {
      url.searchParams.set(key, linkUrlParamObject[key]);
    });
    linkElement.href = url.href;
  });
});
