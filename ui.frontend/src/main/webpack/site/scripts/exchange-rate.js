import _ from 'lodash';
import moment from 'moment';
import { addDataLayerObject } from './analytics';
import <PERSON><PERSON><PERSON>cy<PERSON><PERSON><PERSON> from './currency-helper';
import 'jquery-ui/ui/widgets/datepicker.js';

// Fixing rate
$(document).ready(function () {
  $('.exchange-rate').each(function () {
    let fixingRate = $(this);
    let timeSelect = fixingRate.find('.time-select');

    let viewMoreButton = fixingRate.find('.exchange-rate__view-more button');
    let fixingRatePopup = fixingRate.find('.exchange-rate__popup');
    let popupCloseButton = fixingRatePopup.find('.popup__close-button');
    let downloadButton = fixingRatePopup.find('.popup__download-button button');
    let tableContainer = fixingRate.find('.table-content-container');
    let emptyLabel = fixingRate.find('.exchange-rate__empty-label');
    let tableElements = fixingRate.find('.exchange-rate-table-content');
    let selectedDateElements = fixingRate.find('.calendar__input-field');
    const calendarElements = fixingRate.find('.calendar_real_estate');

    let currentDate = moment().format('DD/MM/yyyy');
    let currentTime = null;
    const currentUrl = window.location.href;

    fixingRatePopup.css({
      opacity: '0',
      transform: 'scale(0.75, 0.5625)',
      transition: 'opacity 461ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 307ms cubic-bezier(0.4, 0, 0.2, 1) 154ms',
      visibility: 'hidden',
    });

    viewMoreButton.each(function () {
      $(this).click(function () {
        $('body').css('overflow', 'hidden');
        $(this).parents().siblings('.exchange-rate__popup').css({
          opacity: '1',
          transform: 'none',
          transition:
            'opacity 461ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 307ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
          visibility: '',
        });
      });
    });

    popupCloseButton.click(function () {
      $('body').css('overflow', '');
      fixingRatePopup.css({
        opacity: '0',
        transform: 'scale(0.75, 0.5625)',
        transition:
          'opacity 461ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 307ms cubic-bezier(0.4, 0, 0.2, 1) 154ms',
        visibility: 'hidden',
      });
    });

    function printExchangeRate() {
      const currentScrollTop = document.documentElement.scrollTop;
      document.body.classList.add('print');
      const popup = document.querySelector('.exchange-rate__popup');

      const printable = document.createElement('div');
      document.body.append(printable);
      printable.classList.add('exchange-rate');
      printable.classList.add('exchange-rate__printable');
      printable.classList.add('printable');

      printable.innerHTML = popup.innerHTML;

      const onafterprint = () => {
        document.body.classList.remove('print');
        document.querySelector('.exchange-rate__printable').remove();
        document.documentElement.scrollTop = currentScrollTop;
        document.documentElement.style.removeProperty('scroll-behavior');
        window.removeEventListener('afterprint', onafterprint);
      };

      window.addEventListener('afterprint', onafterprint);

      document.documentElement.style.setProperty('scroll-behavior', 'unset');
      document.documentElement.scrollTop = 0;
      setTimeout(() => {
        print();
      }, 2000);
    }

    downloadButton.click(function () {
      printExchangeRate();
    });

    function renderExchangeRateRecord(exchangeRate) {
      return `
        <div class='exchange-rate__table-records'>
          <div class='table__first-column left-pos strong-par first-column'>
            <p>${exchangeRate.label ?? ''}</p>
          </div>
          <div class='table-records__data'>
            <div class='table-records__data-content row-divided'>
              <div class='data-content__item'>
                <p>${CurrencyHelper.numberWithCommas(exchangeRate.bidRateTM)}</p>
              </div>
              <div class='data-content__item'>
                <p>${CurrencyHelper.numberWithCommas(exchangeRate.bidRateCK)}</p>
              </div>
            </div>
            <div class='table-records__data-content row-divided last-column'>
              <div class='data-content__item'>
                <p>${CurrencyHelper.numberWithCommas(exchangeRate.askRateTM)}</p>
              </div>
              <div class='data-content__item last-column'>
                <p>${CurrencyHelper.numberWithCommas(exchangeRate.askRate)}</p>
              </div>
            </div>
          </div>
        </div>
      `;
    }

    function generateTable(element, items) {
      // clear element html
      element.empty();

      const exchangeRateGroup = _.chain(items).groupBy('label').value();
      const exchangeRateCurrencies = Object.keys(exchangeRateGroup);
      exchangeRateCurrencies.forEach((currency) => {
        const exchangeRateByCurrency = exchangeRateGroup[currency];
        if (exchangeRateByCurrency.length > 0) {
          const exchangeRateNewest = exchangeRateByCurrency.sort((a, b) => {
            return new Date(b.inputDate) - new Date(a.inputDate);
          })[0];
          element.append(renderExchangeRateRecord(exchangeRateNewest));
        }
      });
    }

    function generateTenorIntRate(items) {
      if (items === null || items.length == 0) {
        fixingRate.find('#tenorint-rate').addClass('hidden');
        return;
      }
      fixingRate.find('#tenorint-rate').removeClass('hidden');
      let container = fixingRate.find('#tenorint-rate-content');
      container.empty();
      const map = _.chain(items)
        .groupBy('rateCD')
        .map((rates, rateCD) => ({ rates, label: rateCD }))
        .value();
      let tenors = [
        ...Array.from(new Set(items.map((e) => Number(e.tenor) ?? ''))).sort((a, b) => a - b),
        '',
        '',
        '',
      ].slice(0, 3);
      container.append(
        `
          <div class='exchange-rate__table-records'>
            <div class='table__first-column left-pos strong-par first-column'>
              <p>${currentDate}</p>
            </div>
            <div class='table-records__data width-66-33'>
              <div class='table-records__data-content row-divided'>
                <div class='data-content__item'>
                  <p>${tenors[0]}</p>
                </div>
                <div class='data-content__item'>
                  <p>${tenors[1]}</p>
                </div>
              </div>
              <div class='table-records__data-content last-column'>
                <p>${tenors[2]}</p>
              </div>
            </div>
          </div>
          `,
      );
      map.forEach((item) => {
        container.append(
          `
          <div class='exchange-rate__table-records'>
            <div class='table__first-column left-pos strong-par first-column'>
              <p>${item.label}</p>
            </div>
            <div class='table-records__data width-66-33'>
              <div class='table-records__data-content row-divided'>
                <div class='data-content__item'>
                  <p>${
                    tenors[0] === ''
                      ? ''
                      : CurrencyHelper.numberWithCommas(item.rates.find((e) => Number(e.tenor) === tenors[0])?.tenorInt)
                  }</p>
                </div>
                <div class='data-content__item'>
                  <p>${
                    tenors[1] === ''
                      ? ''
                      : CurrencyHelper.numberWithCommas(item.rates.find((e) => Number(e.tenor) === tenors[1])?.tenorInt)
                  }</p>
                </div>
              </div>
              <div class='table-records__data-content last-column'>
                <p>${
                  tenors[2] === ''
                    ? ''
                    : CurrencyHelper.numberWithCommas(item.rates.find((e) => Number(e.tenor) === tenors[2])?.tenorInt)
                }</p>
              </div>
            </div>
          </div>
          `,
        );
      });
    }

    function generateTenorRate(items) {
      let container = fixingRate.find('#tenor-rate-content');
      container.empty();
      let data = items.sort((a, b) => a.tenor - b.tenor);
      data.forEach((e) => {
        container.append(
          `
          <div class='exchange-rate__table-records'>
            <div class='table__first-column left-pos strong-par first-column'>
              <p>${e.tenor}</p>
            </div>
            <div class='table-records__data width-66-33'>
            <div class='table-records__data-content row-divided'>
              <div class='data-content__item'>
                <p></p>
              </div>
              <div class='data-content__item'>
                <p>${CurrencyHelper.numberWithCommas(e.bidRate)}</p>
              </div>
            </div>
            <div class='table-records__data-content last-column'>
              <p>${CurrencyHelper.numberWithCommas(e.askRate)}</p>
            </div>
          </div>
          `,
        );
      });
    }

    function generatePopupTable(data) {
      let otherRate = data?.otherRate?.data[0];
      fixingRate.find('#central-rate').text(CurrencyHelper.numberWithCommas(otherRate?.central));
      fixingRate.find('#floor-rate').text(CurrencyHelper.numberWithCommas(otherRate?.floor));
      fixingRate.find('#ceiling-rate').text(CurrencyHelper.numberWithCommas(otherRate?.ceiling));

      let goldRates = data?.goldRate?.data;
      if (goldRates.length > 0) {
        let goldRate = goldRates[0];
        fixingRate.find('#gold-bid-rate').text(CurrencyHelper.numberWithCommas(goldRate.bidRate));
        fixingRate.find('#gold-ask-rate').text(CurrencyHelper.numberWithCommas(goldRate.askRate));
      }
      let tenorIntRate = data?.tenorintRate?.data ?? [];
      generateTenorIntRate(tenorIntRate);

      let tenorRate = data?.tenorRate?.data ?? [];
      generateTenorRate(tenorRate);
    }

    const trackingAA = () => {
      addDataLayerObject(
        'calculator',
        {
          clickInfo: {
            calculatorName: 'Exchange Rate',
            calculatorFields: `${currentDate || ''}${currentDate && currentTime ? '|' : ''}${currentTime || ''}`,
          },
        },
        { webInteractions: { name: 'Calculators', type: 'other' } },
      );
    };

    selectedDateElements.each((idx, element) => {
      $(element).text(currentDate);
      $(element).on('updateCal', function () {
        if (currentDate === $(element).text()) {
          return;
        }
        currentDate = $(element).text();
        currentTime = null;
        updateData(currentDate, currentTime);
        trackingAA();
      });
    });

    calendarElements.each(function () {
      $(this).on('click', function () {
        const calendarPopup = $(this).next('.calendar-popup');
        const calendarInputHeight = $(this).outerHeight();
        if (calendarPopup?.length === 0) {
          return;
        }
        calendarPopup.css({ top: `${calendarInputHeight}px` });
        if (calendarPopup?.hasClass('active')) {
          calendarPopup?.removeClass('active');
        } else {
          calendarPopup?.addClass('active');
        }
      });
    });

    timeSelect.on('timeChanged', function (event, newValue) {
      currentTime = newValue;
      updateData(currentDate, currentTime);
      trackingAA();
    });

    updateData(currentDate, null);

    function updateData(date, time) {
      selectedDateElements.each((idx, element) => {
        $(element).text(currentDate);
      });

      let baseUrl = fixingRate.attr('data-url');
      if (baseUrl === undefined) {
        return;
      }
      const dateUrl = date ? `.${date.split('/').reverse().join('-')}` : '';
      const timeUrl = date && time ? `.${time.replaceAll(':', '-')}` : '';
      const url = `${baseUrl}/_jcr_content.exchange-rates${dateUrl}${timeUrl}.integration.json`;

      $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          let exchangeRate = data?.exchangeRate;
          // If not available
          if (exchangeRate === null || exchangeRate.data.length == 0) {
            emptyLabel.removeClass('hidden');
            tableContainer.addClass('hidden');
            timeSelect.changeOptions(exchangeRate.updatedTimes, currentTime);
            return;
          }

          emptyLabel.addClass('hidden');
          tableContainer.removeClass('hidden');

          // generate table
          tableElements.each((idx, element) => {
            generateTable($(element), exchangeRate.data);
          });
          generatePopupTable(data);
          timeSelect.changeOptions(exchangeRate.updatedTimes, currentTime);
          if (currentTime === null && exchangeRate.updatedTimes.length > 0) {
            currentTime = exchangeRate.updatedTimes[0];
          }
          fixingRate.find('.exchange-rate__table-note').each(function () {
            let element = $(this);
            let note = element.attr('note');
            if (note == null) {
              return;
            }
            note = note
              .replace(
                '%updatedDate',
                currentUrl.includes('/en/') ? moment(currentDate, 'DD/MM/YYYY').format('DD MMM YYYY') : currentDate,
              )
              .replace('%updatedTime', currentTime);
            element.innerHTML = note;
          });
        },
      });
    }

    const hash = window.location.hash;
    if (hash) {
      const headerHeight = $('header .header_layout').outerHeight();
      $(this)
        .parents(`${hash}`)
        .css({ 'scroll-margin-top': `${headerHeight}px` });
    }
  });
});
