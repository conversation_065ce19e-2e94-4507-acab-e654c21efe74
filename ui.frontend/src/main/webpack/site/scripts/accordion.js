$(document).ready(function() {
    let accordion = $('.accordion-component');

    accordion.each(function() {
        // Effects on open/close panels
        $(this).find('.card-product-feature__item').each(function() {
            $(this)
                .find('.card-product-feature__item__content__wrapper')
                .click(function() {
                    openAccordion($(this));
                });
        });

        // Automatically open an accordion
        let accordionTabToAutoOpenIndex = $(this).find('.card-product-feature__container').attr('accordion-tab-to-auto-open');
        if(accordionTabToAutoOpenIndex > 0) {
            let accordionTabToAutoOpen = $(this).find('.card-product-feature__item').eq(accordionTabToAutoOpenIndex - 1);
            openAccordion(accordionTabToAutoOpen.find('.card-product-feature__item__content__wrapper'));
        }
        
        // AA integration
        $(this).find('.card-product-feature__item__content__wrapper').each(function() {
            let accordionTitle = $(this).find('.card-product-feature__item__content__title').text();
            $(this).data("trackingClickInfoKey", "offer");
            $(this).attr("data-tracking-click-event", "accordions");
            $(this).attr("data-tracking-click-info-value", "{'accordionTitle' : '" + accordionTitle + "'}");
            $(this).attr(
                "data-tracking-web-interaction-value", "{'webInteractions': {'name': 'Accordion','type': 'other'}}"
            );
        });
    });
    function openAccordion(accordion) {
        let description = accordion.parent().parent().find('.card-product-feature__item__description');
        let descriptionText = description.find('.text');
        let imgOn = accordion.parent().parent().find('.card-product-feature__item__img__on');
        let imgOff = accordion.parent().parent().find('.card-product-feature__item__img__off');
        let iconOn = accordion.find('.card-product-feature__item__content__icon__on');
        let iconOff = accordion.find('.card-product-feature__item__content__icon__off');
        let item = accordion.parent().parent();
        let outerHeightDescription = description.outerHeight();
        let outerHeightText = descriptionText.outerHeight();
        if (descriptionText.css('visibility') == 'hidden') {
            description.css('height', outerHeightText + outerHeightDescription);
            descriptionText.css('visibility', 'visible');
            imgOff.hide();
            imgOn.show();
            iconOn.hide();
            iconOff.show();
            item.css('background', 'white');
        } else {
            description.css('height', 0);
            imgOff.show();
            imgOn.hide();
            iconOff.hide();
            iconOn.show();
            item.css('background', '#f5f6f8');
            descriptionText.css('visibility', 'hidden');
        }
    }
});
