import { BaseComponent } from './base';
import { LocationUtil } from './utils/location.util';
import { StorageUtils } from './utils/storage.util';

const PREVIOUS_PAGE_KEY = 'previousPage';

interface IPreviousPage {
  slug: string;
  title: string;
}

export class TcbRecommendPrePagePopup extends BaseComponent {
  previousPage: IPreviousPage;

  get hasSocialParam(): boolean {
    const urlParamObj = LocationUtil.getUrlParamObj();
    return Object.keys(urlParamObj).some((x: string) => x.startsWith('utm_'));
  }

  get isDisplay(): boolean {
    const previousPage = this.previousPage;
    return (
      !this.hasSocialParam && previousPage?.slug && previousPage.slug !== '/' && previousPage.slug !== location.pathname
    );
  }

  get iconBell(): Element {
    return this.querySelector('.icon-svg');
  }

  set iconBellDisplay(isDisplay: boolean) {
    if (this.iconBell) {
      if (isDisplay) {
        this.iconBell.classList.add('display');
      } else {
        this.iconBell.classList.remove('display');
      }
    }
  }

  get popup(): Element {
    return this.querySelector('.recommend-pre-page-popup');
  }

  constructor() {
    super();
    this.initPreviousPage();
    this.initCurrentPage();

    if (this.previousPage) {
      this.initIconBell();
      this.initButtons();
      this.initDescription();
    }
  }

  private initPreviousPage() {
    if (!sessionStorage.getItem('ts')) {
      sessionStorage.setItem('ts', new Date().getTime().toString());
      this.previousPage = StorageUtils.get(PREVIOUS_PAGE_KEY);
    }
  }

  private initIconBell() {
    if (this.isDisplay) {
      this.iconBellDisplay = this.isDisplay;
    }

    this.iconBell?.addEventListener('click', () => {
      this.popup?.classList.remove('hidden');
      this.iconBellDisplay = false;
    });
  }

  private initButtons() {
    this.querySelector('button.cancel')?.addEventListener('click', () => {
      StorageUtils.remove(PREVIOUS_PAGE_KEY);
      this.popup?.classList.add('hidden');
    });
    this.querySelector('button.primary')?.addEventListener('click', () => {
      location.href = `${location.origin}${this.previousPage?.slug}`;
    });
  }

  private initDescription() {
    const description = this.querySelector('div.recommend-description');
    description.innerHTML = `${description.textContent} <span style="color: rgb(0, 0, 0); font-weight: 500;">${this.previousPage.title}</span>`;
  }

  private initCurrentPage() {
    const title = document.querySelector('body').getAttribute('data-tracking-page-name');
    StorageUtils.set(PREVIOUS_PAGE_KEY, {
      slug: location.pathname,
      title: title || document.title,
    });
  }
}
