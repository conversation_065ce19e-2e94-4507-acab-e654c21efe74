import { createSearchUrl } from './offer-helper';

/*
* <PERSON><PERSON> retain params when switch languages on offer detail page.
*/
$(function () {
  const languageDropdownItems = $('header .language_dropdown a');
  const offerListingComponent = $(".offer-listing-filter__body");
  const isLoadedJs = offerListingComponent?.attr('data-js-switch-lang-detail');
  if (!offerListingComponent.length || isLoadedJs) {
    return;
  }
  // Make sure this script file only run one time.
  offerListingComponent.attr('data-js-switch-lang-detail', 'true');

  const paramsName = {
    detail: "detail"
  };
  const detailPopup = $("#detail-container");
  const detailName = new URL(window.location)?.searchParams?.get(paramsName.detail);

  const LOCALE_EN_KEY = 'en';
  const LOCALE_VI_KEY = 'vi';

  init();

  function init() {
    listenerDetailPopup();
  }

  function listenerDetailPopup() {
    const detailPopupElement = detailPopup?.[0];
    if (!detailPopupElement) {
      return;
    }
    const observer = new MutationObserver((mutationsList) => {
      mutationsList.forEach((mutation) => {
        const className = mutation.target?.className;
        if (mutation.attributeName === 'class' && className) {
          generateLinkDropdown(className.includes('open'));
        }
      });
    });
    const config = { attributes: true, attributeFilter: ['class'] };
    observer.observe(detailPopupElement, config);
  }

  function getLocaleFromLink(link) {
    const isEnLink = link?.includes('/' + LOCALE_EN_KEY + '/');
    return isEnLink ? LOCALE_EN_KEY : LOCALE_VI_KEY;
  }

  function handleClickLanguageLink(href) {
    let params = null;
    if (isOpen) {
      params = { [paramsName.detail]: detailName };
    }
    window.location.href = createSearchUrl(href, params);
  }

  function generateLinkDropdown(isOpen = true) {
    const localesString = detailPopup.attr('data-locales');
    const localeList = localesString?.split(',');
    languageDropdownItems.each((_, element) => {
      const href = $(element)?.attr("href");
      const localeKey = getLocaleFromLink(href);
      if (localeList?.includes(localeKey)) {
        $(element).on('click', (event) => {
          event.preventDefault();
          handleClickLanguageLink(href);
        });
      } else {
        $(element)?.parent()?.hide();
      }
    });
  }
});
