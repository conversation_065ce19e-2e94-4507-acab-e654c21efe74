import DOMPurify from 'dompurify';
import { BaseTemplateComponent } from './base-template-component';
import { PREVIOUS_ARRAY_PAGE_KEY_NAME, screenSmMax } from './constants/common';
import { clearSearchParams, fetchData, removeHTMLTags, resetHistory, updateSearchParams,
  handleResetDropdownLanguage, disableBodyScroll, convertPxToRem, listenerElement } from './offer-helper';
import { SessionStorageUtil } from './utils/storage.util';
import { ACCORDION_OPEN_STYLE, SELECTOR_DETAIL_LINK, HEADER_HEIGHT_VARIABLE } from './constants/offer-common';

const paramsName = {
  sort: 'sort',
  products: 'products',
  types: 'types',
  partner: 'partner',
  limit: 'limit',
  offset: 'offset',
  detail: 'detail',
  detailName: 'name',
  cardTypes: 'card-types',
};
const RELATED_LIMIT = 5;
const RELATED_OFFSET = 0;
const OFFSET_SCROLL_LOCATION = 48;
const SPEED_SCROLL_LOCATION = 500;
const LOCATION_SECTION_ID = '#offer-address';

export class OfferDetailElement extends BaseTemplateComponent {
  // Global fields to avoid duplication
  detailPlaceholder = $('#detail-container');
  offerListingComponent = $('.promotion-hub-secondary');
  dataElement = this.offerListingComponent.find('.offer-listing-promotions');
  timer = null;
  footer = document.getElementsByTagName('footer')[0];
  dialogDetailData;
  headerJQuery = $('header');
  headerElement = null;
  detailPlaceholderElement = null;
  windowJQuery = $(window);
  cardTypeInputsJQuery = $('.offer-filter__checkbox.card-checkbox:not(.card-checkbox-mobile) input');
  offerDetailElementJQuery = $('tcb-offer-listing-detail');
  shadowRootJQuery = null;
  imageCardProductImgJQuery = $('.image-card-product img');
  detailName = null;
  productTypes = [];
  cardTypes = [];
  categories = [];
  renderPromotionCardItem = () => { };
  renderExpiryDate = (_isRelated, _element) => { };
  showPreviewModalListener = () => { };
  dataURL = '';
  dataLanguageLabel = null;

  selectorPdfLink = `.related-listing .card-content__link a[href]:not(${SELECTOR_DETAIL_LINK})`;
  selectorDetailLink = `.related-listing .card-content__link a${SELECTOR_DETAIL_LINK}`;
  constructor() {
    super('offer-listing-detail-template');
    this.initializeElements();
    this.init();
  }

  initializeElements() {
    // Initialize header element using global headerJQuery field
    if (this.headerJQuery && this.headerJQuery.length > 0) {
      this.headerElement = this.headerJQuery[0];
    }

    // Initialize detail placeholder element using global detailPlaceholder field
    if (this.detailPlaceholder && this.detailPlaceholder.length > 0) {
      this.detailPlaceholderElement = this.detailPlaceholder[0];
    }

    // Initialize shadowRoot jQuery field
    if (this.shadowRoot) {
      this.shadowRootJQuery = $(this.shadowRoot);
    }

    // Initialize data URL and language label
    if (this) {
      this.dataURL = this.dataset.url;
      this.dataLanguageLabel = this.dataset.languageLabel;
    }
  }

  init(){
    this.initDialogDetail();
    this.handleBackBrowserDetail();
    this.handleSetHeaderHeight();
  }

  appendSlotContent(tagName, slot, content, isHTML = false) {
    const element = document.createElement(tagName);
    element.setAttribute('slot', slot);
    if (isHTML) {
      element.innerHTML = DOMPurify.sanitize(content);
    } else {
      element.textContent = content;
    }
    this.appendChild(element);
  }

  appendSlotHTMLContent(tagName, slot, content) {
    this.appendSlotContent(tagName, slot, content, true);
  }

  getCardTypesPrefix(tagItems) {
    if (!tagItems) {
      return null;
    }

    return tagItems.map((item) => {
      let prefix = null;
      if (item && item.path) {
        const pathParts = item.path.split('card-types/');
        if (pathParts && pathParts.length > 1 && pathParts[1]) {
          const subParts = pathParts[1].split('/');
          if (subParts && subParts.length > 0 && subParts[0]) {
            prefix = subParts[0];
          }
        }
      }
      if (!prefix) {
        return item;
      }
      return {
        ...item,
        key: `${prefix}/${item.key}`
      };
    });
  }

  addContent(detailDialogData, detailData, _dataURL, dataLanguageLabel, utils) {
    // Initialize shadowRootJQuery now that shadowRoot is available
    if (this.shadowRoot && !this.shadowRootJQuery) {
      this.shadowRootJQuery = $(this.shadowRoot);
    }
    const {
      description,
      expiryDate,
      merchant,
      locations,
      promotionContent,
      applicableContent,
      noteContent,
      locationSubtitle,
      url: pdfLink,
      productsSubtitle,
      locationSectionCTALabel,
      cardTypes,
      productTypes,
      categories,
      name,
      productsCTAPrimaryLink,
      productsCTASecondaryLink,
    } = detailData || {};

    const {
      imageMasthead,
      imageAppliedProducts,
      imageLocationSection,
      imageMobileMasthead,
      imageMobileAppliedProducts,
      imageMobileLocationSection,
      productsPrimaryCTALabel,
      productsSecondaryCTALabel,
      bgColorAppliedProducts,
      bgColorLocationSection,
      bgColorMasthead,
      labelLocationViewmore,
    } = detailDialogData || {};

    const { renderPromotionCardItem, renderExpiryDate, showPreviewModalListener } = utils || {};

    this.detailName = name;
    this.productTypes = productTypes;
    this.categories = categories;
    this.cardTypes = this.getCardTypesPrefix(cardTypes);

    this.renderPromotionCardItem = renderPromotionCardItem;
    this.renderExpiryDate = renderExpiryDate;
    this.showPreviewModalListener = showPreviewModalListener;
    let locationsDemo = [];

    if (promotionContent) {
      this.appendSlotHTMLContent('span', 'promotionContent', promotionContent);
    } else {
      const offerTypeSectionElement = $(this.shadowRoot).find('.offer-detail-offer-type__wrapper');
      if (offerTypeSectionElement) {
        offerTypeSectionElement.remove();
      }
    }

    this.addCardType(cardTypes, productsSubtitle);

    if (applicableContent) {
      this.appendSlotHTMLContent('p', 'applicableContent', applicableContent);
    } else {
      const applicableSectionElement = $(this.shadowRoot).find('.offer-detail-applicable__wrapper');
      if (applicableSectionElement) {
        applicableSectionElement.remove();
      }
    }

    if (noteContent) {
      this.appendSlotHTMLContent('p', 'noteContent', noteContent);
    } else {
      const noteSectionElement = $(this.shadowRoot).find('.offer-detail-note__wrapper');
      if (noteSectionElement) {
        noteSectionElement.remove();
      }
    }

    locationsDemo = this.addLocation({ locations, labelLocationViewmore, locationSubtitle, pdfLink,
      locationSectionCTALabel, showPreviewModalListener });

    let merchantTags = '';
    if (merchant && Array.isArray(merchant) && merchant.length > 0) {
      const merchantTitles = merchant.map((item) => item.title);
      if (merchantTitles && merchantTitles.length > 0) {
        merchantTags = merchantTitles.join(', ');
      }
    }
    this.appendSlotContent('span', 'merchantTag', merchantTags);
    this.appendSlotHTMLContent('h1', 'description', description);
    this.appendSlotContent('span', 'expiryDate', expiryDate);
    this.appendSlotContent('span', 'locations', locationsDemo.join(', '));

    this.handleClickClose();
    this.handleClickAllRelated();

    this.createBackground({
      imageMasthead,
      imageAppliedProducts,
      imageLocationSection,
      imageMobileMasthead,
      imageMobileAppliedProducts,
      imageMobileLocationSection,
      bgColorAppliedProducts,
      bgColorLocationSection,
      bgColorMasthead,
    });

    this.renderProductsButtons(
      productsCTAPrimaryLink,
      productsCTASecondaryLink,
      productsPrimaryCTALabel,
      productsSecondaryCTALabel,
    );

    this.getRelatedPromotions(_dataURL);

    if (this.footer) {
      this.shadowRoot.appendChild(this.footer.cloneNode(true));
      this.initFooter(this.shadowRoot);
    }
  }

  getCardTypesFilter() {
    if (!this.cardTypeInputsJQuery || this.cardTypeInputsJQuery.length === 0) {
      return [];
    }
    return this.cardTypeInputsJQuery.map((_, input) => {
      const val = $(input).val();
      if (val) {
        return val.toLowerCase();
      }
      return null;
    }).get().filter(item => item !== null);
  }

  removeCardThumbWrapper() {
    if (this.shadowRootJQuery) {
      const cardListWrapper = this.shadowRootJQuery.find('.product-card-list');
      if (cardListWrapper) {
        cardListWrapper.remove();
      }
    }
  }

  addCardType(cardTypes, productsSubtitle) {
    const cardTypesFilterListing = this.getCardTypesFilter();
    const cardTypesFiltered = cardTypes.filter((cardItem) => {
      if (cardItem && cardItem.key) {
        return cardTypesFilterListing.includes(cardItem.key.toLowerCase());
      }
      return false;
    });
    const cardTypesAndProduct = [...cardTypesFiltered, ...this.productTypes];

    if ((!cardTypesAndProduct || cardTypesAndProduct.length === 0) && !productsSubtitle) {
      if (this.shadowRootJQuery) {
        const productSectionElement = this.shadowRootJQuery.find('.offer-detail-product__wrapper');
        if (productSectionElement) {
          productSectionElement.remove();
        }
      }
    } else {
      // Render product section description
      if (productsSubtitle) {
        this.appendSlotHTMLContent('p', 'productsSubtitle', productsSubtitle);
      }

      // Render card type thumbnail
      const cardTypeThumbnails = this.createCardImages(cardTypesFiltered);
      if (cardTypeThumbnails.length === 0) {
        this.removeCardThumbWrapper();
      } else {
        cardTypeThumbnails.forEach((cartItem) => {
          this.appendChild(cartItem);
        });
      }

      // Render card type and product label
      if (cardTypesAndProduct && cardTypesAndProduct.length > 0) {
        cardTypesAndProduct.forEach((item) => {
          if (item && item.title) {
            this.appendSlotContent('li', 'cardTypeTitle', item.title);
          }
        });
      }
    }
  }

  addLocation({ locations, labelLocationViewmore, locationSubtitle, pdfLink,
    locationSectionCTALabel, showPreviewModalListener  }) {
    let locationsDemo = [];

    if (locations && locations.length > 0) {
      locations.forEach((item, index) => {
        if (index < 2) {
          locationsDemo.push(item.title);
        } else if (index === 2) {
          locationsDemo.push('...');
        }
        this.appendSlotContent('li', 'locationContentItem', item.title);
      });

      if (locationsDemo && locationsDemo.length >= 3) {
        const locationMoreSpan = document.createElement('span');
        locationMoreSpan.setAttribute('slot', 'locationMore');

        const locationMoreLink = document.createElement('a');
        locationMoreLink.setAttribute('class', 'view-more masterhead-address__link');
        locationMoreLink.setAttribute('href', LOCATION_SECTION_ID);
        locationMoreLink.textContent = labelLocationViewmore;

        locationMoreSpan.appendChild(locationMoreLink);

        this.appendChild(locationMoreSpan);
        locationMoreSpan.addEventListener('click', (event) => {
          this.handleClickLocationMore(event, this.shadowRoot);
        });
      }
    } else {
      if (this.shadowRootJQuery) {
        this.shadowRootJQuery.find('.location-info-container').remove();
      }
    }

    if (locationSubtitle) {
      this.appendSlotHTMLContent('p', 'locationSectionSubtitle', locationSubtitle);

      if (pdfLink && locationSectionCTALabel) {
        const locationLinkSlot = document.createElement('a');
        locationLinkSlot.setAttribute('class', 'link-custom');
        locationLinkSlot.setAttribute('slot', 'locationLink');
        locationLinkSlot.setAttribute('target', '_blank');
        locationLinkSlot.setAttribute('href', pdfLink);
        locationLinkSlot.textContent = removeHTMLTags(locationSectionCTALabel);
        this.appendChild(locationLinkSlot);
        locationLinkSlot.addEventListener('click', (event) => {
          showPreviewModalListener(event);
        });
      }
    } else if (!locations || locations.length === 0) {
      if (this.shadowRootJQuery) {
        const locationSectionElement = this.shadowRootJQuery.find('.offer-detail-address__wrapper');
        if (locationSectionElement) {
          locationSectionElement.remove();
        }
      }
    }

    return locationsDemo;
  }

  initFooter(shadowRoot) {
    $(shadowRoot).find('.footer-expand').on('click', function () {
      const showHide = $(shadowRoot).find('#footer-links__list');
      const expand = this.lastElementChild;
      if (this.timer) {
        clearTimeout(this.timer);
      }
      if (!showHide.attr('class').includes('expanded')) {
        showHide.addClass('expanded');
        expand.style.transform = 'rotate(-180deg)';
      } else {
        showHide.removeClass('expanded');
        expand.style.transform = 'rotate(-360deg)';
      }
      this.timer = setTimeout(() => {
        $(document.body).animate(
          {
            scrollTop: document.body.scrollHeight,
          },
          400
        );
      }, 300);
    });
  }

  getRelatedEndpoint(dataURL) {
    const relatedParams = new URLSearchParams();
    relatedParams.append(paramsName.limit, RELATED_LIMIT);
    relatedParams.append(paramsName.offset, RELATED_OFFSET);
    relatedParams.append(paramsName.sort, 'related');
    let endpoint = `${dataURL}.offerlisting.json?${relatedParams.toString()}`;
    if (this.cardTypes && this.cardTypes.length) {
      endpoint += `&${paramsName.cardTypes}=${this.cardTypes
        .map((item) => item.key)
        .join(',')
        .replaceAll('-', '%20')}`;
    }

    if (this.productTypes && this.productTypes.length) {
      endpoint += `&${paramsName.products}=${this.productTypes
        .map((item) => item.key)
        .join(',')
        .replaceAll('-', '%20')}`;
    }

    if (this.categories && this.categories.length) {
      endpoint += `&${paramsName.types}=${this.categories
        .map((item) => item.key)
        .join(',')
        .replaceAll('-', '%20')}`;
    }

    return endpoint;
  }

  renderPromotionRelatedCount(relatedTotal) {
    if (this.shadowRootJQuery) {
      const relatedHeader = this.shadowRootJQuery.find('.related-header-info');
      if (relatedTotal < RELATED_LIMIT) {
        this.shadowRootJQuery.find('.see-all-btn').remove();
      } else {
        const quantityContainer = document.createElement('div');
        quantityContainer.setAttribute('class', 'quantity-container');

        const quantity = document.createElement('span');
        quantity.setAttribute('class', 'quantity');
        quantity.innerText = relatedTotal;

        quantityContainer.appendChild(quantity);
        relatedHeader.append(quantityContainer);
      }
    }
  }

  getDetailEndpoint (detailName) {
    console.log(this.dataURL)
    const endpoint = `${this.dataURL}.offerdetail.json?${paramsName.detailName}=${detailName}`;
    return endpoint;
  }

  openDetailModal (detailName) {
    fetchData(this.getDetailEndpoint(detailName)).then((response) => {
      const { detail: detailData, locales } = response;
      if (detailData) {
        this.offerListingComponent.fadeOut(300);

        const detailElements = document.getElementsByTagName('tcb-offer-listing-detail');
        let existDetailPopup = null;
        if (detailElements && detailElements.length > 0) {
          existDetailPopup = detailElements[0];
        }
        if (existDetailPopup) {
          existDetailPopup.remove();
        }
        const detailPopup = document.createElement('tcb-offer-listing-detail');
        detailPopup.addContent(this.dialogDetailData, detailData, this.dataURL, this.dataLanguageLabel, {
          renderPromotionCardItem: this.renderPromotionCardItem,
          renderExpiryDate: this.renderExpiryDate,
          showPreviewModalListener: this.showPreviewModalListener,
        });

        disableBodyScroll();

        let headerHeight = 0;
        if (this.headerElement && this.headerElement.offsetHeight) {
          headerHeight = this.headerElement.offsetHeight;
        }
        if (this.detailPlaceholderElement) {
          let localesString = '';
          if (locales && Array.isArray(locales)) {
            localesString = locales.join(',');
          }
          this.detailPlaceholderElement.setAttribute('data-locales', localesString);
          this.detailPlaceholderElement.classList.add('open');
          this.detailPlaceholderElement.appendChild(detailPopup);
          this.detailPlaceholderElement.scrollTop = headerHeight * -1;
        }
      }
    });
  }

  initDialogDetail () {
    if (!this.offerDetailElementJQuery.length) {
      return;
    }
    const url = new URL(window.location);
    const detailParam = url.searchParams.get(paramsName.detail);
    let parseText = null;
    if (this.offerDetailElementJQuery && this.offerDetailElementJQuery.length > 0) {
      parseText = this.offerDetailElementJQuery.attr('data-init');
    }
    if (parseText) {
      this.dialogDetailData = JSON.parse(parseText);
    }
    if (detailParam) {
      resetHistory();
      this.openDetailModal(detailParam);
    }
  }


  handleBackBrowserDetail () {
    window.addEventListener('popstate', (_) => {
      // Use global detailPlaceholder field instead of creating new query
      if (this.detailPlaceholder && this.detailPlaceholder.length > 0 && this.detailPlaceholder.hasClass('open')) {
        this.detailPlaceholder.removeClass('open');
        this.detailPlaceholder.empty();
        this.offerListingComponent.fadeIn(400);
        disableBodyScroll(false);
        handleResetDropdownLanguage();
      }
    });
  }

  setVariableHeaderHeight(headerElementChanged) {
    let headerHeight = 0;
    if (headerElementChanged && headerElementChanged.offsetHeight) {
      headerHeight = convertPxToRem(headerElementChanged.offsetHeight);
    }
    this.detailPlaceholderElement.style.setProperty(HEADER_HEIGHT_VARIABLE, headerHeight + 'rem');
  }


  handleSetHeaderHeight() {
    if (this.headerElement && this.detailPlaceholderElement) {
      const headerDelayTime = 300;
      setTimeout(() => {
        this.setVariableHeaderHeight(this.headerElement);
        listenerElement(this.headerElement, (_) => {
          // Reuse global headerJQuery field instead of creating new query
          let headerElementRefreshed = null;
          if (this.headerJQuery && this.headerJQuery.length > 0) {
            headerElementRefreshed = this.headerJQuery[0];
          }
          if (headerElementRefreshed) {
            this.setVariableHeaderHeight(headerElementRefreshed);
          }
        });
      }, headerDelayTime);
    }
  }

  handleExpandAccordion() {
    const offerDetailShadowRoot = this.shadowRoot;
    const accordionElement = offerDetailShadowRoot.querySelector('#offer-address tcb-offer-accordion');
    let offerAccorElement = null;
    if (accordionElement && accordionElement.shadowRoot) {
      offerAccorElement = accordionElement.shadowRoot;
    }
    const wrapper = offerDetailShadowRoot.querySelector(LOCATION_SECTION_ID);

    const windowWidth = this.windowJQuery.width();
    let question = null;
    let answer = null;
    if (offerAccorElement) {
      question = offerAccorElement.querySelector('.question');
      answer = offerAccorElement.querySelector('.answer');
    }
    if (question) {
      question.classList.add('active');
    }
    let paddingValue;
    if (windowWidth > screenSmMax) {
      paddingValue = '3rem 0';
    } else {
      paddingValue = '2.5rem 1rem';
    }
    if (wrapper && wrapper.style) {
      Object.assign(wrapper.style, {
        backgroundImage: wrapper.getAttribute('data-bg-img'),
        padding: paddingValue
      });
    }
    if (question && question.style) {
      Object.assign(question.style, ACCORDION_OPEN_STYLE);
    }
    if (answer && answer.style) {
      Object.assign(answer.style, {
        height: answer.scrollHeight + 'px',
        marginBottom: '1.5rem',
      });
    }
    if (offerAccorElement) {
      let arrowImg = offerAccorElement.querySelector('img');
      if (arrowImg) {
        arrowImg.style.transform = 'rotate(-180deg)';
      }
    }
  }

  handleClickLocationMore(event, shadowRoot) {
    event.preventDefault();
    const locationSectionElements = $(shadowRoot).find(LOCATION_SECTION_ID);
    let locationSection = null;
    if (locationSectionElements && locationSectionElements.length > 0) {
      locationSection = locationSectionElements[0];
    }
    if (locationSection) {
      const windowWidth = this.windowJQuery.width();
      let locationOffsetTop = 0;
      if (locationSection && locationSection.offsetTop) {
        locationOffsetTop = locationSection.offsetTop;
      }
      let scrollTopValue;
      if (windowWidth > screenSmMax) {
        scrollTopValue = locationOffsetTop - OFFSET_SCROLL_LOCATION;
      } else {
        scrollTopValue = locationOffsetTop;
      }
      this.detailPlaceholder.animate({
        scrollTop: scrollTopValue,
      }, SPEED_SCROLL_LOCATION);
      this.handleExpandAccordion();
    }
  }

  renderRelatedPromotionCard(response) {
    if (this.shadowRootJQuery) {
      const related = this.shadowRootJQuery.find('.related-listing');

      related.empty();
      response.forEach((item) => {
        const element = this.renderPromotionCardItem(item);
        related.append(element);
      });

      this.renderExpiryDate(true, this.shadowRootJQuery);
    }
  }

  handleClickPdfRelated () {
    if (this.shadowRootJQuery) {
      this.shadowRootJQuery.find(this.selectorPdfLink).click(this.showPreviewModalListener);
    }
  }

  handleClickDetailRelated () {
    if (this.shadowRootJQuery) {
      const detailRelated = this.shadowRootJQuery.find(this.selectorDetailLink);
      detailRelated.click((event) => {
        event.preventDefault();
        const detailName = $(event.currentTarget).data('name');

        const url = new URL(window.location);
        url.search = '';
        url.searchParams.set(paramsName.detail, detailName);

        window.location.href = url;
      });
    }
  }

  getRelatedExcludeCurrent(promotions) {
    if (!promotions || !promotions.length) {
      return [];
    }

    // Remove promotion current
    const excludedCurrent = promotions
      .map((item) => ({ ...item, isFavorite: true })) // Old logic have to set isFavorite true
      .filter((offer) => offer.name !== this.detailName);

    // Remove last if not exist promotion current
    if (excludedCurrent.length === RELATED_LIMIT) {
      excludedCurrent.pop();
    }

    return excludedCurrent;
  }

  getRelatedPromotions(dataURL) {
    const api = this.getRelatedEndpoint(dataURL);
    fetchData(api).then((promotionData) => {
      const { results, total } = promotionData || {};
      const relatedTotalExcludeCurrent = total - 1;

      const showRelatedItems = 4;
      const relatedPromotions = this.getRelatedExcludeCurrent(results);
      const relatedNotEnought4 = relatedPromotions.length < showRelatedItems;

      if (relatedNotEnought4) {
        if (this.shadowRootJQuery) {
          this.shadowRootJQuery.find('.offer-detail-related').remove();
        }
      } else {
        this.renderRelatedPromotionCard(relatedPromotions);
        this.handleClickDetailRelated();
        this.handleClickPdfRelated();
        this.renderPromotionRelatedCount(relatedTotalExcludeCurrent);
      }
    });
  }

  setBackground(element, { desktop, mobile, bgColor }) {
    if (this.shadowRootJQuery) {
      const jElement = this.shadowRootJQuery.find(element);
      if (desktop || mobile) {
        let bgUrl = desktop;
        if (mobile && this.windowJQuery.width() < screenSmMax) {
          bgUrl = mobile;
        }
        if (jElement) {
          jElement.attr('data-bg-img', `url(${bgUrl})`);
          jElement.css('background-image', `url(${bgUrl})`);
        }
      } else {
        if (jElement) {
          jElement.attr('data-bg-color', bgColor);
          jElement.css('background-color', bgColor);
        }
      }
    }
  }

  createBackground({
    imageMasthead,
    imageAppliedProducts,
    imageLocationSection,
    imageMobileMasthead,
    imageMobileAppliedProducts,
    imageMobileLocationSection,
    bgColorAppliedProducts,
    bgColorLocationSection,
    bgColorMasthead,
  }) {
    let mastheadBgColor = '#f5f6f8';
    if (bgColorMasthead) {
      mastheadBgColor = bgColorMasthead;
    }

    let productBgColor = '#e7e7ee';
    if (bgColorAppliedProducts) {
      productBgColor = bgColorAppliedProducts;
    }

    let addressBgColor = '#ffffff';
    if (bgColorLocationSection) {
      addressBgColor = bgColorLocationSection;
    }

    this.setBackground('.offer-detail-masterhead__wrapper', {
      desktop: imageMasthead,
      mobile: imageMobileMasthead,
      bgColor: mastheadBgColor,
    });
    this.setBackground('.offer-detail-product__wrapper', {
      desktop: imageAppliedProducts,
      mobile: imageMobileAppliedProducts,
      bgColor: productBgColor,
    });
    this.setBackground('.offer-detail-address__wrapper', {
      desktop: imageLocationSection,
      mobile: imageMobileLocationSection,
      bgColor: addressBgColor,
    });
  }

  getKeyAsString(tagItems, kebabCase = false) {
    if (!tagItems) {
      return '';
    }
    return tagItems.map((item) => {
      let itemKey = item.key;
      if (!kebabCase && itemKey) {
        const splitResult = itemKey.split('-');
        if (splitResult) {
          itemKey = splitResult.join(' ');
        }
      }
      return itemKey;
    }).join(',');
  }

  handleClickAllRelated() {
    if (this.shadowRootJQuery) {
      const seeAllButton = this.shadowRootJQuery.find('.see-all-btn');
      seeAllButton.click((event) => {
        event.preventDefault();
        window.scrollTo({ top: 0, behavior: 'smooth' });
        resetHistory();

        const { cardTypes, products, types } = paramsName;

        const url = new URL(window.location);
        url.search = '';

        if (this.cardTypes && this.cardTypes.length) {
          url.searchParams.set(cardTypes, this.getKeyAsString(this.cardTypes, true));
        }
        if (this.productTypes && this.productTypes.length) {
          url.searchParams.set(products, this.getKeyAsString(this.productTypes));
        }
        if (this.categories && this.categories.length) {
          url.searchParams.set(types, this.getKeyAsString(this.categories));
        }

        window.location.href = url;
      });
    }
  }

  closeDetailModal() {
    disableBodyScroll(false);
    handleResetDropdownLanguage();

    if (this.detailPlaceholderElement) {
      this.detailPlaceholderElement.classList.remove('open');
    }

    this.detailPlaceholder.empty();
    this.offerListingComponent.fadeIn(400);

    resetHistory();

    if (this.shadowRootJQuery) {
      this.shadowRootJQuery
        .find(this.selectorDetailLink)
        .off(this.handleClickDetailRelated);
      this.shadowRootJQuery
        .find(this.selectorPdfLink)
        .off(this.handleClickPdfRelated);
    }
  }

  handleClickClose() {
    if (this.shadowRootJQuery) {
      const backButton = this.shadowRootJQuery.find('.detail-back');
      backButton.click(() => {
        let previousArray = SessionStorageUtil.get(PREVIOUS_ARRAY_PAGE_KEY_NAME);

        if (previousArray && previousArray.length > 0) {
          const previousURL = new URL(previousArray[previousArray.length - 1]);
          const paramsObject = {};
          if (previousURL && previousURL.searchParams) {
            previousURL.searchParams.forEach((value, key) => {
              paramsObject[key] = value;
            });
          }
          updateSearchParams(paramsObject);
        } else {
          clearSearchParams(paramsName);
        }

        this.closeDetailModal();
      });
    }
  }

  createCardImages(cardTypes) {
    if (!cardTypes || cardTypes.length === 0) {
      return [];
    }

    return cardTypes.reduce((listCard, item) => {
      if (!item.thumbnail) {
        return listCard;
      }

      const cardItem = document.createElement('div');
      cardItem.setAttribute('slot', 'cardTypeItem');

      const imageCardContainer = document.createElement('div');
      imageCardContainer.classList.add('image-card-container');

      const imageCardProduct = document.createElement('div');
      imageCardProduct.classList.add('image-card-product');

      const img = document.createElement('img');
      img.setAttribute('src', item.thumbnail);
      let altText = 'card type thumbnail';
      if (item.title) {
        altText = item.title;
      }
      img.setAttribute('alt', altText);
      img.onerror = () => {
        cardItem.remove();
        if (!this.imageCardProductImgJQuery || this.imageCardProductImgJQuery.length === 0) {
          this.removeCardThumbWrapper();
        }
      };

      imageCardProduct.appendChild(img);
      imageCardContainer.appendChild(imageCardProduct);
      cardItem.appendChild(imageCardContainer);

      listCard.push(cardItem);
      return listCard;
    }, []);
  }

  renderProductsButtons(primaryLink, secondaryLink, productsPrimaryCTALabel, productsSecondaryCTALabel) {
    let hasButton = false;
    const hideButton = (selector, href, label) => {
      if (this.shadowRootJQuery) {
        const primaryButtonElement = this.shadowRootJQuery.find(selector);
        if (primaryButtonElement) {
          primaryButtonElement.attr('href', href);
          primaryButtonElement.toggleClass('hide block');
          const children = primaryButtonElement.children();
          if (children && children.first) {
            const firstChild = children.first();
            if (firstChild) {
              firstChild.text(label);
            }
          }
        }
      }
      return true;
    };

    if (primaryLink && productsPrimaryCTALabel) {
      hasButton = hideButton('.offer-listing-detail__product a.primary', primaryLink, productsPrimaryCTALabel);
    }
    if (secondaryLink && productsSecondaryCTALabel) {
      hasButton = hideButton('.offer-listing-detail__product a.secondary', secondaryLink, productsSecondaryCTALabel);
    }

    if (!hasButton && this.shadowRootJQuery) {
      this.shadowRootJQuery.find('.offer-listing-detail__product .btn-container').remove();
    }
  }
}
