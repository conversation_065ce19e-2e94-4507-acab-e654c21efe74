import { BaseComponent } from './base';
import { debounce } from 'lodash';
import { GCSE_DEFAULT_REFINEMENT, LANG_EN, LANG_VI, TRANSLATIONS } from './translations';

const enterEvent = new KeyboardEvent('keydown', {
  key: 'Enter',
  code: 'Enter',
  which: 13,
  keyCode: 13,
});
const gcseSearchClass = '.gcse-search';
const searchEngineWrapperClass = '.search-engine__wrapper';
const searchResultWrapperClass = '.search-result__wrapper';
const iconSearchPath = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg';
const iconDropdown = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg';
const iconRedArrow = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow.svg';
const iconCloseWhite = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon_close_white.svg';
const iconFavorite = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/fav-icon.png';
const iconTime = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/time-icon.svg';
const urlThumbnails = '/bin/page/thumbnail';
const defaultThumbnail =
  '/content/dam/techcombank/public-site/seo/techcombank-default-thumbnail.jpg.rendition/cq5dam.thumbnail.319.319.png';
const searchEngineId = 'b6e708782620e4010';
const cseScriptId = 'cse-tcb-script';
const HISTORY_KEYWORDS_KEY = 'historyKeywords';
export const mobileBreakPoint = 1024;

export const initGcseScript = () => {
  if (document.getElementById(cseScriptId)) {
    return;
  }
  const script = document.createElement('script');
  script.id = cseScriptId;
  document.head.append(script);
  script.src = `https://cse.google.com/cse.js?cx=${searchEngineId}`;
};

export const getSearchHistoryKeywords = () => {
  return JSON.parse(localStorage.getItem(HISTORY_KEYWORDS_KEY)) ?? [];
};

const setSearchHistoryKeywords = (keywords) => {
  localStorage.setItem(HISTORY_KEYWORDS_KEY, JSON.stringify(keywords));
};

export const removeSearchHistoryKeyword = (keyword) => {
  const keywords = getSearchHistoryKeywords();

  const index = keywords.indexOf(keyword);
  if (index > -1) {
    keywords.splice(index, 1);
  }
  setSearchHistoryKeywords(keywords);
};

export class TcbGoogleSearch extends BaseComponent {
  googleInit = {
    root: null,
    isLoaded: false,
    tabOpen: false,
  };
  refinementTabs = [];
  lang = document.documentElement.lang.toLowerCase();
  isEn = `${location.pathname}/`.includes('/en/');
  selectedLanding = TRANSLATIONS[this.lang].LANDING_LIST[0];
  allResultLandingKeyword = TRANSLATIONS[this.lang].LANDING_LIST[0].keyword;
  selectedRefinement = TRANSLATIONS[this.lang].DEFAULT_REFINEMENT.toLowerCase().trim();
  refinementList = [];
  adsBannerData;
  promotionListData;
  currentQuery;
  isClosedBanner = false;
  promotionListParams = {
    offset: 0,
    limit: 12,
  };
  searchResultWrapper = document.querySelector(searchResultWrapperClass);
  isShowResult = !!this.searchResultWrapper;

  constructor() {
    super();

    if (this.isShowResult) {
      this.initRefinementTabs();
      this.setSearchResultLang();
      this.removeLocationHash();
    }

    initGcseScript();
    this.initGcse();
  }

  initRefinementTabs() {
    const inputs = document.querySelectorAll('input[data-refinement-tab][type=hidden]');
    inputs.forEach((x) => this.refinementTabs.push(x.getAttribute('data-refinement-tab')));
  }

  setSearchResultLang() {
    const gcseSearchElement = this.searchResultWrapper.querySelector(gcseSearchClass);
    if (gcseSearchElement) {
      gcseSearchElement.setAttribute('data-lr', `lang_${this.lang ?? LANG_VI}`);
    }
  }

  initRenderSearchResult(results) {
    if (results?.length) {
      this.googleInit.resultsData = results.map((result) => {
        const ogDescription = result.richSnippet?.metatags?.ogDescription;
        return ogDescription ? { ogDescription } : null;
      });
    } else {
      if (this.selectedRefinement?.toLowerCase().trim() !== TRANSLATIONS[this.lang].PROMOTION.toLowerCase().trim()) {
        this.googleInit.resultsData = [];
      }
    }
  }

  removeLocationHash() {
    const url = new URL(window.location);
    const hash = url.hash;
    const hashList = hash.split('&');
    const isContainRef = hashList.some((item) => item.includes('gsc.ref'));
    const isContainQuery = !!url.searchParams.get('q');

    if (isContainQuery && isContainRef) {
      window.location.hash = 'gsc.tab=0';
    }
  }

  initGcse() {
    window.__gcse = window.__gcse || (window.__gcse = {});
    window.__gcse.initializationCallback = () => {
      this.initCustomSearchInput();
    };

    if (this.isShowResult) {
      window.__gcse.searchCallbacks = {
        web: {
          // Before execute query
          starting: (name, query) => {
            this.removeAdBanner();
            this.removePromotionList();
            this.toggleResultBox(false);
            return query;
          },
          // Before render result
          ready: (name, q, p, results) => {
            this.initRenderSearchResult(results);
          },
          rendered: (name, q, p, resultsDiv) => {
            this.setCurrentQuery(q);
            this.addSearchHistoryKeyword(q);
            this.gsDefineElements();
            this.filterTabByLocale();
            this.setSelectedRefinement();
            this.gsDefineCustomDropdownElements();
            this.defineCustomSearchResultContent();
            this.changeRefinementTabListener();
            this.gsDefineThumbnail(resultsDiv, p);
            this.toggleRefinementTab(this.selectedLanding?.keyword !== this.allResultLandingKeyword);

            if (!this.googleInit?.isLoaded) {
              this.resizeWindowListener();
              this.googleInit.isLoaded = true;
            }

            this.setInternalSearchParamUrl();
          },
        },
      };
    }
  }

  setInternalSearchParamUrl() {
    // CAUTION: cheat way to avoid cse_element__.js remove url params on links
    document.querySelectorAll("a.gs-title[href*='https://']").forEach((linkElement) => {
      const url = new URL(linkElement.href);
      if (!url.searchParams.has('internalsearch')) {
        url.searchParams.append('internalsearch', 1);
      }
      linkElement.href = url.href;
      linkElement.setAttribute('data-ctorig', url.href);

      linkElement.addEventListener('click', (event) => {
        event.preventDefault();

        if (event.ctrlKey || event.shiftKey || event.metaKey || event.which == 2) {
          window.open(url.href, '_blank');
        } else {
          document.location.href = url.href;
        }
      });
    });
  }

  fetchData(url) {
    return new Promise(function (resolve, reject) {
      $.ajax({
        url: url,
        method: 'get',
        contentType: 'application/json',
        success: function (response) {
          resolve(response);
        },
        error: function (xhr, status, error) {
          reject(error);
        },
      });
    });
  }

  getCurrentTimeZone() {
    let timezoneOffset = new Date().getTimezoneOffset() / 60;
    let timeOffset = '';
    const isNegativeTimezone = new Date().getTimezoneOffset() / 60 < 0;
    timezoneOffset = isNegativeTimezone ? -timezoneOffset : timezoneOffset;

    if (isNegativeTimezone) {
      timeOffset = timezoneOffset.toString().length > 1 ? `+${timezoneOffset}` : `+0${timezoneOffset}`;
    } else {
      timeOffset = timezoneOffset.toString().length > 1 ? `-${timezoneOffset}` : `-0${timezoneOffset}`;
    }

    return `${new Date(new Date().getTime() + 7 * 60 * 60000).toISOString().substring(0, 19)}.000${timeOffset}:00`;
  }

  getAdsBannerData() {
    const gscInput = this.searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
    const query = gscInput.value;
    const time = this.getCurrentTimeZone();
    const url = `/bin/searchBannerAds?q=${encodeURIComponent(query)}&t=${encodeURIComponent(time)}&lang=${this.lang}`;
    this.fetchData(url)
      .then((response) => {
        this.adsBannerData = response?.data;
      })
      .catch(() => {
        this.adsBannerData = null;
      })
      .finally(() => {
        this.gsDefineAdBannerElements();
      });
  }

  getPromotionListData() {
    const gscInput = this.searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
    const query = gscInput.value;

    const queryUrl = new URL(`${location.origin}/bin/promotion`);
    queryUrl.searchParams.append('limit', this.promotionListParams.limit);
    queryUrl.searchParams.append('offset', this.promotionListParams.offset);
    queryUrl.searchParams.append('sort', 'most-popular');
    queryUrl.searchParams.append('language', this.isEn ? 'en' : 'vi');
    queryUrl.searchParams.append('query', encodeURIComponent(query));

    this.fetchData(queryUrl.href)
      .then((response) => {
        this.promotionListData = response.data;
      })
      .catch(() => {
        this.promotionListData = { total: '0', results: [] };
      })
      .finally(() => {
        this.gsDefinePromotionListElements();
      });
  }

  gsDefineElements() {
    const root = this.searchResultWrapper.querySelector('.gsc-control-cse');
    this.googleInit.root = root;
    const aboveWrapperArea = root?.getElementsByClassName('gsc-above-wrapper-area')?.[0];
    const refinementBlock = root?.getElementsByClassName('gsc-refinementBlock')?.[0];
    const refinementBlockTabs = refinementBlock?.querySelectorAll('.gsc-refinementHeader');
    const resultYouMean = root?.querySelector('.gsc-tabdActive .gs-spelling');
    const resultInsteadOf = root?.querySelector('.gsc-tabdActive .gs-spelling-original');
    const resultNotFound = root?.querySelector('.gsc-tabdActive .gs-no-results-result');
    const resultPromotion = root?.querySelector('.gsc-tabdActive .gsc-promotion');

    // Show sort and count label
    if (aboveWrapperArea) {
      aboveWrapperArea.style.display = 'none';
    }

    if (refinementBlock) {
      this.googleInit.refinementBlock = refinementBlock;
    }
    if (refinementBlockTabs) {
      this.googleInit.refinementBlockTabs = Array.prototype.slice.apply(refinementBlockTabs);
    }

    if (resultYouMean) {
      this.googleInit.resultYouMean = resultYouMean;
    }
    if (resultInsteadOf) {
      this.googleInit.resultInsteadOf = resultInsteadOf;
    }
    if (resultNotFound) {
      const resultNotFoundParent = resultNotFound.parentElement;
      if (resultNotFoundParent) {
        // Style wrapper of not found when result not found
        resultNotFoundParent.style.cssText = 'padding: 0px;box-shadow: none;background-color: transparent;border: 0;';
      }
      this.googleInit.resultNotFound = resultNotFound;
    }
    if (resultPromotion) {
      this.googleInit.resultPromotion = resultPromotion;
    }
  }

  initRefinementTabByLocale(tab, isInclude) {
    const tabClone = tab;
    const tabSpan = tab?.getElementsByTagName('span')?.[0];
    const tabContent = tabSpan?.textContent.toLowerCase().trim();
    tabClone.style.display = isInclude ? 'inline-block' : 'none';
    const content = `${this.selectedLanding?.keyword.toLowerCase()}-${TRANSLATIONS[
      this.lang
    ].DEFAULT_REFINEMENT.toLowerCase()}`;
    const url = new URL(window.location);
    const gscInput = this.searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
    const query = gscInput?.value;
    if (tabContent === content && !url.hash.includes('gsc.ref') && query) {
      setTimeout(() => {
        tab.dispatchEvent(new Event('click'));
      });
    }
    tabSpan.textContent = tabSpan.textContent.replace(`${this.selectedLanding?.keyword}-`, '').trim();
  }

  filterTabByLocale() {
    const { refinementBlockTabs } = this.googleInit;
    if (refinementBlockTabs?.length) {
      refinementBlockTabs.map((tab, index) => {
        const tabSpan = tab?.getElementsByTagName('span')?.[0];
        const tabContent = tabSpan?.textContent.toLowerCase().trim();
        const activeTabIndex = this.refinementTabs?.findIndex((t) => {
          const refinementTab = t?.toLowerCase().trim();
          return (
            refinementTab === tabContent && refinementTab.includes(`${this.selectedLanding?.keyword?.toLowerCase()}-`)
          );
        });
        const isInclude = activeTabIndex !== -1;

        // Hide default all results tab
        if (index === 0) {
          tab.style.display = 'none';
        }

        // From tab 2, hide tab not include
        if (index > 0) {
          this.initRefinementTabByLocale(tab, isInclude);
        }
      });
    }
    setTimeout(() => {
      this.toggleResultBox(true);
    }, 500);
  }

  defineCustomSearchResultContent() {
    this.removeAdBanner();
    this.removePromotionList();

    // RENDER PROMOTION LIST
    if (this.selectedRefinement?.toLowerCase().trim() === TRANSLATIONS[this.lang].PROMOTION.toLowerCase().trim()) {
      const wrapper = this.searchResultWrapper.querySelector('.gsc-wrapper');
      wrapper.classList.add('gsc-promotion-list');
      if (this.promotionListData) {
        this.gsDefinePromotionListElements();
      } else {
        this.getPromotionListData();
      }
      return;
    }

    // RENDER AD BANNER
    if (this.adsBannerData) {
      this.gsDefineAdBannerElements();
    } else {
      this.getAdsBannerData();
    }
  }

  showThumbnailByRefinement() {
    const selectedRefinement = this.selectedRefinement?.toLowerCase().trim();
    const promotionsRefinement = TRANSLATIONS[this.lang].PROMOTION.toLowerCase().trim();
    const productRefinement = TRANSLATIONS[this.lang].PRODUCT.toLowerCase().trim();

    switch (this.selectedLanding?.keyword) {
      case TRANSLATIONS[this.lang].LANDING_LIST[1].keyword: // PERSONAL
        return selectedRefinement !== productRefinement && selectedRefinement !== promotionsRefinement;

      case TRANSLATIONS[this.lang].LANDING_LIST[2].keyword: // BUSINESS
        return selectedRefinement !== productRefinement;

      case TRANSLATIONS[this.lang].LANDING_LIST[3].keyword: // INVESTORS
        return false;

      case TRANSLATIONS[this.lang].LANDING_LIST[0].keyword: // ALL RESULTS
      case TRANSLATIONS[this.lang].LANDING_LIST[4].keyword: // ABOUT US
      default:
        return true;
    }
  }

  getRemovedPostFixText(gsTitleElement) {
    const postFixText = ' | Techcombank';
    const gsTitle = gsTitleElement.textContent;
    if (!gsTitle) {
      return '';
    }
    return gsTitle.includes(postFixText) ? gsTitle.replaceAll(postFixText, '') : gsTitle;
  }

  renderThumbnail(gsTitleElement, textWithoutPostFix) {
    const url = gsTitleElement.getAttribute('href');
    const pathName = new URL(url).pathname;

    const thumbnail = document.createElement('img');
    thumbnail.classList.add('gs-thumbnail');
    thumbnail.src = `${urlThumbnails}?path=${pathName}`;
    thumbnail.onerror = function () {
      this.onerror = null;
      this.src = defaultThumbnail;
    };
    thumbnail.alt = textWithoutPostFix;

    const thumbnailWrapper = document.createElement('div');
    thumbnailWrapper.classList.add('gs-thumbnail-wrapper');
    thumbnailWrapper.append(thumbnail);

    return thumbnailWrapper;
  }

  gsDefineResultThumbnail(results, selector) {
    if (results.length === 0) {
      return;
    }

    const isShowThumbnail = this.showThumbnailByRefinement();
    results.forEach((item) => {
      const gsTitleElement = item?.querySelector(selector);
      if (!gsTitleElement) {
        return;
      }
      const textWithoutPostFix = this.getRemovedPostFixText(gsTitleElement);
      gsTitleElement.textContent = textWithoutPostFix; // Remove postfix text
      if (isShowThumbnail) {
        const thumbnail = this.renderThumbnail(gsTitleElement, textWithoutPostFix);
        item.prepend(thumbnail); // Prepend thumbnail
      }
    });
  }

  gsDefineThumbnail(results, promotion) {
    const itemSelector = '.gsc-thumbnail-inside .gs-title a.gs-title';
    const promotionSelector = '.gs-promotion .gs-title a.gs-title';
    this.gsDefineResultThumbnail(results, itemSelector);
    this.gsDefineResultThumbnail(promotion, promotionSelector);
  }

  landingDropdownChangeListener(searchResultWrapper) {
    const dropdownLabel = searchResultWrapper?.querySelector(
      '.gsc-dropdown-box .dropdown__wrapper .dropdown__button span',
    );
    if (dropdownLabel) {
      dropdownLabel.addEventListener('change', (event) => {
        const landing = event?.detail?.landingValue;
        if (this.selectedLanding?.keyword === landing?.keyword) {
          return;
        }
        this.selectedLanding = landing;
        this.selectedRefinement = null;
        this.refinementList = [];
        this.manualTriggerSearch();
      });
    }
  }

  removeRefinementParams() {
    const url = new URL(window.location);
    const hash = url.hash.split('&');
    const withoutRefinementHash = hash.filter((item) => !item.toLowerCase().trim().includes('gsc.ref')).join('');
    window.location.hash = withoutRefinementHash;
  }

  removeAdBanner() {
    const root = this.searchResultWrapper.querySelector('.gsc-control-cse');
    const wrapper = root?.getElementsByClassName('gsc-wrapper')[0];
    const expansionArea = wrapper?.querySelector('.gsc-resultsbox-visible .gsc-tabdActive .gsc-expansionArea');
    const desktopAdBanner =
      wrapper && wrapper.children
        ? [...wrapper.children].find((element) => element.classList.contains('gsc-ad-banner'))
        : null;
    const mobileAdBanner =
      expansionArea && expansionArea.children
        ? [...expansionArea.children].find((element) => element.classList.contains('gsc-ad-banner'))
        : null;

    if (desktopAdBanner) {
      desktopAdBanner.remove();
    }
    if (mobileAdBanner) {
      mobileAdBanner.remove();
    }
  }

  removePromotionList() {
    const wrapper = this.searchResultWrapper.querySelector('.gsc-wrapper');
    const currentPromotionList = [...wrapper?.children].find((element) =>
      element.classList.contains('gsc-promotion-list-content'),
    );
    if (currentPromotionList) {
      wrapper.classList.remove('gsc-promotion-list');
      currentPromotionList.remove();
    }
  }

  toggleResultBox(flag) {
    const resultBox = this.searchResultWrapper.querySelector('.gsc-wrapper');
    if (resultBox) {
      const errorCaptchaBox = resultBox.querySelector('.gs-error-result.gs-captcha-outer-wrapper');
      const isErrorCaptchaBoxVisible = errorCaptchaBox?.style.display !== 'none';
      if (flag) {
        resultBox.style.visibility = 'visible';
      } else {
        resultBox.style.visibility = isErrorCaptchaBoxVisible ? 'visible' : 'hidden';
      }
    }
  }

  toggleRefinementTab(flag) {
    const refinementBlock = this.searchResultWrapper.querySelector('.gsc-refinementsArea .gsc-refinementBlock');
    const customRefinementBlock = this.searchResultWrapper.querySelector('.gsc-refinementsArea .gsc-select-custom');

    if (window.innerWidth > mobileBreakPoint && refinementBlock) {
      refinementBlock.style.visibility = `${flag ? 'visible' : 'hidden'}`;
      return;
    }

    if (window.innerWidth <= mobileBreakPoint && customRefinementBlock) {
      customRefinementBlock.style.display = `${flag ? 'flex' : 'none'}`;
    }
  }

  clearSearchListener(searchResultWrapper) {
    const clearButton = searchResultWrapper.querySelector('.gsc-input-box table.gsc-input a');
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        this.clearAllConfiguration();
        this.removeRefinementParams();
      });
    }
  }

  clearAllConfiguration() {
    this.removeAdBanner();
    this.removePromotionList();
    this.adsBannerData = null;
    this.isClosedBanner = false;
    this.currentQuery = null;
    this.promotionListData = null;
    this.promotionListParams.offset = 0;
    this.refinementList = [];
    this.selectedRefinement = null;
  }

  changeRefinementTabListener() {
    const refinementBlock = this.searchResultWrapper.querySelector('.gsc-refinementsArea .gsc-refinementBlock');
    if (refinementBlock && !refinementBlock.classList.contains('refinement-change-listener')) {
      refinementBlock.classList.add('refinement-change-listener');
      refinementBlock.addEventListener('click', (event) => {
        const target = event.target;
        const targetRefinement = target?.getElementsByTagName('span')?.[0];
        const targetRefinementLabel = targetRefinement?.textContent;

        if (
          targetRefinementLabel?.toLowerCase().trim() !== this.selectedRefinement?.toLowerCase()?.trim() &&
          this.refinementList.includes(targetRefinementLabel)
        ) {
          this.selectedRefinement = targetRefinementLabel;
          this.defineCustomSearchResultContent();
        }
      });
    }
  }

  setSelectedRefinement() {
    const activeRefinementTab = this.searchResultWrapper.querySelector(
      '.gsc-refinementsArea .gsc-refinementBlock .gsc-refinementhActive',
    );
    if (activeRefinementTab) {
      const activeRefinementLabel = activeRefinementTab?.getElementsByTagName('span')?.[0]?.textContent;
      if (activeRefinementLabel.toLowerCase().trim() !== this.selectedRefinement?.toLowerCase().trim()) {
        this.selectedRefinement = activeRefinementLabel;
      }
      if (!this.refinementList.includes(activeRefinementLabel)) {
        this.refinementList.push(activeRefinementLabel);
      }
    }
  }

  setCurrentQuery(q) {
    this.adsBannerData = null;
    if (this.currentQuery !== q) {
      this.currentQuery = q;
      this.isClosedBanner = false;
      this.promotionListData = null;
      this.refinementList = [];
      this.promotionListParams.offset = 0;
    }
  }

  resizeWindowListener() {
    window.addEventListener(
      'resize',
      debounce(() => {
        this.gsDefineAdBannerElements();
      }, 200),
    );
  }

  manualTriggerSearch() {
    const gscInput = this.searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
    gscInput.dispatchEvent(enterEvent);
  }

  gsDefineCustomDropdownElements() {
    const refinementsArea = this.searchResultWrapper.getElementsByClassName('gsc-refinementsArea')?.[0];
    const activatedText = this.searchResultWrapper.querySelector('.gsc-refinementhActive span')?.textContent;
    const { refinementBlock } = this.googleInit;

    // Make sure cse dom rendered to continue.
    if (!refinementsArea || !refinementBlock || !activatedText) {
      return;
    }

    const selectCustom = refinementsArea.querySelector('.gsc-refinementsArea .gsc-select-custom');
    if (selectCustom) {
      selectCustom.remove();
    }

    // Create text default value
    const selectTextElement = document.createElement('span');
    selectTextElement.innerHTML =
      this.selectedRefinement === GCSE_DEFAULT_REFINEMENT
        ? TRANSLATIONS[this.lang].DEFAULT_REFINEMENT
        : this.selectedRefinement;

    // Create arrow icon
    const arrowDownIconSvg = `<img src='/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg' />`;
    const selectIconElement = document.createElement('div');
    selectIconElement.className = 'gsc-select-custom-selector';
    selectIconElement.innerHTML = arrowDownIconSvg;

    // Create select UI
    const selectElement = document.createElement('div');
    selectElement.className = 'gsc-select-custom';
    selectElement.style.display = 'none';
    selectElement.prepend(selectIconElement);
    selectElement.prepend(selectTextElement);

    this.googleInit.tabSelectElement = selectElement;

    refinementsArea.prepend(selectElement);

    const toggle = (value) => {
      refinementBlock.style.display = value ? 'flex' : 'none';
      if (selectIconElement) {
        selectIconElement.style.transform = value ? 'rotate(180deg)' : 'unset';
      }
      this.googleInit.tabOpen = value;
    };

    // Toggle show/hide on click select
    selectElement.addEventListener('click', () => {
      const { tabOpen } = this.googleInit;
      toggle(!tabOpen);
    });

    // Choose select option
    refinementBlock.addEventListener('click', (e) => {
      const text = this.selectedRefinement;
      selectTextElement.innerHTML = text;
      toggle(false);
    });
    refinementBlock.customParams = { toggle, selectTextElement };

    document.body.addEventListener('click', (e) => {
      if (
        this.googleInit?.tabOpen &&
        typeof e.target?.className === 'string' &&
        !e.target?.className.includes('gsc-select-custom')
      ) {
        toggle(false);
      }
    });
  }

  getDesktopAdBanner(wrapper) {
    return wrapper && wrapper.children
      ? [...wrapper.children].find((element) => element.classList.contains('gsc-ad-banner'))
      : null;
  }

  getMobileAdBanner(expansionArea) {
    return expansionArea && expansionArea.children
      ? [...expansionArea.children].find((element) => element.classList.contains('gsc-ad-banner'))
      : null;
  }

  appendAdBannerToSearchResult(wrapper, expansionArea) {
    const adBannerElement = this.renderAdBannerElement(this.adsBannerData);
    const desktopAdBanner = this.getDesktopAdBanner(wrapper);
    const mobileAdBanner = this.getMobileAdBanner(expansionArea);
    if (window.innerWidth <= mobileBreakPoint) {
      // MOBILE VIEW
      if (desktopAdBanner) {
        desktopAdBanner.remove();
      }
      if (!mobileAdBanner) {
        expansionArea.insertBefore(adBannerElement, expansionArea.childNodes[3]);
      }
    } else {
      // DESKTOP VIEW
      if (mobileAdBanner) {
        mobileAdBanner.remove();
      }
      if (!desktopAdBanner) {
        wrapper.append(adBannerElement);
      }
    }
  }

  gsDefineAdBannerElements() {
    const root = this.searchResultWrapper.querySelector('.gsc-control-cse');
    const wrapper = root?.getElementsByClassName('gsc-wrapper')[0];
    const expansionArea = wrapper?.querySelector('.gsc-resultsbox-visible .gsc-tabdActive .gsc-expansionArea');
    const noResultBox = expansionArea?.querySelector('.gs-no-results-result');

    if (
      !this.adsBannerData ||
      wrapper.classList.contains('gsc-promotion-list') ||
      this.isClosedBanner ||
      (noResultBox && this.googleInit.resultsData.length === 0)
    ) {
      this.removeAdBanner();
      return;
    }
    this.appendAdBannerToSearchResult(wrapper, expansionArea);
  }

  gsDefinePromotionListElements() {
    const root = this.searchResultWrapper.querySelector('.gsc-control-cse');
    const wrapper = root?.getElementsByClassName('gsc-wrapper')[0];
    const currentPromotionList = [...wrapper?.children].find((element) =>
      element.classList.contains('gsc-promotion-list-content'),
    );

    if (currentPromotionList) {
      currentPromotionList.remove();
    }

    const promotionListElement = this.renderPromotionListElement(this.promotionListData);
    wrapper.append(promotionListElement);
    const promotions = promotionListElement.querySelectorAll('.card-promotion__item .card-offer__content');
    promotions.forEach((element) => {
      this.updateExpiredTimer(element);
    });
    if (this.promotionListData?.items?.length > 0) {
      const pagination = promotionListElement.querySelector('.pagination');
      pagination.addEventListener('pageChange', (event) => {
        this.promotionListParams.offset = event?.detail?.offset;
        window.scrollTo({ top: 0, behavior: 'smooth' });
        this.getPromotionListData();
      });
    }
  }

  updateExpiredTimer(promotionElement) {
    if (!promotionElement) {
      return;
    }

    const expiredElement = promotionElement.querySelector('.promotion__expired');
    const progressBarElement = expiredElement.querySelector('.expired-progress__progress-bar--inner');
    const countdownElement = expiredElement.querySelector('.expired-progress__countdown-time');
    const expiredDate = Number(expiredElement.getAttribute('expired-time'));
    if (!isNaN(expiredDate) && progressBarElement && countdownElement) {
      this.updateExpiredProgressBar(expiredDate, expiredElement, progressBarElement, countdownElement);
    }
  }

  initCustomSearchInput() {
    const customInput = (searchEngineWrapper) => {
      const classNamePrefixIcon = 'gsc-input-prefix-icon';
      const input = searchEngineWrapper.querySelector('table input');
      const inputStack = searchEngineWrapper.querySelector('table.gsc-input tr');
      const insertedPrefixIcon = inputStack?.getElementsByClassName(classNamePrefixIcon)[0];
      if (!input || !inputStack || insertedPrefixIcon) {
        return {};
      }

      if (document.querySelector('input[data-search-placeholder][type=hidden]')) {
        input.placeholder = document
          .querySelector('input[data-search-placeholder][type=hidden]')
          .getAttribute('data-search-placeholder');
      }

      input.style.setProperty('--searchIconUrl', `url(${iconSearchPath})`);

      const tdIcon = document.createElement('td');
      tdIcon.innerHTML = `<img src='${iconSearchPath}' />`;
      tdIcon.className = classNamePrefixIcon;

      const closeButton = searchEngineWrapper.querySelector('table.gsc-input a');
      if (closeButton) {
        closeButton.innerHTML = `<div><span></span></div>`;
      }

      inputStack.prepend(tdIcon);
    };

    const renderDropdownElements = () => {
      const SELECTED_CLASS_NAME = 'selected';
      const EXPANDED_CLASS_NAME = 'expanded';
      const landingList = TRANSLATIONS[this.lang].LANDING_LIST;
      const changeEvent = new CustomEvent('change', { detail: { landingValue: { name: '', keyword: '' } } });

      // LABEL
      const dropdownLabel = document.createElement('span');
      dropdownLabel.classList.add('display__text', 'text--ellipsis', 'font-semibold');
      dropdownLabel.textContent = landingList[0].name;
      const dropdownIcon = document.createElement('img');
      dropdownIcon.src = iconDropdown;
      const dropdownButton = document.createElement('div');
      dropdownButton.classList.add('dropdown__button');
      dropdownButton.append(dropdownLabel, dropdownIcon);

      // MENU
      const dropdownMenu = document.createElement('ul');
      landingList.forEach((item, index) => {
        const dropdownItem = document.createElement('li');
        dropdownItem.classList.add('dropdown__item');
        dropdownItem.setAttribute('value', item.keyword.toLowerCase().trim());
        dropdownItem.textContent = item.name;

        if (index === 0) {
          dropdownItem.classList.add(SELECTED_CLASS_NAME);
        }

        dropdownItem.addEventListener('click', (event) => {
          // DE-SELECT ALL ITEMS
          const dropdownList = dropdownMenu.querySelectorAll('li');
          dropdownList.forEach((element) => element.classList.remove(SELECTED_CLASS_NAME));

          // MARK SELECTED ITEM
          const selectedItem = event.target;
          selectedItem.classList.add(SELECTED_CLASS_NAME);

          // UPDATE BUTTON LABEL
          dropdownLabel.textContent = selectedItem.innerText;
          const selectedLanding = selectedItem.getAttribute('value');
          changeEvent.detail.landingValue = landingList.find(
            (item) => item?.keyword?.toLowerCase() === selectedLanding,
          );
          dropdownLabel.dispatchEvent(changeEvent);

          // HIDE DROPDOWN WHEN SELECTED
          dropdownWrapper.classList.toggle(EXPANDED_CLASS_NAME);
        });

        dropdownMenu.appendChild(dropdownItem);
      });
      const dropdownContent = document.createElement('div');
      dropdownContent.classList.add('dropdown__content', 'dropdown--ease-transition', '--bordered', '--shadow');
      dropdownContent.append(dropdownMenu);

      // WRAPPER
      const dropdownWrapper = document.createElement('div');
      dropdownWrapper.classList.add('dropdown__wrapper');
      dropdownWrapper.append(dropdownButton, dropdownContent);

      dropdownButton.addEventListener('click', () => {
        dropdownWrapper.classList.toggle(EXPANDED_CLASS_NAME);
      });

      document.body.addEventListener('click', (event) => {
        // HIDE DROPDOWN WHEN CLICK OUTSIDE
        const target = event.target;
        const classList = target.classList;
        const parent = target.parentElement;
        const parentClassList = parent.classList;
        if (
          (classList.length > 0 && classList.contains('dropdown__button')) ||
          (parentClassList.length > 0 && parentClassList.contains('dropdown__button'))
        ) {
          return;
        }
        if (dropdownWrapper.classList.contains(EXPANDED_CLASS_NAME)) {
          dropdownWrapper.classList.remove(EXPANDED_CLASS_NAME);
        }
      });

      return dropdownWrapper;
    };

    const customSearchStack = (searchEngineWrapper) => {
      const searchBox = searchEngineWrapper.querySelector('table.gsc-search-box td.gsc-input');
      const currentDropdown = searchBox.querySelector('.gsc-dropdown-box');
      if (!currentDropdown) {
        const landingDropdown = document.createElement('div');
        landingDropdown.classList.add('gsc-dropdown-box');
        const dropdownWrapper = renderDropdownElements();
        landingDropdown.append(dropdownWrapper);
        searchBox.prepend(landingDropdown);
      }
    };

    document.querySelectorAll(searchEngineWrapperClass).forEach((searchEngineWrapper) => {
      customInput(searchEngineWrapper);

      if (searchEngineWrapper.classList.contains(searchResultWrapperClass.substring(1))) {
        customSearchStack(searchEngineWrapper);
        this.landingDropdownChangeListener(searchEngineWrapper);
        this.clearSearchListener(searchEngineWrapper);
      }
    });
  }

  getPromotionViewDetailURL(url) {
    if (!url) {
      return '';
    }

    const internalSearchURL = new URL(url, document.baseURI);
    internalSearchURL.searchParams.append('internalsearch', 1);
    return internalSearchURL.href;
  }

  renderPromotionCardThumbnail(promotion) {
    const { thumbnail, partner } = promotion;

    const thumbnailBackground = document.createElement('img');
    thumbnailBackground.src =
      'data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27620%27%20height=%27348%27/%3e';
    thumbnailBackground.alt = partner?.join();

    const thumbnailWrapper = document.createElement('span');
    thumbnailWrapper.classList.add('image-card-offer');
    thumbnailWrapper.append(thumbnailBackground);

    const thumbnailImage = document.createElement('img');
    thumbnailImage.src = thumbnail;
    thumbnailImage.classList.add('card-offer__image--thumbnail');

    const cardImageWrapper = document.createElement('span');
    cardImageWrapper.classList.add('card-offer__image--wrapper');
    cardImageWrapper.append(thumbnailWrapper, thumbnailImage);

    const cardImage = document.createElement('div');
    cardImage.classList.add('card-offer__image');
    cardImage.append(cardImageWrapper);

    return cardImage;
  }

  renderPromotionCardContent(promotion) {
    const { productTypes, merchant, description, expiryDate, openInNewTab, url, favourite } = promotion;

    const label = document.createElement('span');
    label.classList.add('card-content__label');
    label.textContent = productTypes?.map(x => x.title).filter(x => !!x).join(', ');

    const title = document.createElement('span');
    title.classList.add('card-content__title');
    title.textContent = merchant?.map(x => x.title).filter(x => !!x).join(', ');

    const cardDescription = document.createElement('span');
    cardDescription.classList.add('card-content__description');
    cardDescription.innerHTML = description;

    const cardExpiryDate = this.renderPromotionExpiredElement(expiryDate);

    const buttonText = document.createElement('span');
    buttonText.classList.add('button--text');
    buttonText.textContent = this.isEn ? 'View more' : 'Xem chi tiết';

    const redArrowIcon = document.createElement('img');
    redArrowIcon.src = iconRedArrow;
    redArrowIcon.alt = 'red-arrow';
    const buttonIcon = document.createElement('div');
    buttonIcon.classList.add('button--icon');
    buttonIcon.append(redArrowIcon);

    const viewDetailURL = this.getPromotionViewDetailURL(url);
    const linkButton = document.createElement('a');
    linkButton.classList.add('card-content__link--button');
    linkButton.target = /true/.test(openInNewTab) ? '_blank' : '_self';
    linkButton.href = viewDetailURL;
    linkButton.append(buttonText, buttonIcon);
    linkButton.addEventListener('click', (event) => {
      if (url?.includes('pdf')) {
        event.preventDefault();
        const searchResultWrapper = document.querySelector('.search-result__wrapper');
        const previewModal = searchResultWrapper.querySelector('.popup-download');
        $(previewModal).trigger('show-preview-modal', [
          {
            url: viewDetailURL,
            documentType: 'pdf',
          },
        ]);
      }
    });

    const link = document.createElement('div');
    link.classList.add('card-content__link');
    link.append(linkButton);

    const cardContent = document.createElement('div');
    cardContent.classList.add('card-offer__content');
    cardContent.append(label, title, cardDescription, cardExpiryDate, link);

    if (/true/.test(favourite)) {
      const favoriteImg = document.createElement('img');
      favoriteImg.src = iconFavorite;
      favoriteImg.alt = 'fav-icon';
      const favorite = document.createElement('div');
      favorite.classList.add('card-content__favorite-promo');
      favorite.append(favoriteImg);
      cardContent.append(favorite);
    }

    return cardContent;
  }

  renderPromotionCard(promotion) {
    const cardThumbnail = this.renderPromotionCardThumbnail(promotion);
    const cardContent = this.renderPromotionCardContent(promotion);

    const promotionCard = document.createElement('div');
    promotionCard.classList.add('card-promotion__item');
    promotionCard.append(cardThumbnail, cardContent);

    const promotionCardWrapper = document.createElement('div');
    promotionCardWrapper.classList.add('card-promotion__item-wrapper');
    promotionCardWrapper.append(promotionCard);

    return promotionCardWrapper;
  }

  renderPromotionPagination(totalItem, offset, limit) {
    let currentPage = offset / limit + 1;
    const totalPage = Math.ceil(totalItem / limit);
    const pageChangeEvent = new CustomEvent('pageChange', { detail: { offset: 0 } });

    if (totalPage < 1) {
      return document.createElement('div');
    }

    const prev = document.createElement('label');
    prev.classList.add('pagination-container--previous');
    prev.textContent = this.isEn ? 'Previous' : 'Trước';
    prev.addEventListener('click', () => {
      currentPage -= 1;
      pageChangeEvent.detail.offset = (currentPage - 1) * limit;
      pagination.dispatchEvent(pageChangeEvent);
    });

    const next = document.createElement('label');
    next.classList.add('pagination-container--next');
    next.textContent = this.isEn ? 'Next' : 'Tiếp theo';
    next.addEventListener('click', () => {
      currentPage += 1;
      pageChangeEvent.detail.offset = (currentPage - 1) * limit;
      pagination.dispatchEvent(pageChangeEvent);
    });

    const container = document.createElement('div');
    container.classList.add('pagination__container');

    if (currentPage !== 1) {
      container.append(prev);
    }

    for (let i = 1; i <= totalPage; i++) {
      const page = document.createElement('a');
      if (i === currentPage) {
        page.classList.add('active');
      }
      page.textContent = i.toString();
      page.addEventListener('click', () => {
        currentPage = i;
        pageChangeEvent.detail.offset = (currentPage - 1) * limit;
        pagination.dispatchEvent(pageChangeEvent);
      });
      container.append(page);
    }

    if (currentPage !== totalPage) {
      container.append(next);
    }

    const pagination = document.createElement('div');
    pagination.classList.add('pagination');
    pagination.append(container);

    return pagination;
  }

  renderEmptyPromotion() {
    const emptyText = document.createElement('p');
    emptyText.textContent = this.isEn ? 'No offers can be found' : 'Không tìm thấy ưu đãi';

    const emptyPromotion = document.createElement('div');
    emptyPromotion.classList.add('card-promotion__empty');
    emptyPromotion.append(emptyText);

    return emptyPromotion;
  }

  renderPromotionListElement(promotionData) {
    const { items, total } = promotionData;
    const wrapper = document.createElement('div');
    wrapper.classList.add('card-promotion__list');
    const promotionList = document.createElement('div');
    promotionList.classList.add('gsc-promotion-list-content');

    if (items?.length > 0) {
      items.forEach((promotion) => {
        const cardPromotion = this.renderPromotionCard(promotion);
        wrapper.append(cardPromotion);
      });
      const promotionPagination = this.renderPromotionPagination(
        total,
        this.promotionListParams.offset,
        this.promotionListParams.limit,
      );
      promotionList.append(wrapper, promotionPagination);
    } else {
      const emptyPromotion = this.renderEmptyPromotion();
      wrapper.append(emptyPromotion);
      promotionList.append(wrapper);
    }

    return promotionList;
  }

  renderPromotionExpiredElement(dataExpiredDate) {
    let expiredDate; // en is 'dd/MM/yyyy', vi is dd/MMM/yyyy
    switch (this.lang) {
      case LANG_EN:
        expiredDate = new Date(Date.parse(`${dataExpiredDate} 17:00:00 GMT`));
        break;

      case LANG_VI:
        const dateParts = dataExpiredDate.split('/');
        expiredDate = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0], 23, 59, 59);
        break;
    }
    const duration = expiredDate.getTime() - Date.now();
    const durationOf7Days = 604_800_000;
    const showExpiryCountdownBar = duration < durationOf7Days;

    const wrapper = showExpiryCountdownBar
      ? this.renderExpiredProgressBarElement()
      : this.renderExpiredLabelElement(dataExpiredDate);
    const expired = document.createElement('div');
    expired.classList.add('promotion__expired');
    expired.setAttribute('expired-time', expiredDate.getTime().toString());
    expired.append(wrapper);

    return expired;
  }

  updateExpiredProgressBar(expiredTime, wrapperElement, progressBarElement, countdownElement) {
    const durationOf7Days = 604_800_000;
    const intervalID = setInterval(() => {
      const remainingTime = expiredTime - Date.now();
      const days = Math.floor(remainingTime / (24 * 60 * 60 * 1000));
      const daysms = remainingTime % (24 * 60 * 60 * 1000);
      const hours = Math.floor(daysms / (60 * 60 * 1000));
      const hoursms = remainingTime % (60 * 60 * 1000);
      const minutes = Math.floor(hoursms / (60 * 1000));
      const minutesms = remainingTime % (60 * 1000);
      const seconds = Math.floor(minutesms / 1000);
      const remainingData = {
        day: days,
        time: `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}:${
          seconds < 10 ? '0' + seconds : seconds
        }`,
      };
      const remainingText = `${remainingData.day} ${this.isEn ? 'Day' : 'Ngày'} ${remainingData.time}`;
      const progressBarWidth = Number((remainingTime / durationOf7Days) * 100).toFixed();
      progressBarElement?.style?.setProperty('width', `${progressBarWidth}%`);
      countdownElement.textContent = remainingText;
      wrapperElement?.style?.setProperty('display', 'block');

      if (remainingTime <= 0) {
        clearInterval(intervalID);
      }
    }, 1000);
  }

  addSearchHistoryKeyword(keyword) {
    const keywords = getSearchHistoryKeywords();

    const index = keywords.indexOf(keyword);
    if (index < 0) {
      keywords.unshift(keyword);
      if (keywords.length > 5) {
        keywords.pop();
      }
      setSearchHistoryKeywords(keywords);
    }
  }

  renderExpiredProgressBarElement() {
    const progressBarInner = document.createElement('div');
    progressBarInner.classList.add('expired-progress__progress-bar--inner');

    const progressBar = document.createElement('div');
    progressBar.classList.add('expired-progress__progress-bar');
    progressBar.append(progressBarInner);

    const countdown = document.createElement('div');
    countdown.classList.add('expired-progress__countdown-time');

    const expiredProgress = document.createElement('div');
    expiredProgress.classList.add('expired-progress');
    expiredProgress.append(progressBar, countdown);

    return expiredProgress;
  }

  renderExpiredLabelElement(expiredDate) {
    const expiredIcon = document.createElement('img');
    expiredIcon.src = iconTime;
    expiredIcon.alt = 'time-icon';
    expiredIcon.classList.add('expired-label--icon');

    const expiredText = document.createElement('span');
    expiredText.classList.add('expired-label--text');
    expiredText.textContent = `${this.isEn ? 'Expires' : 'Kết thúc'} ${expiredDate}`;

    const expiredLabel = document.createElement('div');
    expiredLabel.classList.add('expired-label');
    expiredLabel.append(expiredIcon, expiredText);

    return expiredLabel;
  }

  renderAdBannerElement(adsBannerData) {
    const { title, ctaLabel, description, ctaIcon, externalUrl, url, bannerImageMobile, bannerImageDesktop, theme } =
      adsBannerData;

    const closeButtonElement = document.createElement('img');
    closeButtonElement.src = iconCloseWhite;
    closeButtonElement.classList.add('ad-banner__close-button');
    closeButtonElement.addEventListener('click', () => {
      this.isClosedBanner = true;
      this.removeAdBanner();
    });

    const titleElement = document.createElement('h3');
    titleElement.classList.add('ad-banner__title', 'font-light');
    titleElement.textContent = title;

    const descriptionElement = document.createElement('p');
    descriptionElement.classList.add('ad-banner__description', 'font-light');
    descriptionElement.textContent = description;

    const wrapper = document.createElement('div');
    wrapper.classList.add('ad-banner__wrapper');
    wrapper.style.setProperty(
      '--backgroundImageUrl',
      window.innerWidth <= mobileBreakPoint ? `url(${bannerImageMobile})` : `url(${bannerImageDesktop})`,
    );
    wrapper.append(closeButtonElement, titleElement, descriptionElement);

    if (externalUrl || url) {
      const ctaLabelElement = document.createElement('span');
      ctaLabelElement.classList.add('cmp-button__text', 'text-base', 'font-bold');
      ctaLabelElement.textContent = ctaLabel;

      const ctaIconElement = document.createElement('img');
      ctaIconElement.classList.add('cmp-button__icon');
      ctaIconElement.src = ctaIcon;

      const ctaElement = document.createElement('a');
      ctaElement.classList.add('cta-button', 'tcb-button');

      if (externalUrl) {
        ctaElement.href = externalUrl;
        ctaElement.target = '_blank';
      } else {
        ctaElement.href = url;
        ctaElement.target = '_self';
      }
      ctaElement.append(ctaLabelElement, ctaIconElement);
      wrapper.append(ctaElement);
    }

    const adBanner = document.createElement('div');
    adBanner.classList.add('gsc-ad-banner', `theme-${theme}`);
    adBanner.append(wrapper);
    return adBanner;
  }
}
