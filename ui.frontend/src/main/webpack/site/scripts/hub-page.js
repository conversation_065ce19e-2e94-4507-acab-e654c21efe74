import $ from 'jquery';
import { SELECTOR_DETAIL_LINK } from './constants/offer-common';
import { renderDayExpired } from './utils/day-expired.util';
import { renderImage } from './utils/default-image.util';
import { BaseComponent } from './base';
import { screenSmMax } from './constants/common';
import { RecommendCategory } from './recommend-category';

export class OfferCardComponent extends BaseComponent {
  intervalId = null;
  recommendCategory = null;

  constructor() {
    super();
  }

  connectedCallback() {
    this.initOfferCard(window, $(document));
    this.initRecommendCategory();
  }

  initRecommendCategory() {
    if (!this.recommendCategory) {
      this.recommendCategory = new RecommendCategory();
    }
  }

  showPreviewModalListener(event) {
    const target = event.currentTarget;
    const $target = $(target);
    const dataDownloadPdfLabel = $target.data('download-pdf-label');
    const previewURL = $target.attr('href');

    if (previewURL && previewURL.includes('.pdf')) {
      event.preventDefault();
      this.triggerShowPreviewModal(previewURL, 'Preview', 'Loading PDF...', dataDownloadPdfLabel);
    }
  }

  triggerShowPreviewModal(url, title, loadingLabel, downloadLabel) {
    $('.popup-download').trigger('show-preview-modal', [
      {
        url,
        documentType: 'pdf',
        title,
        loadingLabel,
        downloadLabel,
      },
    ]);
  }

  bindPdfClickListener(documentRoot = $(document)) {
    const selectorPdfLink = `.card a.promotion-product-listing__link[href]:not(${SELECTOR_DETAIL_LINK})`;
    documentRoot.off('click', selectorPdfLink);
    documentRoot.on('click', selectorPdfLink, this.showPreviewModalListener.bind(this));
  }

  startDayExpiredInterval(callback = renderDayExpired) {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.intervalId = setInterval(callback, 1000);
  }

  stopDayExpiredInterval() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  renderCardExclusiveOffer() {
    renderDayExpired();
    this.startDayExpiredInterval();
  }

  initOfferCard(windowObj = window, $doc = $(document)) {
    this.bindPdfClickListener($doc);
    this.renderCardExclusiveOffer();

    if (windowObj.innerWidth <= screenSmMax) {
      const $slider = $('.promotion-hub_hub-card').not('.slick-initialized');
      if ($slider.length) {
        $slider.slick({
          slidesToShow: 1.7,
          slidesToScroll: 1,
          autoplay: false,
          arrows: false,
          dots: false,
          infinite: false,
          draggable: true,
          responsive: [
            {
              breakpoint: screenSmMax,
              settings: {
                slidesToShow: 1.7,
                slidesToScroll: 1,
              },
            },
            {
              breakpoint: 992,
              settings: 'unslick',
            },
          ],
        });
      }
    }
    renderImage();
  }
}
