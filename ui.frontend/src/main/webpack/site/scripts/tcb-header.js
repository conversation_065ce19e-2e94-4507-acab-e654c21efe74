import { BaseComponent } from './base';
import { lgSemiBreakpoint } from './constants/common';
import { reCalculateHeaderHeight } from './tcb-global-announcement';

export class TcbHeader extends BaseComponent {
  globalHeader = document.querySelector('.global-header');
  masthead = document.querySelector('.tcb-hero-banner');

  heightOfMasthead = this.masthead ? this.masthead.clientHeight : 300;
  lastScrollTop = 0;
  heightOfHeader = 0;
  hasSearchEngine = false;

  altButton;
  altFooterButton;
  loginButton;
  secondaryMenu;
  primaryMenu;
  searchEngine;

  constructor() {
    super();
    this.onResize();
    if (this.globalHeader && this.headerElement) {
      this.initHeaderChildren();
      this.handleDisplayAlternativeHeader();
    }
  }

  get headerElement() {
    return this.querySelector('.header_layout');
  }

  get altLabelText() {
    return this.getAttribute('data-alt-label-text') || '';
  }

  get altLabelLink() {
    return this.getAttribute('data-alt-label-link') || '';
  }

  get altTarget() {
    return this.getAttribute('data-alt-target') || '_self';
  }

  get stickyCTALabelText() {
    return this.getAttribute('data-sticky-cta-label-text') || '';
  }

  get stickyCTALabelLink() {
    return this.getAttribute('data-sticky-cta-label-link') || '';
  }

  get stickyCTATarget() {
    return this.getAttribute('data-sticky-cta-target') || '_self';
  }

  get hideStickyCTA() {
    const dataHideStickyCta = this.getAttribute('data-hide-sticky-cta');
    return /true/.test(dataHideStickyCta);
  }

  initHeaderChildren() {
    this.heightOfHeader = this.headerElement.offsetHeight;
    this.altButton =
      this.headerElement.querySelector('.navigation-secondary_actions .link_component-link.alt-link.hide-on-mobile#alt-btn');
    this.altFooterButton =
      this.headerElement.querySelector('.alternate-button-mobile .link_component-link.alt-link#alt-btn');
    this.loginButton = this.headerElement.querySelector('.navigation-secondary_actions #login-btn');
    this.secondaryMenu = this.headerElement.querySelector('.navigation_secondary .navigation-secondary_menu');
    this.primaryMenu = this.headerElement.querySelector('.navigation_primary.content-wrapper');
    this.searchEngine = this.headerElement.querySelector('tcb-search-engine-mobile');
    this.hasSearchEngine = this.searchEngine && this.searchEngine.classList.contains('search-engine-show');
  }

  isExistHeaderElement() {
    return !!(this.loginButton && this.secondaryMenu && this.primaryMenu && this.altButton && this.altFooterButton);
  }

  isStickyCTAValid() {
    return !!(!this.hideStickyCTA && this.stickyCTALabelText && this.stickyCTALabelLink);
  }

  isAltButtonValid() {
    return !!(this.altLabelText && this.altLabelLink);
  }

  isShowAlternativeButton() {
    const isStickyCTAValid = this.isStickyCTAValid();
    const isAltButtonValid = this.isAltButtonValid();
    return !!(isStickyCTAValid || isAltButtonValid);
  }

  handleDisplayAlternativeHeader() {
    const isExistHeader = this.isExistHeaderElement();
    const isShowAlternativeButton = this.isShowAlternativeButton();
    if (!isExistHeader || !isShowAlternativeButton) {
      return;
    }

    if (window.innerWidth > lgSemiBreakpoint) {
      this.globalHeader.style.height = `${this.heightOfHeader}px`;
      this.globalHeader.style.position = 'relative';
      this.headerElement.style.width = '100%';
      this.headerElement.style.position = 'fixed';
    }

    this.initAltButton(); // DESKTOP VIEW
    this.initAltFooterButton(); // MOBILE VIEW
    this.adjustFooter();
    this.scrollWindowListener();
    this.resizeWindowListener();
  }

  initAltButton() {
    const isStickyCTAValid = this.isStickyCTAValid();
    const altButtonText = this.altButton.querySelector('span.alt-label-text');
    altButtonText.textContent = isStickyCTAValid ? this.stickyCTALabelText : this.altLabelText;
    this.altButton.href = isStickyCTAValid ? this.stickyCTALabelLink : this.altLabelLink;
    this.altButton.target = isStickyCTAValid ? this.stickyCTATarget : this.altTarget;
  }

  initAltFooterButton() {
    const isStickyCTAValid = this.isStickyCTAValid();
    const altFooterButtonText = this.altFooterButton.querySelector('span.alt-label-text');
    altFooterButtonText.textContent = isStickyCTAValid ? this.stickyCTALabelText : this.altLabelText;
    this.altFooterButton.href = isStickyCTAValid ? this.stickyCTALabelLink : this.altLabelLink;
    this.altFooterButton.target = isStickyCTAValid ? this.stickyCTATarget : this.altTarget;
  }

  togglePrimaryMenu(isShow) {
    this.primaryMenu.classList.remove(isShow ? 'hide-nav' : 'show-nav');
    this.primaryMenu.classList.add(isShow ? 'show-nav' : 'hide-nav');
  }

  toggleAltButton(isShow) {
    this.altButton.style.display = isShow ? 'flex' : 'none';
    this.loginButton.style.display = isShow ? 'none' : 'flex';
    this.secondaryMenu.style.display = isShow ? 'none' : 'flex';
  }

  showMobileMenu() {
    const id = setInterval(() => {
      const menuMobile = this.headerElement.querySelector('.mobile-menu-items .navigation-secondary_menu');
      if (menuMobile) {
        clearInterval(id);
        menuMobile.style.display = 'flex';
      }
    }, 100);
  }

  adjustFooter() {
    this.headerElement.style.width = '100%';
    if (window.innerWidth <= lgSemiBreakpoint) {
      this.globalHeader.style.position = 'sticky';
      this.altButton.style.display = 'none';
      this.loginButton.style.display = 'flex';
      this.secondaryMenu.style.display = 'none';
      this.showMobileMenu();
      setTimeout(() => {
        const buttonMobile = this.headerElement.querySelector('.alternate-button-mobile');
        const footer = document.querySelector('footer');
        if (buttonMobile && footer) {
          footer.style.marginBottom = `${buttonMobile.offsetHeight}px`;
        }
      }, 500);
    } else {
      this.globalHeader.style.position = 'relative';
      this.headerElement.style.position = 'fixed';

      if (window.scrollY > this.heightOfMasthead) {
        this.togglePrimaryMenu(false);
        this.toggleAltButton(true);
      } else {
        this.togglePrimaryMenu(true);
        this.toggleAltButton(false);
      }

      this.lastScrollTop = 0;
    }
  }

  handleScroll() {
    if (window.innerWidth <= lgSemiBreakpoint) {
      return;
    }

    const isShowSticky = window.scrollY > this.heightOfMasthead;
    this.showStickyCTA(isShowSticky);
  }

  showStickyCTA(isShowSticky) {
    if (isShowSticky) {
      const isShowPrimaryMenu = window.scrollY <= this.lastScrollTop;
      this.togglePrimaryMenu(isShowPrimaryMenu);
      this.toggleAltButton(true);
      if (Math.abs(this.lastScrollTop - window.scrollY) > 150) {
        this.lastScrollTop = window.scrollY;
      }
    } else {
      this.togglePrimaryMenu(true);
      this.toggleAltButton(false);
    }
  }

  scrollWindowListener() {
    window.addEventListener('scroll', this.handleScroll.bind(this));
  }

  resizeWindowListener() {
    window.addEventListener('resize', this.adjustFooter.bind(this));
  }

  onResize() {
    window.addEventListener('resize', () => {
      reCalculateHeaderHeight();
    });
  }
}
