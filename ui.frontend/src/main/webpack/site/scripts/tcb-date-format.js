import moment from 'moment';
jQuery(function () {
    $(".list-event-tile").each(function () {
        const eventTile = $(this);
        const dateAuthor = eventTile.find(".tcb-date");
        formatDate(dateAuthor, document.documentElement.lang);
        
    });

    $(".article-header__container").each(function () {
        const articleDate = $(this).find(".article-header-body--date");
        articleFormattedDate(articleDate, document.documentElement.lang);
    });

    $(".currency-converter").each(function () {
        const currencyConverter = $(this);
        const exchangeNoteElement = currencyConverter.find(".exchange-section__note");
        let exchangeDate = exchangeNoteElement.attr("data-latest-exchange-rate-date-time");
        let exchangeNote = exchangeNoteElement.text();
        let exchangeDateFormat;

        if(exchangeDate && exchangeNote.includes("%exchangeRateDateTime")) {
            if (document.documentElement.lang === 'en') {
                exchangeDateFormat = moment(exchangeDate, "YYYY-MM-DD HH:mm:ss").format("DD MMM yyyy HH:mm:ss");
            } else {
                exchangeDateFormat = moment(exchangeDate, "YYYY-MM-DD HH:mm:ss").format("DD/MM/YYYY HH:mm:ss");
            }
            
            exchangeNoteElement.text(exchangeNote.replace("%exchangeRateDateTime", exchangeDateFormat));
        }
    });

    $(".list-row .list-row-content").each(function () {
        const listRows = $(this);
        const rows = listRows.find(".list-row-content_date .color-gray");
        formatDate(rows, document.documentElement.lang);
    });

    $(".quick-access").each(function () {
        const quickAccess = $(this);
        const exchangeNoteElement = quickAccess.find(".inner-footer__right-text");
        let exchangeDate = exchangeNoteElement.attr("data-latest-exchange-rate-date-time");
        let exchangeNote = exchangeNoteElement.text();
        let exchangeDateFormat;

        if(exchangeDate && exchangeNote.includes("%exchangeRateDateTime")) {
            if (document.documentElement.lang === 'en') {
                exchangeDateFormat = moment(exchangeDate, "YYYY-MM-DD HH:mm:ss").format("DD MMM yyyy");
            } else {
                exchangeDateFormat = moment(exchangeDate, "YYYY-MM-DD HH:mm:ss").format("DD/MM/YYYY");
            }
            
            exchangeNoteElement.text(exchangeNote.replace("%exchangeRateDateTime", exchangeDateFormat));
        }
    });
});

export function formatDate(dateAuthor, locale) {
    if(locale === 'en') {
        dateAuthor.each(function() {
            let date = new Date($(this).text());
            $(this).html(moment(date).format("DD MMM yyyy"));
        });
    } else {
        dateAuthor.each(function() {
            let date = new Date($(this).text());
            $(this).html(moment(date).format("DD/MM/YYYY"));
        });
    }
}

export function articleFormattedDate(dateAuthor, locale) {
    const vietnamOffset = 7 * 60 * 60 * 1000;
        let date = new Date($(dateAuthor).text());        
        let vietnamTime = new Date(date.getTime() + vietnamOffset);
        if(locale === 'en') {
            dateAuthor.html(moment(vietnamTime).format("DD MMM yyyy"));
        } else {
            dateAuthor.html(moment(vietnamTime).format("DD/MM/YYYY"));
        }
}
