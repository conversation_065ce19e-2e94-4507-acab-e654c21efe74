$(document).ready(function () {
    let tableBenefits = $('.table-compare-component');
    let listColBenefits = tableBenefits.find('.table-compare-content-col');
    let firstColBenefit = listColBenefits.find('.first-col');
  
    tableBenefits.each(function () {
      let firstCol = $(this).find('.first-col');
      let firstColRows = firstCol.find('.table-compare-item-row:not(.first-row)');
      let headerMsgList = [];
      firstColRows.each(function(){
        headerMsgList.push($(this).find('p:not(:empty)').html());
      })
      let rowsNumber = headerMsgList.length;
      //set moveld title for mobile view
      for(let i = 0; i < rowsNumber; i++){
        let rowItems = $(this).find('.table-compare-item-row:not(.first-col-row)[row-index=' + i + ']');
        let movedTitles = rowItems.find('.moved-title')
        movedTitles.html(headerMsgList[i]);
        if ($(window).width() <= 991) {
          movedTitles.removeClass('moved-title');
        }
      }

      //set all cells in the same rows to have equal height
      for(let i = -1; i < rowsNumber; i++) {
        const rowItems = $(this).find(`.table-compare-item-row[row-index=${i}]`);
        const elementHeight = [];
        let maxHeight = 0;

        if (i === -1) {
          rowItems.each((_, element) => elementHeight.push($(element).find('span').height()));
          maxHeight = Math.max(...elementHeight);
          rowItems.height(maxHeight);
          rowItems.find('picture').height(maxHeight);
          continue;
        }

        rowItems.each((_, element) => elementHeight.push($(element).height()));
        maxHeight = Math.max(...elementHeight);
        rowItems.height(maxHeight);

        if (i === rowsNumber - 1) {
          rowItems.addClass('last-row');
        }
      }
    })

    listColBenefits.each(function () {
      if ($(window).width() <= 991) {
        firstColBenefit.detach();
        $(this).not('.slick-initialized').slick({
          slidesToShow: 1,
          slidesToScroll: 1,
          autoplay: false,
          arrows: false,
          dots: true,
          infinite: true,
          speed: 500,  
          fade: true,  
          cssEase: 'linear'
        });
      }
    });
  
    $(window).resize(function () {
      listColBenefits.each(function () {
        if ($(window).width() <= 991) {
          firstColBenefit.detach();
          $(this).not('.slick-initialized').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: false,
            arrows: false,
            dots: true,
            infinite: true,
          });
        } else {
          $(this).filter('.slick-initialized').slick('unslick');
          $(this).find('.table-compare-item-col').first().before(firstColBenefit);
        }
      });
    });
  });