import { createSearchUrl } from './offer-helper';

/*
 * <PERSON><PERSON> retain params when switch languages on offer listing page.
 */
$(function () {
  const languageDropdownItems = $('header .language_dropdown a');
  const offerListingComponent = $('.offer-listing-filter__body');
  const isLoadedJs = offerListingComponent?.attr('data-js-switch-lang');
  if (!offerListingComponent.length || isLoadedJs) {
    return;
  }
  // Make sure this script file only run one time.
  offerListingComponent.attr('data-js-switch-lang', 'true');

  const paramsNameWillRemove = ['q'];
  const checkParamsName = {
    cardTypes: 'card-types',
    products: 'products',
    memberships: 'memberships',
    types: 'types',
    partner: 'partner',
    location: 'location',
    membership: 'membership',
  };

  function verifyParamValues(value) {
    const removedParentKeys = value?.split('/')?.pop();
    const valueDecoded = removedParentKeys?.replaceAll(' ', '-')?.replaceAll('+', '-');
    const inputValue = document.querySelector(`.offer-filter__container [value*="${valueDecoded}"]`);
    return inputValue !== null ? value : null;
  }

  function getParams() {
    const params = {};
    const url = new URL(window.location);
    for (const [key, value] of url.searchParams) {
      if (paramsNameWillRemove.includes(key)) {
        continue;
      }
      const valueDecoded = decodeURIComponent(value);
      const verifiedValues = valueDecoded
        ?.split(',')
        ?.map((v) => verifyParamValues(v))
        ?.filter((v) => v != null)
        ?.join(',');
      if (verifiedValues) {
        params[key] = verifiedValues;
      } else if (!checkParamsName[key]) {
        params[key] = value;
      }
    }
    if (!Object.keys(params)?.length) {
      return null;
    }
    return params;
  }

  languageDropdownItems.each((index, element) => {
    $(element).on('click', function (event) {
      event.preventDefault();
      const href = $(this)?.attr('href');
      const params = getParams();
      window.location.href = createSearchUrl(href, params);
    });
  });
});
