import { lgSemiBreakpoint } from './constants/common';

$(function() {
  $('.article-listing').each(function() {
    const perPage = $(this).attr('data-number') ?? 9;
    const numberItems = $(this).find('.article-card').length;
    const viewMore = $(this).find('.view-more');
    const viewMoreBtn = viewMore.find('.btn.btn-outline');
    $(this).find('.article-card').css('display', 'none');
    let currentItems = 0;

    const updateItems = () => {
      currentItems += perPage;
      $(this).find(`.article-card:nth-child(-n+${currentItems})`).css('display', '');
      if (numberItems <= currentItems) {
        viewMore.css('display', 'none');
      } else {
        viewMore.css('display', '');
      }
    }

    updateItems();
    viewMoreBtn.on('click', function() {
      updateItems();
    });
  });

  $('.table-content__tableItem-text').click(function () {
    const anchorName = $(this)?.attr('href');

    if (!anchorName) {
      return;
    }

    const headerElement = $('header .header_layout');
    const tocElement = $('.articletableofcontent');
    const articleElement = $('.article-text');
    const anchorElement = articleElement?.find(`${anchorName}`);

    if (!(anchorElement.length > 0
          && headerElement.length > 0
          && tocElement.length > 0
          && articleElement.length > 0)
    ) {
      return;
    }

    let heightOfHeader = headerElement.outerHeight();
    const anchorOffsetTop = anchorElement.offset().top;
    const articleOffsetTop = articleElement.offset().top;
    const tocOffsetTop = tocElement.offset().top;
    const heightOfTOC = tocElement.outerHeight();
    const tcbHeader = document.querySelector('tcb-header');
    const globalAnnouncement = window.innerWidth > lgSemiBreakpoint
                               ? tcbHeader.querySelector('tcb-global-announcement')
                               : tcbHeader.querySelector('.navigation_secondary tcb-global-announcement');
    const heightOfGA = globalAnnouncement?.offsetHeight ?? 0;
    const altLabelText = tcbHeader.getAttribute('data-alt-label-text');
    const altLabelLink = tcbHeader.getAttribute('data-alt-label-link');
    const stickyCTALabelText = tcbHeader.getAttribute('data-sticky-cta-label-text');
    const stickyCTALabelLink = tcbHeader.getAttribute('data-sticky-cta-label-link');
    const hideStickyCTA = /true/.test(tcbHeader.getAttribute('data-hide-sticky-cta'));
    const isStickyCTAValid = !!(!hideStickyCTA && stickyCTALabelText && stickyCTALabelLink);
    const isAltButtonValid = !!(altLabelText && altLabelLink);
    const isShowAlternativeButton = !!(isStickyCTAValid || isAltButtonValid);

    if (isShowAlternativeButton && window.innerWidth > lgSemiBreakpoint) {
      const expandedHeight = 101;
      const collapsedHeight = 56;
      heightOfHeader = anchorOffsetTop > tocOffsetTop ? collapsedHeight : expandedHeight;
      heightOfHeader += heightOfGA;
    }

    if (articleOffsetTop > heightOfHeader) {
      $('.table-content__label').click();
    }

    location.hash = anchorName;

    setTimeout(() => {
      window.scrollTo({ top: anchorOffsetTop - (heightOfHeader + heightOfTOC), behavior: 'smooth' });
    }, 200);
  });

  window.addEventListener('scroll', function () {
    const tableLabel = $('.table-content__label');
    const tableEntered = $('.table-content__entered');
    const tocElement = $('.articletableofcontent');
    const headerElement = $('header .header_layout');
    const articleElement = $('.article-text');

    if (!(tocElement.length > 0
          && articleElement.length > 0
          && tableLabel.length > 0
          && tableEntered.length > 0
          && headerElement.length > 0)
    ) {
      return;
    }
    const articleOffsetTop = articleElement?.get(0)?.getBoundingClientRect()?.top ?? 0;
    const heightOfHeader = headerElement.outerHeight();

    if (articleOffsetTop < heightOfHeader) {
      tocElement.css('top', `${heightOfHeader}px`);
      if (!tableEntered.hasClass('hide')) {
        tableLabel.click();
      }
    } else {
      tocElement.css('top', '');
    }
  });
});
