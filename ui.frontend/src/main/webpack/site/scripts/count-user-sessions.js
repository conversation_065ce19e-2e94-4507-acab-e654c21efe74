import { StorageUtils } from './utils/storage.util';

const SESSION_COUNT_DATA_KEY = 'session-count-data';

export const updateSessionCountData = (data) => {
  const sessionParam = StorageUtils.get(SESSION_COUNT_DATA_KEY) ?? {};
  Object.assign(sessionParam, data);
  StorageUtils.set(SESSION_COUNT_DATA_KEY, sessionParam);
};

export const getSessionCountData = (id) => {
  const sessionParam = StorageUtils.get(SESSION_COUNT_DATA_KEY);
  const data = {
    [id]: {
      isClosed: sessionParam && sessionParam[id] ? sessionParam[id].isClosed : false,
      count: sessionParam && sessionParam[id] && sessionParam[id].isClosed ? ++sessionParam[id].count : 0,
    },
  };
  updateSessionCountData(data);
  return data;
};
