$(document).ready(function () {
    let cCashScoreBoard = $('.c-cash-score-board-component');

    cCashScoreBoard.each(function () {
        let multiStepForm = $(this).closest('.multistepform');
        let scoreBoard = $(this).find('.scoreboard');
        let scoreBoardTBody = scoreBoard.find('tbody');
        let lang = $(this).attr('lang');
        let surveySteps = multiStepForm.find('.multi-step-form__step:not(:first):not(:last)');

        getTotalScoreTitle(lang, scoreBoard);

        let company_name = '';
        multiStepForm.find('input[name=company_name]').change(function() {
            company_name = $(this).val();
            getCompanyName(company_name);
        });

        generateTable(surveySteps);

        getAverageScore(surveySteps);

        calculateScore(surveySteps);

        //localize total score title
        function getTotalScoreTitle(lang, scoreBoard) {
            let totalScoreTitleText = (lang == 'English') ? 'Overall score' : 'Tổng điểm';
            let totalScoreTitle = scoreBoard.find('.total-score-title');
            totalScoreTitle.text(totalScoreTitleText);
        }

        //add company name title
        function getCompanyName(company_name) {
            //santinize string - remove html tags
            company_name = company_name.replace(/<[^>]*>?/gm, '').trim();
            let companyNameTitle = cCashScoreBoard.find('.company-name-title');
            companyNameTitle.text(company_name);
        }

        //generate score board table, each question in form = 1 row
        function generateTable(surveySteps) {
            let totalScoreRow = scoreBoard.find('tbody .total-score-row');
            surveySteps.each(function () {
                let stepTitle = $(this).find('.title .tcb-title').text().replace(/^\s*(\d+|[MDCLXVI]+)\.\s*/, '');
                totalScoreRow.before('<tr><th class="first-col-cell content-title-bold">' + stepTitle + '</th><td class="content-body-regular"></td><td class="content-body-regular"></td></tr>');
            })
        }

        //get average target score from hidden form and insert to scoreboard
        /*
            NOTE: 'AVERAGE' SCORE HERE DOES NOT MEAN THE MEAN SCORE OF THE USER. IT IS A STANDARD VALUE PROVIDED BY
            BUSINESS BY ADDING A HIDDEN FORM FIELD IN MULTISTEPFORM SO THAT USER CAN COMPARE THEIR RESULT WITH A STANDARD
        */
        function getAverageScore(surveySteps) {
            let surveyStepsNumber = surveySteps.length;
            let averageScoreArr = new Array(surveyStepsNumber);
            let totalAverageScore = 0;
            surveySteps.each(function(index) {
                // get average score from hidden form field
                let averageScoreField = $(this).find('input[type=hidden][name=avgScore]');
                if(averageScoreField.length > 0){
                    averageScoreArr[index] = formatNumber(Number(averageScoreField.attr('value')));
                    // calculate final average score
                    totalAverageScore += Number(averageScoreField.attr('value'));
                    const finalAverageScore = formatNumber(totalAverageScore / surveyStepsNumber);
                    scoreBoardTBody.find('tr:nth-child(' + (index + 3) + ') td:nth-child(3)').text(averageScoreArr[index]);
                    scoreBoardTBody.find('tr:nth-child(' + (surveyStepsNumber + 3) + ') td:nth-child(3)').text(finalAverageScore);
                }
            })
        }

        //calculate user score from survey and insert them to score board
        function calculateScore(surveySteps) {
            let surveyStepsNumber = surveySteps.length;
            let scoreArr = new Array(surveyStepsNumber);
            let totalScore = 0;
            let questionsArr = new Array(surveyStepsNumber);
            let totalQuestions = 0;
            let resultArr = new Array(surveyStepsNumber);
            surveySteps.each(function(index) {
                let surveyStep = $(this);
                let surveys = surveyStep.find('.form-survey__single-choice');
                questionsArr[index] = surveys.length;
                totalQuestions += questionsArr[index];
                $(this).find('input.input__radio').click(function() {
                    scoreArr[index] = calculateStepScore(surveys);
                    try {
                        resultArr[index] = formatNumber(scoreArr[index] / questionsArr[index]);
                    }
                    catch(err) {
                        console.log(err);
                        resultArr[index] = 0;
                    }
                    // insert point values to table
                    scoreBoardTBody.find('tr:nth-child(' + (index + 3) + ') td:nth-child(2)').text(resultArr[index]);
                    // get sum of score, divide by total number of questions and insert final score to final row in table
                    totalScore = scoreArr.reduce((a, b) => a + b, 0);
                    let finalScore = formatNumber(totalScore / totalQuestions);
                    scoreBoardTBody.find('tr:nth-child(' + (surveyStepsNumber + 3) + ') td:nth-child(2)').text(finalScore);
                    highlightScore();
                })
            })
        }

        //calculate score of each step
        function calculateStepScore(surveys) {
            let stepScore = 0;
            surveys.each(function() {
                let optionsSelected = $(this).find('.answer__item--input:checked');
                optionsSelected.each(function() {
                    stepScore = stepScore + Number($(this).attr('value'));
                })
            })
            return stepScore;
        }

        //hightlight score as red/green depends on greater/less than average value
        function highlightScore() {
            let scoreBoardRows = scoreBoardTBody.find('tr');
            scoreBoardRows.each(function(index) {
                if(index > 1) {
                    let calculatedScore = $(this).find('td:nth-child(2)').text();
                    let averageScore = $(this).find('td:nth-child(3)').text();
                    if(calculatedScore < averageScore) {
                        $(this).find('td:nth-child(2)').addClass('low-score');
                        $(this).find('td:nth-child(2)').removeClass('high-score');
                    }
                    else {
                        $(this).find('td:nth-child(2)').removeClass('low-score');
                        $(this).find('td:nth-child(2)').addClass('high-score');
                    }
                }
            })
        }

        // get result with 1 decimal place
        function formatNumber(num) {
            if(typeof num === 'number') {
                return (Math.round(num * 100) / 100).toFixed(1);
            }
            else {
                return '0';
            }
        }
    });
});