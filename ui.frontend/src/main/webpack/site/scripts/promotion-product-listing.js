import { BaseComponent } from './base';
import { getDataResultPage } from './utils/get-data-result-page.util';
import { getEndpoint } from './utils/promotion-generate-endpoint.util';
import { DEFAULT_SORT_PARAMS, PARAM_ALL_PRODUCT } from './constants/offer-common';

export class TcbPromotionProductList extends BaseComponent {
  constructor() {
    super();
    this.CLASS_TAB_SELECT = 'active';
    this.PARAM_PROMOTION_LIST = {};
    this.hrefReadMore = '';

    this.paramsValue = {
      limit: 4,
      offset: 0,
      sort: DEFAULT_SORT_PARAMS,
    };

    this.typesTilter = {
      cardTypes: 'card-types',
      products: 'products',
      types: 'types',
    };
    this.init();
  }

  init() {
    this.root = this.querySelector('.tcb-promotion-product-list');
    if (!this.root) {
      return;
    }
    const dataset = this.dataset;
    this.dataUrl = dataset.url;

    this.dataExpiryDate = {
      lang: dataset.lang || '',
      dayText: dataset.dayText || '',
      labelExpired: dataset.labelExpired || '',
      labelExpiredCountDown: dataset.labelExpiredCountDown || '',
    };

    this.cacheDOMElements();
    this.bindEvents();
    this.setDefaultTabSelection();
  }

  setDefaultTabSelection() {
    const firstTab = this.root.querySelector('.tab-card__link');
    if (firstTab) {
      const param = firstTab.getAttribute('data-param');
      const value = firstTab.getAttribute('data-value');

      if (param && value) {
        firstTab.classList.add(this.CLASS_TAB_SELECT);
        this.PARAM_PROMOTION_LIST[param] = [value];
      }
    }

    this.addParamButtonReadMore();
    this.renderProductList();
  }

  cacheDOMElements() {
    this.tabCardItem = this.root.querySelectorAll('.tab-card__link');
    this.classGroupCardName = this.root.querySelector('.group-card');
    this.buttonReadMore = this.root.querySelector('.promotion-product-listing__see-detail-btn a');
    if (this.buttonReadMore) {
      this.hrefReadMore = this.buttonReadMore.getAttribute('href');
    }
  }

  bindEvents() {
    if (this.tabCardItem) {
      this.tabCardItem.forEach((button) => {
        button.addEventListener('click', (e) => {
          this.handleButtonClick(e);
        });
      });
    }
  }

  handleButtonClick(e) {
    const clickedButton = e.currentTarget;
    const param = clickedButton.getAttribute('data-param');
    const value = clickedButton.getAttribute('data-value');

    if (!param || !value) {
      return;
    }

    this.tabCardItem.forEach((button) => {
      button.classList.remove(this.CLASS_TAB_SELECT);
    });
    clickedButton.classList.add(this.CLASS_TAB_SELECT);

    this.PARAM_PROMOTION_LIST = {};
    this.PARAM_PROMOTION_LIST[param] = [value];

    this.addParamButtonReadMore();
    this.renderProductList();
  }

  addParamButtonReadMore() {
    if (this.buttonReadMore) {
      const keys = Object.keys(this.PARAM_PROMOTION_LIST);
      let hrefParam = '';

      if (keys.indexOf('products') !== -1) {
        hrefParam = 'target=product&products=' + PARAM_ALL_PRODUCT;
      } else if (keys.indexOf('card-types') !== -1) {
        const val = this.PARAM_PROMOTION_LIST['card-types'][0];
        const firstVal = val.split('/')[0];
        hrefParam = 'target=product&card-types=' + firstVal;
      }

      let sep = '?';
      if (this.hrefReadMore.indexOf('?') !== -1) {
        sep = '&';
      }

      this.buttonReadMore.setAttribute('href', this.hrefReadMore + sep + hrefParam);
    }
  }

  renderProductList() {
    const baseUrl = getEndpoint(this.dataUrl, this.paramsValue);
    const promotionParams = this.getParamPromotionListAsQuery();
    let fullUrl = baseUrl;

    if (promotionParams) {
      if (baseUrl.includes('?')) {
        fullUrl = baseUrl + '&' + promotionParams;
      } else {
        fullUrl = baseUrl + '?' + promotionParams;
      }
    }

    getDataResultPage({
      dataUrl: fullUrl,
      classGroupCardName: this.classGroupCardName,
      dataExpiryDate: this.dataExpiryDate,
      isCardlabel: false,
    });
  }

  getParamPromotionListAsQuery() {
    const params = [];

    for (const [key, values] of Object.entries(this.PARAM_PROMOTION_LIST)) {
      if (!values || values.length === 0) {
        continue;
      }

      if (key === this.typesTilter.cardTypes) {
        params.push(`${key}=${values.join(',')}`);
      } else {
        const encodedValues = values.map((v) => encodeURIComponent(v)).join(',');
        params.push(`${encodeURIComponent(key)}=${encodedValues}`);
      }
    }

    return params.join('&');
  }
}
