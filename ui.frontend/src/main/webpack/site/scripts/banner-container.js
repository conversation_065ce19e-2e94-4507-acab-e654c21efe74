import { BaseComponent } from './base';
import 'slick-carousel';

export class BannerContainer extends BaseComponent {
  
  constructor() {
    super();
    this.init();
  }

  init() {
    $(this).find('.cardslider-carousel-slicklist').not('.slick-initialized').slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      swipe: true,
      swipeToSlide: true,
      autoplay: true,
      accessibility: true,
      arrows: false,
      dots: true,
      infinite: false,
    });
  }
}
