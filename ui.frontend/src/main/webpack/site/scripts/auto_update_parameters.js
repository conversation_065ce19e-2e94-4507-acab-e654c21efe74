$(document).ready(function () {
  const allowParams = $('tcb-header').data('url-allow-params');

  if (allowParams) {
    const arrAllowParams = allowParams
      .split(',')
      .map((item) => item.trim())
      .filter(Boolean);

    if (arrAllowParams.length) {
      const currentParams = Object.fromEntries(
        new URLSearchParams(window.location.search).entries(),
      );

      const allowedParams = Object.entries(currentParams)
        .filter(([key]) => arrAllowParams.includes(key))
        .map(([key, value]) => `${key}=${value}`);

      if (allowedParams.length) {
        $('a').each((_, el) => {
          const $el = $(el);
          const href = $el.attr('href');

          try {
            const url = new URL(href, window.location.origin);
            const baseUrl = `${url.origin}${url.pathname}`;
            const linkParams = Object.fromEntries(url.searchParams.entries());

            const retainedParams = Object.entries(linkParams)
              .filter(([key]) => !currentParams.hasOwnProperty(key))
              .map(([key, value]) => `${key}=${value}`);

            const newQuery = [...retainedParams, ...allowedParams].join('&');
            $el.attr('href', `${baseUrl}?${newQuery}`);
          } catch (e) {
            console.warn(`Invalid URL in <a>: ${href}`);
          }
        });
      }
    }
  }
});
