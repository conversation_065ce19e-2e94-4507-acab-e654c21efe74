import moment from "moment";
import "jquery-ui/ui/widgets/datepicker.js";

$(document).ready(function () {
  const calculatorElement = $(".bao-loc-certificate-of-deposit-calculator");
  const roiInputWrapperElement = $(".roi-amount-input__wrapper");
  const ratioTableElement = $(
    ".display-none.bao-loc-certificate-of-deposit-calculator__container--ratio-table",
  );
  const roiInputElement = $("input.roi-amount-input__wrapper--input-body");
  const podDateElement = $(
    ".calculator--input.pod-date .calendar_real_estate.loan-realestate__input",
  );
  const sodDateElement = $(
    ".calculator--input.sod-date .calendar_real_estate.loan-realestate__input",
  );
  const interestRateElement = $(
    ".calculator--input .interest-rate .interest-rate--value",
  );
  const investTimeElement = $(
    ".calculator--input .invest-time .invest-time--value",
  );
  const expectedProfitElement = $(
    ".content__profit .content__profit--amount .amount--value",
  );
  const expectedToReceiveElement = $(
    ".content__receive .content__receive--amount .amount--value",
  );

  if (
    !(
      calculatorElement?.length > 0 &&
      roiInputWrapperElement?.length > 0 &&
      ratioTableElement?.length > 0 &&
      roiInputElement?.length > 0 &&
      podDateElement?.length > 0 &&
      sodDateElement?.length > 0 &&
      interestRateElement?.length > 0 &&
      investTimeElement?.length > 0 &&
      expectedProfitElement?.length > 0 &&
      expectedToReceiveElement?.length > 0
    )
  ) {
    return;
  }

  const maxAmount = 500_000_000_000;

  setTimeout(() => {
    renderInvestTime();
  });

  roiInputWrapperElement.keypress(function (event) {
    const charCode = event.which ? event.which : event.keyCode;
    if (String.fromCharCode(charCode).match(/\D/g)) {
      return false;
    }
  });

  roiInputElement.keydown(function (event) {
    const digitRegExp = /^\d$/;
    const amount = toInt($(this).val());
    if (digitRegExp.test(event.key)) {
      const newAmount = toInt(amount.toString() + event.key);
      const selectionStart = $(this)[0].selectionStart;
      const selectionEnd = $(this)[0].selectionEnd;
      if (newAmount > maxAmount && selectionStart === selectionEnd) {
        event.preventDefault();
      }
    }
  });

  roiInputElement.keyup(function () {
    if ($(this).val() === "") {
      expectedProfitElement?.text(0);
      expectedToReceiveElement?.text(0);
      return;
    }
    const amount = toInt($(this).val());
    $(this).val(numberWithDot(amount));
    renderInvestTime();
  });

  roiInputElement.on('input', function (event) {
    const amount = $(this).val().replaceAll('.', '');
    const digitRegExp = /^\d+$/;
    const inputValue = event?.originalEvent?.data ?? '';
    const validAmount = digitRegExp.test(amount) ? amount : amount.replace(inputValue, '');
    $(this).val(numberWithDot(validAmount));
  });

  roiInputElement.bind("paste", function (event) {
    const pastedData =
      event?.originalEvent?.clipboardData?.getData("text") ?? 0;
    const amount = toInt(pastedData);
    if (isNaN(amount)) {
      event.preventDefault();
    }
    if (amount > maxAmount) {
      event.preventDefault();
    }
  });

  podDateElement.click(function () {
    const calendarPopup = $(this).next(".calendar-popup");
    const calendarInputHeight = $(this).parent().outerHeight();
    if (calendarPopup?.length === 0) {
      return;
    }

    calendarPopup.css("top", `${calendarInputHeight}px`);

    // close sod calendar if there is open in pod calendar click action
    const sodCalendarPopup = sodDateElement.next(".calendar-popup");
    if (sodCalendarPopup?.hasClass("active")) {
      sodCalendarPopup?.removeClass("active");
    }

    // handle calendar popup show/hide
    if (calendarPopup?.hasClass("active")) {
      calendarPopup?.removeClass("active");
    } else {
      calendarPopup.datepicker("option", "showOtherMonths", false);
      calendarPopup?.addClass("active");
    }

    // onchange calendar event listener
    $(this)
      ?.find("input[name=podDate]")
      .on("updateCal", function () {
        const podDate = $(this).val();
        const sodDate = sodDateElement?.find("input[name=sodDate]")?.val();
        const formattedPODDate = moment(podDate, "DD/MM/YYYY").format(
          "YYYY-MM-DD",
        );
        const formattedSODDate = moment(sodDate, "DD/MM/YYYY").format(
          "YYYY-MM-DD",
        );
        const sodMaxDate = moment(podDate, "DD/MM/YYYY").add(90, "days");
        const currentSODMaxDate = moment(sodDate, "DD/MM/YYYY");

        if (moment(formattedPODDate).isAfter(formattedSODDate)) {
          sodDateElement?.find("input[name=sodDate]")?.val(podDate);
        } else if (currentSODMaxDate > sodMaxDate) {
          sodDateElement
            ?.find("input[name=sodDate]")
            ?.val(sodMaxDate.format("DD/MM/YYYY"));
        }

        renderInvestTime();
      });
  });

  sodDateElement.click(function () {
    const calendarPopup = $(this).next(".calendar-popup");
    const calendarInputHeight = $(this).parent().outerHeight();
    if (calendarPopup?.length === 0) {
      return;
    }

    calendarPopup.css("top", `${calendarInputHeight}px`);

    // close pod calendar if there is open in sod calendar click action
    const podCalendarPopup = podDateElement.next(".calendar-popup");
    if (podCalendarPopup?.hasClass("active")) {
      podCalendarPopup?.removeClass("active");
    }

    // handle calendar popup show/hide
    if (calendarPopup?.hasClass("active")) {
      calendarPopup?.removeClass("active");
    } else {
      setSODDate();
      calendarPopup.datepicker("option", "showOtherMonths", false);
      calendarPopup?.addClass("active");
    }

    // onchange calendar event listener
    $(this)
      ?.find("input[name=sodDate]")
      .on("updateCal", function () {
        renderInvestTime();
      });
  });

  function setSODDate() {
    const selectedPODDate = podDateElement?.find("input[name=podDate]")?.val();
    const sodMinDate = selectedPODDate;
    const sodMaxDate = moment(selectedPODDate, "DD/MM/YYYY")
      .add(90, "days")
      .format("DD/MM/YYYY");
    const calendarPopup = sodDateElement?.next(".calendar-popup");
    calendarPopup.datepicker("option", "minDate", sodMinDate);
    calendarPopup.datepicker("option", "maxDate", sodMaxDate);
  }

  function renderInvestTime() {
    const podDate = podDateElement?.find("input[name=podDate]")?.val();
    const sodDate = sodDateElement?.find("input[name=sodDate]")?.val();
    const formattedPODDate = moment(podDate, "DD/MM/YYYY").format("YYYY-MM-DD");
    const formattedSODDate = moment(sodDate, "DD/MM/YYYY").format("YYYY-MM-DD");
    const investDay = moment
      .duration(moment(formattedSODDate).diff(moment(formattedPODDate)))
      .asDays();
    investTimeElement?.text(investDay);
    renderInterestRate();
  }

  function renderInterestRate() {
    let interestRate;
    const investTime = Number(investTimeElement?.text());
    ratioTableElement?.each(function () {
      const min = Number($(this).attr("data-min-holding-day"));
      const max = Number($(this).attr("data-max-holding-day"));
      const rate = Number($(this).attr("data-interest-rate"));
      if (min === 90 && investTime >= min || (max >= investTime && investTime >= min)) {
        interestRate = rate;
        return false;
      }
    });
    interestRateElement?.text(isNaN(interestRate) ? 0 : interestRate);
    renderExpectedProfit();
  }

  function renderExpectedProfit() {
    const investTime = Number(investTimeElement?.text());
    const interestRate = Number(interestRateElement?.text());
    const amount = toInt(roiInputElement?.val());
    const expectedProfit = Math.round(
        amount * (investTime / 365) * (interestRate / 100)
    );
    expectedProfitElement?.text(numberWithDot(expectedProfit));
    renderExpectedToReceive(expectedProfit);
  }

  function renderExpectedToReceive(expectedProfit) {
    const amount = toInt(roiInputElement?.val());
    const expectedToReceive = amount + expectedProfit;
    expectedToReceiveElement?.text(numberWithDot(expectedToReceive));
  }

  function numberWithDot(x) {
    return x
      .toString()
      .replaceAll(".", "")
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
  }

  function toInt(x) {
    if (!x) {
      return 0;
    }
    return parseInt(x.replaceAll(".", ""));
  }
});
