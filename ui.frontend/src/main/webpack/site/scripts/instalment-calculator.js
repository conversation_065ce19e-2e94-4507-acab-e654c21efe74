$(document).ready(function () {
  const calculatorElement = $(".instalment-calculator");
  const tabsElement = $(".tcb-tabs.instalment-calculator-group .tcb-tabs_tab-list .tcb-tabs_scrollable .tcb-tabs_item");
  const termElement = $(
    ".calculator--input .dropdown__wrapper .dropdown__list ul",
  );
  const iraInputWrapperElement = $(".installment-registration-amount__wrapper");
  const iraInputInvalidMessageElement = $(
    ".installment-registration-amount__invalid-message",
  );
  const iraInputElement = $(
    "input.installment-registration-amount__wrapper--input-body",
  );
  const termSelectionElement = $(".calculator--input .offer-filter__dropdown");
  const conversionFeeElement = $(
    ".content__conversion-fee .content__conversion-fee--amount .amount--value",
  );
  const monthlyPaymentAmountElement = $(
    ".content__monthly-payment-amount .content__monthly-payment-amount--amount .amount--value",
  );
  const groupTableElement = $(
    ".display-none.instalment-calculator__container--ratio-table",
  );

  const calculatorNoteElement = calculatorElement.find(".instalment-calculator__note");

  if (
    !(
      calculatorElement?.length > 0 &&
      tabsElement?.length > 0 &&
      termElement?.length > 0 &&
      termSelectionElement?.length > 0 &&
      iraInputWrapperElement?.length > 0 &&
      iraInputInvalidMessageElement?.length > 0 &&
      iraInputElement?.length > 0 &&
      conversionFeeElement?.length > 0 &&
      monthlyPaymentAmountElement?.length > 0 &&
      groupTableElement?.length > 0
    )
  ) {
    return;
  }

  const minAmount = 1_000_000;
  const maxAmount = 500_000_000_000;
  const invalidMessage = "Số tiền đăng ký nhỏ nhất là 1,000,000 VNĐ";
  const amountUnit = iraInputWrapperElement?.attr("data-amount-unit");
  let term = 0;
  let feeRatio = 0;
  let minFee = 0;
  let activeIndex = 0;

  getRatioTerm();

  getNoteElement();

  iraInputWrapperElement.keypress(function (event) {
    const charCode = event.which ? event.which : event.keyCode;
    if (String.fromCharCode(charCode).match(/\D/g)) {
      return false;
    }
  });

  iraInputElement.keydown(function (event) {
    const digitRegExp = /^\d$/;
    const amount = toInt($(this).val());
    if (digitRegExp.test(event.key)) {
      const newAmount = toInt(amount.toString() + event.key);
      const selectionStart = $(this)[0].selectionStart;
      const selectionEnd = $(this)[0].selectionEnd;
      if (newAmount > maxAmount && selectionStart === selectionEnd) {
        event.preventDefault();
      }
    }
  });

  iraInputElement.keyup(function () {
    if ($(this).val() === "") {
      return;
    }
    let amount = toInt($(this).val());
    if (amount < minAmount) {
      renderInvalidMessage(true);
    } else {
      renderInvalidMessage(false);
      renderConversionFee();
    }
    $(this).val(numberWithCommas(amount));
  });

  iraInputElement.on('input', function (event) {
    const amount = $(this).val().replaceAll(',', '');
    const digitRegExp = /^\d+$/;
    const inputValue = event?.originalEvent?.data ?? '';
    const validAmount = digitRegExp.test(amount) ? amount : amount.replace(inputValue, '');
    $(this).val(numberWithCommas(validAmount));
  });

  iraInputElement.bind("paste", function (event) {
    const pastedData =
      event?.originalEvent?.clipboardData?.getData("text") ?? 0;
    const amount = toInt(pastedData);
    if (isNaN(amount)) {
      event.preventDefault();
    }
    if (amount > maxAmount) {
      event.preventDefault();
    }
  });

  termSelectionElement.change(function () {
    term =
      Number($(this)?.find($("input.dropdown-selected-value"))?.val()) || 0;
    groupTableElement?.each(function (index) {
      if (activeIndex === Number(index)) {
        const activeTabsElement = $(this)?.find(".ratio-fee-table");
        activeTabsElement?.each(function () {
          const dataTerm = Number($(this).attr("data-term"));
          if (term === dataTerm) {
            feeRatio = Number($(this).attr("data-fee-ratio")) || 0;
            minFee = Number($(this).attr("data-min-fee")) || 0;
            return false;
          }
        });
        return false;
      }
    });

    const amount = toInt(iraInputElement.val());
    if (amount < minAmount) {
      renderInvalidMessage(true);
    } else {
      renderInvalidMessage(false);
      renderConversionFee();
    }
  });

  termSelectionElement.click(function () {
    const imgElement = $(this)?.find("img");
    if (imgElement?.length === 0) {
      return;
    }
    if (imgElement?.hasClass("collapsed")) {
      imgElement?.removeClass("collapsed");
      imgElement?.addClass("expanded");
    } else {
      imgElement?.removeClass("expanded");
      imgElement?.addClass("collapsed");
    }
  });

  tabsElement.click(function () {
    activeIndex = Number($(this)?.attr("data-tabindex")) || 0;
    getRatioTerm();
    getNoteElement();
  });

  function getNoteElement() {
    const activeItem = calculatorElement.find(".tcb-tabs_item--active");
    const activeItemContent = activeItem.text();
    calculatorNoteElement.hide();
    calculatorNoteElement.each((i, e) => {
      if($(e).data("tab") === activeItemContent) {
        $(e).show();
      }
    });
  }

  function getRatioTerm() {
    clearSelection();
    const termUnit = termElement?.attr("data-term-unit-label");
    let termList = [];
    groupTableElement?.each(function (index) {
      if (activeIndex === Number(index)) {
        const activeTabsElement = $(this)?.find(".ratio-fee-table");
        activeTabsElement?.each(function () {
          const dataTerm = $(this).attr("data-term");
          termList.push(dataTerm);
        });
        return false;
      }
    });

    termElement.empty();
    termList.forEach((item) => {
      const element = `<li class="dropdown__item" value="${item}">${item} ${termUnit}</li>`;
      termElement.append(element);
    });
  }

  function renderInvalidMessage(isShow) {
    iraInputInvalidMessageElement?.empty();
    iraInputInvalidMessageElement?.text(invalidMessage);
    iraInputInvalidMessageElement?.css("display", `${isShow ? "" : "none"}`);
    conversionFeeElement?.text("");
    monthlyPaymentAmountElement?.text("");
  }

  function renderConversionFee() {
    const amount = toInt(iraInputElement.val());
    const fee = Math.round(amount * term * (feeRatio / 100));
    const conversionFee = fee < minFee ? minFee : fee;
    conversionFeeElement?.text(
      `${numberWithCommas(conversionFee)} ${amountUnit}`,
    );
    renderMonthlyPaymentAmount(conversionFee, amount);
  }

  function renderMonthlyPaymentAmount(conversionFee, amount) {
    if (term === 0) {
      monthlyPaymentAmountElement?.text(`0 ${amountUnit}`);
    } else {
      const monthlyPaymentAmount = Math.round((amount + conversionFee) / term);
      monthlyPaymentAmountElement?.text(
        `${numberWithCommas(monthlyPaymentAmount)} ${amountUnit}`,
      );
    }
  }

  function clearSelection() {
    iraInputElement?.val("");
    iraInputElement?.text("");
    termSelectionElement?.find($("span.display__text"))?.text("Chọn kỳ hạn");
    term = 0;
    feeRatio = 0;
    minFee = 0;
    conversionFeeElement?.text("");
    monthlyPaymentAmountElement?.text("");
    iraInputInvalidMessageElement?.text("");
  }

  function numberWithCommas(x) {
    return numberWithDot(x).replaceAll(".", ",");
  }

  function numberWithDot(x) {
    return x
      .toString()
      .replaceAll(".", "")
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
  }

  function toInt(x) {
    if (!x) {
      return 0;
    }
    return parseInt(x.replaceAll(",", ""));
  }
});
