import { BaseComponent } from './base';
import { TRANSLATIONS } from './translations';

export class TcbAutoEarning extends BaseComponent {
  get initialAmountWrapper() {
    return this.querySelector('#initialAmount');
  }

  get monthlyIncomeWrapper() {
    return this.querySelector('#monthlyIncome');
  }

  get monthlySpendingWrapper() {
    return this.querySelector('#monthlySpending');
  }

  get spendingEstimateWrapper() {
    return this.querySelector('#spendingEstimates');
  }

  get dailySpendingWrapper() {
    return this.querySelector('#dailySpending');
  }

  get dailyIncomeWrapper() {
    return this.querySelector('#dailyIncome');
  }

  get demandInterestRate() {
    return this.querySelector('.auto-earning-component').getAttribute('data-demand-interest-rate');
  }

  get autoEarningRatioList() {
    return this.querySelectorAll('.auto-earning-ratio');
  }

  get autoProfit() {
    return this.querySelector('span.auto-profit-value');
  }

  get demandProfit() {
    return this.querySelector('span.demand-profit-value');
  }

  get errorMessage() {
    return this.querySelector('.auto-earning-component').getAttribute('data-error-message');
  }

  get dailyIncomeRadioInput() {
    return this.querySelectorAll('input[name=auto-earning-daily-income]');
  }

  lang = document.documentElement.lang.toLowerCase();
  dailyIncomeYesLabel = TRANSLATIONS[this.lang].AUTOEARNING.DAILYINCOMEYES;
  dailyIncomeNoLabel = TRANSLATIONS[this.lang].AUTOEARNING.DAILYINCOMENO;
  dailyDropdownLabel = TRANSLATIONS[this.lang].AUTOEARNING.DAILYDROPDOWNLABEL;
  weeklyDropdownLabel = TRANSLATIONS[this.lang].AUTOEARNING.WEEKLYDROPDOWNLABEL;
  placeHolderInput = TRANSLATIONS[this.lang].AUTOEARNING.PLACEHODLER;

  maxAmount = 300_000_000;
  dailyInComeMaxAmount = 20_000_000;
  weeklyLabel = "weekly";

  initialAmountInput = this.initialAmountWrapper.querySelector("input");
  monthlyIncomeInput = this.monthlyIncomeWrapper.querySelector("input");
  monthlySpendingInput = this.monthlySpendingWrapper.querySelector("input");
  spendingEstimateDropdown =
    this.spendingEstimateWrapper.querySelector(".dropdown__wrapper");
  spendingEstimateInput = this.spendingEstimateWrapper.querySelector("input");
  dailySpendingInput = this.dailySpendingWrapper.querySelector("input");
  dailyIncomeInput = this.dailyIncomeWrapper.querySelector("input");

  calButton = this.querySelector('.auto-earning-calculator .cta-button--dark .cta-button');
  calWrapper = this.querySelector('details');
  tryAgainButton = this.querySelector('.try-again-button a.cta-button');
  errorMessageContainer = this.querySelector('.error-message');
  resultWrapper = this.querySelector('.auto-earning-result');

  calculateResult = this.querySelector('.result-container');

  allInput = this.querySelectorAll('.auto-earning__input');
  persolnalInputs = this.querySelectorAll('.personal-form__input');
  merchantInputs = this.querySelectorAll('.merchant-form__input');

  autoEarningData;

  HIDE_CLASS_NAME = 'hide';
  DISABLED_CLASS_NAME = 'disabled';
  EXPANDED_CLASS_NAME = 'expanded';

  constructor() {
    super();
    this.onInit();
  }

  onInit() {
    this.setDailyIncomeLabel();
    this.setPlaceHolderInput();
    this.renderSpendingDropdownList();
    const isDailyIncome = this.isDailyIncome();
    this.hideShowElements(isDailyIncome);
    this.onRadioIncomeChange();
    this.onClickDropdown();
    this.handleClickOutsideDropdown();
    this.validateInput();
    this.calculateProfit();
    this.calculateAgain();
  }

  isDailyIncome() {
    const inputElements = document.getElementsByName('auto-earning-daily-income');

    for (let element of inputElements) {
      if (element.checked) {
        return element.value;
      }
    }
  }

  setDailyIncomeLabel() {
    let dailyIncomeLabels = this.getElementsByTagName('label');
    dailyIncomeLabels[0].textContent = this.dailyIncomeYesLabel;
    dailyIncomeLabels[1].textContent = this.dailyIncomeNoLabel;
  }

  setPlaceHolderInput() {
    this.allInput.forEach((input) => {
      input.setAttribute('placeholder', this.placeHolderInput);
    });
  }

  renderSpendingDropdownList() {
    const dropdownWrapper = this.spendingEstimateWrapper.querySelector('.dropdown__wrapper');

    const dropdownlabel = dropdownWrapper.querySelector('.display__text');
    dropdownlabel.textContent = TRANSLATIONS[this.lang].AUTOEARNING.WEEKLYDROPDOWNLABEL;

    const dropdownList = dropdownWrapper.querySelector('ul');
    const dropdownDailyItem = document.createElement('li');
    dropdownDailyItem.classList.add('dropdown__item');
    dropdownDailyItem.setAttribute('value', 'daily');
    dropdownDailyItem.textContent = this.dailyDropdownLabel;

    const dropdownWeeklyItem = document.createElement('li');
    dropdownWeeklyItem.classList.add('dropdown__item', 'selected');
    dropdownWeeklyItem.setAttribute('value', 'weekly');
    dropdownWeeklyItem.textContent = this.weeklyDropdownLabel;

    dropdownList.appendChild(dropdownDailyItem);
    dropdownList.appendChild(dropdownWeeklyItem);
  }

  hideShowElements(isShow) {
    if (isShow === 'No') {
      this.monthlyIncomeWrapper.classList.remove(this.HIDE_CLASS_NAME);
      this.monthlySpendingWrapper.classList.remove(this.HIDE_CLASS_NAME);
      this.dailySpendingWrapper.classList.remove(this.HIDE_CLASS_NAME);
      this.spendingEstimateWrapper.classList.add(this.HIDE_CLASS_NAME);
      this.dailyIncomeWrapper.classList.add(this.HIDE_CLASS_NAME);
    } else {
      this.monthlyIncomeWrapper.classList.add(this.HIDE_CLASS_NAME);
      this.monthlySpendingWrapper.classList.add(this.HIDE_CLASS_NAME);
      this.dailySpendingWrapper.classList.add(this.HIDE_CLASS_NAME);
      this.spendingEstimateWrapper.classList.remove(this.HIDE_CLASS_NAME);
      this.dailyIncomeWrapper.classList.remove(this.HIDE_CLASS_NAME);
    }
  }

  onRadioIncomeChange() {
    const inputElements = document.getElementsByName('auto-earning-daily-income');

    for (const ele of inputElements) {
      ele.addEventListener('change', (event) => {
        this.hideShowElements(event.target.value);
      });
    }
  }

  onClickDropdown() {
    const dropdownWrapper = this.spendingEstimateWrapper.querySelector('.dropdown__wrapper');

    const dropdownItem = dropdownWrapper.querySelectorAll('.dropdown__content li.dropdown__item');

    const dropdownButton = dropdownWrapper.querySelector('.dropdown__button');
    const dropdownLabel = dropdownWrapper.querySelector('.display__text');
    dropdownButton.addEventListener('click', () => {
      dropdownWrapper.classList.toggle(this.EXPANDED_CLASS_NAME);
    });

    dropdownItem.forEach((item) => {
      item.addEventListener('click', (event) => {
        const selectedDropdown = dropdownWrapper.querySelectorAll('.dropdown__content li.dropdown__item.selected');
        selectedDropdown.forEach((item) => {
          item.classList.remove('selected');
        });
        item.classList.add('selected');
        dropdownLabel.textContent = event.target.innerText;
        dropdownWrapper.classList.toggle(this.EXPANDED_CLASS_NAME);
      });
    });
  }

  validateInput() {
    this.allInput.forEach((input) => {
      const maxAmount = input.classList.contains('daily-income__input') ? this.dailyInComeMaxAmount : this.maxAmount;
      input.addEventListener('keypress', (e) => {
        this.inputKeypress(e);
      });

      input.addEventListener('keydown', (e) => {
        this.inputKeydown(e, maxAmount);
      });

      input.addEventListener('keyup', (e) => {
        this.inputKeyUp(e);
      });
      input.addEventListener('input', (e) => {
        this.inputEvent(e);
      });

      input.addEventListener('paste', (e) => {
        this.inputPasteEvent(e, maxAmount);
      });
    });
  }

  calculateProfit() {
    this.calButton.addEventListener('click', () => {
      const hasDailyIncome = this.querySelector('input[name="auto-earning-daily-income"]:checked').value;
      if (hasDailyIncome === 'Yes') {
        // calculate form 2 : merchant
        const isValidInput = this.validateInputField(this.merchantInputs);
        if (isValidInput) {
          //call API to get data
          this.getMerchantAutoEarningData();
        } else {
          //show error message
          this.errorMessageContainer.classList.remove(this.HIDE_CLASS_NAME);
          this.errorMessageContainer.textContent = TRANSLATIONS[this.lang].AUTOEARNING.MISSINGINFO;
        }
      } else {
        //calculate form 1: personal
        const isValidInput = this.validateInputField(this.persolnalInputs);
        if (isValidInput) {
          //call API to get data
          this.getPersonalAutoEarningData();
        } else {
          //show error message
          this.errorMessageContainer.classList.remove(this.HIDE_CLASS_NAME);
          this.errorMessageContainer.textContent = TRANSLATIONS[this.lang].AUTOEARNING.MISSINGINFO;
        }
      }
    });
  }

  calculateAgain() {
    this.tryAgainButton.addEventListener('click', () => {
      this.calWrapper.setAttribute('open', true);
      this.calculateResult.classList.add(this.HIDE_CLASS_NAME);
      this.tryAgainButton.classList.add(this.HIDE_CLASS_NAME);
      this.resultWrapper.classList.add(this.HIDE_CLASS_NAME);
      this.errorMessageContainer.classList.add(this.HIDE_CLASS_NAME);
      this.calButton.closest('.cta-button--dark').classList.remove(this.HIDE_CLASS_NAME);

      this.enableAllInput();
    });
  }

  showresult() {
    this.calculateResult.classList.remove(this.HIDE_CLASS_NAME);
    this.tryAgainButton.classList.remove(this.HIDE_CLASS_NAME);
    this.resultWrapper.classList.remove(this.HIDE_CLASS_NAME);
    this.calWrapper.removeAttribute('open');
    this.autoProfit.textContent = this.numberWithCommas(this.autoEarningData.data.autoEarningProfit);
    this.demandProfit.textContent = this.numberWithCommas(this.autoEarningData.data.demandProfit);
  }

  enableAllInput() {
    this.initialAmountInput.removeAttribute(this.DISABLED_CLASS_NAME);
    this.monthlyIncomeInput.removeAttribute(this.DISABLED_CLASS_NAME);
    this.monthlySpendingInput.removeAttribute(this.DISABLED_CLASS_NAME);
    this.spendingEstimateInput.removeAttribute(this.DISABLED_CLASS_NAME);
    this.dailySpendingInput.removeAttribute(this.DISABLED_CLASS_NAME);
    this.dailyIncomeInput.removeAttribute(this.DISABLED_CLASS_NAME);
    this.spendingEstimateDropdown.classList.remove(this.DISABLED_CLASS_NAME);
    this.dailyIncomeRadioInput.forEach((input) => {
      input.disabled = false;
    });
  }

  disableAllInput() {
    this.initialAmountInput.setAttribute(this.DISABLED_CLASS_NAME, true);
    this.monthlyIncomeInput.setAttribute(this.DISABLED_CLASS_NAME, true);
    this.monthlySpendingInput.setAttribute(this.DISABLED_CLASS_NAME, true);
    this.spendingEstimateInput.setAttribute(this.DISABLED_CLASS_NAME, true);
    this.dailySpendingInput.setAttribute(this.DISABLED_CLASS_NAME, true);
    this.dailyIncomeInput.setAttribute(this.DISABLED_CLASS_NAME, true);
    this.spendingEstimateDropdown.classList.add(this.DISABLED_CLASS_NAME);
    this.dailyIncomeRadioInput.forEach((input) => {
      input.disabled = true;
    });
  }

  validateInputField(inputs) {
    let value = [];
    for (const input of inputs) {
      value.push(input.value);
    }

    return value.includes('') ? false : true;
  }

  getMerchantAutoEarningData() {
    const isPersonal = false;
    const selectedDropdown = this.querySelector('.dropdown__wrapper')
      .querySelector('li.selected')
      .getAttribute('value');

    const hasWeeklyExpense = selectedDropdown === this.weeklyLabel ? true : false;
    const initialBalance = this.initialAmountInput.value.replaceAll(',', '');

    const dailyIncome = this.dailyIncomeInput.value.replaceAll(',', '');

    const weeklyExpense = this.spendingEstimateInput.value.replaceAll(',', '');

    let autoEarningRatioArr = encodeURIComponent(JSON.stringify(this.getAutoEarningRatio()).replaceAll('"', ''));

    let url = `/bin/autoEarningCalculator?isPersonal=${isPersonal}`;
    url += `&hasWeeklyExpense=${hasWeeklyExpense}`;
    url += `&initialBalance=${initialBalance}`;
    url += `&dailyIncome=${dailyIncome}`;
    url += `&${
      hasWeeklyExpense ? "weeklyExpense" : "dailyExpense"
    }=${weeklyExpense}`;
    url += `&demandInterestRate=${this.demandInterestRate}`;
    url += `&autoEarningInterestRate=${autoEarningRatioArr}`;
    this.fetchData(url)
      .then((response) => {
        this.autoEarningData = response;
        this.showresult();
        this.disableAllInput();
        //hide button calculate
        this.calButton.closest('.cta-button--dark').classList.add(this.HIDE_CLASS_NAME);
        //hide error message
        this.errorMessageContainer.classList.add(this.HIDE_CLASS_NAME);
      })
      .catch((error) => {
        this.handleError(error);
      });
  }

  getPersonalAutoEarningData() {
    const isPersonal = true;
    const initialBalance = this.initialAmountInput.value.replaceAll(',', '');
    const monthlyIncome = this.monthlyIncomeInput.value.replaceAll(',', '');
    const monthlyExpense = this.monthlySpendingInput.value.replaceAll(',', '');
    const dailyExpense = this.dailySpendingInput.value.replaceAll(',', '');
    let autoEarningRatioArr = encodeURIComponent(JSON.stringify(this.getAutoEarningRatio()).replaceAll('"', ''));
    let url = `/bin/autoEarningCalculator?isPersonal=${isPersonal}`;
    url += `&initialBalance=${initialBalance}`;
    url += `&monthlyIncome=${monthlyIncome}`;
    url += `&monthlyExpense=${monthlyExpense}`;
    url += `&dailyExpense=${dailyExpense}`;
    url += `&demandInterestRate=${this.demandInterestRate}`;
    url += `&autoEarningInterestRate=${autoEarningRatioArr}`;
    this.fetchData(url)
      .then((response) => {
        this.autoEarningData = response;
        this.showresult();
        this.disableAllInput();
        //hide button calculate
        this.calButton.closest('.cta-button--dark').classList.add(this.HIDE_CLASS_NAME);
        //hide error message
        this.errorMessageContainer.classList.add(this.HIDE_CLASS_NAME);
      })
      .catch((error) => {
        this.handleError(error);
      });
  }

  fetchData(url) {
    return new Promise(function (resolve, reject) {
      $.ajax({
        url: url,
        method: 'get',
        contentType: 'application/json',
        success: function (response) {
          resolve(response);
        },
        error: function (xhr, status, error) {
          reject(xhr);
        },
      });
    });
  }

  handleError(error) {
    const firstDigitErrorCode = error.status.toString()[0];
    this.autoEarningData = null;
    // 4xx error status code
    if (firstDigitErrorCode === '4') {
      this.errorMessageContainer.textContent = this.errorMessage;
    }
    // 5xx error status code
    if (firstDigitErrorCode === '5') {
      this.errorMessageContainer.textContent = TRANSLATIONS[this.lang].AUTOEARNING.ERRORMESSAGE5XX;
    }
    this.errorMessageContainer.classList.remove(this.HIDE_CLASS_NAME);
  }

  getAutoEarningRatio() {
    const autoEarningRatioArr = [];
    this.autoEarningRatioList.forEach((ratio) => {
      let obj = {
        minHoldingDay: ratio.querySelector('.minHoldingDay').innerHTML.trim(),
        maxHoldingDay: ratio.querySelector('.maxHoldingDay').innerHTML.trim(),
        interestRate: ratio.querySelector('.autoEarningInterestRate').innerHTML.trim(),
      };
      autoEarningRatioArr.push(obj);
    });
    return autoEarningRatioArr;
  }

  handleClickOutsideDropdown() {
    const dropdownWrapper = this.spendingEstimateWrapper.querySelector('.dropdown__wrapper');
    document.body.addEventListener('click', (event) => {
      // HIDE DROPDOWN WHEN CLICK OUTSIDE
      const target = event.target;
      const classList = target.classList;
      const parent = target.parentElement;
      const parentClassList = parent.classList;
      if (classList.contains('dropdown__button') || parentClassList.contains('dropdown__button')) {
        return;
      }

      if (dropdownWrapper.classList.contains(this.EXPANDED_CLASS_NAME)) {
        dropdownWrapper.classList.remove(this.EXPANDED_CLASS_NAME);
      }
    });
  }

  inputKeypress(event) {
    const charCode = event.which ? event.which : event.keyCode;
    if (String.fromCharCode(charCode).match(/\D/g)) {
      event.preventDefault();
      return false;
    }
  }

  inputKeydown(event, maxAmount) {
    const digitRegExp = /^\d$/;
    const amount = this.toInt(event.target.value);
    if (digitRegExp.test(event.key)) {
      const newAmount = this.toInt(amount.toString() + event.key);
      const selectionStart = event.target.selectionStart;
      const selectionEnd = event.target.selectionEnd;
      if (newAmount > maxAmount && selectionStart === selectionEnd) {
        event.preventDefault();
      }
    }
  }

  inputKeyUp(event) {
    const inputValue = event.target.value;
    if (inputValue === '') {
      event.preventDefault();
      return;
    }

    const amount = this.toInt(inputValue);
    event.target.value = this.numberWithCommas(amount);
  }

  inputEvent(event) {
    const amount = event.target.value.replaceAll(',', '').replace(/\D/g, "");
    const digitRegExp = /^\d+$/;
    const inputValue = event?.data ?? '';
    const validAmount = digitRegExp.test(amount) ? amount : amount.replace(inputValue, '');

    event.target.value = this.numberWithCommas(validAmount);
  }

  inputPasteEvent(event, maxAmount) {
    const pastedData = event?.clipboardData?.getData('text').replace(/\D/g, "") ?? 0;
    const currentData = this.toInt(event.target.value) ?? 0;
    const pastedAmount = this.toInt(pastedData);
    const totalAmount = this.toInt(currentData + pastedData);
    if (isNaN(pastedAmount) || totalAmount > maxAmount || pastedAmount < 0) {
      event.preventDefault();
    }
  }

  toInt(x) {
    if (!x) {
      return 0;
    }
    return parseInt(x.replaceAll(',', ''));
  }

  numberWithDot(x) {
    return x
      .toString()
      .replaceAll('.', '')
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
  }

  numberWithCommas(x) {
    return this.numberWithDot(x).replaceAll('.', ',');
  }
}
