import { BaseComponent } from './base';
import { getSearchHistoryKeywords, removeSearchHistoryKeyword } from './tcb-google-search';

export class TcbSearchPrimary extends BaseComponent {
  constructor() {
    super();

    this.initSlick();
    this.renderKeywords();
    this.initKeywordItems();
  }

  initSlick() {
    $('.card-list')
      .not('.slick-initialized')
      .slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        dots: false,
        infinite: false,
        variableWidth: true,
        responsive: [
          {
            breakpoint: 1024,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
            },
          },
        ],
      });
  }

  tilteReason = $(this).find('.search-primary-container .search-box_title');
  keywordList = $(this).find('.search-primary-container .key-word-list');

  renderKeywords() {
    const historyKeyWord = getSearchHistoryKeywords();
    if (!historyKeyWord.length) {
      this.tilteReason.hide();
    }
    historyKeyWord.forEach((key) => {
      this.keywordList.append(this.renderKeyword(key));
    });
  }

  renderKeyword(item) {
    const resultSlug = this.keywordList.attr('data-primary-search-result-slug');
    return `
      <div class="key-word-container">
        <a class="key-word-item" href="${resultSlug}?q=${item}">${item}</a>
        <div class="close-icon">
          <img
            src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-grey.svg" />
        </div>
      </div>
    `;
  }

  initKeywordItems() {
    const closeReasonIcon = this.keywordList.find('.close-icon');
    closeReasonIcon.each(function () {
      $(this).click(function () {
        const keyword = $(this).closest('.key-word-container').find('.key-word-item').text();

        removeSearchHistoryKeyword(keyword);

        $(this).parent().remove();
        if (this.keywordList.children().length === 0) {
          this.tilteReason.hide();
        }
      });
    });

    const keyWordButton = $('.search-primary-container').find('.key-word-item');
    keyWordButton.each((_, keywordItem) => {
      $(keywordItem).click(() => {
        const resultSlug = this.keyWordList.attr('data-primary-search-result-slug');
        const url = `${resultSlug}?q=${$(keywordItem).text()}`;
        window.location.href = url;
      });
    });
  }
}
