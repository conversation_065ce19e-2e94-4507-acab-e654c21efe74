.help-panel {
  .help-panel__title {
    margin-bottom: 8px;
    padding: 12px;
    font-size: 1.75rem;
    font-weight: 300;
    line-height: 1.25;
  }
  .help-panel__tab {
    padding-top: 12px;
    display: flex;
  }
  .help-panel__tab-control {
    flex-grow: 0;
    max-width: 33.333333%;
    flex-basis: 33.333333%;
    padding: 1.5rem 0.75rem 0.75rem 0;
  }
  .help-panel__tab-content {
    flex-grow: 0;
    max-width: 66.666667%;
    flex-basis: 66.666667%;
    margin: 0.75rem 0 0.75rem 0.75rem;
    background-color: #fff;
    border-radius: 8px;
    height: fit-content;
  }
  .tab-active {
    background-color: #333 !important;
    color: #f5f5f5 !important;
    padding-left: 1rem !important;
    img {
      filter: brightness(0) invert(1);
    }
  }
  .help-panel__tab-control-btn {
    padding: 8px 16px;
    padding-left: 0;
    background-color: transparent;
    border-radius: 8px;
    color: #333;
    border: none;
    margin-top: 32px;
    font-size: 16px;
    line-height: 24px;
    width: 100%;
    text-transform: none;
    opacity: 1;
    min-height: 40px;
    letter-spacing: 0;
    max-width: none;
    text-align: left;
    cursor: pointer;
    &:first-child {
      margin-top: 0;
    }
    img {
      display: none;
    }
    &.tab-active {
      h3 {
        font-weight: 600;
      }
    }
  }
  .help-panel__article-content {
    padding: 48px 56px;
    border-radius: 8px;
  }
  .help-panel__tab-content-title {
    color: #000;
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 24px;
  }
  .help-panel__tab-content-body {
    color: #333;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    max-width: 100%;
    ul {
      padding-left: 20px;
      list-style-type: circle;
      li {
        margin: 8px 0;
        list-style-type: circle;
      }
    }

    .help-panel__video {
      position: relative;
      padding-top: calc(100% - 101px);

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        height: 100%;
        width: 100%;
      }
    }
  }
  .help-panel__modal-header {
    display: none;
  }
  @media (max-width: 991px) {
    .help-panel__tab {
      flex-direction: column;
    }
  
    .help-panel__tab-control {
      max-width: 100%;
      flex-basis: 100%;
      padding: 1.5rem 0 0.75rem 0;
    }
  
    .help-panel__text-link {
      flex-direction: column;
      align-items: flex-start;
    }

    .help-panel__button {
      margin-left: 0;
      padding: 16px 32px;
      margin-top: 24px;
      width: 328px;
      justify-content: space-between;
      &:before {
        display: none;
      }
    }
  
    .tab-active {
      color: #333 !important;
    }
  
    .help-panel__tab-control-btn {
      padding: 1rem 1.5rem;
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
      background-color: white;
      img {
        margin-left: 12px;
        display: block;
        width: 16px;
        height: 16px;
        align-self: center;
      }
    }
  
    .help-panel__modal-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 1rem;
      border-bottom: 1px solid #e9ecef;
      border-top-left-radius: 0.3rem;
      border-top-right-radius: 0.3rem;
      span {
        padding: 1rem;
        margin: -1rem -1rem -1rem auto;
        background-color: transparent;
        border: 0;
        -webkit-appearance: none;
        cursor: pointer;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        opacity: 0.5;
      }
    }
  
    .help-panel__article-content {
      opacity: 0;
      background-color: white;
      padding: 1rem;
      position: fixed;
      flex-basis: 100%;
      z-index: 1;
      width: calc(100% - 16px );
      overflow: auto;
      max-width: 500px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -75%);
      background-clip: padding-box;
      border: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 0.3rem;
      transition: all 0.3s ease-in-out;
      &.open {
        opacity: 1;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease-in-out;
        max-height: 100%;
        overflow: hidden;
        overflow-y: scroll;
      }
    }
  
    .help-panel__modal-close {
      width: 24px;
      height: 24px;
      position: absolute;
      right: 16px;
      top: 16px;
      &:before {
        content: "";
        height: 18px;
        border-left: 2px solid #a2a2a2;
        position: absolute;
        transform: rotate(-45deg);
        left: 8px;
      }
      &:after {
        content: "";
        height: 18px;
        border-left: 2px solid #a2a2a2;
        position: absolute;
        transform: rotate(45deg);
        left: 8px;
      }
    }
  }
}

