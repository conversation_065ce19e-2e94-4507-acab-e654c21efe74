.glossary-tooltip {
    position: relative;
    display: inline-block;
    color: #4D4DE6;
}

.glossary-tooltip-text-content {
    background-color: #fff;
    color: #000000;
    position: absolute;
    z-index: 9999;
    padding: 1rem;
    font-size: 16px;
    line-height: 1.5rem;
    font-weight: 400;
    width: max-content;
    max-width: 16.875rem;
    max-height: 11.5rem;
    border-radius: 0.5rem;

    &>*:not(.view-more) {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
        text-align: left !important;
    }

    .view-more {
        display: flex;
        align-items: center;
        gap: 12px;
        text-decoration: none !important;
        color: var(--primary-black) !important;
        font-weight: bold;
        margin-top: 8px;

        img {
            width: 1rem;
            height: 1rem;
        }
    }
}

.show-glossary-tooltip {
    visibility: visible;
    opacity: 1;
}

.hide-glossary-tooltip {
    visibility: hidden;
    opacity: 0;
}