.management-team-panel {
  background-color: transparent; //Change by Adobe
  .tab-horizontal-report__header-title::before {
    display: none;
  }
  .tab-horizontal-report__tab-content {
    padding: 0;
    margin-top: 0;//Change by Adobe
  }
  .tab-horizontal-report__tab-content-card {
    width: 100%;
    background: transparent;//Change by Adobe
    border-radius: 0;
    box-shadow: unset;
  }
  .tab-horizontal-bio__list-card {
    width: calc(100% + 24px);
    margin: -12px;
    display: flex;
    flex-wrap: wrap;
  }
  .tab-horizontal-bio__card {
    flex-grow: 0;
    max-width: 33.333333%;
    flex-basis: 33.333333%;
    padding: 12px;
  }
  .tab-horizontal-bio__card-container {
    display: flex;
    flex-flow: column;
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 8px;
    background-clip: padding-box;
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%),
      0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%),
      0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    transition: all 0.3s ease;
    transform: scale(1);
    img {
      display: block;
      border-radius: 8px 8px 0 0;
      // changed by Adobe
      max-width: 100%; 
    }
  }
  .tab-horizontal-bio__card-header {
    display: flex;
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
  }
  .tab-horizontal-bio__card-content {
    display: flex;
    flex-flow: column;
    flex: 1 1 auto;
    border-radius: 0 0 8px 8px;
    background-clip: padding-box;
    padding: 24px;
    background-color: #fff;
  }
  .tab-horizontal-bio__card-content-role {
    color: #ed1b24;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    letter-spacing: 2px;
    text-transform: uppercase;
  }
  .tab-horizontal-bio__card-content-title {
    line-height: 1.5;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
  }
  .tab-horizontal-bio__card-content-description {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    line-break: normal;//Change by Adobe
    max-height: 48px;
    margin: 8px 0;
    color: rgb(51, 51, 51);

    p{
      line-break: auto;
    }
  }
  .tab-horizontal-bio__card-action {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    width: fit-content;
  }
  .tab-horizontal-bio__card-action-btn {
    justify-content: flex-start;
    color: #000;
    position: relative;
    display: inline-flex;
    outline: none;
    border: none;
    cursor: pointer;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease-in;
    width: inherit;
    grid-gap: 12px;
    gap: 12px;
    background-color: transparent;//Change by Adobe
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.5;
    padding: 1px 6px 1px 0;
  }
  .tab-horizontal-report__tab-control {
    margin-top: 12px;
  }

  .tab-horizontal-bio__button-see-more {
    max-width: 310px;
    width: 100%;
    margin: 32px auto 0;

    button {
      position: relative;
      display: inline-flex;
      padding: 16px 24px;
      border-radius: 8px;
      outline: none;
      justify-content: space-between;
      background-color: transparent;
      border: 1px solid #000;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all 0.3s ease-in;
      align-items: center;
      grid-gap: 12px;
      gap: 12px;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      &:hover {
        background-color: black;
        color: white;

        > svg > path {
          fill: white;
        }
      }
      > span {
        line-height: 1.5;
        font-weight: 600;
        font-size: 1rem;
      }
    }
  }

  .display-none {
    display: none;
  }

  .display-block {
    display: block;
  }

  .tab-horizontal-bio__card-container:hover {
    box-shadow: 0 33px 172px rgba(0, 0, 0, 0.1),
      0 8.7866px 9.6175px rgba(0, 0, 0, 0.1),
      0 7.37098px 15.4287px rgba(0, 0, 0, 0.02),
      0 4.13211px 8.664px rgba(0, 0, 0, 0.02),
      0 2.19453px 7.0367px rgba(0, 0, 0, 0.02),
      0 0.913195px 3.00873px rgba(0, 0, 0, 0.01);
  }
  .tab-horizontal-bio__card-action-btn:hover {
    text-decoration: underline;
  }
  .bio-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    overflow-x: hidden;
    overflow-y: auto;
    display: none;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    z-index: -1;
    &.showed {
      z-index: 99;
      opacity: 1;
      display: flex;

      .bio-popup-content {
        opacity: 1;
        transform: translate(0);
      }
    }
  }
  .bio-popup-content {
    max-width: 1090px;
    width: 90%; //changed by Adobe
    position: relative;
    margin: auto;   
    max-height: 592px; //changed by Adobe
    height: 75%;  //changed by Adobe
    transition: all 0.3s ease-in-out;
    transform: translateY(-25%);
    .background {
      position: absolute;
      width: 100%;
      height: 100%;
      img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center center;
        border-radius: 8px;
      }
    }
    .personal-info {
      display: flex;
      position: relative;
      padding: 0 calc(100% / 22.5); //changed by Adobe
      gap: 8px;
      height: 100%;
      .info {
        padding: 64px 32px 64px 0;
        max-height: 530px;
        overflow-y: auto;
        margin: auto auto 50px;
      }
      .info,
      .avatar {
        flex: 0 0 50%;
        height: 100%;
      }
      .position {
        color: #ed1b24;
        margin-bottom: 4px;
        font-weight: 600;
        font-size: 0.875rem;
        letter-spacing: 2px;
        text-transform: uppercase;
      }
      .name {
        margin: 0;
      }
      .detail-info {
        margin-top: 16px;
        color: #616161;
        //changed by Adobe
        line-break:normal;//Change by Adobe
      }
    }
    .avatar img {
      height: calc(100% - 32px);
      width: calc(100% - 24px - calc(100% / 22.5)); //changed by Adobe
      max-width: 420px;
      max-height: 560px;
      position: relative;
      top: -50px;
      object-fit: cover;
      object-position: center top;
      border-radius: 8px;
    }

    .close-btn {
      position: absolute;
      width: 24px;
      height: 24px;
      right: calc(100% / 22.5); //changed by Adobe
      top: 64px;
      cursor: pointer;
      img {
        width: 100%;
        object-fit: cover;
        object-position: center center;
      }
    }
  }
  .management-team-panel__load-more {
    max-width: 328px;
    width: 100%;
    margin: auto;
    padding-top: 24px;

    &.edit-mode {
      margin-bottom: 30px;
    }

    button {
      border: 1px solid #000;
      position: relative;
      display: inline-flex;
      padding: 16px 24px;
      border-radius: 8px;
      outline: none;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all 0.3s ease-in;
      justify-content: space-between;
      align-items: center;
      grid-gap: 12px;
      gap: 12px;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      line-height: 1.5;
      font-weight: 600;
    }

    .load-more__button-icon {
      display: flex;
      align-items: center;
      margin-left: 0;
      transition: all 0.3s ease-in-out;
    }
  }
}
/* End of Horizontal Tab Bio */
@media (max-width: 1024px) {
  .management-team-panel {
    .bio-popup-content {
      max-height: 451px;
    }
  }
}

@media (max-width: 992px) {
  .management-team-panel {
    .bio-popup-content {
      max-height: 592px;
    }
    .tab-horizontal-report__tab-control {
      overflow-x: scroll;
      display: flex;
      scroll-behavior: smooth;
    }
    .tab-horizontal-report__tab-control::-webkit-scrollbar {
      display: none;
    }
    .tab-horizontal-bio__card {
      max-width: 50%;
      flex-basis: 50%;
    }
    .tab-horizontal-bio__button-scroll-right {
      display: block;
    }
  }
}

@media (min-width: 768px) {
  .management-team-panel {
    .management-team-panel__load-more {
      button {    
        &:hover {
          background-color: #000;
          color: #fff;
          path {
            fill:#fff
          }

          .load-more__button-icon {
            filter: brightness(0) invert(1);
          }
        }
      }
  
      .load-more__button-icon {
        display: flex;
        align-items: center;
        margin-left: 0;
        transition: all 0.3s ease-in-out;
      }
    }
  }
}

@media (max-width: 767px) {
  .management-team-panel {
    .tab-horizontal-bio__card {
      max-width: 100%;
      flex-basis: 100%;
    }
    .bio-popup-content {
      width: 95%; //changed by Adobe
      max-height: 95%; //changed by Adobe
      //changed by Adobe
      height: 95%;
      .personal-info {
        flex-direction: column-reverse;
        overflow: auto;
        gap: 0;
        justify-content: flex-end;
      }
      .personal-info .avatar {
        max-height: 325px;
        margin-top: 64px;
        margin-bottom: 4px;
        height: auto;
      }
      .avatar img {
        max-width: 243px;
        max-height: 325px;
        height: 100%;
        top: 0;
      }
      .personal-info .info {
        padding: 32px 0 64px;
        margin-bottom: 0;
        margin-top: 0;
      }
      .close-btn {
        top: 15px;
      }
    }

    .bio-popup-content .personal-info .avatar,
    .bio-popup-content .personal-info .info {
      flex: unset;
    }
  }
}
