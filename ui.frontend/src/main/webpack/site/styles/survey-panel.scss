.survey-panel {
  &__container {
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
  }

  &__description {
    padding: 12px;
  }

  .description-text {
    max-width: 472px;
    text-align: center;
    margin: 0 auto 8px;
    color: #616161;
  }

  &__QA {
    display: flex;
    box-sizing: border-box;
    width: 100%;
    flex-wrap: wrap;
  }

  &__question-wrapper,
  &__process-wrapper {
    background-color: #fff;
    padding: 48px;
    border-radius: 8px;
    width: 100%;
  }

  &__question-wrapper {
    max-width: 58.333333%;
  }

  &__question-label {
    color: #ed1b24;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    letter-spacing: 2px;
    text-transform: uppercase;
  }

  &__question-title {
    margin: 16px 0 24px;
  }

  &__question-radio-wrapper {
    padding: 32px 40px;
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    border-radius: 8px;
    margin-top: 24px;
  }

  &__question-input-radio {
    color: #616161;
    display: inline-flex;
    align-items: center;
  }

  &__question-input-radio input {
    margin: 0 16px 0 0;
    padding: 0;
    width: 24px;
    height: 24px;
    min-width: 24px;
    border-radius: 50%;
    border: 1px solid #e3e4e6;
    background-color: #fff;
    -webkit-appearance: none;
    display: grid;
  }

  &__question-input-radio input:checked {
    border-color: #ed1b24;
  }

  &__question-input-radio input:checked:before {
    background-color: #ed1b24;
  }
  
  &__question-input-radio input:before {
    content: "";
    width: 0.9375rem;
    height: 0.9375rem;
    transform: translate(25%, 25%);
    border-radius: 50%;
  }

  &__button-wrapper {
    margin: 32px auto auto;
    text-align: center;
    max-width: 310px;

    .btn--disabled {
      color: #a2a2a2;
      background-color: #d9d9d9;
      border: 1px solid #d9d9d9;
      img {
        filter: brightness(1) invert(1) grayscale(1);
      }
    }
  
    .btn__icon--disabled {
      fill: #a2a2a2;
    }
  }

  &__question-button {
    background-color: #000;
    color: #fff;
    padding: 16px 24px;
    border-radius: 8px;
    min-width: max-content;
    width: 100%;
    display: inline-flex;
    cursor: pointer;
    border: none;
    align-items: center;
    justify-content: space-between;
    transition: all .3s linear;
  }

  &__question-button:hover {
    background-color: #616161;
    color: #fff;
    img {
      filter: brightness(0) invert(1) grayscale(1);
    }
  }

  &__process-wrapper {
    max-width: 41.666667%;
    text-align: center;
  }

  &__progress-bar {
    background-color: #eaeaea;
    height: 16px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
  }

  &__process-label {
    margin: 40px 0 0;
  }

  .button-text {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .button-icon {
    display: flex;    
  }

  .linear-progress-bar {
    background-color: #ed1b24;
    border-radius: 8px;
    transition: transform 0.4s linear;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    bottom: 0;
    transform-origin: left;
    transform: translateX(-100%);
  }

  &__question-submit-button {
    justify-content: space-between;
  }

  &__process-image {
    width: 100%;
    height: 100%;
    max-width: 272px;
    max-height: 424px;
    border: 2px dashed #adadad;
    box-shadow: 0 33px 181px hsl(0deg 0% 6% / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    border-radius: 8px;
    transform: rotate(17.03deg) scale(0.8);
    writing-mode: vertical-rl;
    display: inline-block;
    overflow: visible;
  }

  &__card-image {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: 300px;
    max-height: 424px;
    margin: auto;
    writing-mode: vertical-rl;
  }

  &__card-image>img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    object-position: center center;
  }

  @media (min-width: 768px) {
    &__QA {
      flex-wrap: nowrap;
    }
  }

  @media (max-width: 767px) {
    &__question-wrapper {
      padding: 48px 16px 8px;
      max-width: 100%;
    }

    &__process-wrapper {
      padding: 8px 16px 48px;
      max-width: 100%;
    }

    &__process-image {
      margin: auto auto 16px;
    }
  }

  @media (min-width: 768px) {
    &__process-image {
      margin: 48px auto 64px;
    }
  }
}
