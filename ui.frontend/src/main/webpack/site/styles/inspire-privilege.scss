@keyframes fadeInBottom {
    from {
        opacity: 0;
        transform: translateY(100%);
    }

    to {
        opacity: 1;
    }
}

.inspire-privilege {

    .swiper2 {
        &:not([dir="rtl"]) {
            .swiper-slide {
                margin-right: 8px;
            }
        }
        &[dir="rtl"] {
            .swiper-slide {
                margin-left: 8px;
            }
        }
    }

    .title-cmp__title {
        font-weight: 600;
        line-height: 24px;
        font-size: 32px;
        letter-spacing: 0;
    }

    .inspire-privilege__container {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .inspire-privilege__container {
        .inspire-privilege__container2 {
            width: 100%;
            margin-bottom: 24px;
            padding-bottom: 20px;

            &.bottom-separator {
                border-bottom: 1px solid rgb(223, 127, 69);
            }
        }
    }

    .inspire-privilege__tab-menu {
        display: flex;
        flex-direction: column;
        width: 30%;
    }

    .tab-menu-item {
        transition-duration: 300ms;
        padding: 24px;
        border-radius: 20px;
        cursor: pointer;
        box-shadow: 0 0 #0000, 0 0 #0000, 0 0 16px rgba(0, 0, 0, 0.05);

        &:not(:first-child) {
            margin-top: 24px;
        }

        h3 {
            line-height: 1.25;
            text-transform: uppercase;
            font-weight: 800;
            font-size: 40px;
            max-width: 80%;
            margin: 0;
        }
    }

    .tab-menu-item.active {
        box-shadow: none;

        h3 {
            color: var(--primary-background);
        }
    }

    .inspire-privilege__tab-list {
        width: 65%;
        display: none;
    }

    .inspire-privilege__tab-list.active {
        animation-duration: 1s;
        animation-fill-mode: both;
        animation-name: fadeInBottom;
        display: block;
    }

    .tab-list__content-item {
        transform: translate(0px, 0px);
        margin-bottom: 24px;
    }

    .content-item__title {
        line-height: 24px;
        font-weight: 600;
        margin-bottom: 32px;
    }

    .content-item__container {
        display: flex;
        align-items: flex-start;
    }

    .content-item__multi-picture {
        border-radius: 8px;
        width: 100%;
        position: relative;
        margin: 0;

        picture {
            display: flex;
            justify-content: center;
            width: 100%;
        }

        .swiper-slide {
            width: auto;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }

        .content-item__image {
            height: 100px;
            transition: all 0.2s ease-in-out;
        }
    }

    .content-item__picture {
        border-radius: 8px;
        position: relative;
        margin: 0;

        picture {
            display: flex;
            justify-content: center;
            width: 100%;
        }

        .swiper-slide {
            width: 300px;
        }

        .swiper-button-next,
        .swiper-button-prev {
            position: absolute;
            top: calc(50% - 15px - 3px);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease-in-out;
            width: 30px;
            height: 30px;
            background-color: var(--primary-background);
            border: none;
            cursor: pointer;
            outline: 0;

            &::after {
                height: 24px;
                width: 24px;
            }
        }

        .swiper-button-next {
            right: 10px !important;
            left: auto;

            &::after {
                content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-right-red.png);
            }
        }

        .swiper-button-prev {
            left: 10px !important;
            right: auto;

            &::after {
                content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-left-red.png);
            }
        }
    }

    .content-item__image {
        object-fit: contain;
        border-radius: 8px;
        width: 100%;
        height: auto;
    }

    .content-item__text {
        margin-left: 10%;
        width: 40%;
    }

    .list-item {
        display: flex;
        margin-bottom: 24px;
        margin-left: 8px;
        margin-right: 8px;
        align-items: center;
    }

    .list-item__image {
        align-items: center;
        margin-right: 20px;

        img {
            object-fit: contain;
            width: 44px;
            height: 44px;
        }
    }

    .info-link {
        display: flex;
        align-items: center;
        line-height: 24px;
        font-weight: 500;

        &:hover {
            .info-link__icon {
                --tw-translate-x: 5px;
                --tw-translate-y: 0;
                --tw-rotate: 0;
                --tw-skew-x: 0;
                --tw-skew-y: 0;
                --tw-scale-x: 1;
                --tw-scale-y: 1;
                transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            }
        }
    }

    .info-link__icon {
        width: 16px;
        height: 16px;
        margin-left: 12px;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    }

    .inspire-privilege__tab-menu-mobile {
        width: 100%;
    }

    .tab-menu-mobile-item {
        width: 100%;
        position: relative;
        min-height: 200px;
        z-index: 0;

        &:nth-child(odd) {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;

            .tab-menu-mobile-content {
                &::before {
                    content: "";
                    border-radius: 20px;
                    width: 100%;
                    height: 130px;
                    z-index: 10;
                    position: absolute;
                    pointer-events: none;
                    background-color: inherit;
                }

                &::after {
                    content: "";
                    border-radius: 20px;
                    width: 100%;
                    height: 130px;
                    z-index: 10;
                    top: 35%;
                    position: absolute;
                    pointer-events: none;
                    background-color: inherit;
                    transform: translate(0, 0) rotate(0) skewX(0) skewY(5deg) scaleX(1) scaleY(1);
                }

                flex-direction: row-reverse;

                h3 {
                    padding-right: 48px;
                    min-height: 150px;
                    text-align: right;
                    padding-top: 24px;
                }
            }

            .dropdown-title-icon {
                padding-left: 24px;
            }

            .tab-list-mobile {
                padding-top: 210px;
                height: auto;
            }

            .tab-menu-collapse-icon {
                min-height: 100px;
                position: relative;

                &::before {
                    content: "";
                    background-color: var(--primary-background);
                    border-bottom-left-radius: 20px;
                    border-bottom-right-radius: 20px;
                    width: 100%;
                    height: 45px;
                    position: absolute;
                    pointer-events: none;
                }

                &::after {
                    content: "";
                    background-color: var(--primary-background);
                    border-bottom-left-radius: 20px;
                    border-bottom-right-radius: 20px;
                    width: 100%;
                    height: 80px;
                    position: absolute;
                    pointer-events: none;
                }
            }
        }

        &:nth-child(even) {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .tab-menu-mobile-content {
                &::before {
                    content: "";
                    border-radius: 20px;
                    width: 100%;
                    height: 130px;
                    z-index: 10;
                    position: absolute;
                    pointer-events: none;
                    background-color: inherit;
                    transform: translate(0, 0) rotate(0) skewX(0) skewY(5deg) scaleX(1) scaleY(1);
                    bottom: 35%;
                }

                &::after {
                    content: "";
                    border-radius: 20px;
                    width: 100%;
                    height: 130px;
                    z-index: 10;
                    position: absolute;
                    pointer-events: none;
                    background-color: inherit;
                }

                top: 50px;

                h3 {
                    padding-left: 48px;
                    text-align: left;
                    padding-bottom: 48px;
                }
            }

            .dropdown-title-icon {
                padding-right: 24px;
            }

            .tab-list-mobile {
                border-radius: 20px;
                padding-top: 130px;
                padding-bottom: 70px;
                top: 70px;
            }

            .tab-menu-collapse-icon {
                min-height: 80px;
                margin-bottom: 15px;
                position: relative;
            }
        }
    }

    .tab-menu-mobile-content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        z-index: 12;
        cursor: pointer;
        border-radius: 20px;
        position: absolute;
        color: var(--primary-background);

        h3 {
            margin: 0;
            font-size: 32px;
            line-height: 1.25;
            text-transform: uppercase;
            font-weight: 800;
            width: 65%;
            z-index: 11;
        }
    }

    .dropdown-title-icon {
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        z-index: 11;

        p {
            font-size: unset;
            font-weight: 400;
            line-height: 1.5;
            white-space: nowrap;
            margin-bottom: 4px;
        }
    }

    .tab-list-mobile {
        max-width: 100%;
        position: relative;
        display: none;
        background-color: var(--primary-background);
        z-index: 11;
    }

    .tab-menu-mobile-item.active {
        .tab-list-mobile {
            display: block;
        }

        &:nth-child(odd) {
            .tab-menu-collapse-icon {
                &::after {
                    transform: translate(0, 0) rotate(0) skewX(0) skewY(5deg) scaleX(1) scaleY(1);
                }
            }
        }

        .dropdown-title-icon {
            svg {
                transform: translate(0, 0) rotate(180deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
                cursor: pointer;
            }
        }
    }

    .tab-menu-collapse-icon {
        color: var(--primary-background);
        border-radius: 20px;
    }

    .collapse-icon-content {
        bottom: 24px;
        right: 24px;
        position: absolute;
        z-index: 11;
        display: flex;
        flex-direction: column;
        align-items: center;

        svg {
            transform: translate(0, 0) rotate(180deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
            cursor: pointer;
        }
    }

    .content-item__primary {
        width: 50%;

        .info-link {
            display: none;
        }
    }

    .content-item__text {
        .info-link {
            display: flex;
        }
    }

    .swiper1 {
        &.content-item__picture {
            .swiper-slide {
                --tw-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.32);
                --tw-ring-offset-shadow: 0 0 #0000;
                --tw-ring-shadow: 0 0 #0000;
                box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
                border-radius: 8px;
            }

            .swiper-wrapper {
                height: 176px;
                .slick-slide {
                    width: 300px;
                    margin-right: 15px;
                }
            }
        }
    }

    .swiper-wrapper {
        padding-top: 5px;
        padding-bottom: 5px;
        transition-duration: 500ms !important;
        -webkit-transition-duration: 500ms !important;
        transition-timing-function: ease-out;
    }

    .enable-shadow-true {
        --tw-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.32);
        --tw-ring-offset-shadow: 0 0 #0000;
        --tw-ring-shadow: 0 0 #0000;
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        border-radius: 8px;
    }

    .list-item__content {
        display: flex;
        vertical-align: middle;
    }
}

@media screen and (max-width: 976px) {
    .inspire-privilege {
        .swiper1 {
            .swiper-button-prev,
            .swiper-button-next {
                display: none !important;
            }
        }
    }
}

@media screen and (max-width: 767px) {
    .inspire-privilege {
        .title-cmp__title {
            padding: 0;
            margin-bottom: 32px;
            font-size: 24px;
        }

        .content-item__primary {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 25px;
            width: 100%;

            .info-link {
                display: flex;
            }
        }

        .content-item__text {
            .info-link {
                display: none;
            }
        }

        .list-item {
            flex-direction: column;
            align-items: center;
        }

        .inspire-privilege__container {
            .inspire-privilege__container2 {
                padding-bottom: 0;
            }

            .content-item__container {
                flex-direction: column-reverse;
            }

            .content-item__multi-picture {
                width: 100%;
            }

            .content-item__picture {
                width: 100%;
            }

            .content-item__text {
                width: 100%;
                margin-left: 0;
                margin-bottom: 24px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                gap: 15px;
            }

            .info-link {
                margin-top: 20px;
            }
        }

        .list-item__image {
            margin-right: 0;
        }
    }

    .list-item__image {
        margin-bottom: 8px;
    }

    .list-item__content {
        p {
            line-height: 21px;
            text-align: center;
        }
    }

    .list-item {
        width: 33%;
    }

    .list-item-count-1 {
        .list-item {
            width: 100%;
        }
    }

    .list-item-count-2 {
        .list-item {
            width: 50%;
        }
    }

    .list-item-count-3 {
        width: 33%;
    }

    .list-item-count-4 {
        width: 25%;
    }

}