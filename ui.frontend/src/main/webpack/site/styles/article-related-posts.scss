.article-related-post {
  padding: 0;
  margin-top: 32px;
  margin-bottom: 32px;

  &_title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
    font-weight: 300;
    font-size: 28px;
    line-height: 35px;

    @media (max-width: 575px) {
      margin-bottom: 0;
    }
  }
  &_content {
    padding-top: 24px;
    min-height: 300px;
  }
  &_list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    width: calc(100% + 24px);
    margin: -12px;

    @media (max-width: 767px) {
      width: calc(100% + 16px);
      margin: -8px;
    }

    @media (max-width: 1024px) {
      flex-wrap: nowrap;
      overflow: auto;
    }

    .article-card {
      padding: 12px;
      flex-grow: 0;
      max-width: 33.333333%;
      flex-basis: 33.333333%;
  
      @media (max-width: 1024px) {
        max-width: 370px;
        flex: 0 0 370px;
      }
  
      @media (max-width: 767px) {
        max-width: 304px;
        flex: 0 0 304px;
        padding: 8px;
      }

      &_container {
        &:hover {
          box-shadow: 0 33px 181px rgba(0,0,0,.04),
                      0 13.7866px 75.6175px rgba(0,0,0,.029),
                      0 7.37098px 40.4287px rgba(0,0,0,.024),
                      0 4.13211px 22.664px rgba(0,0,0,.02),
                      0 2.19453px 12.0367px rgba(0,0,0,.016),
                      0 .913195px 5.00873px rgba(0,0,0,.011);
          @media (max-width: 1024px) {
            box-shadow: none;
          }
        }
      }
    }
  }
}

.article-related-post, .article-listing {
  .article-card {
    &_container {
      border-radius: 8px;
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-direction: column;
      transition: all .2s ease;

      &_image {
        height: 236px;
        position: relative;

        @media (max-width: 767px) {
          height: 171px;
        }

        img {
          position: absolute;
          inset: 0px;
          box-sizing: border-box;
          padding: 0px;
          border: none;
          margin: auto;
          display: block;
          width: 0px;
          height: 0px;
          min-width: 100%;
          max-width: 100%;
          min-height: 100%;
          max-height: 100%;
          object-fit: cover;
          object-position: center center;
        }
      }
    }

    &_content {
      padding: 24px 16px;
      background-color: #fff;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &_top {
        .article-label {
          margin-bottom: 8px;
          letter-spacing: 2px;
          font-size: 14px;

          @media (max-width: 991px) {
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
          }

          span {
            color: #616161;
            font-weight: 700;
            font-size: 14px;
            line-height: 21px;
            letter-spacing: 2px;
            text-transform: uppercase;
          }
        }
        .article-title {
          font-weight: 700;
          font-size: 16px;
          line-height: 24px;
          margin-bottom: 8px;
          margin-block-start: 0;

          @media (max-width: 991px) {
            margin-bottom: 16px;
          }
        }
        .article-content {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          margin-bottom: 8px;
          color: #616161;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
        }
      }
      .article-viewmore {
        position: relative;
        display: inline-flex;
        outline: none;
        border: none;
        cursor: pointer;
        align-items: center;
        text-decoration: none;
        transition: all .3s ease-in;
        justify-content: space-between;
        width: inherit;
        grid-gap: 12px;
        gap: 12px;
        background-color: inherit;
        padding: 0;

        line-height: 24px;
        font-weight: 600;
        font-size: 16px;

        &:hover {
          text-decoration: underline;
        }

        .button-icon {
          width: 16px;
          height: 16px;

          display: flex;
          align-items: center;
          margin-left: 0;
          transition: all .3s ease-in-out;
        }
      }
    }
  }
}
