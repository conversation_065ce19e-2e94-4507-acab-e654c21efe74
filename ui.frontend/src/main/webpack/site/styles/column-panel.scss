.column-panel-component {
  padding-top: 0.25rem;
  .column-panel-component__container {
    display: flex;
    flex-direction: column;
    .column-panels {
      display: flex;
      flex-direction: row;
      gap: 1.5rem;
      .item-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      .image {
        text-align: center;

        &.left {
          text-align: left;
        }

        img {
          width: 120px;
          height: 100px;
        }
        .image-title {
          text-align: left;
          margin: 24px 0;
          font-size: 1.5rem;
          line-height: 1.5;
          font-weight: 600;

          &.center {
              text-align: center;
              .tooltiptext {
                left: 50%;
                transform: translateX(-50%);
              }
          }
        }
      }
      .list-option {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .option {
          display: flex;
          flex-direction: row;
          background-color: white;
          padding: 24px;
          border-radius: 8px;
          justify-content: space-between;
          border: 1px solid #dedede;

          &:hover {
            transition: all 0.3s ease-in-out;
            box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
          }

          .content {
            padding: 0 12px;
            .content-title {
              font-weight: 600;
              font-size: 16px;
              line-height: 24px;
              //margin-bottom: 8px; // commented by adobe
            }
            .content-description {
              font-size: 16px;
              line-height: 24px;
              font-weight: 400;
              color: var(--gray-600);
              margin-top: 8px;
            }
          }

          .arrow {
            // align-self: center; //commented by adobe
          }
        }
      }
    }
    .title {
      font-weight: 300;
      font-size: 1.75rem;
      line-height: 1.25;
      padding-bottom: 12px;
    }
    .read-more {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      margin-top: 8px;
      .expand img {
        display: flex;
        justify-content: center;
        width: 16px;
      }
      &:hover {
        cursor: pointer;
        text-decoration: underline;
      }
      button {
        cursor: pointer;
        border: none;
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.5;
        margin-right: 10px;
        background: transparent;
      }
    }
    @media (min-width: 768px) {
      .column-panels .list-option .option:nth-child(n + 3) {
        display: none;
      }

      .column-panels .image .image-title {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        line-break: normal;
        position: relative;

        .tooltiptext {
          left: 0;
          top: 100%;
          visibility: hidden;
          background-color: #000;
          color: #fff;
          position: absolute;
          z-index: 400;
          border-radius: 8px;
          padding: 8px;
          font-size: 16px;

          h1,h2,h3,h4,h5,h6 {
            font-size: unset;
            line-height: unset;
            font-weight: unset;
          }
        }

        &:hover {
          overflow: unset;
          .tooltiptext {
            visibility: visible;
          }
        }
      }

      .column-panels .list-option .option .content .content-description {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        line-break: normal;
        position: relative;

        .tooltiptext {
          left: 0;
          top: 100%;
          visibility: hidden;
          background-color: #000;
          color: #fff;
          position: absolute;
          z-index: 400;
          border-radius: 8px;
          padding: 8px;
          font-size: 16px;

          h1,h2,h3,h4,h5,h6 {
            font-size: unset;
            line-height: unset;
            font-weight: unset;
          }
        }

        &:hover {
          overflow: unset;
          .tooltiptext {
            visibility: visible;
          }
        }
      }
    }

    @media (max-width: 767px) {
      .column-panels .image .image-title .tooltiptext {
        display: none;
      }
      .column-panels .list-option .option {
        min-height: unset;
      }
      .column-panels .list-option .option .content .content-description .tooltiptext {
        display: none;
      }
      .column-panels {
        flex-direction: column;
      }

      .read-more {
        display: none;
      }
    }
  }
}
@media (max-width: 1024px) {
  .column-panel-component {
    .column-panel-component__container {
      .column-panels {
        .item-panel {
          padding-bottom: 16px;
          &:last-child {
            padding-bottom: 0;
          }
        }
        .image {
          .image-title {
            margin: 16px 0;
          }
        }
        .list-option {
          .option {
            padding: 16px;
          }
        }
      }
    }
  }
}
@media (max-width: 767px) {
  .column-panel-component {
    .column-panel-component__container {
      .column-panels {
        .item-panel {
          padding-left: 0;
          padding-right: 0;
          &:last-child {
            padding-bottom: 0;
          }
        }
      }
    }
  }
}
