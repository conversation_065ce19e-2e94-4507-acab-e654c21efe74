.insurance-calculation {

    &.decrease-interest-loan, &.stable-interest-loan {

        .loan-realestate__panel {
            padding-top: 0;
            
            .panel-inputs {
                .input-items {
                    width: calc(100% + 8px);
    
                    .item__label {
                        padding: 4px;
                        .loan-realestate__des {
                            font-style: italic;
                            color: #616161;
                            max-width: 190px;
                            font-size: 14px;
                            line-height: 21px;
                        }

                        @media (max-width: 767px) {
                            padding-bottom: 0px;
                        }
                    }
    
                    .item__input-fields {
                        padding: 4px;
    
                        .tcb-input-range {
                            margin-top: 12px;
                            padding-bottom: 10px;
                            .tcb-input-range_bar-wrapper {
                                .tcb-input-range_bar {
                                    width: unset;
                                    .tcb-input-range_thumb {
                                        .tcb-input-range_inline-value {
                                            transform: scale(1) translateY(-10px);
                                            top: 44px;
                                            left: calc(-50% - 5px );
                                            z-index: 1;
                                            line-height: 1.2;
                                        }
                                    }
                                }
                            }

                            .tcb-input-range_labels {
                                margin-top: 3px;
                            }
                        }
                    }

                    .input-field__currency-field {
                        &.loan-realestate__input {

                            input:hover {
                                border: 1px solid black;
                            }

                            &.calendar_real_estate {
                                input:focus {
                                    border: 0;
                                }
                            }
                        }

                        input {
                            padding-right: 0;
                        }
                    }
                    
                    .date-time-wrapper__input-extra {
                        
                        p.loan-realestate__icon {
                            flex: 0 0 auto;
                            color: rgba(0, 0, 0, 0.54);
                            padding: 12px;
                            overflow: visible;
                            font-size: 1.5rem;
                            text-align: center;
                            transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
                            border-radius: 50%;
                            width: 48px;
                            height: 48px;
                        }

                        &:hover p.loan-realestate__icon {
                            background-color: rgba(0, 0, 0, 0.04);
                        }

                        &.calendar__input-field {
                            outline: 0.1em solid lightskyblue;
                        }
                    }
                    
                    .loan-realestate__input:hover input {
                        border: 1px solid black;
                    }
                }
            }
    
            .panel-info__content-button {
                
                a {
                    background-color: #fff;
                    color: #000;
                    padding: 16px 24px;
                }
                
                a.loan-calc {
                    justify-content: unset;
                    border: none;
    
                    &:hover {
                        background-color: #a2a2a2;
                        color: #616161;
                    }
                }
            }
    
            .panel-info {
                .panel-info__content {
                    .panel-info__content-text{
    
                        .info-content-text__icon {
                            span {
                                width: unset;
                                height: auto;
                                @media (max-width: 767px) {
                                    max-width: 100px;
                                }
                                img {
                                    position: relative;
                                    display: flex;
                                    max-width: 120px;
                                }
                            }
                        }
                    
                        .info-content-text__label {
                            margin-left: 0;
                            
                            display: flex;
                            flex-direction: column;

                            div {
                                margin: 10px 16px 8px 0;
                                &.info-title {
                                    color: #e3e4e6;
                                    margin-bottom: 8px;
                                    margin-top: 24px;
                                    font-weight: 300;
                                }
                            }
                            
                            &.info-estate {
                                flex-direction: column;
                                padding-top: 16px;
                            }
                        }

                        &.payment-detail {
                            
                            .info-content-text__label {
                                
                                @media (max-width: 991px) { 
                                    flex-direction: row;
                                }    
                            }
                        }
                        &.loan-info {
                            display: flex;
                            flex-direction: column;
                            .info-content-text__label {
                                margin-top: 24px;
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                                &:nth-child(2) {
                                    margin-top: 16px;
                                }
                                > div {
                                    margin: auto 16px auto 0;
                                }
                                
                                @media (max-width: 767px) {
                                    flex-direction: column;
                                    align-items: flex-start;
                                }
                            }
                        }
                    }
                }
            }
            
        }

        @media (max-width: 767px) { 
            .calendar-popup {
                bottom: unset;
            }
        }
    }

    a.loan-calc {
        border: none;
    
        &:hover {
            background-color: #a2a2a2;
            color: var(--gray-600);
        }
    }

    /* modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    outline: 0;
    overflow-x: hidden;
    overflow-y: auto;
    opacity: 0;
    z-index: -1;
    transition: all 0.3s ease-in-out;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    pointer-events: none;
  }
  
  .modal.active {
    opacity: 1;
    z-index: 15;
    pointer-events: auto;
  }
  
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 16;
    background: rgba(0, 0, 0, 0.6);
  }
  
  .modal-dialog {
    margin: auto;
    max-width: 80%;
    transition: all 0.3s ease-in-out;
    position: relative;
    z-index: 17;
    width: 100%;
    opacity: 1;
    transform: translate(0);
  }
  
  .modal-content {
    position: relative;
    background: #fff;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
  }
  
  .modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
  }
  
  span.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
    font-size: 1.25rem;
    font-weight: 300;
  }
  
  .material-symbols-outlined.close-modal {
    cursor: pointer;
    opacity: 0.5;
  }
  
  .material-symbols-outlined.close-modal:hover {
    color: #000;
    opacity: 0.7;
  }
  
  .modal-inner-content {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
  }
  
  .table-sum {
    max-height: 400px;
    border-radius: 8px;
    width: 100%;
    overflow-x: auto;
    overflow-y: auto;
  }
  
  .table-sum table {
    display: table;
    border-collapse: separate;
    border-spacing: 0;
    border-style: hidden;
    width: 100%;
    background: #f5f6f8;
  }
  
  .table-sum::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
  }
  
  .table-sum::-webkit-scrollbar-thumb {
    background: #a2a2a2;
    border-radius: 4px;
  }
  
  .table-sum::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  .table-sum thead {
    height: 72px;
    display: table-header-group;
    position: sticky;
    top: 0;
  }
  
  .cell-header {
    background-color: #898989;
    color: #fff;
    font-size: 14px;
    line-height: 21px;
    font-weight: 300;
    letter-spacing: 2px;
  }
  
  .table-cell {
    display: table-cell;
    padding: 16px;
    font-size: 0.875rem;
    text-align: center;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
    font-weight: 300;
    line-height: 1.5;
    border-bottom: 1px solid rgba(224, 224, 224, 1);
    letter-spacing: 0.01071em;
    vertical-align: inherit;
    word-wrap: normal;
  }
  
  .footer-tbl {
    width: 100%;
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
    background-color: #898989;
    height: 64px;
  }
  
  .footer-cell {
    color: #fff;
    font-size: 16px;
    border-bottom: unset;
    font-weight: bolder;
  }
  
  /* End modal*/
}

.loancalculator.button--link .insurance-calculation{
    .panel-info__content-button a {
        background-color: unset;
        color: var(--primary-white);
        padding: unset;

        &.loan-calc:hover {
            background-color: unset;
            color: var(--primary-white);
            text-decoration: underline;
        }
    }
}
