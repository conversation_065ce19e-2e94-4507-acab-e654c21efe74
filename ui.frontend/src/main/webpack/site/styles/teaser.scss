/** Teaser **/
.tcb-teaser {
  position: relative;
  padding-top: 48px;
  padding-bottom: 0;

  .tcb-button--link {
    border: none;
    margin-top: 1rem;
  }

  .right-container__link-icon {
    flex-shrink: 0;
  }
  .tcb-button {
    display: inline-flex;
    justify-content: flex-start;
    border: none;

    &.tcb-button--link {
      gap: 12px;
      width: fit-content;
      &:hover {
        cursor: pointer;
        .tcb-button--title {
          text-decoration: underline;
        }
      }
    }
    &.tcb-button--dark {
      text-align: center;
      align-items: center;
      // Change from Adobe
      justify-content: space-between;
      background-color: #000000;
      border-radius: 8px;
      box-shadow: 0 3px 6px rgb(0 0 0 / 16%), 0 3px 6px rgb(0 0 0 / 23%);
      // Change from Adobe
      padding: 12 16px;
      margin-top: 16px;
      color: #ffffff;
      width: max-content;
      gap: 16px;
      img {
        padding: 16px;
      }
      .section-font-color & {
        color: inherit;
      }
    }
    &.tcb-button--gradient {
      padding: 16px 24px;
      justify-content: space-between;
      background: linear-gradient(180deg, #8d8175 -24.11%, #35322b 305.36%);
      transition: all 0.3s ease-in;
      &:hover {
        background: #fff;
        color: #616161;
      }
      img {
        padding: 0;
      }
    }
    &.full-width {
      // Change from Adobe
      width: 100%;
    }
  }

  // Change from Adobe
  .tcb-button.tcb-button--dark:hover {
    color: #fff;
  }

  // Change from Adobe
  .tcb-button--arrow {
    color: #ed1c24;
  }
  // Change from Adobe
  .tcb-teaser_app-preview {
    display: flex;
    justify-content: center;
    &.left-align {
      justify-content: flex-start;
      @media screen and (max-width: 767px) {
        justify-content: center;
      }
    }
    &.right-align {
      justify-content: flex-end;
      @media screen and (max-width: 767px) {
        justify-content: center;
      }
    }
  }
}
// Change from Adobe : DefaultImage
.tcb-teaser
  .tcb-teaser_content
  .tcb-teaser_body
  .tcb-teaser_app-preview
  .app-preview-image {
  max-width: 300px;
  object-fit: contain;
  object-position: bottom;

  @media (max-width: 768px) {
    max-width: unset;
  }

  img {
    width: 100%;
    height: 100%;
    display: block;
  }
}
// Change from Adobe : SmallImage
.tcb-teaser--smallImage
  .tcb-teaser
  .tcb-teaser_content
  .tcb-teaser_body
  .tcb-teaser_app-preview
  .app-preview-image {
  width: 176px;
  max-width: 176px;
  object-position: top;
}
// Change from Adobe : MediumImage
.tcb-teaser--mediumImage
  .tcb-teaser
  .tcb-teaser_content
  .tcb-teaser_body
  .tcb-teaser_app-preview
  .app-preview-image {
  width: 380px;
  max-width: 380px;

  @media screen and (max-width: 767px) {
    max-width: 76%;
    margin: auto;
  }
}

.tcb-teaser--largeImage .tcb-teaser .tcb-teaser_content .tcb-teaser_body .tcb-teaser_app-preview .app-preview-image {
  width: 500px;
  max-width: 500px;

  @media screen and (max-width: 767px) {
    width: 100%;
  }
}
.tcb-teaser--desktopSmall .tcb-teaser .tcb-teaser_content .tcb-teaser_body .tcb-teaser_app-preview .app-preview-image {
  max-width: 244px;

  @media screen and (max-width: 767px) {
    max-width: 100%;
  }
}

.tcb-teaser--mobileSmall .tcb-teaser .tcb-teaser_content .tcb-teaser_body .tcb-teaser_app-preview .app-preview-image {
  @media screen and (max-width: 767px) {
    max-width: 76%;
  }
}
.tcb-teaser--smallRight .tcb-teaser .tcb-teaser_content .tcb-teaser_body .tcb-teaser_app-preview {
  justify-content: right;
  @media screen and (max-width: 767px) {
    justify-content: center;
  }
  .app-preview-image img {
    max-width: 176px;
    height: auto;
  }
}
.tcb-teaser--small286 .tcb-teaser .tcb-teaser_content .tcb-teaser_body .tcb-teaser_app-preview {
  .app-preview-image img {
    width: 100%;
    height: 286px;
  }
  @media screen and (max-width: 767px) {
    margin-bottom: 20px;
  }
}
// Change from Adobe
.tcb-teaser--dark .tcb-teaser .tcb-button--arrow {
  color: #fff;
}

.tcb-teaser--larger-height {
  .tcb-teaser_body {
    min-height: 328.83px;
  }
}

.tcb-teaser--column-padding {
  padding-top: 0;
  padding-bottom: 0;

  .tcb-teaser_column-left {
    padding: 48px 12px;
  }

  .tcb-teaser_column-right {
    padding: 48px 12px;
  }

  .tcb-teaser--nopadding {
    padding-top: 0;
    padding-bottom: 0;
  }
}

.tcb-teaser--dark {
  .tcb-teaser_title {
    color: var(--primary-background);
  }

  .tcb-teaser_description p {
    color: var(--primary-background);
  }

  color: var(--primary-background);

  .tcb-button--link {
    color: var(--primary-background);

    &:hover {
      border: none;
    }
  }
}

.tcb-teaser--reverse {
  .tcb-teaser_body {
    flex-direction: row-reverse;
  }
}

.tcb-teaser_column-left {
  flex: 0.4 40%;
}

.tcb-teaser_column-right {
  flex: 1 63%;
  display: flex;
  flex-direction: column;
}

.tcb-teaser--layout-1-1 {
  .tcb-teaser_column-left {
    flex: 1;
  }

  .tcb-teaser_column-right {
    flex: 1;
  }
}

.tcb-teaser_tag {
  color: var(--primary-background);
}

.tcb-teaser_background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  .tcb-teaser--mobile-large & {
    object-position: top;
  }
}

.tcb-teaser_body {
  display: flex;
  // Change from Adobe
  gap: 82px;

  .tcb-teaser_align-center {
    margin: auto 0;
  }
}

.tcb-teaser_content {
  position: relative;
  z-index: 1;
}

.tcb-teaser_description {
  &--small {
    margin-top: 8px;
  }
}

.tcb-teaser_title {
  font-weight: 300;
  font-size: 28px;
  color: var(--body);
  line-height: 35px;
  // Change from Adobe
  margin-top: 16px;

  .section-font-color & {
    color: inherit;
  }
}

// Change from Adobe
.tcb-teaser_description p {
  color: var(--light-secondary-text);
  margin-top: 8px;
  font-weight: 300;
  font-size: 1rem;
  line-height: 1.5;

  .section-font-color & {
    color: inherit;
  }
}

.tcb-teaser_info {
  gap: 5px;
  display: flex;
  flex-direction: column;
  margin-top: 24px;
}

.tcb-teaser_body {
  .tcb-teaser_column {
    .app-preview-image {
      line-height: 0;
    }
  }
}

.tcb-teaser_info-item {
  display: flex;
  gap: 12px;
}

.tcb-teaser_info-title {
  font-weight: 600;
  line-height: 24px;
  padding-left: 36px;
}

.tcb-download-app-block {
  border-radius: 16px;
  justify-content: space-between;
  padding: 4px;
  background: var(--primary-background);
  display: inline-flex;
  align-items: center;
  margin-top: 1rem;
  color: var(--body);
  overflow: hidden;
  // Change from Adobe
  .tcb-scanner--text {
    text-align: left;
    padding: 0 6px;
    line-height: 1.5;
    font-weight: 600;
    font-size: 1rem;
  }

  picture {
    display: flex;
    width: 70px;
    height: 70px;
  }

  .app-button-arrow {
    display: none;
  }
}

.tcb-download-app-block.revert-on-mobile {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  border-radius: 8px;
}

.tcb-download-app-block_qr-code {
  width: 70px !important;
  height: 70px !important;
  padding-left: 4px;
  padding-right: 4px;
  align-self: center;
}
.desktop-only {
  display: flex;
  width: fit-content;
}
.mobile-only {
  display: none;
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  // Change from Adobe
  .tcb-teaser {
    padding-top: 32px;
    padding-bottom: 0;
  }
  .tcb-teaser_column-left {
    flex: 1;
  }

  .tcb-teaser_column-right {
    flex: 1;
  }
}

@media screen and (max-width: 1023px) {
  .tcb-teaser_column {
    .center-align {
      align-self: center;
    }
  }
}

@media screen and (max-width: 767px) {
  .desktop-only {
    display: none;
  }
  .mobile-only {
    display: flex;
  }
  .tcb-teaser {
    // Change from Adobe
    padding-top: 32px;
    padding-bottom: 0;
    &.tcb-teaser--mobile-large {
      // Change from Adobe
      padding-bottom: 55%;
      .tcb-teaser--mobile-bottom-alignment & {
        padding-top: 95%;
        padding-bottom: 0;
      }
    }
  }
  .tcb-teaser--column-padding {
    .tcb-teaser_column-right,
    .tcb-teaser_column-left {
      padding: 0;
    }
  }
  .tcb-teaser_body {
    flex-direction: column;
    gap: 12px;
    .tcb-teaser--reverse & {
      flex-direction: column-reverse;
      gap: 32px;
    }
  }
  .tcb-teaser.large-mobile {
    padding-top: 236px;
  }
  .tcb-teaser_qr-desktop {
    width: 100%;
  }
  .tcb-teaser--mobile-button {
    min-width: 100%;
  }
  .tcb-teaser_qr-desktop.tcb-teaser_qr-mobile {
    visibility: hidden;
  }
  .tcb-teaser {
    .tcb-button {
      &.tcb-button--link {
        // Change from Adobe
        display: flex;
        justify-content: space-between;
        align-items: center;
        .tcb-button--title {
          width: calc(100% - 36px);
          white-space: initial;
        }
      }
      &.tcb-button--dark {
        width: 100%;
        justify-content: space-between;
        max-width: 328px;
      }
      &.tcb-button--gradient {
        max-width: unset;
      }
    }
    .app-button-arrow {
      display: block;
    }
  }
  .tcb-download-app-block {
    margin-top: 24px;
    padding: 12px 16px;
    width: 100%;
    justify-content: space-between;
    border-radius: 8px;
    .app-button-arrow {
      display: inline-block;
    }
  }
  .tcb-download-app-block_qr-code {
    display: none;
  }

  .tcb-button.tcb-button--link.tcb-button--border-on-mobile {
    width: 330px;
    padding: 12px 16px;
    border: 1px solid var(--body);
    white-space: normal;
    align-items: center;
  }
  .tcb-download-app-block.revert-on-mobile {
    background: var(--body);
    color: var(--primary-background);
  }
  // Change from Adobe
  .tcb-teaser_download-app-row {
    display: flex;
    justify-content: start;
  }
}

.background-position-right {
  @media screen and (max-width: 767px) {
    .tcb-teaser_download-app-row {
      width: 100%;
      margin-top: 24px;
  
      .button.padding-top-12 {
        padding: 0 !important;
        width: 100%;
        .cta-button {
          margin: 0;
        }
      }
    }
  
    .tcb-teaser {
      padding-bottom: 44px;
    }
  }
  .tcb-teaser_app-preview {
    
    @media screen and (min-width: 768px) {
      width: 40%;
    }
  }
  .tcb-teaser_body {
    .tcb-teaser_align-center {
      margin: auto 0;
  
      @media screen and (min-width: 768px) {
        padding-bottom: 50px;
      }
    }
  }
}

.tcb-teaser .tcb-teaser_download-app-row {
  .button {
    max-width: 328px;
    .cta-button {
      width: var(--desktop-width, 100%);
    }
    @media screen and (max-width: 767px) {
      max-width: unset;
      width: 100%;
      .cta-button {
        width: var(--mobile-width, 328px);
      }
    }
    @media (max-width: 320px) {
      .cta-button {
        width: var(--mobile-width, 100%);
      }
    }
  }
}

/** End of Teaser **/
