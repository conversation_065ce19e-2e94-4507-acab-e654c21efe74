.cmp-sitemap {
    h4 {
        padding-top: 12px;
        font-size: 16px;
        display: flex;
        justify-content: space-between;

        .rotated {
            transform: rotate(180deg);
            transform: transform 0.2s ease-in-out;
        }

        @media (min-width: 768px)  {
            img {
                display: none;
            }
        }

        + ul {
            display: none;
            @media (min-width: 768px)  {
                display: block;
            }
        }
    }

    a {
        line-height: 24px;
        :hover {
            cursor: pointer;
        }
    }
    span {
        :hover {
            color: #ed1b24;
            cursor: pointer;
        }
    }

    &-heading {
        color: #333;
        font-weight: 600;
    }
    ul, li {
        list-style: none;
    }
    ul {
        padding-inline-start: unset
    }
    li {
        padding-top: 12px;
    }

    .cmp-list__item {
        &-title {
            color: #616161;
        }
    }
}