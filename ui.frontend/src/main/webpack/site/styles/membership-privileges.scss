.membership-privileges {
  margin: 0 auto;
  max-width: 90rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .privileges-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .privileges-description {
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5rem;
    color: var(--secondary-grey-60);
  }

  .content-privilege {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;

    @include maxXlSemi {
      grid-template-columns: repeat(2, 1fr);
    }

    @include maxSm {
      grid-template-columns: repeat(1, 1fr);
      gap: 0.75rem;
    }
  }

  .membership-list {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    height: 8.75rem;
    display: flex;
    flex-direction: column;

    .tier-img {
      width: 100%;
      height: 8.75rem;
      object-fit: cover;
    }

    .tier-name {
      position: absolute;
      top: 1rem;
      left: 1.25rem;
      height: 0.625rem;
    }

    .detail-content {
      position: absolute;
      left: 1.25rem;
      top: 6.25rem;

      .item-detail {
        display: inline-flex;
        align-items: center;
        color: var(--primary-white);
        text-decoration: none;
        gap: 0.5rem;

        p {
          font-size: 1rem;
          font-weight: 600;
        }
      }
    }

    &.dark-text {
      .item-detail {
        color: var(--primary-black);
      }
    }
  }
}
