.multi-step-form {
  .multi-step-form__error-container {
    position: sticky;
    z-index: 1;
    top: 100px;

    &:has(.multi-step-form__error-message.active) {
      margin-bottom: 48px;
    }

    @media screen and (max-width: 1024px) {
      top: 65px;
    }

    .multi-step-form__error-message {
      display: none;
      padding: 16px;
      background-color: #FFE2E0;
      color: #8C201A;
      cursor: pointer;
  
      .toggle-button {
        margin-left: auto;
  
        &.closed {
          img {
            transform: rotate(180deg);
          }
        }
      }
  
      img.error-icon {
        height: 24px;
        margin-right: 16px;
      }
  
      &.active {
        display: flex;
      }
    }
    
    .multi-step-form__unfilled-surveys-list {
      overflow: hidden;
      li {
        text-decoration: underline;
        list-style-position: inside;
        cursor: pointer;
        width: fit-content;
      }
  
      li::marker {
        font-size: 0.5em;
      }
  
      &:has(li) {
        background-color: var(--primary-white);
        padding: 16px 48px;

        @media screen and (max-width: 1024px) {
          padding: 16px;
        }
      }
    }
  }
  
  .multi-step-form__step {
    display: none;

    @media screen and (min-width: 768px) {
      .button {
        float: left;
        margin-right: 1rem;
      }
    }

    &.active {
      display: grid;
      grid-row-gap: 16px;
    }

    .tcb-title {
      padding-bottom: 0;
    }

    .title-cmp {
      padding-bottom: 16px;
    }
  }

  .cmp-form-button {
    min-width: 200px;
    font-weight: 600;
    max-width: unset;
  }
}
