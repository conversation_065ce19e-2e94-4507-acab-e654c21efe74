/* Override CSS */

// changes for footer CSS
.footer .footer-container .content-wrapper {
  max-width: 1440px;
}

.footer-links .footer-links__list {
  padding-top: 12px;
  padding-bottom: 12px;
  width: calc(100% + 24px);
  margin: -12px;
}

.footer-links__item h3,
.footer-links__item h4 {
  line-height: 1.5;
  margin-bottom: 8px;
}
.footer-info a:hover,
.footer-links__item a:hover {
  text-decoration: underline;
}
.footer-links li {
  font-weight: 400;
}

@media (max-width: 767px) {
  .footer-head .footer-expand {
    margin-top: 8px;
    margin-bottom: 0px;
  }
  .footer-logo {
    padding-bottom: 0;
    padding-top: 8px;
  }
  .footer-links__social-container {
    padding-bottom: 8px;
  }
  .footer-info {
    padding-top: 16px;
    padding-bottom: 8px;
  }
  .footer-links .footer-links__list {
    padding-top: 0;
  }
}

// Article Component CSS addon
.press-article {
  .banner-image {
    max-width: 1920px;
    margin: auto;
    width: 100%;
    height: 280px;
  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
  }
}
  img{
      &.table-content__icon {
        width: 24px!important;
        height: 24px;
        display: unset!important;
        margin: unset!important;
      }
    }
}
@media screen and (max-width: 1024px) {
  .press-article {
    .banner-image {
      img {
        object-fit: cover;
      }
    }
  }
}

// Header
// Adobe Start
.mobile-button .mobile-menu-dropdown .mobile-arrow-down .dropdown_holder {
  letter-spacing: 2px;
  padding: 0;
}
.mobile-button .mobile-menu-dropdown .mobile-arrow-down img {
  transform: rotate(-90deg);
  fill: #ed1c24;
  display: block;
}

.mobile-button .mobile-menu-dropdown.open .mobile-arrow-down img {
  transform: rotate(0deg);
  fill: #ed1c24;
}

.mobile-button .dropdown-item:first-child {
  border-top: none;
}
.mobile-button .dropdown-item.active {
  color: #ed1c24;
}
.mobile-button .dropdown-item {
  color: #a2a2a2;
}
// Explore
.mobile-menu .discover_category {
  margin-top: 16px;
}
.header_layout .mobile-menu .discover_category .navigation-primary_item {
  font-weight: 700;
  padding: 0 !important;
}

.mobile-menu-items .navigation-secondary_menu .navigation-secondary_item span.icon {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.mobile-menu-items .navigation-secondary_menu .navigation-secondary_item span.icon {
  height: 24px;
  fill: #ed1c24;
}

// Adobe End
