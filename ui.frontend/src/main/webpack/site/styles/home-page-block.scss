.home-page {
  position: relative;
  overflow-y: scroll;
  z-index: 0;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  &.full-height {
    @media screen and (min-width: 1025px) {
      height: calc(100vh - 60px);
    }
    width: 100vw;

    &::-webkit-scrollbar {
      display: none;
    }
  }
  a:hover {
    text-decoration: underline;
  }

  .home-page-background {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.home-page-left__wrap {
  width: 35%;
  z-index: 1300;
  background-color: $color-homepage-background;
  padding: 72px 48px 48px calc(100% / 22.5);
  flex-grow: 1;
  color: var(--primary-white);

  .dark & {
    background-color: $color-homepage-background-dark;
  }
  .phone-number {
    .dark & {
      color: var(--primary-white);
    }
    color: var(--primary-red);
  }
}

.home-page-left__bottom--content span {
  color: var(--primary-white);
}
.home-page-list {
  max-width: 1440px;
  height: 100%;
  margin: 0 auto;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.home-page-left__content {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  height: 100%;
  // Change from Adobe
  grid-gap: 32px;
  gap: 32px;
  &.row {
    flex-direction: row;
    justify-content: flex-start;
    gap: 5px;
  }
}

.home-page-left__item {
  display: flex;
  justify-content: unset;
  align-items: center;
  gap: 16px;
  // Change from Adobe
  &--img {
    width: 48px;
    height: 48px;
  }
}
.home-page-left__img {
  width: 40px;
}
.home-page-left__bottom-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.home-page-left__menu-item {
  display: flex;
  gap: 12px;
}
.home-page-left__text:after {
  content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/white-arrow-icon.svg);
  line-height: 0;
}
.home-page-left__top-panel {
  width: 100%;
  height: 1px;
  background: white;
  opacity: 0.2;
  margin-bottom: 16px;
}

.home-page-left__text {
  font-weight: 600;
  display: flex;
  align-items: center;
  grid-gap: 16px;
  gap: 16px;
  position: relative;
  font-size: 16px;
}
.home-page-left__contact {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: flex-start;
  margin-top: 32px;

  h2 {
    font-size: unset;
    font-weight: unset;
    line-height: unset;
  }
}
// Change from Adobe
.home-page-left__bottom--content {
  margin-top: 8px;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  a:hover {
    text-decoration: none;
  }
}
.home-page-right__wrap {
  width: 65%;
  position: relative;
}
.home-page-right__img--destop {
  display: block;
}
.home-page-right__img--mobile {
  display: none;
}
@media screen and (max-width: 1024px) {
  .home-page-left__wrap {
    padding: 32px calc(100% / 22.5) 24px;
    width: 100% !important;
    // Change from Adobe
    height: fit-content !important;
    justify-content: flex-start;
    // Change from Adobe
    flex-grow: unset;
  }
  .home-page-left__content {
    justify-content: flex-start;
    gap: 32px;
    // Change from Adobe
    height: fit-content;
  }
  .home-page-list {
    flex-direction: column;
    background-size: 0;
  }
  .home-page-right__wrap {
    // Change from Adobe
    height: 540px;
    width: 100% !important;
    flex: 1 1;
  }
  .home-page-right__img--destop {
    display: none !important;
  }
  .home-page-right__img--mobile {
    display: block !important;
    width: 100%;
    // Change from Adobe
    height: 100%;
    object-fit: cover;
  }
  .home-page-left__item {
    flex-direction: column;
    width: 32.6%;
  }
  .home-page-left__text {
    text-align: center;
    &.mobile-display:after {
      content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow-icon.svg);
      line-height: 0;
      .dark & {
        content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/white-arrow-icon.svg);
      }
    }
  }
  .home-page-left__bottom-container {
    grid-gap: 16px;
    gap: 16px;
  }
  .home-page-left__content--bottom {
    margin: 0;
  }
  // Change by Adobe
  .home-page-left__top-container .home-page-left__contact .home-page-left__item .home-page-left__menu-item {
    .home-page-left__text:after {
      content: none;
      .dark & {
        content: none;
      }
    }
  }
}
@media (max-width: 767px) {
  .home-page-list {
    flex-direction: column;
  }
  .home-page-left__wrap {
    width: 100% !important;
    height: auto !important;
    justify-content: flex-start;
  }
  .home-page-right__wrap {
    // Change from Adobe
    height: 320px;
    width: 100% !important;
    flex: 1 1;
  }

  .home-page-right__img--destop {
    display: none !important;
  }
  .home-page-right__img--mobile {
    display: block !important;
    // Change from Adobe
    height: 100%;
  }
}

@media screen and (max-width: 1024px) {
  // Change from Adobe
  .home-page-left__contact {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    min-width: unset;
    grid-gap: 8px;
    gap: 8px;
    grid-row-gap: 32px;
    row-gap: 32px;
    width: 60%;
  }
  // Change from Adobe
  .home-page-left__item {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

@media screen and (max-width: 991px) {
  // Change from Adobe
  .home-page-left__contact {
    grid-template-columns: repeat(3, 1fr) !important;
    width: 100%;
  }
}
