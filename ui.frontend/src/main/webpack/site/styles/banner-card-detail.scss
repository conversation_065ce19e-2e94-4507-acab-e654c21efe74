.tcb-hero-product.card-revamp {
  .breadcrumb-item {
    a {
      font-size: 0.875rem;
    }
  }

  .hero-product__wrapper{
    align-items: end;
    height: 100%;
  }

  .hero-product__wrapper-image {
    img {
      display: block;
    }
    @include maxSm {
      margin-top: 1rem;
      margin-bottom: 0;
    }
  }

  .hero-product__wrapper-content {
    padding: 5rem 0 5rem 0;
    label {
      margin-bottom: 0.75rem;
    }

    h1 {
      font-size: 2rem;
    }

    h3 {
      font-size: 1.5rem;
      color: var(--secondary-grey-80);
      font-weight: 600;
      margin-bottom: 2.25rem;

      @include maxSm {
        margin-bottom: 1.25rem;
      }
    }
  }

  .hero-product__wrapper-subtitle {
    h3 {
      margin-bottom: 0;
    }
  }

  .hero-product__wrapper-subtitle,
  .hero-product__wrapper-button {
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
    @include maxSm {
      margin-bottom: 0.75rem;
      margin-top: 0.75rem;
    }
  }

  .tcb-hero-product_background {
    img {
      width: 100%;
      height: 100%;
    }
  }
  @include md {
    .hero-product__wrapper-item {
      flex-grow: 0;
      max-width: 50%;
      flex-basis: 50%;
      padding: 0 0.75rem;
    }
  }
}

.btn-banner-card-detail {
  background-color: var(--primary-white);
  position: relative;
  width: 100%;
  margin: auto;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;

  &.btn-banner-card-detail-center {
    padding: 0 1.5rem;
    height: 5.875rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: baseline;
    .img-tag-btn {
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .slick-text {
    margin-bottom: 0.25rem;
    .slick-slide {
      letter-spacing: 0.019rem;

      > div {
        @include breakLines(2);
        > * {
          display: block !important;
          max-height: 3rem;
        }
      }
    }
    img {
      vertical-align: baseline;
      display: inline;
      position: relative;
      top: 0.375rem;
    }

    p {
      font-size: 0.875rem;
      font-weight: 400;
    }

    width: 90%;
  }

  .btn-see-promotions {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    span {
      font-size: 0.875rem;
      color: var(--secondary-grey-60);
      font-weight: 200;
    }

    img {
      width: 1rem;
      height: auto;
    }
  }

  .img-tag-btn {
    position: absolute;
    right: 1.5rem;
    top: 0.75rem;
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
