.offer-detail-related {
  background-color: #f5f6f8;
  padding: 3rem 4rem; /* 48px to rem */
  display: flex;
  justify-content: center;

  .offer-related-container {
    max-width: 1314px;
    width: 100%;
  }

  .related-header {
    display: flex;
    justify-content: space-between;

    .related-header-info {
      display: flex;
      align-items: center;
      gap: 1rem; /* 16px to rem */

      .quantity-container {
        width: 62px;
        height: 62px;
        border-radius: 50%;
        padding: 0.75rem 1.4375rem; /* 12px 23px to rem */
        background-color: var(--primary-red);
        display: flex;
        justify-content: center;
        align-items: center;

        .quantity {
          color: #ffffff;
          font-size: 2rem;
          line-height: 2.4rem;
          font-weight: 400;
        }
      }
    }
  }

  .related-listing {
    display: flex;
    gap: 1.5rem; /* 24px to rem */

    @media (min-width: 769px) {
      overflow: auto;

      &::-webkit-scrollbar-thumb {
        background: #a2a2a2;
        border-radius: 0.25rem; /* 4px to rem */
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar {
        height: 10px;
      }
    }

    @media (max-width: 768px) {
      flex-wrap: wrap;
    }

    .card-promotion__item-wrapper {
      max-width: 310px;
      padding: 2.25rem 0 1rem; /* 36px 0 16px to rem */
    }

    .card-promotion__item {
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 0.5rem; /* 8px to rem */
      height: 100%;
      max-width: 400px;
    }

    .card-offer__image {
      display: flex;
      justify-content: center;
      flex-shrink: 0;
      aspect-ratio: 4/3;

      img {
        border-top-left-radius: 0.5rem; /* 8px to rem */
        border-top-right-radius: 0.5rem; /* 8px to rem */
        height: 100%;
        object-fit: cover;
        @include maxSm {
          max-width: 100%;
          width: 100%;
        }
      }
    }

    .card-offer__image--wrapper {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      display: inline-block;
      overflow: hidden;
      width: initial;
      height: initial;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      position: relative;
      max-width: 100%;
    }

    .image-card-offer {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      display: block;
      width: initial;
      height: initial;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      max-width: 100%;
    }

    .card-offer__image--wrapper > img {
      position: absolute;
      inset: 0px;
      box-sizing: border-box;
      padding: 0px;
      border: none;
      margin: auto;
      display: block;
      width: 0px;
      height: 0px;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      -o-object-fit: cover;
      object-fit: cover;
      -o-object-position: center center;
      object-position: center center;
    }

    .card-offer__content {
      padding: 1.5rem 1rem; /* 24px 16px to rem */
      display: flex;
      flex-direction: column;
      position: relative;
      width: 100%;
      justify-content: space-between;
      gap: 0.5rem; /* 8px to rem */
    }

    .card-content__label {
      font-size: 0.875rem; /* 14px to rem */
      text-transform: uppercase;
      font-weight: 600;
      line-height: 1.313rem; /* 21px to rem */
      letter-spacing: 0.125rem; /* 2px to rem */
      color: var(--accent);
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      margin-top: 0;
    }

    .card-content__title {
      font-weight: 600;
      line-height: 1.5rem; /* 24px to rem */
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      margin-bottom: 0;
      text-wrap: nowrap;
    }

    .card-content__description {
      color: var(--secondary-grey-60);
      flex-grow: 1;
      overflow: hidden;
      max-height: 2.375rem; /* 38px to rem */
      font-weight: 400;
    }

    .card-content__description > * {
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      line-height: 1.25rem; /* 20px to rem */
    }

    .card-content__link--button {
      position: relative;
      display: inline-flex;
      outline: none;
      border: none;
      cursor: pointer;
      align-items: center;
      text-decoration: none;
      transition: all 0.3s ease-in;
      justify-content: space-between;
      gap: 0.75rem; /* 12px to rem */
      background-color: inherit;
      padding: 0;
      line-height: 1.5;

      .button--text {
        font-weight: 600;
        color: black;
      }

      .button--icon {
        display: flex;
        align-items: center;
        margin-left: 0;
        transition: all 0.3s ease-in-out;
      }
    }

    .card-content__favorite-promo {
      display: flex;
      position: absolute;
      bottom: 0;
      right: 0;
    }

    .card-content__favorite-promo > img {
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  .related-btn-mobile-container {
    display: none;
  }
}

@media (max-width: 768px) {
  .offer-listing-detail {
    &__masterhead {
      .merchant-tag {
        font-size: 1rem; /* 16px to rem */
      }

      .exp-and-location {
        flex-wrap: wrap;
        width: 100%;
        gap: 0.5rem; /* 8px to rem */

        div > img {
          width: 16px;
          height: 16px;
        }
      }
    }

    &__content {
    }

    &__product {
      .list-images {
        gap: 1.5rem; /* 24px to rem */
        grid-template-columns: auto auto auto;

        .image-card-container {
          width: 90px;
          height: 60px;
          display: flex;
          align-items: center;
        }

        .image-card-product {
          width: 90px;
          height: 60px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .btn-container {
        flex-wrap: wrap;

        .primary-cta-btn {
          width: 100%;

          span {
            max-width: 16.25rem; /* 260px to rem */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .secondary-cta-btn {
          width: 100%;

          span {
            max-width: 16.25rem; /* 260px to rem */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    &__target {
    }

    &__address {
      .offer-address-list {
        grid-template-columns: auto auto;
      }
    }

    &__info {
    }
  }

  .offer-detail-related {
    padding: 3rem 1rem; /* 48px 16px to rem */

    .related-listing {
      gap: 0.5rem; /* 8px to rem */
      margin-top: 1.5rem; /* 24px to rem */

      .card-promotion__item-wrapper {
        flex-grow: 0 !important;
        max-width: calc(50% - 0.25rem) !important; /* 4px to rem */
        flex-basis: calc(50% - 0.25rem) !important; /* 4px to rem */
        box-sizing: border-box !important;
        padding: 0;
      }

      .card-promotion__item {
        width: unset !important;
      }

      .card-offer__image--wrapper > img {
        /* object-fit: contain; */
      }

      .card-offer__content {
        padding: 1rem 0.75rem; /* 16px 12px to rem */
      }

      .card-content__label {
        font-size: 0.625rem; /* 10px to rem */
        line-height: 0.9375rem; /* 15px to rem */
      }

      .card-content__title {
        font-size: 0.625rem; /* 10px to rem */
        line-height: 0.8125rem; /* 13px to rem */
      }

      .card-content__description {
        font-size: 0.625rem; /* 10px to rem */
        line-height: 0.8125rem; /* 13px to rem */
      }

      .card-content__description > * {
        overflow: hidden !important;
        font-size: 0.625rem; /* 10px to rem */
        line-height: 0.8125rem; /* 13px to rem */
      }

      .card-content__expired-date {
        font-size: 0.625rem; /* 10px to rem */
        margin: 0;
      }

      .card-content__expired-date > img {
        width: 0.625rem; /* 10px to rem */
        height: 0.625rem; /* 10px to rem */
      }

      .expiry-date-section {
        font-size: 0.625rem; /* 10px to rem */
      }

      .expired-date__label {
        display: none;
      }

      .card-content__expired-date-count-down {
        .progress-bar {
          &__inner {
          }
        }

        .countdown-time {
          font-size: 0.625rem; /* 10px to rem */
        }
      }

      .button--text {
        font-size: 0.625rem; /* 10px to rem */
      }

      .button--icon > img {
        width: 0.625rem; /* 10px to rem */
        height: 0.625rem; /* 10px to rem */
      }
    }

    .related-btn-container {
      display: none;
    }

    .related-btn-mobile-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 1.5rem; /* 24px to rem */

      .see-all-btn {
        max-width: 100%;
        background-color: black;
        color: white;
        height: 3.5rem; /* unchanged */
        justify-content: space-between;
        align-items: center;
        border: none;
        border-radius: 0.5rem; /* 8px to rem */
        cursor: pointer;
        display: inline-flex;
        font-weight: 600;
        gap: 0.75rem; /* 12px to rem */
        line-height: 1.5;
        min-width: max-content;
        outline: none;
        padding: 1rem 1.5rem; /* 16px 24px to rem */
        position: relative;
        text-decoration: none;
        transition: all 0.3s ease-in;
        white-space: nowrap;
        width: 100%;
        z-index: 1;

        &-icon {
          align-items: center;
          display: flex;
          margin-left: 0;
          transition: all 0.3s ease-in-out;
        }

        &:hover {
          background-color: var(--secondary-grey-60);

          .load-more__button-icon {
            filter: brightness(0) saturate(100%) invert(100%) sepia(100%)
              saturate(1%) hue-rotate(232deg) brightness(107%) contrast(101%);
          }
        }
      }
    }
  }
}
