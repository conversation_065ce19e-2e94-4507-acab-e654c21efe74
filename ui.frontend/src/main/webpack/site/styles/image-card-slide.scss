/* Online Security */
.security-slideshow {
  position: relative;
  padding-top: 48px;
  margin: -12px -12px 0 -12px;
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  overflow-y: clip;

  @include maxMd {
    padding-right: 0.75rem;
    padding-left: 0.75rem;
  }
}

.security-grid-container {
  margin-left: auto;
  margin-right: auto;
}

.security-grid-root {
  padding-top: 48px;
  margin: -12px;
  justify-content: center;
  flex-wrap: wrap;
}

.security-grid-root::before {
  position: absolute;
  content: "";
  height: calc(100% - (24px));
  max-width: 1312px;
  width: calc(100% - (24px));
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--bg-color);
  border-radius: 8px;
}

.security-grid-item {
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
  padding: 12px 12px 0 12px;
  margin: 0;
}

.security-slideshow-container {
  z-index: 1;
}

.security-slideshow-title {
  text-align: center;
  margin-bottom: 40px;

  >h2 {
    font-size: 24px;
    line-height: 36px;
    margin: 0 auto 16px;
    font-weight: 600;
  }

  >p {
    font-size: 16px;
    line-height: 24px;
    color: var(--gray-600);
    margin: auto;
    font-weight: 400;
  }
}

.security-slideshow-gallery {
  margin: auto;
}

.security-gallery-images {
  display: flex !important;
  align-items: center;
  justify-content: center;
  margin-left: -12px;
  margin-right: -12px;

  &.smaller {
    align-items: flex-end;

    .security-gallery-item {
      flex: 0 0 15.05%;
      max-width: 15.05%;
      height: 164px;
      padding: 0 12px;

      &.security-gallery-center {
        flex: 0 0 261px;
        max-width: 261px;
        height: auto;
      }
    }

    @media (max-width: 767px) {
      justify-content: center;

      .security-gallery-item {
        display: none;

        &.security-gallery-center {
          display: block;
          flex: 0 0 282px;
          max-width: 282px;
        }
      }
    }
  }

  >.slick-list {
    width: 100%;

    >.slick-track {
      display: flex;
      min-width: 100%;
    }
  }

  .image-center & {
    align-items: center;
  }

  .image-bottom & {
    align-items: flex-end;
  }
}

.security-gallery-item {
  flex: 0 0 17.6%;
  max-width: 17.6%;
  padding: 0 12px;

  &:has(img:not([src])) {
    display: none;
  }

  @include md {
    .image-overflow & {
      flex: 0 0 21.1%;
      max-width: 21.1%;
    }
  }
}

.security-gallery-item-small {
  padding: 0 12px;
  flex: 0 0 15.05%;
  max-width: 15.05%;
  height: 164px;
}

.security-gallery-center {
  flex: 0 0 18.3%;
  max-width: 18.3%;
  height: auto !important;
  padding: 0 12px;

  @include md {
    .image-overflow & {
      flex: 0 0 18.3%;
      max-width: 18.3%;
    }
  }
}

.security-item-outer {
  height: 100%;
}

.security-item-inner {
  width: 100%;
  display: block;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 2px -3px 22px rgb(155 119 87 / 5%), 0 10px 20px rgb(155 119 87 / 25%);
  height: 100%;

  >span {
    box-sizing: border-box;
    overflow: hidden;
    width: initial;
    height: initial;
    background: none;
    opacity: 1;
    border: 0px;
    margin: 0px;
    padding: 0px;
    position: relative;
    max-width: 100%;
    height: 100% !important;

    >span {
      box-sizing: border-box;
      display: none;
      width: initial;
      height: initial;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      max-width: 100%;
      height: 100% !important;

      >img {
        display: block;
        max-width: 100%;
        width: initial;
        height: initial;
        background: none;
        opacity: 1;
        border: 0px;
        margin: 0px;
        padding: 0px;
      }
    }

    >picture {
      >img {
        inset: 0px;
        box-sizing: border-box;
        padding: 0px;
        border: none;
        margin: auto;
        display: block;
        min-width: 100%;
        max-width: 100%;
        min-height: 100%;
        max-height: 100%;
        object-fit: fill;
        object-position: center center;
      }
    }

  }
}

/* Online Security Media */
@media (min-width: 1200px) {
  .security-grid-container {
    display: flex;
  }
}

@media (min-width: 1440px) {
  .security-grid-container {
    max-width: 1440px;
  }
}

@media (min-width: 390px) and (max-width: 768px) {
  .content-wrapper>.security-wrapper {
    padding-left: calc(100vw / 22.5) !important;
    padding-right: calc(100vw / 22.5) !important;
  }
}

@media (max-width: 767px) {
  .security-grid-root {
    padding: 32px 16px 0;
    width: calc(100% + 16px);
    margin: -8px;
  }

  .security-grid-root::before {
    width: calc(100% - (16px));
    height: calc(100% - (16px));
  }

  .security-slideshow-title {
    padding: 0 16px;
    margin-bottom: 25px;

    h3 {
      font-weight: var(--heading3-font-weight);
    }
  }

  .security-slideshow-title>h2 {
    padding: 0 4px;
  }

  .security-slideshow-gallery {
    overflow: hidden;
    margin-bottom: 8px;
    margin-right: -16px;
    padding-left: 16px;
  }

  .security-gallery-images {
    display: flex !important;
    overflow: auto;
    padding-top: 10px;
    justify-content: flex-start;
    margin-left: -2px;
    margin-right: -2px;
    align-items: flex-start;
    margin: 0;
  }

  .security-gallery-item {
    order: 2;
    flex: 0 0 228px;
    max-width: 228px;
    height: 235px;
    padding: 0 2px;
  }

  .not-slider-mobile .security-gallery-images {
    justify-content: center;
  }

  .not-slider-mobile .security-gallery-item-small {
    display: none;
  }

  .not-slider-mobile .security-gallery-item-small.security-gallery-center {
    display: block;
  }

  .security-item-inner {
    border-radius: 15px;
    overflow: hidden;
  }

  .security-gallery-center {
    flex: 0 0 201px;
    max-width: 201px;
    order: -1;
  }

  .not-slider-mobile .security-gallery-center {
    flex: 0 0 201px;
    max-width: 201px;
    order: -1;
  }
}

/* End of Online Security Media */
/* End of Online Security */
.card-item {
  margin: 0;
  height: 100%;
  width: 100%;
}

.card-item__slide-show {
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 3.5rem;
}

.card-image__over-height {
  z-index: 1;
  margin-right: auto;
  box-shadow: 0 26.4px 144.8px rgb(0 0 0 / 4%), 0 11.0293px 60.494px rgb(0 0 0 / 3%);
  max-width: 202px;

  img {
    width: 100%;
  }
}

.card-image__content-text {
  background-color: var(--bg-color);
  position: absolute;
  padding: 56px 50px;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 8px;
  width: 100%;
  min-height: 70%;
  right: 0;
  top: 8%;

  h3 {
    font-size: 24px;
    line-height: 36px;
    font-weight: 600;
    max-width: 51%;
    margin: 0 0 16px auto;

    @include maxMd {
      max-width: 100%;
      margin-left: 0;
      text-align: left;
    }

    @include maxSm {
      text-align: center;
    }
  }

  p {
    font-size: 16px;
    line-height: 24px;
    max-width: 51%;
    margin: 0 0 0 auto;
    font-weight: 400;
    color: #616161;
  }

  &.card-image__content-text-align-left {
    text-align: left;
  }

  &.card-image__content-text-align-right {
    text-align: right;
  }

  &.card-image__content-text-align-center {
    text-align: center;
  }
}

@media (max-width: 1024px) {
  .card-image__content-text {
    padding: 33px 25px;
    height: 85%;

    h5 {
      max-width: 45%;
    }

    p {
      max-width: 50%;
    }
  }

  .card-image__over-height {
    max-width: 180px;
    left: 33px;
  }
}

@media (max-width: 991px) {
  .card-item {
    &:nth-child(1) {
      padding-right: unset;
      margin-bottom: 40px;
    }

    &:nth-child(2) {
      padding-left: unset;
    }

    max-width: 100%;
    flex-basis: 100%;
    height: 388px;
  }

  .card-image__content-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 40%;
    top: 10%;
    height: 67%;
    min-height: 272px;

    h5 {
      max-width: 100%;
      margin-left: 0;
    }

    p {
      max-width: 100%;
      margin-left: 0;
    }
  }
}

@media screen and (max-width: 1024px) and (min-width: 992px){
  .card-image__content-text {
    h3 {
      max-width: 50%;
    }
  }
}

@media screen and (max-width: 1024px) and (min-width: 768px){
  .card-item__slide-show {
    padding-left: 33px;
  }
}

@media (max-width: 767px) {
  .slide-show__wrapper {
    margin-top: 48px;
  }

  .card-item {
    height: 100%;
  }

  .card-item__slide-show {
    display: flex;
    flex-direction: column-reverse;
    text-align: center;
    overflow: hidden;
    background-color: #fff;
    border-radius: 8px;
    background-color: var(--bg-color);
    background-repeat: no-repeat;
    background-size: cover;
    padding: 2rem 1rem 0;
  }

  .card-image__over-height {
    min-width: 296px;
    min-height: 421px;
    max-height: 442px;
    margin-left: 13px;
    margin-right: 13px;
    overflow: hidden;
    position: relative;
    left: 0;

    span {
      width: 100%;
    }
  }

  .card-image__content-text {
    background-image: none;
    background-color: transparent;
    max-width: 100%;
    position: unset;
    transform: none;
    padding: 0 0 24px;
    min-height: 6.25rem;
    display: block;
    height: auto;

    h5 {
      text-align: center;
    }

    p {
      text-align: center;
    }
  }
}

@media (max-width: 390px) {
  .card-image__content-text {
    padding-top: 20px;
  }
}
