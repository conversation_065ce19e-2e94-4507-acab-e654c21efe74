@use 'sass:math';
// $size  : px or em
// $color : color
// $thickness : px

.tcb-sectionContainer {
  .input-search {
    width: 100%;
    padding: 0.75rem 3rem;
    border-radius: 0.5rem;
    background-color: #fff;
    display: inline-flex;
    border: 1px solid #e3e4e6;
    position: relative;
    outline: none;
    height: 3.5rem;
  }

  .tabs-event-card {
    .expansion-area {
      display: flex;
      flex-direction: column;
      row-gap: 1.5rem;

      .result {
        border-radius: 0.5rem;
        box-shadow: 1px 1px 6px 0 rgba(0, 0, 0, 0.08);
        padding: 1.5rem;
        background-color: #ffffff;
        border: 1px solid #fff;

        .result_title {
          line-height: 1.5;
          color: var(--body);
          letter-spacing: -0.01em;
          font-weight: 600;
          font-size: 19px;
        }

        .result_description {
          margin-top: 0.5rem;
          color: var(--gray-600);
        }
      }
    }

    .pagination {
      margin-top: 1.5rem;
      .pag-container {
        float: right;
      }

      a {
        color: black;
        margin-right: 0.375rem;
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.875rem;
        text-align: center;
        padding: 0.25rem 0.5rem;
      }

      a:last-child {
        margin-right: 0px;
      }

      a.active {
        background-color: #ff0000;
        color: white;
        border-radius: 0.25rem;
      }

      a:hover:not(.active) {
        color: #ff0000;
      }
    }
  }
}

.search-result__wrapper {
  max-width: 1216px;
  margin: auto;
  min-height: calc(100vh - 200px);

  [class*='gsc-'] {
    font-family: 'SF Pro Display';
    scroll-margin-top: 130px;
  }

  [class='gsc-adBlock'],
  [class='gsc-search-button'],
  [class='gs-per-result-labels'],
  [class='gcsc-more-maybe-branding-root'] {
    display: none;
  }

  [class='gsc-expansionArea'] {
    display: flex;
    flex-direction: column;
    row-gap: 24px;
    @include maxLgSemi {
      row-gap: 16px;
    }
  }

  div[class^='gsc-webResult gsc-result'] {
    padding: 16px;
    border-radius: 8px;
    box-shadow: 1px 1px 6px 0px rgba(0, 0, 0, 0.08);
    padding: 24px;
    @include maxLgSemi {
      padding: 16px;
    }
  }

  [class^='gs-webResult gs-result'] {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
  }

  [class*='gs-result'] a[class='gs-title'],
  [class*='gs-result'] a[class='gs-title'] * {
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 25px;
  }

  [class*='gs-snippet'] {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;

    b {
      color: var(--primary-black);
    }
  }

  [class*='gs-no-results-result'] [class='gs-snippet'] {
    background-color: unset;
    border: unset;
  }

  [class*='gsc-refinementHeader'][class*='gsc-refinementhActive'],
  [class*='gsc-refinementHeader'][class*='gsc-refinementhInactive'] {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    margin-right: 24px;
    background-color: transparent;
  }

  [class*='gsc-refinementHeader'][class*='gsc-refinementhActive'] {
    border-color: var(--primary-red);
    font-weight: 700;
    border-width: 4px;
  }

  [class*='gs-visibleUrl-breadcrumb'] {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }

  [class*='gsc-results'] > [class*='gsc-cursor-box'] {
    text-align: left;
    margin: 24px 0 0;
    [class='gsc-cursor'] {
      display: flex;
      justify-content: flex-start;
    }
    [class*='gsc-cursor-page'] {
      height: 30px;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;
      color: var(--gray-600);
      background-color: transparent;
      text-align: center;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        text-decoration: none;
        color: var(--primary-red);
      }
    }

    [class*='gsc-cursor-page'][class*='gsc-cursor-current-pag'] {
      background-color: var(--primary-red);
      color: var(--primary-white);
      border-radius: 4px;
    }
  }

  .gsc-refinementHeader {
    padding: 0;
    &.gsc-refinementhInactive {
      font-weight: 400;
    }
    &.gsc-refinementhActive {
      padding-bottom: 8px;
      font-weight: 700;
    }
    span {
      pointer-events: none;
    }
  }

  .gsc-resultsbox-visible .gs-result .gsc-url-top,
  .gcsc-more-maybe-branding-box,
  .gsc-adBlockNoHeight {
    display: none;
  }

  .gs-spelling {
    a,
    span {
      font-size: 16px;
    }
    span {
      &:after {
        content: ' ';
      }
    }
  }

  .gsc-cursor-page {
    &.gsc-cursor-current-page {
      width: 26px;
    }
  }

  .gsc-webResult.gsc-result {
    &.gsc-promotion {
      margin-bottom: 24px;
      background: var(--primary-white);
      .gs-promotion-table {
        padding: 0;
      }
      .gs-promotion-text-cell {
        margin: 0;
        display: flex;
        flex-direction: column;
        grid-row-gap: 8px;
        row-gap: 8px;
        > div.gs-title {
          display: flex;
          align-items: center;
          column-gap: 8px;
          img {
            width: 25px;
            height: 25px;
          }
        }
        a.gs-title {
          @include breakLines(1);
          @include maxSm {
            -webkit-line-clamp: 2;
          }
        }
      }
      .gs-visibleUrl {
        display: none;
      }
      .gs-snippet {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }
    }
  }

  .gsc-results {
    width: 100%;
    margin-top: 24px;

    @include maxSm {
      margin-top: 16px;
    }
  }

  .gs-no-results-result {
    margin-top: 24px;
    .gs-snippet {
      padding: 0;
      margin: 0;
      background-color: transparent;
    }
  }

  .gsc-control-cse {
    border: 0;
    background-color: inherit;
  }

  .gsc-refinementsArea {
    margin-top: 24px;
    @media screen and (max-width: 1024px) {
      border-left: 0;
      border-right: 0;
      border-top: 0;
      background-color: transparent;
      border-radius: 0;
    }
  }

  .gsc-above-wrapper-area {
    padding: 8px 0 24px;
    border-bottom: 0;
    @include maxLgSemi {
      padding: 0 0 16px;
    }
  }

  .gsc-result-info-container {
    display: none;
  }

  .gsc-results > .gsc-result {
    margin-bottom: 24px;
  }
  .gsc-refinementsGradient {
    display: none;
  }
  .gsc-table-result,
  .gsc-thumbnail-inside {
    padding: 0;
  }
  div[id*='___gcse_'] .gsc-input-box {
    .gsc-input-prefix-icon {
      padding-left: 8px;
      img {
        display: none;
      }
    }
    input.gsc-input {
      &::-webkit-input-placeholder {
        margin-right: 30px;
        padding-left: 5px;
        background-image: var(--searchIconUrl);
        background-repeat: no-repeat;
        text-indent: 24px;
        background-position: 0 center;
      }
    }
  }

  @include maxLgSemi {
    .gsc-control-cse .gsc-option-menu-container {
      width: 100%;
    }
    .gsc-above-wrapper-area-container tr {
      display: flex;
      flex-direction: column-reverse;
    }
    .gsc-control-cse {
      padding: 0;
    }
    .gsc-select-custom {
      background-color: var(--primary-white);
      border-radius: 8px;
      border: 1px solid var(--light-background-hover);
      height: 56px;
      display: flex;
      align-items: center;
      padding: 0 16px;
      justify-content: space-between;
      .gsc-select-custom-selector {
        transition: all 0.5s ease 0s;
        pointer-events: none;
        display: flex;
        align-items: center;
      }
      span {
        font-family: SF Pro Display;
        font-size: 16px;
        line-height: 24px;
        pointer-events: none;
      }
    }
    .gsc-refinementsArea {
      position: relative;
      overflow: unset;
      border: 0;
      margin: 24px 0 16px;
    }
    .gsc-refinementBlock {
      display: none;
      border: 1px solid var(--light-background-hover);
      flex-direction: column;
      position: absolute;
      top: 58px;
      z-index: 1;
      width: 100%;
      background-color: var(--primary-white);
      border-radius: 8px;
      padding: 8px 0;
      box-shadow: 0 5px 8px rgba(0, 0, 0, 0.1);
    }
    .gsc-refinementHeader {
      &.gsc-refinementhInactive {
        width: 100%;
        padding: 8px 16px;
      }
      &.gsc-refinementhActive {
        background-color: rgba(0, 0, 0, 0.08);
        border-width: 0;
        border-bottom: 0;
        width: 100%;
        padding: 8px 16px;
        span {
          font-weight: 600;
        }
      }
      span {
        font-size: 16px;
        font-weight: 400;
        pointer-events: none;
      }
    }
    .gsc-results > .gsc-result {
      margin-bottom: 16px;
    }
  }

  @media screen and (min-width: 1025px) {
    .gsc-select-custom {
      display: none;
    }
    .gsc-refinementBlock {
      display: unset !important;
    }
  }
}

@mixin cross($size: 20px, $color: currentColor, $thickness: 1px) {
  margin: 0;
  padding: 0;
  border: 0;
  background: none;
  position: relative;
  width: $size;
  height: $size;

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: math.div(($size - $thickness), 2);
    left: 0;
    right: 0;
    height: $thickness;
    background: $color;
    border-radius: $thickness;
  }

  &:before {
    transform: rotate(45deg);
  }

  &:after {
    transform: rotate(-45deg);
  }

  span {
    display: block;
  }
}

body {
  &:has(.search-result__wrapper) > table[class*='gstl_'] {
    z-index: 14 !important;
  }

  &:not(:has(.search-result__wrapper)) > table[class*='gstl_'] {
    // Autocomplete popup on header
    &:nth-of-type(1) {
      position: fixed !important;
    }

    &:nth-of-type(2) {
      position: fixed !important;
    }
  }
}

// Autocomplete box
body > table[class*='gstl_'] {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  &::-webkit-scrollbar-thumb {
    background: var(--secondary-mid-grey-100);
    border-radius: 4px;
  }
  tbody,
  td,
  tr {
    display: block;
  }

  .gssb_a {
    color: var(--secondary-grey-60);
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    cursor: pointer;
    border: 0;
    padding-left: 0;
    padding-right: 0;

    b {
      color: var(--primary-black);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      white-space: break-spaces;
    }

    * {
      font-family: 'SF Pro Display';
    }
  }

  .gssb_e,
  .gsc-completion-container {
    box-shadow: none;
  }

  // Autocomplete box - SearchPopup
  // &:nth-of-type(1) {
  //   display: block;
  //   max-height: 160px;
  //   overflow-x: hidden;
  //   overflow-y: auto;
  //   background-color: var(--primary-white);

  //   .gssb_a {
  //     padding: 4px 0 4px 8px;
  //     table td span {
  //       white-space: break-spaces;
  //     }
  //   }

  //   .gsc-completion-container {
  //     overflow: hidden;
  //     position: relative;
  //     border: 0;
  //   }
  // }
  // Autocomplete box - SearchResult
  // &:nth-of-type(1) {
    max-height: 184px;
    overflow-x: hidden;
    overflow-y: auto;
    display: block;
    border-radius: 8px;
    border: 1px solid var(--secondary-mid-grey-60);
    box-shadow: 1px 1px 6px 0px rgba(0, 0, 0, 0.08);
    padding: 16px 0;
    background-color: var(--primary-white);

    .gssb_a {
      padding: 4px 24px;
    }

    .gsc-completion-container {
      border: none;
      overflow: hidden;
    }
  // }
}

// Input box
div[id*='___gcse_'] {
  .gsc-search-button {
    display: none;
  }
  table td.gsc-input {
    padding-right: 0;

    .search-result__wrapper & {
      display: flex;
      align-content: center;
      gap: 1rem;

      @include maxLgSemi {
        flex-direction: column;
        gap: 1.5rem;
      }

      .gsc-input-box {
        width: 70%;

        @include maxLgSemi {
          width: 100%;
        }
      }
    }

    input {
      &::placeholder {
        color: var(--secondary-mid-grey-100);
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }
    }
  }
  .gsc-input-box {
    border-radius: 8px;
    padding: 0;

    .gsc-input-prefix-icon {
      padding-left: 16px;

      img {
        display: block;
      }
    }

    .gsst_a {
      padding: 0;

      > span {
        display: none;
      }

      > div {
        margin: 0;
        border: 0;
        padding: 0;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 150ms;
        background-color: transparent;

        > span {
          @include cross(20px, #ed1b24, 2px);
        }

        &:hover,
        &:focus {
          background: rgba(0, 0, 0, 0.1);
        }
      }
    }

    .gsib_a {
      padding: 16px;
      line-height: 24px;
    }

    .gsst_b {
      display: flex;
      padding-right: 16px;
      cursor: pointer;
    }
  }

  .gsc-dropdown-box {
    width: 30%;

    @include maxLgSemi {
      width: 100%;
    }

    .dropdown__wrapper {
      position: relative;
      margin-bottom: 0;

      &.expanded {
        .dropdown__button {
          img {
            transform: rotate(180deg);
            transition: transform 500ms;
          }
        }

        .dropdown__content {
          display: flex;
          flex-direction: column;
          transform-origin: top;
          transform: scaleY(1);
        }
      }

      .dropdown__button {
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid black;
        background: transparent;

        img {
          transform: rotate(0);
          transition: transform 500ms;
        }

        &:hover {
          cursor: pointer;
        }
      }

      .dropdown__content {
        position: absolute;
        z-index: 1;
        box-shadow: 0 5px 8px rgba(0, 0, 0, .1);
        border: 1px solid var(--light-background-hover);
        background-color: var(--primary-background);
        width: 100%;
        display: none;
        transform: scaleY(0);

        ul {
          list-style-type: none;
          padding-inline-start: unset;
          padding: 0.5rem 0;
        }

        .dropdown__item {
          padding: 0.5rem 1.5rem;
          align-content: center;
          border-bottom: unset;
          max-height: 2.5rem;

          &:hover,
          &.selected {
            background-color: rgba(0, 0, 0, .08);
            font-weight: 600;
          }
        }
      }
    }
  }
}

// search result box
.gsc-resultsbox-visible {
  min-width: calc(70% - 1.5rem);
  max-width: 100%;

  .gs-image-box {
    display: none;
  }

  .gsc-tabdActive {
    .gsc-promotion {
      &:has(.gs-thumbnail-wrapper) {
        display: flex;

        @include xs {
          flex-direction: column;
        }

        .gs-thumbnail-wrapper {
          width: 15%;
          height: 5.5rem;
          margin-right: 1.5rem;
          margin-top: auto;
          margin-bottom: auto;

          @include maxMd {
            width: 20%;
          }

          @include maxSm {
            width: 30%;
          }

          @include xs {
            width: 100%;

            margin-right: 0;
            margin-top: 0;
            margin-bottom: 1.5rem;
            height: 7.875rem;
          }

          img.gs-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center center;
          }
        }

        .gs-promotion.gs-result {
          width: 85%;

          @include maxMd {
            width: 80%;
          }

          @include maxSm {
            width: 70%;
          }

          @include xs {
            width: 100%;
          }
        }
      }

      .gs-promotion-table .gs-snippet {
        @include xs {
          & > * {
            display: -webkit-box;
            text-overflow: ellipsis;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }

    .gsc-expansionArea .gsc-webResult.gsc-result {
      &:has(.gs-thumbnail-wrapper) {
        display: flex;

        @include xs {
          flex-direction: column;
        }

        .gs-thumbnail-wrapper {
          width: 15%;
          height: 5.5rem;
          margin-right: 1.5rem;
          margin-top: auto;
          margin-bottom: auto;

          @include maxMd {
            width: 20%;
          }

          @include maxSm {
            width: 30%;
          }

          @include xs {
            width: 100%;

            margin-right: 0;
            margin-top: 0;
            margin-bottom: 1.5rem;
            height: 7.875rem;
          }

          img.gs-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center center;
          }
        }

        .gs-webResult.gs-result {
          width: 85%;

          @include maxMd {
            width: 80%;
          }

          @include maxSm {
            width: 70%;
          }

          @include xs {
            width: 100%;
          }
        }
      }

      .gs-webResult.gs-result {
        .gsc-table-result .gsc-table-cell-snippet-close .gs-bidi-start-align.gs-snippet {
          @include xs {
            display: -webkit-box;
            text-overflow: ellipsis;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }
  }
}

.gsc-wrapper {
  display: flex !important;
  gap: 1.5rem;

  &:has(.gs-captcha-outer-wrapper:not([style*='display'])) {
    .gsc-ad-banner,
    .gsc-promotion-list-content {
      display: none;
    }
  }

  &:not(.gsc-promotion-list) .gsc-promotion-list-content {
    display: none;
  }

  .gsc-resultsbox-invisible {
    & ~ .gsc-ad-banner {
      display: none;
    }
  }

  &.gsc-promotion-list {
    display: block !important;

    .gsc-ad-banner,
    .gsc-resultsbox-visible {
      display: none;
    }

    .gsc-promotion-list-content {
      margin-top: 1rem;

      .card-promotion {
        &__list {
          overflow: unset;
          display: flex;
          flex-wrap: wrap;
          box-sizing: border-box;
          gap: 1.5rem;

          @include maxSm {
            gap: 1rem;
          }
        }

        &__empty {
          padding: 1.5rem 0;
        }

        &__item-wrapper {
          flex: 0 1 calc((100% / 3) - 1rem);

          @include maxSm {
            flex: 0 1 calc((100% / 2) - 0.5rem);
          }
        }

        &__item {
          display: flex;
          flex-direction: column;
          background-color: white;
          border-radius: .5rem;
          height: 100%;
          max-width: 25rem;

          .card-offer {
            &__image {
              max-height: 10.625rem;
              min-height: 5.5rem;
              flex-shrink: 0;
              display: flex;
              justify-content: center;

              @media (max-width: 768px) {
                height: initial !important;
              }

              &--wrapper {
                box-sizing: border-box;
                display: inline-block;
                overflow: hidden;
                width: initial;
                height: initial;
                background: none;
                opacity: 1;
                border: 0;
                margin: 0;
                padding: 0;
                position: relative;
                max-width: 100%;

                .image-card-offer {
                  box-sizing: border-box;
                  display: block;
                  width: initial;
                  height: initial;
                  background: none;
                  opacity: 1;
                  border: 0;
                  margin: 0;
                  padding: 0;
                  max-width: 100%;

                  & > img {
                    display: block;
                    max-width: 100%;
                    width: initial;
                    height: initial;
                    background: none;
                    opacity: 1;
                    border: 0;
                    margin: 0;
                    padding: 0;
                  }
                }

                & > img {
                  position: absolute;
                  inset: 0;
                  box-sizing: border-box;
                  padding: 0;
                  border: none;
                  margin: auto;
                  display: block;
                  width: 0;
                  height: 0;
                  min-width: 100%;
                  max-width: 100%;
                  min-height: 100%;
                  max-height: 100%;
                  object-fit: cover;
                  object-position: center center;
                }
              }
            }

            &__content {
              padding: 1.5rem 1rem;
              display: flex;
              flex-direction: column;
              position: relative;
              height: 100%;
              width: 100%;
              justify-content: space-between;
              gap: .5rem;
              grid-gap: .5rem;

              @include maxSm {
                padding: .75rem .5rem;

                &::before,
                &::after {
                  width: 1.125rem !important;
                  height: 1.125rem !important;
                }
              }

              &:hover {
                text-decoration: unset;
                cursor: unset;
              }

              &::before {
                content: "";
                display: block;
                height: 1.75rem;
                border-radius: 50%;
                width: 1.75rem;
                position: absolute;
                transform: translateX(-50%) translateY(-50%);
                top: 0;
                left: 0;
                background-color: #f5f6f8;
              }

              &::after {
                content: "";
                display: block;
                height: 1.75rem;
                border-radius: 50%;
                width: 1.75rem;
                position: absolute;
                transform: translateX(50%) translateY(-50%);
                top: 0;
                right: 0;
                background-color: #f5f6f8;
              }

              .card-content {
                &__label {
                  font-size: .875rem;
                  text-transform: uppercase;
                  font-weight: 600;
                  line-height: 1.313rem;
                  letter-spacing: .125rem;
                  color: var(--accent);
                  height: 2.625rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-word;
                  margin-top: 0;

                  @include maxSm {
                    font-size: .625rem;
                    height: 1.875rem;
                    line-height: .938rem;
                  }
                }

                &__title {
                  font-weight: 600;
                  font-size: 1rem;
                  line-height: 1.5rem;
                  height: 3rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-word;
                  margin-bottom: 0;

                  @include maxSm {
                    font-size: .625rem;
                    height: 1.875rem;
                  }
                }

                &__description {
                  color: var(--secondary-grey-60);
                  flex-grow: 1;
                  overflow: hidden;
                  max-height: 2.375rem;
                  font-weight: 400;

                  & > * {
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    line-height: 1.25rem;
                  }

                  @include maxSm {
                    max-height: 2rem;

                    & > * {
                      font-size: .625rem;
                      line-height: .938rem;
                    }
                  }
                }

                &__link {
                  &--button {
                    position: relative;
                    display: inline-flex;
                    outline: none;
                    border: none;
                    cursor: pointer;
                    align-items: center;
                    text-decoration: none;
                    transition: all .3s ease-in;
                    justify-content: space-between;
                    gap: .75rem;
                    background-color: inherit;
                    padding: 0;

                    .button--text {
                      font-weight: 600;
                      font-size: 1rem;
                      color: black;

                      &:hover {
                        color: red;
                      }

                      @include maxSm {
                        font-size: .625rem;
                      }
                    }

                    .button--icon {
                      display: flex;
                      align-items: center;
                      margin-left: 0;
                      transition: all .3s ease-in-out;
                    }
                  }
                }

                &__favorite-promo {
                  display: flex;
                  position: absolute;
                  bottom: 0;
                  right: 0;

                  img {
                    width: 1rem;
                    height: 1rem;
                  }
                }

                .display-none {
                  display: none;
                }

                .display-unset {
                  display: unset;
                }
              }

              .promotion__expired {
                @include maxSm {
                  margin-top: 0.5rem;
                }

                .expired-progress {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  grid-gap: 1rem;
                  gap: 1rem;
                  max-width: 100%;

                  @include maxSm {
                    display: block;
                  }

                  &__progress-bar {
                    flex: 1;
                    width: 100%;
                    border-radius: 0.5rem;
                    background-color: var(--secondary-mid-grey-60);
                    height: 0.25rem;
                    overflow: hidden;
                    position: relative;

                    &--inner {
                      width: 100%;
                      border-radius: 0.5rem;
                      background-color: var(--primary-red);
                      position: absolute;
                      top: 0;
                      left: 0;
                      bottom: 0;
                    }
                  }

                  &__countdown-time {
                    font-size: 0.875rem;
                    text-align: right;
                    color: var(--secondary-grey-60);

                    @include maxSm {
                      text-align: left;
                      margin-top: 0.5rem;
                      font-size: .625rem;
                    }
                  }
                }

                .expired-label {
                  display: flex;
                  align-items: center;
                  column-gap: 0.5rem;
                  margin-top: 0.5rem;
                  margin-bottom: 0.5rem;

                  @include maxSm {
                    column-gap: 0.25rem;
                    margin-top: 0;
                    margin-bottom: 0;
                  }

                  .expired-label--icon {
                    width: 1.5rem;
                    height: 1.5rem;

                    @include maxSm {
                      width: .625rem;
                      height: .625rem;
                    }
                  }

                  .expired-label--text {
                    font-size: 1rem;
                    line-height: 1.5rem;
                    font-weight: 400;
                    color: var(--secondary-grey-60);

                    @include maxSm {
                      font-size: .625rem;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .pagination {
        margin-top: 1.5rem;

        .pagination__container {
          float: left;
          display: flex;
          align-items: center;
          color: #616161;
          flex-wrap: wrap;

          a {
            color: var(--secondary-grey-60);
            font-size: 1.125rem;
            font-style: normal;
            font-weight: 400;
            line-height: 1.875rem;
            height: 1.875rem;
            display: flex;
            align-items: center;
            text-align: center;
            padding: .25rem .5rem;

            &:last-child {
              margin-right: 0;
            }

            &.active {
              background-color: red;
              color: white;
              border-radius: .25rem;
              margin-left: 0.375rem;
              margin-right: 0.375rem;
            }

            &:hover:not(.active) {
              color: red;
            }
          }

          label {
            cursor: pointer;
            font-size: 1.125rem;
            line-height: 1.875rem;
            color: var(--secondary-grey-60);
          }

          &--previous {
            margin-right: 0.5rem;
          }

          &--next {
            margin-left: 0.5rem;
          }
        }
      }
    }
  }

  @include maxLgSemi {
    display: block !important;
    gap: unset;
  }

  .gs-captcha-outer-wrapper {
    position: absolute;
    left: 0;
  }

  .gsc-ad-banner {
    min-width: 30%;
    max-width: 30%;

    @include maxLgSemi {
      width: 100%;
      min-width: unset;
      max-width: unset;
    }

    &.theme-light {
      .ad-banner__close-button {
        filter: brightness(1);
      }

      .ad-banner__title,
      .ad-banner__description {
        color: var(--primary-white);
      }

      .cta-button {
        background-color: var(--primary-white);
        color: var(--primary-black);
        border: 1px solid var(--primary-black);

        &:hover {
          background-color: var(--primary-black);
          color: var(--primary-white);
          border: 1px solid var(--primary-white);
        }
      }
    }

    &.theme-dark {
      .ad-banner__close-button {
        filter: brightness(0);
      }

      .ad-banner__title,
      .ad-banner__description {
        color: var(--primary-black);
      }

      .cta-button {
        background-color: var(--primary-black);
        color: var(--primary-white);
        border: 1px solid var(--primary-white);

        &:hover {
          background-color: var(--primary-white);
          color: var(--primary-black);
          border: 1px solid var(--primary-black);
        }
      }
    }

    .ad-banner__wrapper {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      background-image: var(--backgroundImageUrl);
      background-repeat: no-repeat;
      background-size: cover;
      padding: 3rem 1rem;
      margin-top: 1.5rem;
      min-height: 31.25rem;
      max-height: 100%;
      border-radius: 0.5rem;
      position: sticky;
      top: 7.313rem;

      @include maxLgSemi {
        margin-top: 0;
        min-height: 17.5rem;
        padding: 4rem 4.75rem;
        position: relative;
        top: unset;
      }

      @include xs {
        min-height: 33.125rem;
        padding: 2rem 1rem;
      }

      .cta-button {
        width: 100%;
        min-width: unset;
        transition: background-color 0.5s, color 0.5s;

        @media screen and (min-width: 768px) and (max-width: 1024px) {
          width: fit-content;
          gap: 2rem;
        }
      }
    }

    .ad-banner__close-button {
      width: 1.5rem;
      height: 1.5rem;
      position: absolute;
      right: 1rem;
      top: 1rem;
      cursor: pointer;
    }
  }
}
