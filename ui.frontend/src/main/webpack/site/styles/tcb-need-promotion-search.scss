.tcb-need-promotion-search {
  max-width: 120rem;
  margin: 0 auto;
  z-index: 10;
  position: relative;

  @include maxSm {
    padding: 0;
  }

  .need-promotion-search-container {
    max-width: 90rem;
    width: 100%;
    margin: 0 auto;
    position: relative;

    .need-search-box {
      width: 100%;

      .need-input-container {
        display: flex;
        justify-content: space-between;
        gap: 0;

        .need-search-container {
          flex: 1 1 auto;
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 0.25rem;
          padding: 1.125rem 1.5rem;
          height: 3.75rem;
          background-color: var(--primary-white);
          border: 0.0625rem solid var(--secondary-light-grey-100);
          border-top-left-radius: 0.5rem;
          border-bottom-left-radius: 0.5rem;

          @include maxSm {
            border-radius: 0.5rem;
            padding: 1rem;
          }

          .need-icon-search-mobile {
            height: 1.5rem;
            width: 1.5rem;
            display: none;

            img {
              width: 100%;
              height: 100%;
            }

            @include maxSm {
              display: block;
            }
          }

          .need-search-input {
            background-color: var(--primary-white);
            outline: none;
            border: none;
            width: 100%;
          }

          .need-icon-remove-search {
            height: 1.25rem;
            width: 1.25rem;
            cursor: pointer;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }

        .need-promotions-filter {
          padding: 1rem 1.5rem;
          background-color: var(--primary-black);
          border-top-right-radius: 0.5rem;
          border-bottom-right-radius: 0.5rem;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 3.75rem;
          cursor: pointer;

          @include maxSm {
            display: none;
          }
        }
      }

      .need-input-dropdown {
        width: 100%;
        background-color: var(--primary-white);
        padding: 1.5rem;
        border-bottom-left-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
        border: 0.0625rem solid var(--secondary-light-grey-100);
        overflow: hidden;
        flex-direction: column;
        gap: 1.5rem;
        position: absolute;
        margin-top: -0.0625rem;

        .need-group-promotion-dropdown {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;

          .need-tilte-type-brand {
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5rem;
            letter-spacing: 0;
            color: var(--secondary-grey-60);
          }

          .need-input-dropdown-list {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;

            .need-input-dropdown-item {
              border-radius: 2.0625rem;
              padding: 0.5rem 0.75rem;
              border: 0.0625rem solid var(--primary-color-gray-500);
              cursor: pointer;
              height: 2.5rem;
              box-sizing: border-box;
            }
          }
        }

        .need-infor-search {
          display: flex;
          flex-direction: column;
          gap: 1.25rem;
        }
      }
    }
  }

  &.tcb-hub-search {
    margin-top: -1.875rem;
    padding: 0 4rem;

    @include maxSm {
      padding: 0;
    }

    .need-promotion-search-container {
      max-width: 74rem;
    }
  }
  @include maxSm {
    &.popup-search-mobile {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--primary-white);
      z-index: 9999;
      transform: none;
      max-width: none;
      margin: 0;
      overflow: auto;

      .need-search-container {
        border: none !important;
        border-bottom: 0.0625rem solid var(--secondary-light-grey-100) !important;
        padding: 1rem 0;

        .need-icon-remove-search {
          display: block !important;
        }
      }
      .need-input-dropdown {
        border: none !important;
        padding: 1.5rem 1rem !important;
        margin-top: 0 !important;
      }
    }
  }
}
