.offer-listing-filter__body,
.offer-listing-detail {

  @include maxSm {
    .card-promotion__item-body {
      height: 100%;
      display: flex;
      flex-direction: column;

      .card-offer__content {
        flex: 1;
      }
    }
  }

  .card-content {
    &__expired-date-count-down {
      display: flex;
      grid-gap: 1rem;
      gap: 1rem;
      max-width: 100%;
      justify-content: space-between;
      align-items: center;

      @include maxSm {
        grid-gap: 0.5rem;
        gap: 0.5rem;
        flex-direction: column;
        align-items: flex-start;
      }

      .progress-bar {
        width: 100%;
        border-radius: 0.5rem;
        background-color: #dedede;
        height: 0.25rem;
        overflow: hidden;
        position: relative;

        @include maxSm {
          width: calc(100% - 1.813rem);
        }

        &__inner {
          width: 100%;
          border-radius: 0.5rem;
          background-color: #ed1b24;
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
        }
      }

      .countdown-time {
        font-size: 0.875rem;
        text-align: right;
        font-weight: 400;
        line-height: 1.5rem;
        color: var(--secondary-grey-60);
      }
    }

    &__expired-date--date-time {
      white-space: nowrap;
    }

    &__expired-date--time {
      display: inline-block;
      width: 3.938rem !important;

      @include maxSm {
        width: 2.5rem !important;
      }
    }

    &__favorite-promo {
      display: flex;
      position: absolute;
      bottom: 0;
      right: 0;

      &>img {
        width: 2rem;
        height: 1.875rem;
      }
    }

    .display-none {
      display: none;
    }

    .display-unset {
      display: unset;
    }
  }
}