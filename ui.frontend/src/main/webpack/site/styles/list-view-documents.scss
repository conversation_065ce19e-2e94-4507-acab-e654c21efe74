
/* Service Fee Component */
@keyframes rippleServiceFee {
	100% {
		transform: scale(2);
		opacity: 0;
	}
}
.list-view-documents {
	.menu.tcb-tabs {
		border-bottom: none;
		margin-bottom: unset;
		.tcb-tabs_control--prev, .tcb-tabs_control--next {
			display: none !important;
		}
	}
	.tab {
		&:not(:first-child) {
			display: none;
		}
		.row {
			display: flex;
			flex-direction: row;
			align-self: flex-start;
			padding: 24px 0 24px 24px;
			border-top: 1px solid #dedede;
			.date {
				color: var(--gray-600);
				flex: 0 0 100px;
				max-width: 100px;
				margin-right: 40px;
				white-space: nowrap;
				span {
					font-weight: 600;
					font-size: 0.875rem;
					line-height: 1.5;
					letter-spacing: 2px;
					text-transform: uppercase;
				}
			}
			.content {
				flex: 0 0 36.875rem;
				max-width: 36.875rem;
				margin-right: 36px;
				p {
					color: #212121;
					letter-spacing: -0.01em;
					font-weight: 600;
				}

				h4 {
					color: var(--secondary-grey-100);
				}
			}

			.content-category {
				flex: 0 0 130px;
				margin-right: 40px;

				@include maxSm {
					flex: unset;
					margin-bottom: 1rem;
				}

				@include maxMd {
					flex: unset;
				}
			}

			&:nth-child(2n) {
				background-color: white;
			}
			&.filter-hide {
				display: none !important;
			}
		}
	}
	.file-download {
		display: flex;
		flex: 1;
		flex-direction: column;
		.item {
			width: 100%;
			display: flex;
			flex-direction: row;
			justify-content: flex-end;
			border-bottom: 1px solid #dedede;
			padding-bottom: 24px;
			margin-bottom: 24px;
			&:last-child {
				border-bottom: none;
				padding-bottom: 0;
				margin-bottom: 0;
			}
		}
		.item.have-file-name {
			justify-content: space-between;
			align-items: center;
			.file-name {
				max-width: 17.375rem;
				color: #212121;
				padding-right: 10px;
			}
		}
		.btn {
			min-width: 39.8%;
			display: flex;
			align-items: center;
			position: relative;
			padding-left: 28px;
			transition: all 0.3s ease;
			padding-right: 35px;
			justify-content: space-between;
			&::before {
				position: absolute;
				content: "";
				width: 2px;
				height: 15px;
				top: 50%;
				transform: translateY(-50%);
				background-color: #dadada;
				left: 0;
			}
		}
		.link {
			width: 100%;
			position: relative;
			display: inline-flex;
			border: none;
			cursor: pointer;
			align-items: center;
			justify-content: space-between;
			grid-gap: 12px;
			gap: 12px;
			background-color: inherit;
			font-weight: 600;
			font-size: 1rem;
			line-height: 1.5;
			color: #000;
			&:hover {
				span {
					color: red;
					text-decoration: underline;
				}
			}
			span {
				text-align: left;
			}
			picture {
				display: flex;
				align-items: center;
			}
			img {
				width: 24px;
				height: 17px;
			}
		}
		.link[data-file-type="youtube"] {
			img {
				width: 24px;
				height: 24px;
			}
		}
	}
	.popup-download {
		display: none;
		position: fixed;
		z-index: 1500;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		overflow-x: hidden;
		overflow-y: auto;
		background-color: rgba(0, 0, 0, 0.5);
		padding: 0 32px;
		@media (max-width: 767px) {
			padding: 0 8px;
		}
		.popup-content {
			position: relative;
			margin: auto;
			display: flex;
			flex-direction: column;
			background-color: white;
			color: rgba(0, 0, 0, 0.87);
			border-radius: 4px;
			width: 100%;
			max-width: 866px;
			max-height: calc(100% - 64px);
			box-shadow: 0px 11px 15px -7px rgb(0 0 0 / 20%), 0px 24px 38px 3px rgb(0 0 0 / 14%), 0px 9px 46px 8px rgb(0 0 0 / 12%);
		}
	}
	.popup-content {
		.head {
			padding: 8px 8px 8px 16px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			span.title {
				font-size: 1.5rem;
				line-height: 1.5;
				font-weight: 600;
			}
		}
		.loading {
			display: none;
			text-align: center;
			padding: 8px 24px;
		}
		.close-btn {
			color: rgba(0, 0, 0, 0.54);
			padding: 12px;
			flex: 0 0 auto;
			background-color: inherit;
			border: none;
			border-radius: 50%;
			cursor: pointer;
			transition: 0.2s;
			.button-label {
				width: 100%;
				display: flex;
				align-items: inherit;
				justify-content: inherit;
			}
			.button-ripple-root {
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				z-index: 0;
				overflow: hidden;
				position: absolute;
				border-radius: inherit;
				pointer-events: none;
			}
		}
		&:not(.video) {
			.close-btn:hover {
				background-color: rgba(128, 128, 128, 0.082);
			}
		}
		iframe {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			width: 100%;
			height: 100%;
		}
		canvas {
			margin: auto;
		}
		.file-content {
			flex: 1 1 auto;
			padding: 8px 24px;
			overflow-y: auto;
		}
		.foot {
			flex: 0 0 auto;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 24px 0;
			margin: 0 auto;
			line-height: 24px;
			a {
				display: inline-flex;
				cursor: pointer;
				align-items: center;
				gap: 12px;
				&:hover {
					text-decoration: underline;
				}
				span {
					font-size: 16px;
					font-weight: 600;
				}
				img {
					width: 16px;
				}
			}
		}
		&.video {
			max-height: unset;
			margin-top: calc(1.75rem + 72px);
			.head {
				border-bottom: 1px solid #e9ecef;
				span.title {
					line-height: 1.5;
    			font-size: 1.25rem;
				}
			}
			.foot {
				display: none;
			}
			.video-container {
				position: relative;
				&:before {
					padding-top: 56.25%;
					display: block;
    			content: "";
				}
			}
			.file-content {
				padding: 16px;
				overflow-y: visible;
			}
		}
	}
	.tab-content {
		display: flex;
		flex-direction: column;
		.view-more {
			margin-top: 56px;
			display: flex;
			justify-content: center;
		}
	}
	.view-more {
		&.edit-mode {
			width: 300px;
    		height: 50px;
		}
		.cta-button {
			display: flex;
			flex-grow: 0;
			max-width: 33.333333%;
			flex-basis: 33.333333%;
			align-items: center;
			justify-content: space-between;
			padding: 16px 24px;
			border-radius: 8px;
			cursor: pointer;
			grid-gap: 12px;
			gap: 12px;
			transition: all 0.3s ease-in;
			font-weight: 600;
			background-color: #fff;
			line-height: 24px;
			color: #000;
			&.btn-outline {
				border: 1px solid #000;
				background-color: transparent;
				color: #000;
				&:hover {
					background-color: #000;
					color: #fff;
					img {
						filter: brightness(0) saturate(100%) invert(100%) sepia(0%) 
								saturate(1%) hue-rotate(306deg) brightness(103%) contrast(101%);
					}
				}
			}

			picture {
				height: auto;
			}

			.cmp-button__icon {
				height: auto;
			}
			
		}
	}

	.show-document {
		display: block;
	}

	.hide-document {
		display: none !important;
	}

	.type-output-none-seemore,
	.type-output-seemore {
		width: 100%;
	}
}
.tab-horizontal-report__tab-control-btn {
	&:hover {
		background-color: #f2f2f2;
	}
}
.ripple-btn-service-fee {
	width: 0;
	height: 0;
	border-radius: 50%;
	background: rgba(128, 128, 128, 0.575);
	transform: scale(0);
	position: absolute;
	opacity: 1;
}
.ripple-animation-service-fee {
	animation: rippleServiceFee 0.6s linear;
}
@media (min-width: 1200px) and (max-width: 1339px) {
	.list-view-documents {
		.tab {
			.row {
				.content {
					flex: 0 0 365px;
					max-width: 365px;
				}
			}
		}
	}
}
@media (max-width: 1199px) {
	.list-view-documents {
		.tab {
			.row {
				.content {
					flex: 0 0 340px;
					max-width: 340px;
				}
			}
		}
		.file-download {
			.btn {
				min-width: 45%;
			}
		}
	}
}
@media (max-width: 991px) {
	.list-view-documents {
		.tab {
			.row {
				display: block;
				padding: 32px 16px;
				flex-direction: column;
				.date {
					flex: unset;
				}
				.content {
					flex: 0 0 100%;
					max-width: 100%;
					margin-right: 0;
				}
			}
		}
		.file-download {
			padding-top: 16px;
			.item {
				display: block;
			}
			.btn {
				min-width: inherit;
				padding-left: 0px;
				&::before {
					content: none;
				}
			}
			.redirect {
				padding-left: 0;
			}
			.item.have-file-name {
				.file-name {
					flex: 0 0 100%;
					max-width: 100%;
					padding-right: 0;
					padding-bottom: 16px;
				}
			}
		}
		.view-more {
			.cta-button {
				max-width: 100%;
				flex-basis: 100%;
			}
		}
	}
}
@media (max-width: 767px) {
	.list-view-documents {
		.file-download {
			.link {
				width: auto;
				grid-gap: 12px;
				gap: 12px;
			}
		}
		.menu {
			border-bottom: 1px solid #dedede;
			margin: 0 calc(100vw / 22.5);
		}
		.tab {
			.row {
				padding: 32px calc(100vw / 22.5);
				&:first-child {
					border-top: none;
				}
				.file-name {
					&:empty {
						display: none;
					}
				}
			}
		}
		.tab-content {
			.view-more {
				margin: 28px calc(100vw / 22.5) 0;
			}
		}
		.file-content {
			padding: 16px;
		}

		.select-checkbox-filter {
			display: block;
			padding-inline: calc(100vw / 22.5);
			.select-options {
				margin-left: 0;
			}
			.select span {
				white-space: break-spaces;
			}
		}
	}
	.content-wrapper.service-fee-container {
		padding-left: 0 !important;
		padding-right: 0 !important;
	}
	.service-fee-container {
		.title-cmp {
			padding: 0 calc(100vw / 22.5) 12px;
		}
	}
}

/* Select options filter */
.select-checkbox-filter {
	display: flex;
	h2 {
		color: #212121;
		font-size: 1.75rem;
		font-weight: 300;
		line-height: 1.25;
	}
	.select-options {
		display: flex;
		align-items: center;
		margin-left: 32px;
		position: relative;
		z-index: 1;
		.select {
			padding: 12px 23px 12px 17px;
			display: flex;
			align-items: center;
			background-color: #fff;
			border: 1px solid #e3e4e6;
			border-radius: 8px;
			position: relative;
			z-index: 2;
			cursor: pointer;
			min-height: 48px;
			min-width: 181px;
			max-width: 400px;
			h6 {
				font-size: 16px;
			}
			@include maxSm {
				margin-bottom: 0.5rem;
			}

			@media (max-width: 767px) {
				max-width: unset;
				width: 100%;
			}
		}
		.checkbox {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: -1;
			transition: all 0.3s ease-in-out;
			transform: scaleX(0) scaleY(0);
		}
	}
	.select {
		.calendar-icon {
			width: 14px;
			height: 14px;
			margin-right: 10px;
			filter: brightness(0) saturate(100%) invert(32%) sepia(25%) 
					saturate(0%) hue-rotate(196deg) brightness(106%) contrast(87%);
		}
		span {
			margin-left: 16px;
			font-size: 16px;
			color: #c5c5c5;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}
		.fa-chevron-down {
			color: #ed1c24;
			width: 16px;
			height: 10px;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			margin-left: 8px;

			@media (max-width: 767px) {
				margin-left: auto;
			}
		}
	}
	.checkbox {
		.checkbox-list {
			position: absolute;
			top: 0;
			left: 0;
			opacity: 0;
			padding: 23px 0 32px;
			border: 1px solid #e3e4e6;
			border-radius: 8px;
			background-color: #fff;
			transition: all 0.5s ease-in-out;
		}
		.type {
			display: flex;
			max-height: 310px;
			overflow-x: hidden;
			overflow-y: scroll;
			&::-webkit-scrollbar {
				width: 5px;
			}
			&::-webkit-scrollbar-thumb {
				background: #585454;
			}
			&::-webkit-scrollbar-track {
				background: transparent;
			}
		}
	}
	.checkbox-list {
		.year, .category {
			min-width: 161px;
			padding: 0 24px;
			width: 100%;
			p {
				color: #000;
				font-size: 16px;
				line-height: 24px;
				margin-bottom: 24px;
				font-weight: 700;
			}
		}
		.quarter {
			p {
				color: #000;
				font-size: 16px;
				line-height: 24px;
				margin-bottom: 24px;
				font-weight: 700;
			}
			border-left: 1px solid #dedede;
			min-width: 161px;
			padding: 0 24px;
		}
		ul {
			list-style: none;
			margin: 0;
			padding: 0;
			li {
				opacity: 1;
				transition: all 0.3s ease-in-out;
				line-height: 24px;
				font-size: 16px;
				cursor: pointer;
				text-align: left;
				margin-top: 16px;
			}
		}
		label {
			display: flex;
			line-height: 24px;
			color: var(--gray-600);
			font-size: 16px;
			align-items: center;
			font-weight: 400;
			text-transform: none;
			letter-spacing: normal;
			text-align: left;
			cursor: pointer;
			user-select: none;
		}
		input {
			max-width: 24px;
			height: 24px;
			flex: 0 0 24px;
			margin: 0;
			-webkit-appearance: none;
			border-radius: 5px;
			outline: none;
			border: 1px solid #e3e4e6;
			background-color: #fff;
			position: relative;
			margin-right: 8px;
			cursor: pointer;
			&:checked {
				border-color: #ed1b24;
				background-color: #ed1b24;
				&::before {
					opacity: 1;
				}
			}
			&::before {
				content: "";
				position: absolute;
				width: 0.4rem;
				height: 0.9rem;
				top: 50%;
				left: 50%;
				opacity: 0;
				border: solid #fff;
				border-width: 0 0.1875rem 0.1875rem 0;
				transform: rotate(45deg) translate(-0.55rem, -0.25rem);
			}
		}
	}
	.select.showed {
		+ {
			.checkbox {
				transform: scaleX(1) scaleY(1);
				z-index: 1;
				.checkbox-list {
					top: 100%;
					opacity: 1;
				}
			}
		}
		.fa-chevron-down {
			transform: rotate(180deg);
		}
	}
	.select-options.mobile {
		.fa-chevron-down {
			transform: rotate(180deg);
		}
		~ {
			.popup {
				z-index: 1500;
				display: block;
				+ {
					.popup-content {
						opacity: 1;
						z-index: 1600;
						bottom: 0;
					}
				}
			}
		}
	}
	.popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: -1;
		display: none;
	}
	.popup-content {
		position: fixed;
		top: auto;
		left: 50%;
		right: auto;
		bottom: -100%;
		transform: translateX(-50%);
		z-index: -1;
		opacity: 0;
		background: #fff;
		box-shadow: 0 4px 30px rgb(0 0 0 / 30%);
		border-radius: 20px 20px 0 0;
		padding: 24px 0;
		transition: all 0.5s ease-in-out;
		max-height: 100%;
		.content {
			min-height: 21.25rem;
		}
		.checkbox-list {
			margin: 24px 0;
			.quarter {
				border-left: none;
				padding: 0 1rem;
				min-width: 180px;
				@media (max-width: 320px) {
					min-width: 8.75rem;
				}
			}
			.year {
				padding: 0 1rem;
				min-width: 180px;
				@media (max-width: 320px) {
					min-width: 8.75rem;
				}
			}
		}
		.type {
			display: flex;
			overflow: auto;
			max-height: 60vh;
		}
		&::before {
			content: "";
			position: absolute;
			width: 65px;
			height: 2px;
			background: #ded4d4;
			top: 8.35px;
			left: 50%;
			transform: translateX(-50%);
		}
		.title {
			padding: 0 17px 8px;
			border-bottom: 1px solid #f5f5f5;
			font-size: 16px;
			line-height: 24px;
			font-weight: 700;
		}
		.btn {
			padding: 0 16px;
			button {
				background-color: #000;
				color: #fff;
				padding: 12px 16px;
				border-radius: 8px;
				cursor: pointer;
				border: none;
				transition: all 0.3s ease-in;
				width: 100%;
				line-height: 1.5;
				font-weight: 600;
				font-size: 1rem;
				text-align: left;
				&:hover {
					background-color: var(--gray-600);
					color: #fff;
				}
			}
		}
	}
	.select-category-filter {
		.checkbox-list {
			width: 100%;
		}
	}
}

/* End Of Service Fee Component */
