/* Currency Exchange Priority component */
.currency-converter {  
  border-radius: 8px;
  height: 100%;
  box-shadow: 0 33px 181px rgba(0,0,0,.04), 0 13.7866px 75.6175px rgba(0,0,0,.029), 0 7.37098px 40.4287px rgba(0,0,0,.024), 0 4.13211px 22.664px rgba(0,0,0,.02), 0 2.19453px 12.0367px rgba(0,0,0,.016), 0 0.913195px 5.00873px rgba(0,0,0,.011);

  .priority-exchange__container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    height: 100%;
  }
  
  .priority-exchange__panel {
    flex-grow: unset;
    max-width: 100%;
    flex-basis: unset;
    padding-right: 0;
    height: 100%;
    // Change by Adobe
    background-color: var(--primary-background);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .priority-exchange__suggestion {
    flex-grow: 0;
    max-width: 33.333333%;
    flex-basis: 33.333333%;
  }
  
  .panel-item {
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    padding: 32px 24px 32px 24px;
    height: 100%;
    background: unset;
    color: var(--body);
  }

  .panel-item {
    .dark & {
      background: linear-gradient(128.02deg, #e2ded7 -104.26%, #8d8175 59.55%);
      color: #fff;
    }    
  }
  
  .panel-item__title {
    margin-bottom: 20px;
  }
  
  .exchange-section__transaction-type-selector {
    margin-bottom: 24px;
  }
  
  .exchange-section__currency-selector {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  
  .exchange-section__note {
    margin-top: 16px;
    text-align: left;
    text-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    font-style: italic;
  }
  .exchange-section__error {
    display: none;
    margin-top: 16px;
    text-align: left;
    font-family: SF Pro Display Italic;
    font-style: italic;
    color: #ed1b24;
  }
  
  .exchange-section__label {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }
  
  .exchange-section__tab-wrapper {
    display: inline-flex;
    border: 1px solid #e2ded7;
    border-radius: 8px;
    max-width: 100%;
    // Change by Adobe 
    overflow: auto;
  }
  
  .exchange-section__tabs {
    padding: 8px;
    display: inline-flex;
  }
  
  .exchange-section__tabs span {
    white-space: nowrap;
    padding: 8px;
    margin-right: 12px;
    cursor: pointer;
    background-color: transparent;
    transition: all 0.1s ease;
    border-radius: 8px;
    line-height: 24px;
    text-align: center;
    width: 95%;
    display: inline-block;
  }
  
  .exchange-section__tabs .material-symbols-outlined {
    font-size: 40px;
    color: #ed1b24;
    margin-right:0;
    &.chevron_left {
      transform: rotate(180deg);
    }
  }

  .exchange-section__tabs .material-symbols-outlined {
    .dark & {
      color: #fff;
    }    
  }
  
  .single-item span {
    width: 10%;
    min-width: 60px;
  }

  .exchange-section__tabs-item:hover {
    .dark & {
      background-color: #333;
    }    
  }
  
  .exchange-section__tabs .tab-active {
    background-color: var(--gray-600);
    color: var(--primary-background);
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
  }

  .exchange-section__tabs .tab-active {
    .dark & {
      background-color: #333;
    }
    
  }
  
  .exchange-section__tabs-scroller {
    
    display: flex;
    align-items: stretch;
    padding: 8px;
    overflow: auto;
  }
  
  .exchange-section__tabs-scroller::-webkit-scrollbar {
    display: none;
  }
  
  .tcb-scroll-control {
    background-color: var(--primary-background);
    box-shadow: none;
    height: 56px;
    width: 56px;
    display: flex;
  }
  
  .tcb-scroll-control.tcb-scroll-control_next {
    .dark & {
      background-color: #91857a;
    }   
  }

  .tcb-scroll-control.tcb-scroll-control_prev {
    .dark & {
      background-color: #a3998e;
    }   
  }
  
  .tcb-scroll-control.tcb-scroll-control_prev { 
    left: -200px;
    border-left: 1px solid #e2ded7;
    transition: all 1.5s ease;
    opacity: 0;
  }
  
  .tcb-scroll-control.tcb-scroll-control_next {
    right: -200px;
    border-left: 1px solid #e2ded7;
    transition: all 1.5s ease;
    opacity: 0;
  }

  .can-prev .tcb-scroll-control_prev {
    left:0;
    transition: all 0.5s ease;
    opacity: 1;
  }

  .can-next .tcb-scroll-control_next {
    right: 0;
    transition: all 0.7s ease;
    opacity: 1;
  }
  .currency-selector__currency-item {
    flex: 0 0 48.5%;
    max-width: 48.5%;
    display: flex;
    flex-direction: column;

    &.not-found {
      .exchange-section__input {
        background-color: #d9d9d9;

        .dark & {
          background-color: hsla(0,0%,85%,.3);
        }
      }
    }
  }
  
  .currency-item__inner {
    border: 1px solid #e2ded7;
    border-radius: 8px;
    position: relative;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .currency-item__inner .exchange-section__tabs {
    width: 100%;
    display: block;
    padding: 0;
  }
  
  .exchange-section__input {
    padding: 0 16px;
    border-top: 1px solid #e2ded7;
    flex-grow: 1;
    display: flex;
    align-items: center;
    min-height: 70px;

    .item__input-fields {
      width: 100%;
    }

    input {
      width: 100%;
      background-color: transparent;
      border: transparent;
      font-size: 24px;
      line-height: 36px;
      font-weight: 700;
      color: var(--body);
      outline: none;

      .dark & {
        color: #fff;
      }

      &::placeholder {
        display: block;
        color: #616161;
        text-transform: capitalize;
        font-weight: 400;
        letter-spacing: normal;
        font-size: 14px;
        line-height: 21px;

        .dark & {
          color: #e2ded7;
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
        }
      }
    }

    label {
      color: #e2ded7;
      .dark & {
        color: var(--gray-600);
      }
    }
  }

  .scroll-currency-converter {
    display: flex;
    position: relative;
  }
  .slick-next,.slick-prev {
    position: absolute;
    visibility: hidden;
    width: 48px;
    opacity: 1;
    transition: width 1s ease;
  }
  .slick-prev.slick-disabled {
    width: 0;
  }
  
  @media (max-width: 991px) {
    .priority-exchange__panel {
      max-width: 100%;
      flex-basis: 100%;
      padding-right: unset;
    }
  
    .tab-active {
        color: var(--primary-background) !important;
    }
  }
  
  @media (max-width: 767px) {
    .panel-item {
      padding: 32px 16px;
    }
  
    .panel-item__title {
      margin-bottom: 24px;
    }
  
    .exchange-section__tabs {
      overflow: auto;
    }
  
    .priority-exchange__suggestion {
      max-width: 100%;
      flex-basis: 100%;
    }
  
    .currency-selector__currency-item {
      flex: 0 0 100%;
      max-width: 100%;
    }
  
    .currency-selector__currency-item:first-child {
      margin-bottom: 16px;
    }
  
    .scroll-currency-converter {
      width: calc(100% + 40px);
      margin-left: 0;
      overflow:hidden;
    }
  }
}

/* End Currency Exchange Priority component */
