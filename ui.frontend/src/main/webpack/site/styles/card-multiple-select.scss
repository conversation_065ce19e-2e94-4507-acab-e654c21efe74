/* Card Product Filter */
.card-multiple-select {
	min-height: 90px;
	-ms-overflow-style: none;
	scrollbar-width: none;
	::-webkit-scrollbar {
		display: none;
	}
	.card-multiple-select__content {
		background-color: #fff;
		margin-bottom: 12px;
		width: 100%;
		padding: 12px 0;
		filter: drop-shadow(0 5px 22px rgba(0, 0, 0, 0.1));
	}
	.card-multiple-select__content.no-margin {
		margin-bottom: 0;
	}
	.card-multiple-select__container {
		display: flex;
		flex-wrap: nowrap;
		box-sizing: inherit;
		flex-grow: 0;
		flex-basis: 100%;
		align-items: center;
		h6 {
			margin-right: 16px;
			white-space: nowrap;
			font-size: 16px;
			font-weight: 600;
			line-height: 20px;
		}
	}
	.card-multiple-select__items {
		display: flex;
		align-items: center;
		flex-grow: 1;
		flex-wrap: wrap;
		overflow-x: auto;
	}
	.filter__item {
		padding: 8px;
	}
	.card-multiple-select__button {
		justify-content: center;
		border: 1px solid #dedede;
		border-radius: 27px;
		color: var(--gray-600);
		font-weight: 400;
		padding: 12px 15px;
		background-color: transparent;
		position: relative;
		display: inline-flex;
		outline: none;
		cursor: pointer;
		white-space: nowrap;
		text-decoration: none;
		transition: all 0.3s ease-in;
		align-items: center;
		grid-gap: 12px;
		gap: 12px;
		min-width: max-content;
		z-index: 1;
		line-height: 24px;
		margin: 0;
	}
	.card-multiple-select__button.big-size {
		color: var(--body);
		font-size: 16px;
		padding: 12px 24px;
	}
	.card-multiple-select__button.big-size.filter-selected {
		background-color: #000;
		color: #fff;
	}
	.filter-selected {
		background-color: #000;
		color: #fff;
	}
}
@media (max-width: 767px) {
	.card-multiple-select {
		.card-multiple-select__container {
			flex-wrap: nowrap;
			overflow-y: hidden;
		}
    .card-multiple-select__title {
      padding-bottom: 16px;
    }
		.card-multiple-select__items {
			flex-wrap: nowrap;
      .filter__item {
        padding: 4px;
      }
		}
		.card-multiple-select__button {
			max-width: 265px;
			display: block;
			white-space: normal;
		}
    &.wrap {
      .card-multiple-select__container {
        flex-wrap: wrap;
      }
      .card-multiple-select__items {
        flex-wrap: wrap;
      }
    }
	}
}

/* End of Card Product Filter component */