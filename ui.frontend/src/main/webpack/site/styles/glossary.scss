.glossary {
  position: relative;
  &-header {
    margin-bottom: 3rem;
    .header {
      &-description {
        margin-top: 1rem;
      }
    }
  }

  &-alphabet {
    display: flex;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: #f5f6f8;
    padding: 1rem 0;
    z-index: 1;
    @include maxSm {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: scroll;
    }

    .active {
      text-decoration: underline;
      color: var(--primary-red);
      font-weight: 600;
      width: 40px;
      height: 46px;
      display: inline-flex;
      border-radius: 0.25rem;
      background-color: #dedede;
      text-align: center;
      justify-content: center;
      line-height: 46px;
    }

    a {
      white-space: nowrap;
      font-size: 1.25rem;
      line-height: 46px;
      font-weight: 500;
      color: var(--secondary-grey-80);
      width: 40px;
      text-align: center;

      @include maxSm {
        padding: 0 13px;
      }
    }
  }

  &-container {
    margin-top: 3rem;
    .container-title {
      font-size: 2rem;
      line-height: 3rem;
    }
    .glossary-card {
      .content {
        &-subtitle {
          margin-top: 2rem;
          &::first-letter {
            text-transform: capitalize;
          }
        }

        &-description {
          margin-top: 2rem;
        }
      }

      .line-break {
        margin-top: 1rem;
        border: 1px solid var(--secondary-mid-grey-80);
        &-unset {
          border: unset;
        }
      }
    }
  }

  .pagination {
    text-align: center;
    font-size: 0;
    padding: 1rem 0;
    display: flex;
    justify-content: flex-end;

    &-index {
      font-size: 1rem;
      text-decoration: none;
      display: inline-block;
      margin: 0 0.25rem;
      height: 1.5rem;
      min-width: 1.25rem;
      line-height: 1.5rem;
      padding: 0;
      color: var(--secondary-grey-60);
      display: none;

      &:first-child,
      &:last-child {
        margin: 0;
        min-width: 3rem;
      }

      &:last-child,
      &:nth-child(2),
      &:nth-last-child(2),
      &:nth-last-child(3) {
        display: inline-block;
      }

      &:only-child {
        background-color: var(--primary-red);
        color: var(--primary-white);
        border-radius: 0.25rem;
        min-width: 1.25rem;
      }
    }

    &:not([actpage="1"]) {
      .pagination-index:nth-child(1) {
        display: inline-block;
      }
    }
  }
  @for $i from 1 through 80 {
    .pagination[actpage="#{$i}"] {
      // 2 before
      .pagination-index:nth-child(#{$i - 2}):not(:first-child):not(
          :nth-child(2)
        ) {
        display: inline-block;
        pointer-events: none;
        color: transparent;
        border-color: transparent;
        width: 50px;
        &:after {
          content: "...";
          color: var(--secondary-grey-60);
          font-size: 32px;
          margin-left: -6px;
        }
      }

      // 2 before
      .pagination-index:nth-child(#{$i - 1}):not(:first-child) {
        display: inline-block;
      }
      // before
      .pagination-index:nth-child(#{$i}):not(:first-child) {
        display: inline-block;
      }
      // active
      .pagination-index:nth-child(#{$i + 1}) {
        background-color: var(--primary-red);
        color: #fff;
        display: inline-block;
        border-radius: 0.25rem;

        + .pagination-index:last-child {
          display: none !important;
        }
      }
      // next
      .pagination-index:nth-child(#{$i + 2}):not(:last-child) {
        display: inline-block;
      }
      // 2 next
      .pagination-index:nth-child(#{$i + 3}):not(:last-child) {
        display: inline-block;
      }
      .pagination-index:nth-child(#{$i + 4}):not(:last-child):not(
          :nth-last-child(2)
        ) {
        display: inline-block;
        pointer-events: none;
        color: transparent;
        border-color: transparent;
        width: 50px;
        &:after {
          content: "...";
          color: var(--secondary-grey-60);
          font-size: 32px;
          margin-left: -6px;
        }
      }
    }
  }
}
