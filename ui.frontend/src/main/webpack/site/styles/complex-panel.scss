.complex-panel {
  .complex-panel__body-content {
    color: #fff;
    position: relative;
  }

  .body-content__container {
    position: relative;
  }

  .image-desktop {
    display: block;
  }

  .image-mobile {
    display: none;
  }

  .body-content__container img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
  }

  .content-item__wrapper {
    width: 100%;
    display: flex;
    max-width: 1440px;
    padding-left: 64px;
    padding-right: 64px;
    margin: 0 auto;
  }

  .content-item__panel {
    z-index: 1;
    padding: 138px 0;
    justify-content: flex-start;
    align-items: center;
    width: calc(100% + 24px);
    margin: -12px;
    display: flex;
    flex-wrap: wrap;
  }

  .content-item__panel-text {
    z-index: 200;
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
    padding: 12px;
    line-height: 1;
  }

  .content-item__panel-text h3 {
    font-size: 24px;
    line-height: 36px;
    font-weight: 600;
    margin: 0;
  }

  .panel-text__information {
    max-width: 635px;
  }

  .complex-title {
    color: inherit;
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.5rem;
    line-height: 1.5;
  }

  .panel-text__information .complex-information {
    margin: 16px 0;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }

  .panel-text__information .complex-date {
    font-weight: 600;
    margin: 0;
  }

  .content-item__card-image {
    padding: 0;
    height: 100%;
    flex: 1 1;
    position: absolute;
    right: 0;
    width: 100%;
    max-width: 50%;
  }

  .card-image__container {
    display: flex;
    flex-direction: row;
    height: 100%;
    justify-content: flex-end;
    flex-wrap: nowrap;
    width: 100%;
  }

  .card-image__left-site {
    flex-grow: 0;
    max-width: 58.333333%;
    flex-basis: 58.333333%;
  }

  .card-image__two-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .card-image__image {
    border-radius: 8px 8px 0 0;
    height: calc(50% - 12px);
    position: relative;
    overflow: hidden;
  }

  .card-image__image:first-child {
    border-radius: 0 0 8px 8px;
  }

  .card-image__right-site {
    flex-grow: 0;
    flex-basis: 41.666667%;
    max-width: 243px;
    margin-left: 24px;
    margin-top: auto;
    margin-bottom: auto;
  }

  .card-image__one-card {
    display: flex;
    margin-top: auto;
    margin-bottom: auto;
  }

  .card-image__one-card .card-image__image {
    width: 100%;
    height: 325px;
    border-radius: 8px 0 0 8px;
  }
  @media (max-width: 1199px) {
    .content-item__wrapper {
      padding-left: calc(100vw / 22.5);
      padding-right: calc(100vw / 22.5);
    }
  }
  @media (max-width: 767px) {
    .content-item__panel-text {
      padding: 8px;
    }
    .image-desktop {
      display: none;
    }

    .image-mobile {
      display: block;
    }

    .content-item__panel {
      padding: 0;
      height: 741px;
      flex-direction: column-reverse;
      width: calc(100% + 16px);
      margin: -8px;
    }

    .content-item__panel-text {
      max-height: 433px;
      padding-top: 40px;
      max-width: 100%;
      flex-basis: 100%;
    }

    .panel-text__information .complex-information {
      margin-top: 16px;
      font-size: 24px;
      -webkit-line-clamp: 7;
    }

    .content-item__card-image {
      max-height: 308px;
      top: 0;
      max-width: 100%;
    }

    .card-image__container {
      justify-content: space-between;
    }

    .card-image__right-site {
      margin-left: 0;
      max-width: unset;
    }

    .card-image__image:first-child {
      border-radius: 0;
    }

    .card-image__image {
      border-radius: 0;
      height: calc(50%);
    }

    .card-image__one-card .card-image__image {
      max-height: 308px;
    }
  }
}
