.network-grid-component {
  background-color: white;
  .network-grid-component__container {
    display: flex;
    flex-direction: column;
    > .title {
      font-weight: 300;
      font-size: 1.75rem;
      line-height: 1.25;
      margin-bottom: 12px;
    }
    .network-grid {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      position: relative;
      width: 100%;
      .slideshow {
        max-width: 58.333333%;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 504px;
        .item-list {
          border-radius: 8px;
        }
        .item {
          position: absolute;
          height: 100%;
          max-height: 528px;
          border-radius: 8px;
          width: 100%;
          opacity: 0;
          transition: opacity 400ms;
          img {
            width: 100%;
            height: 100%;
            object-position: center center;
            object-fit: cover;
          }
          .content {
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: rgb(33, 33, 33);
            color: white;
            padding: 32px 24px;
            box-shadow: rgba(0, 0, 0, 0.06) 0px 106px 98px -36px;
            border-radius: 8px 0 8px;
            width: 60%;
            height: auto;
            .tag {
              color: rgb(237, 27, 36);
              width: 60%;
              letter-spacing: 2px;
              text-transform: uppercase;
              font-weight: 600;
              font-size: 0.875rem;
              line-height: 1.5;
            }
            h3 {
              font-size: 1.5rem;
              line-height: 1.5;
              font-weight: 600;
              margin-bottom: 8px;
              margin-top: 8px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-word;
            }
          }
          &:before {
            position: absolute;
            content: "";
            height: 4px;
            width: 0px;
            background-color: rgb(237, 27, 36);
            bottom: 0px;
            left: 0px;
            z-index: 1;
            transition: all 5s ease 0s;
          }
          &.active {
            opacity: 1;
            &:before {
              width: 100%;
              transition: all 5s ease 0s;
            }
          }
        }
      }
      .panel-list {
        display: flex;
        flex-direction: column;
        width: 40%;
        .panel {
          padding: 16px 24px;
          border-radius: 8px;
          background-color: rgb(242, 242, 242);
          display: flex;
          align-items: center;
          width: 100%;
          margin: 12px 0;
          outline: none;
          border: 1px solid transparent;
          .content {
            display: flex;
            flex-flow: column;
            flex: 1 1;
            justify-content: center;
            .title {
              margin: 0;
              font-size: 24px;
            }
            p {
              color: rgb(97, 97, 97);
            }
          }
          .number {
            margin-right: 25px;
            width: 84px;
            height: 60px;
            text-align: center;
            span {
              font-weight: 300;
              font-size: 1.75rem;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
  @media (max-width: 991px) {
    .network-grid-component__container .network-grid {
      flex-direction: column;
      .slideshow {
        max-width: 100%;
        height: 357px;
        .item {
          width: 100%;
          height: 357px;
          .content {
            width: 100%;
            border-radius: 0 0 8px;
          }
        }
      }
      .panel-list {
        width: 100%;
        padding-top: 24px;
        margin-top: 32px;
      }
    }
  }
  @media (max-width: 767px) {
    .network-grid-component__container .network-grid {
      .slideshow .item .content {
        padding: 24px 24px 32px;
        .title {
          margin-top: 5px;
          margin-bottom: 16px;
        }
      }
      .panel-list {
        margin-top: 0;
      }
      .panel-list .panel .number {
        width: 66px;
        height: 56px;
      }
      .slideshow .item-list .description {
        white-space: nowrap;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
      }
    }
  }
  @media (max-width: 575px) {
    .network-grid-component__container .network-grid .panel-list .panel .number {
      margin-right: 15px;
    }
  }
}
