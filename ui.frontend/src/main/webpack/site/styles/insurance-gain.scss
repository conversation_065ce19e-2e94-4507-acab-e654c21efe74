.insurance-gain {
  &__container {
    width: 100%;
    display: flex;
    margin: 0 auto;
  }

  &__panel {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
  }

  .panel-inputs {
    background-color: var(--primary-white);
    width: 59%;
    display: inline-block;
    padding: 32px 24px; //added by Adobe
    border-radius: 8px 0 0 8px;
  }

  .input-items {
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + 16px);
    margin: -8px;
  }

  .item__label {
    flex-grow: 0;
    max-width: 41.666667%;
    flex-basis: 41.666667%;
    box-sizing: inherit;
    padding: 8px;
    margin: auto 0; //added by Adobe

    h3 {
      display: flex;
      align-items: center;

      .icon-info {
        position: relative;
        line-height: 0;
        margin-left: 12px;

        img {
          height: 16px; //change by Adobe
          width: 16px; //change by Adobe
          object-fit: cover;
          object-position: center center;
        }
      }
    }
  }

  .label__footnote {
    color: var(--gray-600);
    max-width: 190px;
    font-weight: 400;
    font-style: italic;
    line-height: 24px;
  }

  .item__input-fields {
    position: relative;
    flex-grow: 0;
    max-width: 58.333333%;
    flex-basis: 58.333333%;
    padding: 8px;

    .tcb-input--save-calc {
      padding: 20px 0;

      .tcb-input-range_bar-wrapper {
        .tcb-input-range_bar {
          .tcb-input-range_thumb {

            .tcb-input-range_inline-value {
              transform: scale(1) translateY(-10px);
              top: 44px;
              left: calc(-50% - 5px);
              z-index: 1;
              position: absolute;
              font-size: 0.75rem;
              transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
              line-height: 1.2;
              letter-spacing: 0.01071em;
              transform-origin: bottom center;
              padding: 7.8px 21.2px;
            }
          }
        }
      }

      .tcb-input-range_labels {
        margin-top: 2px;
      }
    }
  }

  .input-fields__date-time-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: inherit;
  }

  .date-time-wrapper__input-field {
    width: 100%;
    border: 0;
    margin: 0;
    display: inline-flex;
    padding: 0;
    position: relative;
    min-width: 0;
    vertical-align: top;
    border-radius: 8px;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    align-items: center;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    input {
      width: 100%;
      height: 19px;
      margin: 0;
      display: block;
      padding: 18.5px 14px;
      min-width: 0;
      box-sizing: content-box;
      letter-spacing: inherit;
      animation-duration: 10ms;
      -webkit-tap-highlight-color: transparent;
      border-color: var(--secondary-light-grey-100);
      border-style: solid;
      border-width: 1px;
      border-radius: inherit;

      &::placeholder {
        opacity: 0.42; //change by Adobe
      }

      &:focus {
        outline: 0.1em solid #0a84ff;
      }
    }
  }

  .date-time-wrapper__separator {
    margin: 0 12px;
  }

  .input-fields__error-msg {
    color: var(--primary-red);
    font-weight: 400;
    line-height: 24px;
  }

  .input-fields__drop-down {
    width: 120px;
    background-color: var(--primary-white);
    box-sizing: inherit;
  }

  .drop-down__container {
    width: 100%;
    border: 0;
    margin: 0;
    display: inline-flex;
    padding: 0;
    position: relative;
    min-width: 0;
    flex-direction: column;
    vertical-align: top;
    box-sizing: inherit;
  }

  .drop-down__controls {
    width: 100%;
    position: relative;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    display: inline-flex;
    box-sizing: border-box;
    align-items: center;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    form {
      width: 100%;
    }

    input {
      left: 0;
      width: 100%;
      bottom: 0;
      opacity: 0;
      position: absolute;
      pointer-events: none;
    }

    img {
      position: absolute;
      right: 17px;
    }
  }

  .drop-down__select {
    padding: 16px;
    border: 1px solid var(--secondary-light-grey-100);
    border-radius: 8px;
    background-color: var(--primary-white);
    position: relative;
    height: 56px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    overflow: hidden;
    min-height: 1.1876em;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    min-width: 16px;
    user-select: none;
    width: 100%;
    letter-spacing: inherit;
    animation-duration: 10ms;
    -webkit-tap-highlight-color: transparent;
    appearance: none;

    span {
      margin-right: 14px;
      white-space: nowrap;
    }
  }

  .input-field__currency-field {
    width: 100%;
    border: 0;
    margin: 0;
    display: inline-flex;
    padding: 0;
    position: relative;
    min-width: 0;
    vertical-align: top;
    border-radius: 8px;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    align-items: center;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    input {
      width: 100%;
      height: 19px;
      margin: 0;
      display: block;
      padding: 18.5px 14px;
      min-width: 0;
      background: none;
      box-sizing: content-box;
      letter-spacing: inherit;
      animation-duration: 10ms;
      -webkit-tap-highlight-color: transparent;
      border-color: var(--secondary-light-grey-100);
      border-style: solid;
      border-width: 1px;
      border-radius: inherit;

      &:focus {
        outline: 0.1em solid #0a84ff;
      }
    }
  }

  .date-time-wrapper__input-extra {
    height: 0.01em;
    display: flex;
    max-height: 2em;
    align-items: center;
    white-space: nowrap;
    position: absolute;
    right: 14px;
  }

  .currency__place-holder {
    color: rgba(0, 0, 0, 0.54);
    line-height: 24px;
    letter-spacing: 0.00938em;
  }

  .panel-info {
    position: relative;
    display: inline-block;
    padding: 40px 0;
    width: 41%;

    span {
      display: block;
    }

    img {
      position: absolute;
      inset: 0;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: cover;
      border-radius: 0 8px 8px 0;
    }

    //added by Adobe
    .cmp-button__icon{
      position: relative;
      min-width: auto;
    }

  }

  .panel-info__content {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    padding: 0 40px;
  }

  .panel-info__content-text {
    display: flex;
  }

  .panel-info__content-button {
    color: var(--primary-white);
    height: auto;
    margin-top: 32px;

    a {
      background-color: var(--primary-white);
      color: var(--primary-black);
      border: 1px solid #404040;
      justify-content: space-between;
      max-width: 328px;
      max-height: 56px;
      /* width: 100%; --> add class width-100 if need*/
      height: 100%;
      border-radius: 8px;
      padding: 16px 24px;
      line-height: 24px;
      display: inline-flex;
      align-items: center;
      transition: all 0.3s ease-in-out;
      text-decoration: none;
      cursor: pointer;
      font-weight: 600;
    }
    .cta-button {
      width: 100%;
    }
  }

  .content-button__icon-arrow {
    height: 16px;
    width: 16px;
    position: relative;
    margin-left: 12px;
  }

  .info-content-text__icon {
    position: relative;
    span {
      width: 92px; // changed by adobe
      height: 63px; // changed by adobe
      position: relative;
    }
    &.img-medium span {
      width: 99px;
      height: 69px;
    }
  }

  .info-content-text__label {
    margin-left: 8px;
    color: var(--primary-white);
    display: inline-block;
    flex: 1;

    h3 {
      overflow-wrap: anywhere;
      font-size: 24px;
      line-height: 36px;
      margin: 0;
      padding: 0;
    }

    > div {
      color: var(--secondary-light-grey-100);
      margin-bottom: 8px;
      margin-top: 24px;
      font-weight: 300;
    }

    > h3 {
      max-width: 100%;//change by Adobe
      overflow-wrap: break-word;
    }

    p {
      color: var(--secondary-light-grey-100);
      font-weight: 300;
      margin-bottom: 8px;
    }

    .info-title {
      font-weight: 300;
      margin-bottom: 8px;
      margin-top: 24px;
    }

  } 

  input[type="range"] {
    outline: none !important;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0;
    /* <-- Apparently some margin are still there even though it's hidden */
  }

  input[type="number"] {
    -moz-appearance: textfield;
    /* Firefox */
  }

  .select-option {
    position: absolute;
    display: none;
    width: 100%;
    background: white;
    padding-top: 8px;
    padding-bottom: 8px;
    z-index: 2;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
  }

  .select-option.opened {
    display: block;
  }

  .option {
    line-height: 1.5;
    letter-spacing: 0.00938em;
    padding: 6px 16px;
    cursor: pointer;

    &:hover {
      font-weight: 600;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

  .option.selected {
    background-color: rgba(0, 0, 0, 0.08);
  }

  .insurance-gain__content input:focus,
  .drop-down__select:focus {
    outline: 0.1em solid lightskyblue;
  }

  @media (max-width: 991px) {
    .panel-inputs {
      width: 100%;
      border-radius: 8px 8px 0 0;
    }

    .panel-info {
      width: 100%;

      img {
        border-radius: 0 0 8px 8px;
      }
    }
    //change by Adobe
    .info-content-text__icon {
      position: relative;
      span {
        width: 80px;
        height: 55px;
      }
    }
  }

  @media (max-width: 767px) {
    padding: 16px 0 32px;
    

    .label__text {
      position: relative;
      .icon-info {
        position: static;
      } 
    } 

    .insurance-gain__panel {
      padding-top: 8px;

      .panel-inputs {
        padding: 32px 16px;//change by Adobe

        .input-items {

          .item__label {
            padding-bottom: 0;
          }
        }
      }
    }

    .item__label {
      max-width: 100%;
      flex-basis: 100%;
    }

    .item__input-fields {
      max-width: 100%;
      flex-basis: 100%;
    }

    .panel-info__content-button a {
      max-width: 100%;
      width: 100%;
      margin: 0;
    }
  }

  @media (max-width: 575px) {
    .panel-info__content {
      padding: 0 16px;
    }

    /* .panel-info__content-text { //changed by adobe 
       flex-direction: column; 
    } */

    .info-content-text__label {
      margin-left: 8px; //changed by adobe

      >div {
        margin-bottom: 8px;
        margin-top: 24px;
      }
    }
  }

  @media (max-width: 390px) {
    .panel-inputs {
      padding: 24px 8px;
    }

    .item__label {
      padding-bottom: 0;
    }
  }
}
