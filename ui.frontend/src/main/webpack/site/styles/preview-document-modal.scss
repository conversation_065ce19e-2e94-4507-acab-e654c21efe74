.popup-download {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.6);
  overflow-x: hidden;
  overflow-y: auto;
  display: none;

  .popup-content {
    position: relative;
    max-width: 54.125rem;
    width: calc(100% - 4rem);
    max-height: calc(100% - 4rem);
    margin: auto;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 0.25rem;
    box-shadow:
      0 0.688rem 0.938rem -0.438rem rgb(0 0 0 / 20%),
      0 1.5rem 2.375rem 0.188rem rgb(0 0 0 / 14%),
      0 0.563rem 2.875rem 0.5rem rgb(0 0 0 / 12%);

    .head {
      padding: 0.5rem 0.5rem 0.5rem 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;

      span.title {
        font-size: var(--heading3-font-size);
        line-height: var(--heading3-line-height);
        font-weight: var(--heading3-font-weight);
      }

      .close-btn {
        color: rgba(0, 0, 0, 0.54);
        padding: 0.75rem;
        flex: 0 0 auto;
        background-color: inherit;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        transition: 0.2s;

        &:hover {
          background-color: rgba(128, 128, 128, 0.082);
        }

        .button-label {
          width: 100%;
          display: flex;
          align-items: inherit;
          justify-content: inherit;
        }
      }
    }

    .loading {
      display: none;
      text-align: center;
      padding: 0.5rem 1.5rem;
    }

    .file-content {
      flex: 1 1 auto;
      padding: 0.5rem 1.5rem;
      overflow-y: auto;

      canvas {
        margin: auto;
      }
    }

    .foot {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1.5rem 0;
      margin: 0 auto;
      line-height: 1.5rem;

      a {
        display: inline-flex;
        cursor: pointer;
        align-items: center;
        gap: 0.75rem;

        &:hover {
          text-decoration: underline;
        }

        img {
          max-width: 1rem;
        }
      }
    }

    &.video {
      max-height: unset;
      margin-top: calc(1.75rem + 4.5rem);
      width: calc(100% - 1rem);

      .head {
        border-bottom: 1px solid #e9ecef;

        span.title {
          font-size: var(--subtitle);
        }

        .close-btn:hover {
          background-color: unset;
        }
      }

      .file-content {
        padding: 1rem;
        overflow-y: visible;

        .video-container {
          position: relative;

          &:before {
            padding-top: 56.25%;
            display: block;
            content: "";
          }

          iframe {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
          }
        }
      }

      .foot {
        display: none;
      }
    }
  }
}
