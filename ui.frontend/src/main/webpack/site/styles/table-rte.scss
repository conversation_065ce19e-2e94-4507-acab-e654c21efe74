.cmp-text,
.financialhighlights {
  .statistics-table-component {
    min-height: 310px;
  }
  table:has(tr.gray-striped:first-child) {
    tr:nth-child(even) {
      background: var(--primary-white);
    }
  }

  table:has(tr.alternate:first-child) {
    tr:nth-child(odd):not(:first-child) {
      background: var(--primary-white);
    }
  }

  table:has(tr.large-cell:first-child) {
    td,th {
      padding: 24px;
    }
  }

  table:has(tr.medium-cell:first-child) {
    th {
      padding: 24px 24px 8px;
    }
  }

  table:has(tr.small-cell:first-child) {
    td {
      padding: 0.5rem 1rem;
    }
  }

  table:has(tr.alternate:first-child),
  table:has(tr.gray-striped:first-child) {
    tr:first-child {
      border: solid 1px rgb(128, 128, 128);
    }

    th,
    td {
      border: none;
      border-bottom: 1px solid rgba(224, 224, 224, 1);
    }

    th {
      text-align: left;

    }

    td {
      &:nth-child(3n+3),
      &:first-child {
        width: 25%;
      }
    }

    &:has(tr.free-width:first-child) {
      td {
        width: unset;
      }
    }
  }

  table {
    border-color: var(--secondary-mid-grey-60);

    tr, th, td {
      &.red {
        background: #e64d4d;
        color: var(--primary-white);
      }

      &.white {
        background: var(--primary-white);
        color: var(--primary-black);
      }

      &.black {
        background: var(--primary-black);
        color: var(--primary-white);
      }

      &.blue {
        background: var(--primary-navy-blue);
        color: var(--primary-white);
      }

      &.gray {
        background: var(--secondary-light-grey-100);
        color: var(--secondary-grey-60);
      }

      &.light-gray {
        background: var(--secondary-light-grey-80);
        color: var(--secondary-grey-60);
      }
    }

    td, th {
      padding: 16px;
    }

  }
}
