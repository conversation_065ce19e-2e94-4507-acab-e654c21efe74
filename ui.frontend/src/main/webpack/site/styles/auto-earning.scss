.auto-earning-component {
  .auto-earning-calculator {
    .profit-simulation_label {
      font-weight: 300;
      font-size: 2rem;
      line-height: 2.5rem;
      margin-bottom: 1rem;
    }

    .description {
      font-weight: 400;
      font-size: 1rem;
      line-height: 1.5rem;
      margin-bottom: 2rem;
    }

    details {
      summary {
        margin-bottom: 2rem;
        display: flex;
        width: fit-content;
        cursor: pointer;
        &::after {
          content: "";
          min-width: 18px;
          height: 11px;
          background: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg");
          background-size: cover;
          margin-left: 0.75em;
          transition: 0.2s;
          position: relative;
          top: 5px;
        }

        &::-webkit-details-marker {
          display: none;
        }
      }
      .daily-income-radio {
        label {
          color: var(--secondary-grey-60);
        }
      }
    }

    details[open] > summary::after {
      transform: rotate(180deg);
    }

    .label {
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.5rem;
      margin-bottom: 0.25rem;

      .monthly-spending-captions {
        color: red;
      }

      .caption {
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.5rem;
        color: var(--secondary-grey-60);

        @include maxSm {
          display: block;
        }
      }
    }

    .questions-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .questions {
        flex: 0 0 48%;

        @include maxSm {
          flex: 0 0 100%;
        }

        .item__input-fields {
          width: 100%;
          .input-field__currency-field {
            width: 100%;
            border: 0;
            margin: 0;
            display: inline-flex;
            padding: 0;
            position: relative;
            min-width: 0;
            vertical-align: top;
            border-radius: 0.5rem;
            color: rgba(0, 0, 0, 0.87);
            cursor: text;
            align-items: center;
            font-weight: 400;
            line-height: 1.1876em;
            letter-spacing: 0.00938em;

            input {
              font: inherit;
              width: 100%;
              height: 19px;
              margin: 0;
              display: block;
              padding: 1rem;
              min-width: 0;
              box-sizing: content-box;
              border-style: solid;
              border-width: 1px;
              border-radius: inherit;
              border-color: var(--secondary-light-grey-100);
            }
          }

          .currency__place-holder {
            font-weight: 400;
            line-height: 1.5rem;
          }
        }

        
      }

      .input-balance {
        position: relative;
      }
      input,
      select {
        padding: 1rem;
        border: 1px solid;
        border-radius: 0.25rem;
        height: 3.6rem;
        width: 100%;
      }
    }

    .spent-content {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      gap: 1rem;
      .item__input-fields {
        flex: 0 1 75%;
      }

      .dropdown__wrapper {
        margin-bottom: unset;
        flex: 0 1 25%;
        &.expanded {
          .dropdown__button {
            img {
              transform: rotate(180deg);
              transition: transform 500ms;
            }
          }

          .dropdown__content {
            display: flex;
            flex-direction: column;
            transform-origin: top;
            transform: scaleY(1);
            border-radius: 0.5rem;
          }
        }

        .dropdown__button {
          line-height: 19px;
          padding: 1rem 1.5rem;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border: 1px solid var(--secondary-light-grey-100);
          background: var(--primary-white);

          .display__text{
            padding-right: 0.5rem;
          }

          img {
            transform: rotate(0);
            transition: transform 500ms;
          }

          &:hover {
            cursor: pointer;
          }
        }

        .dropdown__content {
          position: absolute;
          z-index: 1;
          box-shadow: 0 5px 8px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--light-background-hover);
          background-color: var(--primary-background);
          width: 100%;
          display: none;
          transform: scaleY(0);

          ul {
            list-style-type: none;
            padding-inline-start: unset;
            padding: 0.5rem 0;
          }

          .dropdown__item {
            padding: 0.5rem 1.5rem;
            align-content: center;
            border-bottom: unset;
            max-height: 2.5rem;

            &:hover,
            &.selected {
              background-color: rgba(0, 0, 0, 0.08);
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .error-message {
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5rem;
    background-color: #ffe2e0;
    padding: 0.75rem 1rem;
    color: var(--primary-red);
    margin-top: 1rem;
    border-radius: 0.5rem;
  }

  .result-container {
    display: flex;
    flex-wrap: wrap;

    @include maxSm {
      flex-direction: column-reverse;
    }
    .try-again-button {
      flex: 0 0 100%;

      .cta-button--light {
        margin-top: 2px;
        .cta-button {
          background: var(--primary-white);
          color: var(--primary-black);
          width: 20.5rem;

          &:hover {
            background: var(--primary-black) !important;
            color: var(--primary-white) !important;
          }

          @include maxSm {
            width: 100%;
          }
        }
      }
    }

    .auto-earning-result-container {
      flex: 0 0 100%;
      margin-top: 2rem;
      @include maxSm {
        margin-top: unset;
      }
      .auto-earning-result {
        background-image: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/auto-earning-background-desktop.png");
        padding: 2rem;
        background-repeat: no-repeat;
        background-size: cover;

        @include maxSm {
          padding-left: 1rem;
          padding-right: 1rem;
          background-image: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/auto-earning-background-mobile.png");
          background-size: cover;
          border-radius: 0.5rem;
        }
        .profit {
          display: flex;
          padding-bottom: 3rem;
          justify-content: space-between;

          @include maxSm {
            flex-wrap: wrap;
            padding-bottom: unset;
          }

          .auto-profit,
          .demand-profit {
            flex: 0 0 49%;

            @include maxSm {
              flex: 0 0 100%;
            }

            .description {
              font-weight: 400;
              font-size: 1rem;
              line-height: 1.5rem;
              color: #e3e4e5;
            }

            .profit-activated {
              font-weight: 600;
              font-size: 1.5rem;
              line-height: 1.75rem;
            }

            .profit-not-activated {
              font-weight: 200;
              font-size: 1.5rem;
              line-height: 1.5rem;
              height: 1.75rem;
            }

            .color-white {
              color: var(--primary-white);
            }
          }

          .auto-profit {
            @include maxSm {
              margin-bottom: 2rem;
            }
          }
        }
      }
    }
  }

  .cta-button--dark {
    display: flex;
    .cta-button {
      background: var(--primary-black);
      border: 1px solid var(--primary-black);
      color: var(--primary-white);
      width: 20.5rem;

      &:hover {
        background: var(--primary-white) !important;
        color: var(--primary-black) !important;
      }

      @include maxSm {
        width: 100%;
      }
    }
  }

  .cta-button {
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    align-items: center;
    border-radius: 0.5rem;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-weight: 600;
    gap: 9pt;
    justify-content: space-between;
    min-width: 200px;
    padding: 1rem 1.5rem;
    text-decoration: none;
    transition: unset;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
  }

  #daily-income {
    accent-color: red;
  }

  .red-asterisk {
    color: red;
  }

  .w-100 {
    width: 100%;
  }

  .w-300 {
    width: 300px;
  }

  .m-r-24 {
    margin-right: 1.5rem;
  }

  .m-b-16 {
    margin-bottom: 1rem;
  }

  .p-r-48 {
    padding-right: 3rem;
  }

  .p-b-16 {
    padding-bottom: 1rem;
  }

  .display-table {
    display: table;
    table-layout: fixed;
    width: 100%;
  }

  .text-ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    @include maxSm {
      white-space: unset;
    }
  }

  .hide {
    display: none !important;
  }

  .hide-mobile {
    @include maxSm {
      display: none;
    }
  }

  .hide-desktop {
    display: none !important;
    @include maxSm {
      display: block !important;
    }
  }

  .disabled {
    pointer-events: none;
    .dropdown__button {
      background: #efefef4d !important;
      .display__text {
        color: #757575;
      }
    }
  }
}
