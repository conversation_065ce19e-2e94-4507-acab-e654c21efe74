.list-row {
  .list-row-content {
    display: grid;
    row-gap: 16px;
    column-gap: 24px;
    padding-bottom: 12px;

    &_item {
      display: flex;
      padding: 16px;
      gap: 16px;
      justify-content: space-between;
      border-bottom: 1px solid var(--light-border);
      border-radius: 8px;
      background: #fff;
      box-shadow: 0px 0.91319px 5.00873px 0px rgba(0, 0, 0, 0.01), 
                  0px 2.19453px 12.03668px 0px rgba(0, 0, 0, 0.02), 
                  0px 4.13211px 22.66401px 0px rgba(0, 0, 0, 0.02), 
                  0px 7.37098px 40.42872px 0px rgba(0, 0, 0, 0.02), 
                  0px 13.78661px 75.61747px 0px rgba(0, 0, 0, 0.03), 
                  0px 33px 181px 0px rgba(0, 0, 0, 0.04);
    }

    &_main {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      gap: 24px;
      min-width: 0;
    }

    &_type {
      word-break: break-word;
    }

    &_title {
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .arrow-right {
        fill: var(--accent);
        display: flex;
        align-items: center;
        padding-left: 40px;
        padding-right: 75px;
        img {
          width: 10px;
          height: 16px;
        }
      }
    }

    &_date {
      display: flex;
      color: var(--accent);
      font-size: 14px;
      line-height: 21px;
      font-weight: 600;
      letter-spacing: 2px;
      text-transform: uppercase;
      align-items: center;
    }

    &_divider {
      align-self: center;
      border-left: 1px solid #c5c5c5;
      height: 11px;
      margin: 0 16px;
    }

    @media screen and (max-width: 991px) {
      &_title {
        margin-right: 0;
      }
    }

    @media screen and (max-width: 767px) {
      display: flex;
      flex-direction: column;
      &_title {
        .arrow-right {
          padding-right: 0;
        }
      }
      &_date {
        font-size: 12px;
        line-height: 14px;
      }
      &_divider {
        margin: 0 13px;
      }
    }
  }

  .title-cmp__title {
    font-size: 28px;
  }
}
.listrow.medium-height {
  .sectioncontainer .tcb-content-container {
    margin: 0;
  }
  .list-row-content {
    row-gap: 0;

    .list-row-content_item {
      border-bottom: 1px solid #dedede;
      border-radius: 0;
      padding: 32px 0 44px;
      background-color: transparent;
      box-shadow: none;

      .list-row-content_arrow {
        padding-right: 75px;

        @media screen and (max-width: 767px) {
          padding-right: 0;
        }
      }
    }
  }
}