.appointment-booking {
  h3.personal,
  h3.bussiness,
  .address-list .item h3,
  .time-list .item h3,
  .service-list .item h3 {
    font-size: 16px;
    font-weight: 400;
  }

  .search-filter {
    display: flex;
    width: calc(100% + 24px);
    margin: -12px;
    flex-wrap: wrap;
    margin-bottom: 0;
  }

  .search-filter .filter {
    padding: 12px;
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
    position: relative;
  }

  .filter .option {
    border: 1px solid #e3e4e6;
    border-radius: 8px;
    padding: 12px 16px;
    position: relative;
    z-index: 1;
    &:hover {
      font-weight: unset;
    }
  }

  .filter input {
    pointer-events: none;
    border: none;
    background-color: transparent;
    font-size: 16px;
    width: 100%;
  }

  .filter svg, .filter img {
    color: #ed1b24;
    position: absolute;
    top: 43%;
    right: 25px;
    width: 10px;
    height: 10px;
    border-top: 2.5px solid #ed1b24;
    border-left: 2.5px solid #ed1b24;
    transform: translateY(-50%) rotate(225deg);
    z-index: -1;
  }

  .filter path {
    display: none;
  }

  .filter.disable .option {
    background-color: #d9d9d9;
    pointer-events: none;
  }

  .filter.disable .option svg, .filter.disable .option img {
    border-color: #616161;
  }

  .filter .select-options {
    opacity: 0;
    z-index: -1;
    min-width: 197px;
    max-width: calc(100% - 32px);
    max-height: 400px;
    min-height: 16px;
    position: absolute;
    overflow-x: hidden;
    overflow-y: auto;
    box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%),
      0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
    transition: opacity 337ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
      transform 224ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    border-radius: 4px;
    background-color: white;
  }

  .filter .option.showed + .select-options {
    opacity: 1;
    z-index: 10;
  }

  .filter .option.showed svg, .filter .option.showed img {
    border-color: #616161;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
  }

  .filter .select-options ul {
    padding-top: 8px;
    padding-bottom: 8px;
    list-style: none;
    padding-inline-start: 0;
  }

  .filter .select-options li {
    min-height: auto;
    width: auto;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    line-height: 1.5;
    letter-spacing: 0.00938em;
    padding: 6px 16px;
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    cursor: pointer;
  }

  .filter .select-options li.selected {
    background-color: rgba(0, 0, 0, 0.08);
  }

  .filter .select-options li:hover {
    background-color: rgba(0, 0, 0, 0.04);
    font-weight: 600;
  }

  .popup {
    transition: all .3s ease-in-out;

    &.show {
      &::-webkit-scrollbar {
        background: transparent;
        width: 0px;
      }
      .popup__container {
        -webkit-animation: displayPopup 1s ease normal;
      };
      display: flex;
      opacity: 1;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      outline: 0;
      overflow-x: hidden;
      overflow-y: auto;
      opacity: 1;
      z-index: 999;
      transition: all .3s ease-in-out;
      display: flex;
      align-items: flex-start;
      pointer-events: none;
    }

    &.hide {
      .popup__container {
        -webkit-animation: hidePopup 1s ease normal;
      };
    }
  }

  .popup {
    .popup__container {
      max-width: 564px;
      position: relative;
      margin: auto;
      display: flex;
      flex-direction: column;
      background-color: white;
      width: 100%;
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.2);
    }

    .popup__header {
      padding: 8px 28px;
      @media (max-width: 767px) {
        padding: 8px 16px;
      }
    }
    .popup__content {
      padding: 24px 28px 24px!important;
      @media (max-width: 767px) {
        padding: 24px 16px 24px!important;
      }
    }
  }

  .booking .title {
    display: flex;
    flex-direction: row;
    padding: 8px 28px;
    background: #f5f6f8;
    justify-content: space-between;
    border-radius: 12px 12px 0 0;
    @media (max-width: 768px) { 
      padding: 8px 16px;
    }
  }

  .booking .title h2 {
    font-size: 24px;
    line-height: 36px;
  }

  .booking .title span {
    background-color: transparent;
    cursor: pointer;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    opacity: 0.5;
    display: flex;
    align-items: center;
  }

  .booking .schedule {
    padding-bottom: 50px;
  }

  .booking .schedule .date {
    display: flex;
    flex-direction: row;
    margin-bottom: 24px;
    grid-column-gap: 16px;
    column-gap: 16px;

    @media (max-width: 768px) { 
      column-gap: 1px;
    }
  }

  .booking .schedule .date div, 
  .booking .schedule .date h3 {
    width: 100%;
    height: 48px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    letter-spacing: 2px;
    color: #fff;
    border: 1px solid #e3e4e6;
    border-radius: 28px;
    padding: 12px 24px;
    color: var(--gray-600);
    font-size: 16px;
    line-height: 24px;
    letter-spacing: normal;
    font-weight: 400;
    text-align: center;
    @media (max-width: 768px) { 
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-right: 10px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 400;
      text-transform: capitalize;
    }
  }

  .booking .schedule .time p:first-child {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.25;
  }

  .booking .schedule .time h6:first-child {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.25;
    margin-bottom: 1pc;
  }

  .schedule .time-list {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    grid-gap: 16px;
    gap: 16px;
    max-height: 350px;
    overflow: auto;
  }

  .schedule .time-list .item {
    width: 30%;
    height: 48px;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: normal;
    text-transform: uppercase;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e3e4e6;
    padding: 12px 24px;
    margin-top: unset;
    @media (max-width: 575px) {  
      width: 45%;
    }
  }

  .schedule .time-list .item.enable {
    cursor: pointer;
  }

  .form {
    display: none;
    &.active {
      display: flex;
      flex-direction: column;
    }
    .input-form {
      margin-top: 24px;
      display: flex;
      flex-direction: column;
      grid-gap: 24px;
      gap: 24px;
    }

    .recaptcha {
      padding: unset;
    }
  }

  .form h3,
  .form h6 {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.25;
    margin-bottom: unset;
  }

  .form label {
    font-size: 16px;
    text-transform: unset;
  }

  .form .input {
    max-height: 83px;
    .form-error-message {
      display: none;
      line-height: 12px;
      font-style: normal;
      font-weight: unset;
    }
    span {
      color: red;
    }
    &.error {
      max-height: 103px;
    }
  }

  .form input[type="text"] {
    margin-top: 8px;
    height: 56px;
    border: 1px solid #e3e4e6;
    border-radius: 8px;
    padding: 16px;
    background-color: #fff !important;
    width: 100%;
    font-size: 16px;
    line-height: 24px;
  }

  .form .captcha {
    margin-top: 24px;
    margin-bottom: 16px;
  }

  .form input[type="submit"] {
    margin: auto;
    padding: 8px 22px;
    border-radius: 28px;
    background-color: transparent;
    border: 1px solid #dedede;
    cursor: pointer;
    display: flex;
    justify-content: center;
    font-size: 13.3333px;
    margin-bottom: unset;
    margin-top: unset;
  }

  .form input[type="submit"]:hover {
    background-color: grey;
  }
  

  .booking .title {
    display: flex;
    flex-direction: row;
    padding: 8px 28px;
    background: #f5f6f8;
    justify-content: space-between;
    border-radius: 12px 12px 0 0;
    @media (max-width: 768px) { 
      padding: 8px 16px;
    }
  }
  .type,
  .name,
  .address
  {
    p:first-child, h6:first-child {
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.25;
    }
  }
  .type,
  .time {
    height: 100%;
    > p {
      margin-bottom: 16px;
    }
  }
  
  .name {
    padding-bottom: 4px;
  }
  .address {
    padding-top: 0;
    .address-list {
      overflow: auto;
      height: 100%;
      margin-top: 16px;
      overflow-x: hidden;
      max-height: 300px;
      .item {
        &:nth-child(1) {
          margin-top: unset;
        }
      }
    }
  }

  .service {
    .service-list {
      max-height: 450px;
      overflow: auto;
      .item:last-child {
        margin-bottom: 5px;
      }
    }
  }

  .item {
    cursor: pointer;
    margin-top: 16px;
    color: #616161;
    height: 48px;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e3e4e6;
    padding: 12px 24px;
    span, h3 {
      width: calc(100% - 20px);
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      line-break: normal;
      position: relative;
    }
  }
  .search-filter {
    flex-direction: column;
    margin-top: 16px;

    .filter {
      max-width: 100%;
      flex-basis: 100%;
      &:first-of-type {
        padding-top: 0;
      }
      .select-options {
        width: calc(100% - 24px);
        max-width: none;
      }
      .option {
        padding: 5px 24px 8px 16px;
        &.showed {
          svg {
            border-color: #ed1b24;
          }
          img {
            border-color: #ed1b24;
          }
        }
      }
    }
  }

  .dropdown-text {
    height: 41px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .option.selected {
    background-color: #fff;
    .dropdown-label {
      font-size: 12px;
    }
  }

  .dropdown-label {
    color: #616161;
  }
  .dropdown-body {
    color: #212121;
  }
  
  .form {
    .input {
      input {
        &:focus {
          outline: 4px solid #daecff;
        }
      }

      &.error {
        input {
          color: #ed1b24;
          border-color: #ed1b24;
        }
      }
    }
  }

  .booking-container-wrapper {
    display: flex;
    flex-direction: row;
    gap: 16px;

    .booking-container , .confirm-appointment {
      transition: all .3s ease-in-out;
      transform: translateZ(0);
      &.hidden {
        display: none;
        visibility: hidden;
        width: 0px;
        opacity: 0;
        &:not(.active) {
          padding: unset!important;
        }
      }
      
      &.active {
        visibility: visible;
        width: 100%;
        opacity: 1;
        display: block!important;
      }
    }
  }

  .confirm-appointment {
    display: none;
    height: 541px;
    .confirm-content-wrapper {

      .comfirm-img-wrapper {
        height: 85px;
        width: auto;
        position: relative;
        margin-bottom: 40px;
        > span {
          box-sizing: border-box;
          display: block;
          overflow: hidden;
          width: initial;
          height: initial;
          background: none;
          opacity: 1;
          border: 0px;
          margin: 0px;
          padding: 0px;
          position: absolute;
          inset: 0px;
          img {
            position: absolute;
            inset: 0px;
            box-sizing: border-box;
            padding: 0px;
            border: none;
            margin: auto;
            display: block;
            width: 0px;
            height: 0px;
            min-width: 100%;
            max-width: 100%;
            min-height: 100%;
            max-height: 100%;
            object-fit: contain;
            object-position: left center;
          }
        }
      }
      .confirm-label {
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.25;
        &.ticket-number {
          margin-top: 24px;
        }
      }
      .confirm-ticket-number {
        color: #0a84ff;
        margin-top: 8px;
      }

      .confirm-note{
        margin-top: 16px;
        color: #a2a2a2;
        font-weight: 300;
      }
    }
  }

  
  .loading {
    display: none;
    position: relative;
    width: 80px;
    height: 80px;
    z-index: 2;
    transform: translateX(-50%) translateY(-50%);
    left: 50%;
    top: 50%
  }

  .loading div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border-radius: 50%;
    -webkit-animation: loading_ring 1.2s cubic-bezier(.5,0,.5,1) infinite;
    animation: loading_ring 1.2s cubic-bezier(.5,0,.5,1) infinite;
    border: 8px solid transparent;
    border-top-color: #fff
  }

  .loading div:first-child {
    -webkit-animation-delay: -.45s;
    animation-delay: -.45s
  }

  .loading div:nth-child(2) {
    -webkit-animation-delay: -.3s;
    animation-delay: -.3s
  }

  .loading div:nth-child(3) {
    -webkit-animation-delay: -.15s;
    animation-delay: -.15s
  }

  @-webkit-keyframes loading_ring {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
  }

  @keyframes loading_ring {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
  }

  .loading_backdrop {
    height: 100%;
    width: 100%;
    background-color: rgba(0,0,0,.3);
    position: absolute;
    top: 0;
    display: none;
  }

  .loading_backdrop {
    left: 0;
    background-color: rgba(0,0,0,.08);
    z-index: 3
  }

  .loading {
    position: absolute;
    z-index: 4
  }

  .error-response {
    color: red;
  }
}

@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(50%, 0, 0);
    transform: translate3d(50%, 0, 0);
    visibility: hidden;
  }

  55% {
    visibility: hidden;
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@-webkit-keyframes displayPopup{
  from {
      transform: translateY(-110%);
  }
  to {
      transform: translateY(0%);
  }
}

@-webkit-keyframes hidePopup{
  to {
      transform: translateY(-110%);
  }
}

.hide-animate {
  display: none!important;
}

//Changed by adobe
#captchaError{
  display: none;
}
.contact-form {
  .popup__container {
    width: 400px;
    max-width: 100%;
    .cta-button  {
      width: 100%;
    }
  }
}