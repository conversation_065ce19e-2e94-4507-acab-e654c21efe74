/* Form Information */
.form-container {
  padding-bottom: 16px;
  margin-bottom: 40px;
  border-bottom: 1px solid var(--secondary-light-grey-100);
}

.form-container.last-child {
  border: none;
  padding: 0;
  margin: 0;
}

form {
  .title-cmp__subtitle {
    padding-bottom: 20px;
  }

  .form-error-message, #captchaError{ // added by adobe
    color: var(--primary-red);
    margin-top: 8px;
    font-size: 12px;
  }

  .radio-wrapper,
  .checkbox-wrapper {
    display: grid;
    gap: 24px;
    grid-template-columns: repeat(2, 1fr);
    margin-top: 0;

    &.small {
      gap: 16px;
    }
  }

  .radio-wrapper .radio-item .radio-label,
  .checkbox-wrapper .checkbox-item .checkbox-label {
    display: flex;
    align-items: center;
    text-transform: none;
    letter-spacing: normal;
    color: var(--gray-600);
    font-weight: 400;
  }

  .checkbox-wrapper {
    .checkbox-item .checkbox-label {
      input {
        margin-right: 1rem;
        max-width: 24px;
        height: 24px;
        flex: 0 0 24px;
        -webkit-appearance: none;
        border-radius: 5px;
        outline: none;
        border: 1px solid var(--secondary-light-grey-100);
        position: relative;
        background-color: var(--primary-white);

        &:checked {
          border-color: var(--primary-red);
          background-color: var(--primary-red);

          &::before {
            opacity: 1;
          }
        }

        &::before {
          opacity: 0;
          content: "";
          position: absolute;
          width: 0.4rem;
          height: 0.9rem;
          top: 50%;
          left: 50%;
          border: solid var(--primary-white);
          border-width: 0 0.1875rem 0.1875rem 0;
          transform: rotate(45deg) translate(-0.55rem, -0.25rem);
        }
      }

      input[disabled] {
        background-color: var(--cta-disabled);
      }
    }

    &.circle .checkbox-item .checkbox-label input {
      border-radius: 20px;
    }
  }

  .radio-wrapper .radio-item .radio-label {
    input {
      margin-right: 1rem;
      max-width: 24px;
      height: 24px;
      flex: 0 0 24px;
      -webkit-appearance: none;
      border-radius: 50%;
      outline: none;
      border: 1px solid var(--secondary-light-grey-100);
      position: relative;
      background-color: var(--primary-white);

      &:checked {
        border-color: var(--primary-red);
        background-color: transparent;
        &::before {
          background-color: var(--primary-red);
        }
      }

      &::before {
        content: "";
        position: absolute;
        width: 0.9375rem;
        height: 0.9375rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: transparent;
        border-radius: 50%;
      }
    }

    input[disabled] {
      background-color: var(--cta-disabled);
    }

  }

  .list-dropdown {
    position: absolute;
    top: 0;
    max-height: 400px;
    overflow: auto;
    min-width: 100%;
    z-index: 5;
    list-style: none;
    background-color: var(--primary-white);
    border-radius: 8px;
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
    cursor: default;
    
    li[disabled] {
      background-color: var(--cta-disabled);
      pointer-events: none;
    }
    .dropdown-item {
      padding:16px;
      cursor: pointer;
      transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

      &:hover {
        text-decoration: none;
        background-color: rgba(0, 0, 0, 0.04);
        p {
          font-weight: 600;
        }
      }

      &:first-child {
        padding-top: 12px;
      }
      &:last-child {
        padding-top: 16px;
      }

      span {
        p {
          font-weight: 400;
          line-height: 1.5;
        }
      }
    }
  }

  .dropdown-wrapper {
    width: 100%;
    position: relative;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    display: inline-flex;
    align-items: center;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    &.disabled {
      cursor: default;
    }

    input {
      left: 0;
      width: 100%;
      bottom: 0;
      opacity: 0;
      position: absolute;
      pointer-events: none;
    }

    img {
      color: var(--primary-red);
      position: absolute;
      top: 50%;
      right: 20px;
      width: 10px;
      height: 10px;
      border-top: 2.5px solid var(--primary-red);
      border-left: 2.5px solid var(--primary-red);
      transform: translateY(-50%) rotate(225deg);
      pointer-events: none;
      fill: currentColor;
      display: inline-block;
      font-size: 1.5rem;
      transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      flex-shrink: 0;
      user-select: none;
    }
  }

  .dropdown-inputbase {
    width: 100%;
    padding: 16px;
    border: 1px solid var(--secondary-light-grey-100);
    border-radius: 8px;
    background-color: var(--primary-white);
    color: rgba(0, 0, 0, 0.87);
    position: relative;
    height: 56px;
    display: flex;
    align-items: center;
    min-height: 1.1876em;
    cursor: pointer;
    min-width: 16px;
    user-select: none;
    -webkit-appearance: none;
    animation-duration: 10ms;
    -webkit-tap-highlight-color: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .dropdown-placeholder {
      width: 92%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #0000008a;
    }

    .dropdown-viewvalue {
      width: 92%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--body);
    }
    .dropdown-viewValue {
      white-space: break-spaces;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      &:not(.hidden) {
        width: 100%;
        padding-right: 26px;
      }
    }
  }

  .recaptcha,
  .button {
    padding: 12px 0;
    .form-modal & {
      padding: 0;
    }
  }

  .disabled {
    .dropdown-inputbase {
      pointer-events: none;
      background-color: var(--cta-disabled);

      > img {
        border-top: 2.5px solid var(--light-secondary-text);
        border-left: 2.5px solid var(--light-secondary-text);
      }
    }
  }

  .width-100 {
    width: 100%;
    flex-basis: 100%;
  }

  .width-50 {
    width: calc(50% - 12px);
    flex-basis: calc(50% - 12px);
  }

  .width-80 {
    width: 80%;
    flex-basis: 80%;
  }

  @media (max-width: 991px) {
    .width-80 {
      width: 100%;
      flex-basis: 100%;
    }
    .radio-wrapper,
    .checkbox-wrapper {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  @media (max-width: 767px) {
    .width-50 {
      width: 100%;
      flex-basis: 100%;
    }
  }
}

.dropdown-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 4;
  cursor: default;
}

.error input::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: var(--primary-red);
  opacity: 1; /* Firefox */
}

.error input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--primary-red);
}

.error input::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--primary-red);
}

.error input,
form .error .dropdown-inputbase,
.text-input-wrapper.error .input-field__currency-field input,
.item__input-fields.input-date-field.error .input-field__currency-field input,
.text-input-wrapper.error input {
  border: 1px solid var(--primary-red);
}

form .error .dropdown-inputbase .dropdown-placeholder,
.error .dropdown-inputbase .dropdown-placeholder {
  color: var(--primary-red);
}

.date-time-wrapper__input-extra {
  position: absolute;
  right: 16px;
  pointer-events: none;
}

.date-time-wrapper__input-extra .material-symbols-outlined {
  cursor: pointer;
  pointer-events: visible;
}
