.form-survey {
  &__single-choice {
    .single-choice {
      &__container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        &--question {
          display: flex;
          align-items: center;
          color: #000000;

          .question {
            &--title {
              font-weight: 600;
              font-size: 1rem;
              line-height: 1.5rem;
            }
          }
        }

        &--answer {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          .answer {
            &__item {
              display: flex;
              align-items: start;

              &.display-1-column {
                flex-basis: 100%;
              }

              &.display-2-column {
                flex-basis: 49%;

                @media (max-width: 768px) {
                  flex-basis: 100%;
                }
              }

              &--input {
                background-color: white;
              }

              &--text {
                margin-left: 0.5rem;
                font-size: 1rem;
                font-weight: 400;
                line-height: 1.5rem;
                color: #616161;
              }
            }
          }
        }
      }
    }
  }


  &__multiple-choice {
    .multiple-choice {
      &__container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        &--question {
          display: flex;
          align-items: center;
          color: #000000;

          .question {
            &--title {
              font-weight: 600;
              font-size: 1rem;
              line-height: 1.5rem;
            }

            &--caption {
              font-weight: 400;
              font-size: 0.875rem;
              line-height: 1.313rem;
            }
          }
        }

        &--answer {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          .answer {
            &__item {
              display: flex;
              align-items: start;
              width: unset;

              &.display-1-column {
                flex-basis: 100%;
              }

              &.display-2-column {
                flex-basis: 49%;

                @media (max-width: 768px) {
                  flex-basis: 100%;
                }
              }

              &--text {
                font-size: 1rem;
                font-weight: 400;
                line-height: 1.5rem;
                color: #616161;
              }
            }
          }
        }

        &--answer-alert {
          font-size: 0.875rem;
          color: var(--primary-red);
        }
      }
    }
  }
}
