.nav-equal-panel {
  position: relative;
  &.overlay {
    position: relative;
    min-height: 136px;
    margin-top: -100px;
  }
  
  .with-cta-container {
    > picture {
      box-sizing: border-box;
      display: block;
      overflow: hidden;
      width: initial;
      height: initial;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      position: absolute;
      inset: 0px;
      > img {
        position: absolute;
        inset: 0px;
        box-sizing: border-box;
        padding: 0px;
        border: none;
        margin: auto;
        display: block;
        width: 0px;
        height: 0px;
        min-width: 100%;
        max-width: 100%;
        min-height: 100%;
        max-height: 100%;
        object-fit: cover;
        object-position: center center;
      }
    }
    .with-cta-wrapper {
      text-align: center;
    }
    .nav-equal__title {
      display: flex;
      color: #fff;
      text-align: center;
      padding-bottom: 12px;
      justify-content: center;
      flex-direction: column;
      width: 100%;
    }
    .with-cta-titlecontain {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 8px;
      flex-wrap: wrap;
      flex-direction: column;
      z-index: 2;
    }
    .with-cta-title {
      display: flex;
      align-items: center;
      > div {
        font-weight: 300;
        font-size: 1.75rem;
        line-height: 1.25;
        position: relative;
      }
    }
    .with-cta-subtitle {
      color: white;
      padding-bottom: 8px;
      position: relative;
    }
    .nav-equal__content {
      display: flex;
      margin-left: auto;
      margin-right: auto;
      width: 100%;
    }
    .with-cta-root {
      display: flex;
      width: calc(100% + 24px);
      margin: -12px;
      justify-content: center;
      flex-wrap: wrap;
      position: relative;
    }
    .with-cta-grid {
      padding: 12px;
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
    }
    .with-cta-innergrid {
      display: flex;
      flex-shrink: 1;
      width: calc(100% + 24px);
      margin: -12px;
      flex-wrap: wrap;
    }
    .with-cta-grid-item {
      padding: 12px;
      width: 100%;
    }

    .with-cta-event-card {
      height: 100%;
    }
    .with-cta-event-wrapper {
      border-radius: 8px;
      background-color: #fff;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 32px 52px 39px;
      > h4 {
        color: var(--gray-600);
        @extend .text-lg, .font-light;
      }
      > h2.title {
        font-weight: 600;
        font-size: 1rem;
        padding-bottom: 24px;
      }

      .padding-32-24 & {
        padding: 32px 24px;
        @media (max-width: 767px) {
          padding: 24px;
        }
      }
    }
    .with-cta-event-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &.for-mobile {
        display: none;
      }
      
    }
    .with-cta-event-nav {
      //max-width: 209px;
      margin-right: 24px;
      cursor: pointer;
      display: inline-block;
      margin: 38px 0 0;
      flex: 1 1;
      > a {
        justify-content: space-between;
        padding: 12px 15px 12px 24px;
        background-color: transparent;
        border: 1px solid #000;
        position: relative;
        display: inline-flex;
        border-radius: 8px;
        outline: none;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease-in;
        align-items: center;
        grid-gap: 12px;
        gap: 12px;
        width: 100%;
        z-index: 1;
        line-height: 1.5;
        font-weight: 600;
        font-size: 1rem;
        > span {
          cursor: pointer;
          color: black;
          width: calc(100% - 28px);
        }
        &.btn-dark {
          > span {
            color: #ffffff;
          }
          background-color: #000000;
        }
      }
    }
    .with-cta-event-svg {
      margin-left: 0px;
      display: flex;
      align-items: center;
      transition: all 0.3s ease-in-out;
      cursor: pointer;
      white-space: nowrap;
      // Adobe change
      width: 1.5rem;
      height: 1.5rem;
      flex-flow: row-reverse;
      img{
        display: block;
      }
    }
  }

  @media (min-width: 768px) {
    .with-cta-container {
      .with-cta-title > div {
        padding-right: 24px;
        margin-right: 24px;
      } 
      .with-cta-grid-item {
        flex-grow: 0;
        max-width: 50%;
        flex-basis: 50%;
      }
      .with-cta-event-nav > a:hover {
        span {
          text-decoration: underline;
        }
      }
    } 
  }

  @media (min-width: 1440px) {
    .with-cta-container .with-cta-wrapper {
      max-width: 1440px;
    }
  }

  @media (max-width: 1999px) {
    .with-cta-container {
      .with-cta-event-btn {
        flex-direction: column;
        align-items: flex-start;
      }
      .with-cta-event-nav {
        width: 100%;
        margin: 16px 0 0;
        max-width: 100%;
      }
    }
  }
  
  @media (max-width: 991px) {
    .with-cta-container .with-cta-event-wrapper {
      padding: 24px 16px;
    }
  }
  
  @media (max-width: 767px) {
    .with-cta-container .nav-equal__title .with-cta-titlecontain {
      align-items: flex-start;
    }
    &.overlay {
      margin-top: -224px;
    }
    .with-cta-container .with-cta-event-btn.for-mobile {
      display: flex;
    }
  }
  
  @media (max-width: 390px) {
    .with-cta-container {
      .nav-equal__title {
        justify-content: flex-start;
      }
    }
  }
}
