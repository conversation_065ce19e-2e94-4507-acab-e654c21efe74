.article-listing {
  padding: 0;
  margin-top: 32px;
  margin-bottom: 32px;

  @media (min-width: 992px) {
    margin-top: 48px;
    margin-bottom: 48px;
  }

  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;

  gap: 24px;

  @media (max-width: 767px) {
    gap: 16px;
  }

  &_heading {
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;

    .tag-title {
      font-weight: 300;
      font-size: 1.75rem;
      line-height: 1.25;
      line-height: 32px;
      padding-bottom: 8px;
    }
    .tag-description {
      font-size: 1rem;
      line-height: 1.25;
      color: #333;
      margin: 8px 0;
      font-weight: 300;
    }
  }

  &_list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    width: calc(100% + 24px);
    margin: -12px;

    @media (max-width: 767px) {
      width: calc(100% + 16px);
      margin: -8px;
    }

    .article-card {
      padding: 12px;
      flex-grow: 0;
      max-width: 33.333333%;
      flex-basis: 33.333333%;
  
      @media (max-width: 1199.5px) {
        flex-grow: 0;
        max-width: 50%;
        flex-basis: 50%;
      }
  
      @media (max-width: 767px) {
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
        padding: 8px;
      }

      &_container {
        box-shadow: 0 2px 8px rgba(0,0,0,.15);
        transition: all .2s linear;

        &:hover {
          box-shadow: 0 33px 181px rgba(0,0,0,.04), 0 13.7866px 75.6175px rgba(0,0,0,.029), 0 7.37098px 40.4287px rgba(0,0,0,.024), 0 4.13211px 22.664px rgba(0,0,0,.02), 0 2.19453px 12.0367px rgba(0,0,0,.016), 0 0.913195px 5.00873px rgba(0,0,0,.011);
          transform: scale(1);
        }
        &_image {
          display: flex;
          position: relative;
          padding-top: 56%;
          overflow: hidden;
        }
      }
      &_content {
        padding: 24px !important;
        @media (max-width: 767px) {
          padding: 16px !important;
        }
        &_top {
          .article-label {
            margin-bottom: 8px;
  
            @media (max-width: 991px) {
              margin-bottom: 8px;
            }
            span {
              font-weight: 600 !important;
            }
          }
          .article-title {
            margin-bottom: 8px;
            font-weight: 600 !important;
  
            @media (max-width: 991px) {
              margin-bottom: 8px;
            }
          }
          .article-content {
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  .view-more {
    max-width: 328px;
    width: 100%;
    margin: auto;
    line-height: 1.5;
    font-weight: 600;
    font-size: 1rem;
    
		.btn {
      position: relative;
      display: inline-flex;
      padding: 16px 24px;
      border-radius: 8px;
      outline: none;
      border: none;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all .3s ease-in;
      justify-content: space-between;
      align-items: center;
      grid-gap: 12px;
      gap: 12px;
      min-width: -webkit-max-content;
      min-width: -moz-max-content;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      background-color: inherit;
      @media (max-width: 767px) {
        padding: 12px 16px;
      }
			&:hover {
				background-color: #a2a2a2;
				color: var(--gray-600);
				img {
					filter: brightness(0) saturate(100%) invert(37%) sepia(0%)
              saturate(1112%) hue-rotate(136deg) brightness(95%) contrast(81%);
				}
			}
			&.btn-outline {
				border: 1px solid #000;
				background-color: transparent;
				color: #000;
				&:hover {
					background-color: #212121;
					color: #fff;
					img {
						filter: brightness(0) saturate(100%) invert(100%) sepia(0%) 
								saturate(1%) hue-rotate(306deg) brightness(103%) contrast(101%);
					}
				}
			}
			
		}
	}
}
