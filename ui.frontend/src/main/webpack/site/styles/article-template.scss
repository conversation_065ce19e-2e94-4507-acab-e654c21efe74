.article-content--wrapper {
  .content-wrapper {
    padding: 2.25rem 4rem 1rem;
    max-width: 1920px;
    width: 100%;
    background-color: white;

    @media (max-width: 992px) {
      max-width: 100%;
      padding-left: calc(100% / 22.5);
      padding-right: calc(100% / 22.5);
    }

    @media (max-width: 767px) {
      max-width: 100%;
      padding: 2rem calc(100% / 22.5);
    }

    .wrapper {
      flex-direction: column;
      align-items: center;
      max-width: 1336px;
      width: 100%;
      margin-left: auto;
      margin-right: auto;
      @media (max-width: 992px) {
        margin: 0.25rem 0 !important;
      }
      @media (max-width: 767px) {
        margin: 0.5rem 0 !important;
      }
    }
  }

  .tcb-container {
    display: flex;
    flex-direction: column;
    gap: 24px;

    &:has(.article-tag-cloud) {
      width: 63.5%;
      padding: 0 2rem;
      justify-content: center;
    }

    @media (max-width: 992px) {
      padding: .5rem .25rem !important;
      flex-direction: column;
    }
  }

  &:has(.article-tag-cloud) {
    display: flex;
    padding-top: 2.25rem;
    padding-bottom: 3rem;
  }

  &:has(.article-content) {
    @media (max-width: 991px) {
      max-width: 100%;
    }
  }

  .article-content {
    flex-grow: 0;
    max-width: 68%;
    flex-basis: 68%;
    margin: 0 auto;

    .articletableofcontent {
      padding: .75rem;
      background-color: white;
      position: sticky;
      z-index: 10;

      @media (max-width: 992px) {
        padding: .75rem 0;
      }
      @media (max-width: 767px) {
        padding: .5rem 0;
      }
    }

    .articletableofcontent:has(.table-content) + .article-text {
      h2 {
        font-size: 1.125rem;
        font-weight: 700;
        line-height: 1.25;
      }

      h3 {
        font-size: 1rem;
        font-weight: 700;
        line-height: 1.25;
      }

      h4 {
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
      }
    }

    .table-content {
      .title {
        line-height: 1.5;
      }

      &__tableItem {
        margin-top: 16px;
      }

      &__content {
        padding: 0.688rem;
      }
    }

    .article-table {
      padding: 12px;
      top: 80px !important;
      background-color: white;
      position: sticky;
      @media (max-width: 992px) {
        padding-left: .25rem;
        padding-right: .25rem;
      }
    }

    .article-text {
      margin-top: .5rem;
      padding: .75rem .75rem 0 .75rem;
      overflow-anchor: none;

      @media (max-width: 992px) {
        margin-top: 2rem;
        padding: .75rem 0 0;
      }

      @media (max-width: 767px) {
        padding: .5rem 0 0;
      }

      .tcb-sectionContainer {
        .tcb-content-container {
          margin: unset;
        }
      }

      .text-align-justify {
        text-align: justify;
      }

      a {
        color: #0a84ff;
        text-decoration: underline;

        &:visited {
          color: #551a8b;
        }
      }

      .cmp-text {
        table {
          border-collapse: collapse;
          @include maxSm {
            overflow-x: scroll;
            display: block;
          }
        }
      }

    }

    @media (max-width: 992px) {
      max-width: 100%;
      flex-basis: 100%;
    }

    p {
      padding: 0;
      text-align: justify;
    }

    figure {
      margin: 0 auto;

      img {
        width: 100%;
        display: block;
        margin: auto;
      }

      &.table {
        margin: 0 !important;
        max-width: 100% !important;
      }
    }

    img {
      width: 100%;
    }

    .cmp-image__image {
      width: var(--desktopWidth);

      @media screen and (max-width: 767px) {
        width: var(--widthMobile);
      }
    }

    h2, h3 {
      img {
        width: initial;
      }
    }
  }

  .breadcrumb {
    width: 100%;
  }

  .article-content {
    .article-tag-cloud {
      padding: 16px;
      width: 100%;
      max-width: unset;
    }
  }
}

.article-content {
  &__tag-cloud {
    background-color: white;
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding: 1rem 4rem;

    @media (max-width: 992px) {
      padding-left: 4.4444444444%;
      padding-right: 4.4444444444%;
    }

    &--wrapper {
      display: block;
      width: 100%;
      max-width: 83.5pc;
      padding: 0 .75rem;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 992px) {
        padding: unset;
      }

      .content-body {
        width: 68%;
        margin-left: auto;
        margin-right: auto;

        @media (max-width: 992px) {
          width: 100%;
          flex-basis: 100%;
          padding: unset;
        }

        .article-tag-cloud {
          padding-top: 0 !important;
          padding-bottom: 0 !important;
        }
      }
    }
  }
}

.article-content {
  &__social-share {
    background-color: white;
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding: 1rem 4rem 3rem;

    @media (max-width: 992px) {
      padding-left: 4.4444444444%;
      padding-right: 4.4444444444%;
      padding-bottom: 2rem;
    }

    &--wrapper {
      display: block;
      width: 100%;
      max-width: 83.5pc;
      padding: 0 .75rem;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 992px) {
        padding: unset;
      }

      .content-body {
        width: 68%;
        margin-left: auto;
        margin-right: auto;

        @media (max-width: 992px) {
          width: 100%;
          flex-basis: 100%;
          padding: unset;
          margin: unset;
        }

        .content-social-share {
          display: flex;
          flex-direction: row;
          gap: 1rem;
          align-items: center;
          width: 100%;

          @media (max-width: 992px) {
            width: 100%;
            max-width: 100%;
            padding: .5rem 0rem;
          }

          .social-label {
            font-weight: bold;
          }

          .social-content {
            display: flex;
            flex-direction: row;
            gap: 1.5rem;

            .content-social-share-icon-wrapper {
              &:hover {
                transform: scale(1.04);
              }

              .content-social-share-icon {
                width: 2.5rem;
                height: 2.5rem;

                @media (max-width: 992px) {
                  width: 2rem;
                  height: 2rem;
                }
              }
            }
          }
        }
      }
    }
  }
}

.article-header--navigation {
  height: 101px;
  background-color: grey;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
}

.article-header--footer {
  height: 480px;
  background-color: grey;
  width: 100%;
}

.article-header__container {
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 16px;
  margin-bottom: 16px;

  @media (min-width: 992px) {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  &--wrapper {
    width: 100%;
    display: block;
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0.5rem;
    padding-right: 0.5rem;

    @media (min-width: 768px) {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }

    @media (min-width: 320px) and (max-width: 1199.95px) {
      display: flex;
      padding-left: calc(100vw / 22.5);
      padding-right: calc(100vw / 22.5);
    }

    @media (min-width: 1200px) {
      display: flex;
      padding-left: 4rem;
      padding-right: 4rem;
    }

    @media (min-width: 1440px) {
      max-width: 1440px;
    }

    .article-header-content {
      overflow: hidden;
      width: calc(100% + 24px);
      margin: -0.75rem;
      justify-content: center;
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;

      .article-header-body {
        text-align: center;
        padding: 0.75rem;
        max-width: 100%;
        flex-grow: 0;
        flex-basis: 100%;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        @media (min-width: 768px) {
          flex-grow: 0;
          max-width: 100%;
          flex-basis: 100%;
        }

        @media (min-width: 992px) {
          flex-grow: 0;
          max-width: calc(100% * 2 / 3);
          flex-basis: calc(100% * 2 / 3);
        }

        &--title {
          font-size: 1.5rem;
          font-weight: 600;
          line-height: 1.5;
        }

        &--subTitle {
          font-weight: 600;
          font-size: 1.25rem;
          line-height: 1.25;
          color: #8d8175;

          @media (min-width: 768px) {
            margin-top: 0.5rem;
          }
        }

        &--date {
          font-weight: 600;
          font-size: .875rem;
          font-style: italic;
          line-height: 1.5;
          color: #a2a2a2;
        }
      }
    }
  }
}
