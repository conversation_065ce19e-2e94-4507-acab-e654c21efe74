.tcb-date-picker {
  padding: 32px 0 0;
  overflow: hidden;
  min-width: 310px;
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0,0,0,.1);
  border-radius: 5px;
}

.ui-datepicker {
  display: flex;
  align-items: center;
  flex-direction: column;
  min-height: 284px;
  .ui-state-hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
  .ui-datepicker-calendar {
    max-width: 325px;
    min-width: 310px;
    table-layout: fixed;
    width: 294px;
    display: flex;
    flex-direction: column;
    tbody, thead {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    tbody {
      justify-content: flex-start;
      min-height: 216px;
    }
    thead {
      margin-bottom: 6px;
    }
    th {
      font-weight: 600;
      font-size: 14px;
      color: var(--secondary-mid-grey-80);
      text-transform: uppercase;
      width: 38px;
      max-height: 16px;
      margin: 0 2px;
    }
    tr {
      display: flex;
    }
    td {
      width: 38px;
      height: 36px;
      margin: 0 2px;
      font-weight: 700;
      font-size: 14px;
      border-radius: 50%;
      >* {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        border-radius: 50%;
      }
    }
    .ui-state-highlight {
      background-color: var(--primary-white);
      color: var(--primary-black);
    }
    .ui-datepicker-unselectable, .ui-state-disabled {
      background-color: var(--primary-white);
      color: var(--secondary-mid-grey-80);
    }
    .ui-state-active {
      background-color: var(--primary-red);
      color: var(--primary-white);
    }
  }
  .ui-datepicker-header {
    position: relative;
    width: 100%;
    margin: 2px 0 20px;
  }
  .ui-datepicker-prev, .ui-datepicker-next {
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    text-align: center;
    cursor: pointer;
    overflow: hidden;
    border-radius: 50%;
    line-height: 600%;
  }
  .ui-datepicker-title {
    height: 100%;
    padding: 0 16px;
    line-height: 24px;
    font-size: 16px;
    font-weight: 700;
  }
  .ui-datepicker-prev {
    right: 40px;
    background: url('/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/calendar-left-icon.svg');
  }
  .ui-datepicker-next {
    right: 16px;
    background: url('/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/calendar-right-icon.svg');
  }
}

@keyframes red-flash-point {
  0% {
    background-color: transparent
  }

  to {
    background-color: var(--primary-red);
  }
}

@keyframes grey-flash-point {
  0% {
    background-color: transparent
  }
  to {
    background-color: var(--secondary-mid-grey-80);
  }
}
