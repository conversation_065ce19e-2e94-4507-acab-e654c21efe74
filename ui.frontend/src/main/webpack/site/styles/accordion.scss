.card-product-feature__item__description {
    max-width: 384px;
    min-width: 100%;
    color: var(--gray-600);
    font-weight: 400;
    line-height: 1.5;
    height: 0;
    overflow: hidden;
    transition: height 300ms ease-in-out;
}

.card-product-feature__item__description>.text {
    visibility: hidden;
}

.card-product-feature__item {
    border: 1px solid #dedede;
    border-radius: 8px;
    background-color: #f5f6f8;
    margin-bottom: 24px;
    word-wrap: break-word;
}

.card-product-feature__title {
    font-size: 2rem;
    font-weight: 300;
    line-height: 1.25;
    margin-top: 0;
    margin-bottom: 40px;
    background-color: white;
}

.card-product-feature__wrapper {
    background-color: white;
}

.card-product-feature__img {
    display: flex;
}

.card-product-feature__item__content__wrapper {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    line-break: strict;
}

.card-product-feature__item__content__title {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
}

.card-product-feature__item__img__on {
    display: none;
}

.card-product-feature__content {
    display: flex;
}

.card-product-feature__item__content__icon__on {
    color: #ed1b24;
}

.card-product-feature__item__content__icon__off {
    color: #ed1b24;
    display: none;
}

.card-product-feature__item__wrapper {
    display: flex;
    @include maxLgSemi {
        max-height: unset;
    }
}

.cmp-text {
    line-break: strict;
}

.card-product-feature__item__img__off,
.card-product-feature__item__img__on {
    width: 40px;
    height: 40px;
}

.card-product-feature__item__img__wrapper {
    margin-top: -4px;
    padding-right: 18px;
    max-height: 1.5rem;
    @include maxSm {
        max-height: unset;
    }
}

@media screen and (min-width: 1025px) {
    .card-product-feature__item__description {
        padding-right: 58px;
    }
}

@media (min-width: 768px) {
    .card-product-feature__accordion {
        width: 50%;
    }
    .card-product-feature__wrapper {
        padding: 64px 64px 0;
        display: flex;
        justify-content: space-between;
    }
    .card-product-feature__img {
        justify-content: flex-end;
        width: 50%;
        position: relative;
        padding-bottom: 12px;
    }
    .card-product-feature__container .card-product-feature__img .card-product-feature__title {
        display: none;
    }
    .card-product-feature__item {
        margin-bottom: 24px;
        max-width: 533px;
        padding: 16px 16px 8px;
    }
    .card-product-feature__item__description {
        padding-left: 58px;
        margin-top: 5px;
    }
}

@media (min-width: 768px) and (max-width: 1280px) {
    .card-product-feature__img img {
        position: absolute;
    }
    .card-product-feature__item__wrapper {
        max-height: 100%;
    }
}

@media (min-width: 1440px) {
    .card-product-feature__img img {
        width: 490px;
        height: 594px;
        object-fit: cover;
        object-position: center center;
    }
}

@media (min-width: 768px) and (max-width: 1440px) {
    .card-product-feature__img img {
        height: auto;
        width: 75%;
        max-width: 490px;
        max-height: 594px;
    }
    .card-product-feature__accordion {
        width: 50%;
    }
}

@media (max-width: 767px) {
    .card-product-feature__wrapper {
        display: flex;
        flex-direction: column-reverse;
    }
    .card-product-feature__item {
        padding: 24px 24px 8px;
    }
    .card-product-feature__item__description {
        padding-left: 0;
        padding-top: 16px;
    }
    .card-product-feature__img img {
        height: auto;
        width: 100%;
    }
    .card-product-feature__accordion {
        width: 100%;
    }
    .card-product-feature__wrapper {
        padding: 12px;
    }
    .card-product-feature__container .card-product-feature__accordion .card-product-feature__title {
        display: none;
    }
    .card-product-feature__img {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .card-product-feature__item__wrapper {
        align-items: center;
    }
}