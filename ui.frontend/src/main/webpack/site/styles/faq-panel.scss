/* Question Answer Component */
.faq-panel {
  .faq-panel-container {
    .question {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      padding-bottom: 20px;
      img {
        width: 24px;
        height: 24px;
      }
    }
    .read-more {
      max-width: 327px;
      margin-top: 44px;
      background-color: transparent;
      border: 1px solid #000;
      display: flex;
      justify-content: space-between;
      padding: 16px 24px;
      border-radius: 8px;
      align-items: center;
      cursor: pointer;
      white-space: nowrap;
      transition: all 0.3s ease-in;
      font-weight: 600;

      .dark & {
        background-color: $color-background;
        color: #000;
      }

      &:hover {
        background-color: black;
        color: white;
        path {
          fill: white;
        }
      }
    }
    .answer-question {
      border-bottom: 1px solid rgba(128, 128, 128, 0.363);
      padding-top: 20px;
      .answer ul {
        list-style-type: disc;
        padding-left: 30px;
      }
      &:last-child {
        border: none;
      }
      &:nth-child(n + 6) {
        display: none;
      }

      .question p {
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.5;
      }

      .answer {
        color: var(--gray-600);
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.5;
        img {
          width: 100%;
        }
        .dark & {
          color: $color-foreground-dark;
        }
      }
    }

    .question:hover {
      cursor: pointer;
    }

    .answer {
      height: 0;
      overflow: hidden;
      transition: height 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    }
  }
}
@media (max-width: 991px) {
  .faq-panel .faq-panel-container {
    flex-direction: column;
    align-items: center;
  }
}


@media (max-width: 767px) {
  .content-wrapper.faq-panel-container {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .faq-panel .faq-panel-container {
    .read-more {
      width: calc(100% - 24px);
      
    }
  } 
}

@media (max-width: 718px) {
  .content-wrapper.faq-panel-container {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}



/* End of Question Answer Component*/
.card-product-feature__item {
  line-break: anywhere;
  width: 100%;
}

.enhancedfaqpanel{
  .enhancedfaq{
    border-bottom: 1px solid rgba(128, 128, 128, 0.363);

    &:last-child {
      border: none;
    }
    @media (min-width:992px) {
      &:first-child{
        .answer-question{
          padding-top: 0;
        }
      }
    }
    .answer-question{
      &.author-margin {
        margin-bottom: 500px;
      }
    }

    .answer{
      .cta-button{
        width: max-content;
      }
    }
  }
  .read-more {
    max-width: 327px;
    margin-top: 44px;
    background-color: transparent;
    border: 1px solid #000;
    display: flex;
    justify-content: space-between;
    padding: 16px 24px;
    border-radius: 8px;
    align-items: center;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.3s ease-in;
    font-weight: 600;

    .dark & {
      background-color: $color-background;
      color: #000;
    }

    &:hover {
      background-color: black;
      color: white;
      path {
        fill: white;
      }

      img {
        filter: brightness(0) invert(1);
      }
    }
    img{
      width:24px;
      height:24px;
      scale: 1.3;
    }
  }
}

.hide-faqs .list-faq {
  > div.enhancedfaq {
    display: none;
  }
  > div:nth-child(-n + 5 of .enhancedfaq) {
    display: block; /* Hide all elements after the 5th child */
  }
}

.filter-panel-container {
  @media (max-width:991px) {
    .column-item {

      .image {
        &.section {
          margin: 0 calc(-100vw/22.5);
        }
      }
      &:first-child {
        display: flex;
        flex-direction: column-reverse;

        .image{
          &.section {
            display: flex;
            justify-content: center;
          }
        }

        .title{
            &.section {
              .tcb-title{
                margin-top: 24px;
                padding-bottom: 0;
              }
            }
          }

          .button {
            &.section {
              .cta-button{
                width: fit-content;
              }
            }
          }
        
      }
    }
  }
}

@media (max-width:991px){
  .filter-panel {
      .tcb-sectionContainer {
        .tcb-bgColor {
          .tcb-content-container {
            margin: 0;
          }
        }
      }
  }
}
