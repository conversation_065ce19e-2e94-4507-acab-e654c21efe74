$color_1: #aaa;
$color_2: var(--primary-black);
$color_3: var(--primary-white);
$color_4: var(--secondary-gray);
$color_6: #eee;
$background-color_1: rgba(0, 0, 0, 0);
$background-color_2: rgba(0, 0, 0, 0.4);
$background-color_3: #fefefe;
$background-color_4: #007bff;
$background-color_5: #6c757d;

:root {
  --main-color: #f2e1bc;
  --bg-icon: #f2ece0;
  --secondary-gray: #404040;
}

.modal-card-benefits {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100dvh;
  background-color: $background-color_1;
  opacity: 0;
  transition:
    opacity 0.3s ease-in-out,
    background-color 0.3s ease-in-out;

  * {
    box-sizing: border-box;
    word-break: break-word;
  }

  &.show {
    display: flex;
    opacity: 1;
    background-color: $background-color_2;

    .modal-content {
      opacity: 1;
    }
  }

  .modal-dialog {
    height: 100%;
    max-height: 100dvh;
    width: 100%;
    display: flex;
    padding: 2rem 1rem;
    overflow-y: auto;

    .modal-content {
      display: flex;
      flex-direction: column;
      background-color: $background-color_3;
      max-width: 37.5rem;
      max-height: 50rem;
      border-radius: 0.5rem;
      position: relative;
      margin: auto;

      .modal-card-benefits-header {
        position: relative;
        display: flex;

        .banner-modal-img {
          width: 100%;
          aspect-ratio: 600/284;
          object-fit: cover;
          border-radius: 0.5rem 0.5rem 0 0;
          @include xs {
            aspect-ratio: unset;
            height: 11.938rem;
            max-height: 11.938rem;
          }
        }

        .close-btn {
          cursor: pointer;
          position: absolute;
          top: 1.25rem;
          right: 1.25rem;

          img {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }
      .modal-card-benefits-content {
        max-height: calc(100% - 17.8125rem);
        padding: 2rem;
        @include xs {
          padding: 2rem 1rem;
        }

        .content {
          max-height: 23rem;
          overflow-y: auto;
          padding-right: 1rem;
          &::-webkit-scrollbar {
            background: var(--primary-white);
            width: 0.313rem;
          }
          &::-webkit-scrollbar-track {
            background: var(--primary-white);
            width: 0.313rem;
          }

          &::-webkit-scrollbar-thumb {
            background: var(--primary-color-gray-500);
            border-radius: 0.625rem;

            &:hover {
              background: var(--primary-color-gray-500);
            }
          }
          b {
            font-weight: 600;
          }

          h2,
          h3,
          h4 {
            margin-bottom: 1.5rem;
          }

          strong {
            font-weight: 600;
          }

          @include xs {
            max-height: 24.875rem;
          }
        }
        .sticky-btn {
          background-color: var(--primary-white);
          border: none;
          background-clip: padding-box;
          padding-top: 1.5rem;
          @include xs {
            padding-top: 1rem;
          }
        }

        .btn-card {
          text-decoration: none;
          font-weight: 600;
          display: inline-flex;
          align-items: center;
          gap: 1.125rem;
          border-radius: 0.5rem;
          padding: 1rem 1.5rem;
          position: relative;
          z-index: 10;
          @include xs {
            width: 100%;
            justify-content: space-between;
          }
        }
        .arrow-right-white {
          display: none;
        }
        .btn-card.bg-default {
          background: var(--primary-white);

          padding: 0;

          &:hover {
            span {
              text-decoration: underline;
            }
          }
        }

        .btn-card.bg-black {
          background: var(--primary-black);
          color: $color_3;
          .arrow-right-red-2 {
            display: block;
          }
          &:hover {
            background: var(--secondary-gray);
            .arrow-right-red-2 {
              display: none;
            }
            .arrow-right-white {
              display: block;
            }
          }
        }
      }
    }
  }

  .close {
    color: $color_1;
    float: right;
    font-size: 1.75rem;
    font-weight: bold;

    &:hover {
      color: $color_2;
      text-decoration: none;
      cursor: pointer;
    }

    &:focus {
      color: $color_2;
      text-decoration: none;
      cursor: pointer;
    }
  }

  .modal-header {
    padding: 0.625rem 0;
    border-bottom: 0.063rem solid $color_6;
    margin-bottom: 0.9375rem;
  }

  .modal-body {
    padding: 0.9375rem 0;
  }

  .modal-footer {
    padding: 0.9375rem 0;
    border-top: 0.063rem solid $color_6;
    margin-top: 0.9375rem;
    text-align: right;

    button {
      padding: 0.625rem 0.9375rem;
      margin-left: 0.625rem;
      border: none;
      border-radius: 0.5rem;
      cursor: pointer;
    }
  }

  .btn-primary {
    background-color: $background-color_4;
    color: $color_3;
  }

  .btn-secondary {
    background-color: $background-color_5;
    color: $color_3;
  }
}

.wrapper-card-benefits-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2.125rem;
  margin: 3rem auto;

  .title-card-benefits-container {
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 300;
  }

  .card-benefits-container {
    width: 100%;
    display: grid;
    gap: 1.5rem;

    .card-benefits-item {
      width: 100%;
      border-radius: 0.5rem;
      line-height: 0;
      position: relative;
      transition: all 0.1s linear;
      cursor: pointer;

      &:hover {
        transform: scale(1.008);
        box-shadow: 0 0.25rem 1.25rem 0 rgba(168, 133, 70, 0.5);
      }

      .card-benefits-item-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.5rem;
      }

      .card-benefits-content {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        padding: 1.5rem;
        display: flex;
        gap: 0.4375rem;
        justify-content: space-between;
        align-items: center;
        border-radius: 0.5rem;

        @include xs {
          padding: 1.25rem;
        }
        .card-benefit-content-text {
          display: flex;
          flex-direction: column;
          gap: 0.4375rem;

          .card-benefit-title {
            font-size: 1.5rem;
            line-height: 2.25rem;
            font-weight: 600;

            margin-top: auto;
            width: 100%;
          }

          .card-benefit-description {
            font-size: 1rem;
            line-height: 1.5rem;
            font-weight: 400;
            color: $color_4;
            width: 100%;
          }
        }

        .arrow-right {
          width: 1rem;
          min-width: 1rem;
          height: 1rem;
          margin-top: auto;
        }
      }
    }
  }

  .layout-1 {
    grid-template-columns: repeat(1, 1fr);
    grid-template-rows: repeat(1, 1fr);

    .card-benefits-item {
      aspect-ratio: 1312/392;

      .card-benefits-item-image {
        width: 100%;
        border-radius: 0.5rem;
      }
      .card-benefits-content {
        position: absolute;
        top: 0;
        right: 0;
        left: auto;
        width: 50%;
        height: 100%;
      }
    }
  }

  .layout-2 {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(1, 1fr);

    .card-benefits-item {
      aspect-ratio: 645/392;
    }
  }

  .layout-3 {
    display: flex;
    .card-benefits-item {
      width: calc(50% - 0.75rem);
      aspect-ratio: 645/392;
    }
    .card-benefits-item-double {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(1, 1fr);
      width: calc(50% - 0.75rem);
      gap: 1.5rem;

      .card-benefits-item {
        aspect-ratio: 310/392;
        width: auto;
      }
    }
  }

  .layout-4-5 {
    display: flex;
    flex-direction: column;

    .card-benefits-container-top {
      width: 100%;
      display: flex;
      gap: 1.5rem;

      .card-benefits-item {
        width: calc(50% - 0.75rem);
        aspect-ratio: 645/392;
      }
      .card-benefits-item-double {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(1, 1fr);
        width: calc(50% - 0.75rem);
        gap: 1.5rem;

        .card-benefits-item {
          aspect-ratio: 310/392;
          width: auto;
        }
      }
    }

    .card-benefits-container-bottom {
      width: 100%;
      display: grid;
      gap: 1.5rem;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(1, 1fr);

      .card-benefits-item {
        aspect-ratio: 645/228;

        .card-benefits-content {
          width: 17.3125rem;
          left: auto;
          top: 0;
          right: 1.5rem;
          height: 100%;
          padding: 1.5rem 0;

          .card-benefit-content-text {
            max-width: 13.8125rem;
          }
        }
      }
    }
  }
}

@include maxTabletPro {
  .wrapper-card-benefits-container {
    .layout-3 {
      display: flex;
      flex-direction: column;
      .card-benefits-item {
        width: 100%;
        aspect-ratio: 1312/392;
      }

      .card-benefits-item-double {
        width: 100%;
        .card-benefits-item {
          aspect-ratio: 645/392;
          width: auto;
          @include maxTabletPro {
            height: 22.5rem;
            width: auto;
            aspect-ratio: unset;
          }
        }
      }
    }

    .layout-4-5 {
      .card-benefits-container-top {
        flex-direction: column;

        .card-benefits-item {
          width: 100%;
          aspect-ratio: 1312/392;
        }

        .card-benefits-item-double {
          width: 100%;
          .card-benefits-item {
            aspect-ratio: 645/392;
            width: auto;
            @include maxTabletPro {
              height: 22.5rem;
              width: auto;
              aspect-ratio: unset;
            }
          }
        }
      }

      .card-benefits-container-bottom {
        display: flex;
        flex-direction: column;

        .card-benefits-item {
          width: 100%;
          aspect-ratio: 1312/392;
        }
      }
    }
  }
}

@include xs {
  .wrapper-card-benefits-container {
    .card-benefits-container {
      .card-benefits-item {
        aspect-ratio: 328/392;

        .card-benefits-content {
          bottom: 0;
          top: auto;
          width: 100%;
          height: auto;
          gap: 2rem;
        }
      }
    }

    .layout-2 {
      grid-template-columns: repeat(1, 1fr);
      grid-template-rows: repeat(2, 1fr);

      .card-benefits-item {
        aspect-ratio: 328/392;
      }
    }

    .layout-3 {
      display: flex;
      flex-direction: column;

      .card-benefits-item {
        width: 100%;
        aspect-ratio: 328/392;
      }
      .card-benefits-item-double {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        grid-template-rows: repeat(2, 1fr);
        width: 100%;
        gap: 1.5rem;

        .card-benefits-item {
          aspect-ratio: 328/392;
          width: 100%;
        }
      }
    }

    .layout-4-5 {
      .card-benefits-container-top {
        flex-direction: column;

        .card-benefits-item {
          width: 100%;
          aspect-ratio: 328/392;
        }

        .card-benefits-item-double {
          width: 100%;
          grid-template-columns: repeat(1, 1fr);
          grid-template-rows: repeat(2, 1fr);

          .card-benefits-item {
            width: 100%;
            aspect-ratio: 328/392;
          }
        }
      }

      .card-benefits-container-bottom {
        grid-template-columns: repeat(1, 1fr);
        grid-template-rows: repeat(2, 1fr);
        .card-benefits-item {
          width: 100%;
          aspect-ratio: 328/392;

          .card-benefits-content {
            height: fit-content;
            width: 100%;
            top: auto;
            left: 0;
            bottom: 0;
            right: 0;
            padding: 1.5rem;
            @include xs {
              padding: 1.25rem;
            }
            .card-benefit-content-text {
              max-width: 100%;
            }
          }
        }
      }
    }
  }
}
