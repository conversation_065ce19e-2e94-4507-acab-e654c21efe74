$light-background-menu: #f2f2f2;
/** New Hero Banner **/
.page-menu::-webkit-scrollbar {
  display: none;
}

.page-menu {
  display: inline-flex;
  list-style: none;
  margin: 0;
  padding: 0 30px;
  align-items: center;
  border-radius: 8px 8px 0 0;
  background: var(--primary-background);
  scrollbar-width: none;
  box-sizing: border-box;
  scroll-behavior: smooth;
  overflow: scroll;
  max-height: 106px;
}

.page-menu .page-menu_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  // Change from Adobe
  gap: 16px;
  white-space: nowrap;
  color: $color-inactive;
  transition: all 0.3s ease-in-out;
  border-top: 4px solid transparent;
  // Change from Adobe

  white-space: nowrap;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  padding: 8px 12px 0;
  height: 5.625rem;

  @include maxSm {
    gap: 0.5rem;
  }

  h2.tab-title {
    font-size: 16px;
    margin: 0;
    padding: 0;
    line-height: 24px;
    font-weight: 400;
  }
}

.page-menu .page-menu_item:hover {
  background-color: $light-background-menu;
  transition: all 0.3s ease-in-out;
}

.page-menu .page-menu_item--active {
  color: var(--body);
  background-color: $light-background-menu;
  transition: all 0.3s ease-in-out;
}

.page-menu .page-menu_item--active, .page-menu_item--active {
  border-color: var(--accent);
  font-weight: 700;
  h2.tab-title {
    font-weight: 700;
    line-height: 24px;
  }
  // Change from Adobe
  &--label {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
  }
}

.page-menu_item img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  object-position: center;
  transition: all 0.3s ease-in-out;
  filter: saturate(0) brightness(150%);
  opacity: 0.5;
}

.page-menu .page-menu_item--active img {
  transition: all 0.3s ease-in-out;
  filter: none;
  opacity: 1;
}

.tcb-hero-banner {
  position: relative;
  max-width: 1920px;
  margin: auto;
  display: flex;

  &.tcb-hero-banner-dark-background {
    // Change from Adobe
    .tcb-hero-banner_hero-title,
    .tcb-hero-banner_description,
    .tcb-hero-banner_description p {
      color: white;
    }
  }

  .external-component {
    @media screen and (min-width: 767px) {
      margin-bottom: auto;
      margin-top: auto;
    }
    @media screen and (max-width: 767px) {
      .tcb-hero-banner_hero-title br.mobile-no-break,
      .tcb-hero-banner_description br {
         display: none;
      }
    }
    margin-bottom: auto;
    .tcb-button {
      margin-top: 16px;
    }
  }

  &.type-small {
    min-height: 280px;

    @media screen and (max-width: 767px) {
      height: auto;
    }

    @media screen and (min-width: 768px) and (max-width: 1023px) {
      min-height: unset;
      height: 400px;
    }
  }

  &.type-medium {
    min-height: 400px;
    @media screen and (max-width: 767px) {
      height: auto;
    }

    @media screen and (min-width: 768px) and (max-width: 1023px) {
      min-height: unset;
      height: 400px;
    }
    @media screen and (min-width: 1024px) {
      height: 467px;
    }
  }

  &.type-large {
    min-height: 650px;
    @media screen and (max-width: 767px) {
      height: auto;
      min-height: 280px;
    }

    @media screen and (min-width: 768px) and (max-width: 1023px) {
      min-height: unset;
    }
  }

  &.type-tablet-small {
    @media screen and (min-width: 767px) {
      min-height: 280px;
    }
    @media screen and (max-width: 480px) {
      min-height: 280px;
    }
    @media screen and (max-width: 768px) {
      min-height: 480px;
    }
  }

  &.type-mobile-small {
    @media screen and (max-width: 767px) {
      height: 280px;
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
      min-height: unset;
      height: 280px;
    }
  }
  @media screen and (max-width: 767px) {
    &.type-mobile-medium  {
      min-height: 467px;
    }

    &.type-mobile-large  {
      min-height: 617px;
    }
  }
  @media screen and (min-width: 768px) and (max-width: 1023px) {
    &.type-mobile-medium,
    &.type-mobile-large  {
      min-height: unset;
    }
  }

  &.align-top {
    .external-component {
      @media screen and (max-width: 767px) {
        margin-bottom: auto;
        &.full-width.mobile-large {
          padding: 45px 0 290px;
        }
        &_p-0 {
          padding-top: 0 !important;
        }
      }
    }
  }

  &.align-bottom {
    .external-component {
      @media (max-width: 767px) {
        margin-bottom: 0;
        margin-top: auto;
        &.mobile-large {
          padding: 337px 0 32px;
        }
      }
    }
  }

  &.align-center {
    .external-component {
      @media (max-width: 767px) {
       margin-top: auto;
       margin-bottom: auto;
      }
    }
  }
}

.tcb-hero-banner_background {
  position: absolute;
}

.tcb-hero-banner_background-image {
  object-fit: cover;
  width: 100%;
  height: 100%;

  @media screen and (min-width: 768px) and (max-width: 1280px) {
    object-position: var(--tablet-image-position) !important;
  }
}

.tcb-hero-banner_content {
  position: relative;
  z-index: 2;
  display: flex;
  flex: 1;
  display: block;
  position: relative;
  width: 100%;
}

// Change from Adobe
.tcb-hero-banner--content-reverse.tcb-hero-banner {
  .tcb-hero-banner_customize-content--wrapper {
    @media screen and (max-width: 767px) {
      padding-top: 337px;
      padding-bottom: 32px;
    }
  }
}

.tcb-hero-banner {
  a {
    color: inherit;
    text-decoration: none;
    cursor: pointer;
  }
  &_hero-title {
    font-size: 32px;
    font-weight: 300;
    color: var(--body);

    .section-font-color & {
      color: inherit;
    }
  }
  &_hero-description {
    font-size: 1.5rem;
    color: var(--gray-600);

    .section-font-color & {
      color: inherit;
    }
  }
  &_body {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    // Change from Adobe
    @media screen and (max-width: 767px) {
      min-height: 280px;
    }
    //Change from Adobe
    &.tcb-hero-banner--remove-alignment {
      margin-top: unset;
      margin-bottom: unset;
    }
  }
  &_customize-content {
    padding: 24px 0;
    flex: 1;
    display: flex;
    flex-direction: column;

    // Change from Adobe
    &--wrapper {
      width: 100%;
      padding-top: 40px;
      padding-bottom: 40px;
      @media screen and (max-width: 767px) {
        padding-bottom: 290px;
        padding-top: 45px;
      }
    }

    // Change from Adobe
    .tcb-button {
      min-width: 328px;
      @media screen and (max-width: 360px) {
        min-width: 100%;
      }
      justify-content: space-between;
      max-width: 100%;
      gap: 1rem;
      align-items: center;
      padding: 0.75rem 1rem;

      .white-space {
        @media screen and (max-width: 767px) {
          white-space: normal;
        }
      }
    }
    // Change from Adobe
    .tcb-arrow {
      color: #ed1c24;
      font-size: 1.25rem;
    }
    // Change from Adobe
    .external-component {
      width: 40%;
      &.full-width {
        width: 100%;
        @media screen and (max-width: 767px) {
          width: 100%;
          padding: 0.5rem;
          padding-left: 0;
        }

        @media screen and (max-width: 390px) {
          padding: 0.25rem;
        }
      }
      @media screen and (max-width: 1023px) {
        width: 70%;
      }
      @media screen and (max-width: 767px) {
        width: 100%;
      }
    }
  }
}

.tcb-hero-banner_content.tcb-hero-banner_content--use-breadcrumb
  .tcb-hero-banner_customize-content {
  padding-top: 0;
  padding-bottom: 0;
}

.tcb-hero-banner_content.tcb-hero-banner_content--use-breadcrumb.tcb-hero-banner_content--use-menu
  .tcb-hero-banner_customize-content {
  padding-top: 0;
  padding-bottom: 0;
}

.tcb-hero-banner_customize-content {
  padding: 24px 0;
}

// Change from Adobe
.tcb-hero-banner_description,
.tcb-hero-banner_description p {
  font-size: 24px;
  margin: 16px 0 16px 0;
  // Change from Adobe
  color: #616161;

  .section-font-color & {
    color: inherit;
  }
}

.tcb-hero-banner_bottom-content {
  display: flex;
  // Change from Adobe
  overflow: hidden;
  position: relative;
}

.tcb-teaser_tag,
.tcb-hero-banner_tag {
  display: block;
  text-transform: uppercase;
  color: var(--accent);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: 2px;
  margin-bottom: 16px;

  .section-font-color & {
    color: inherit;
  }
}

.tcb-hero-banner_tag.priority {
  color: var(--priority-text);
}

.tcb-hero-banner_control {
  display: none;
}

.tcb-hero-banner_control {
  cursor: pointer;
  // Change from Adobe
  z-index: 99;
  height: 90px;
}

.tcb-hero-banner_control--next,
.tcb-hero-banner_control--prev {
  width: 28px;
  color: #ed1c24;
  background: linear-gradient(90deg, hsla(0, 0%, 100%, 0) 10%, #fff);
  .material-symbols-outlined {
    font-size: 1.75rem;
    font-weight: 600;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}

.tcb-hero-banner_control--next {
  display: block;
  position: absolute;
  right: 0;
}

.tcb-hero-banner_control--prev {
  position: absolute;
  display: none;
  left: 0;
  background: linear-gradient(90deg, #fff 0, #fff 1%, hsla(0, 0%, 100%, 0));
}

.tcb-hero-banner .material-symbols-outlined {
  .tcb-hero-banner_control--next & {
    right: 0;
  }
}

.tcb-hero-banner_looking-for-wrapper {
  max-width: fit-content;

  .looking-for {
    display: inline-flex;
    background-color: #fff;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1rem;
    align-items: center;
    width: 100%;
    gap: 1rem;

    &_title {
      font-weight: 600;
      font-size: 1.25rem;
      line-height: 1.2;
    }

    &_input {
      min-width: 370px;
      position: relative;
      @media screen and (max-width: 1199px) {
        min-width: 320px;
      }

      input {
        height: 3.5rem;
        border: 1px solid #e3e4e6;
        border-radius: 0.5rem;
        padding: 1rem;
        background-color: #fff;
        width: 100%;
        font-size: 1rem;
        line-height: 1.5rem;
        outline: none;
        padding-right: 2.75rem;
      }

      .wrapper {
        position: relative;
      }

      .icon {
        position: absolute;
        width: 24px;
        height: 24px;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);

        span {
          box-sizing: border-box;
          display: block;
          overflow: hidden;
          background: none;
          border: 0px;
          margin: 0px;
          padding: 0px;
          position: absolute;
          inset: 0px;
        }

        img {
          position: absolute;
          inset: 0px;
          box-sizing: border-box;
          padding: 0px;
          border: none;
          margin: auto;
          display: block;
          width: 0px;
          height: 0px;
          min-width: 100%;
          max-width: 100%;
          min-height: 100%;
          max-height: 100%;
          object-fit: cover;
          object-position: center center;
        }
      }

      &-options {
        position: absolute;
        width: 100%;

        @media screen and (max-width: 767px) {
          background-color: #fff;

          .options-active-mobile {
            margin-top: 64px;
            z-index: 100;
          }
        }

        &-roots {
          min-height: 0px;
          transition-duration: 300ms;
          height: 0;
          overflow: hidden;
          transition: height 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
        }

        &-entered {
          height: auto;
          overflow: visible;
        }

        &-hidden {
          visibility: hidden;
        }

        &-wrapper {
          display: flex;
        }

        &-wrapper-inner {
          width: 100%;
        }

        &-content {
          padding: 16px;
          margin-top: 4px;
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 8px 24px -8px rgba(0, 0, 0, 0.1);
          display: flex;
          flex-direction: column;
          grid-gap: 8px;
          gap: 8px;

          span {
            margin-bottom: 8px;
            font-weight: 600;
          }

          a {
            line-height: 24px;
            color: #616161;

            &:hover {
              color: #ed1c24;
            }
          }
        }
      }
    }

    &_submit {
      display: none;
      transition: all 0.3s;

      &.active {
        display: block;
      }

      button {
        background-color: #000;
        color: #fff;
        padding: 16px 24px;
        border-radius: 8px;
        outline: none;
        border: none;

        display: inline-flex;
        gap: 0.75rem;
        align-items: center;
        width: 100%;
        justify-content: space-between;

        cursor: pointer;
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .tcb-hero-banner_content--extra-large .tcb-hero-banner_customize-content {
    align-items: flex-start;
    justify-content: flex-start;
  }

  .tcb-hero-banner_content--extra-large .tcb-download-app-block {
    width: 100%;
    color: white;
    background-color: black;
    cursor: pointer;
  }

  .tcb-hero-banner_content--extra-large .tcb-download-app-block:hover {
    background-color: #616161;
    color: #fff;
  }

  .tcb-hero-banner_body.tcb-container {
    margin: 0;
    padding: 0 20px;
    @media screen and (max-width: 767px) {
      padding: 45px calc(100vw / 22.5) 0 calc(100vw / 22.5);
    }
  }

  .tcb-hero-banner_bottom-content {
    display: flex;
    overflow: hidden;
    margin-left: calc(-100vw / 22.5);
    margin-right: calc(-100vw / 22.5);
    background: #ffffff;
  }

  .tcb-hero-banner_bottom-content.tcb-hero-banner_full-width,
  .tcb-hero-banner_full-width ul {
    margin: 0 auto;
    width: 100%;
  }

  .tcb-hero-banner_customize-content.tcb-hero-banner_mobile-content--bottom-left {
    justify-content: flex-end;
  }

  .tcb-hero-banner_customize-content.tcb-hero-banner_mobile-content--top-left {
    justify-content: flex-start;
  }
  .page-menu {
    padding: 0 8px;
    overflow: scroll;
  }

  .tcb-hero-banner_looking-for-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    scroll-margin: 8.5rem;

    .looking-for {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;

      &_input {
        width: 100%;

        &-options-entered {
          margin-top: 4.5rem;
        }
      }

      &_submit {
        width: 100%;
        margin-top: 0;
        button {
          padding: 12px 16px;
        }
      }
    }
  }
  .active-mobile {
    position: absolute;
    height: 50%;
    left: 0;
    bottom: -3.5rem;
    background-color: #fff;
    button {
      transform: translateX(5%);
      background-color: #fff;
      width: calc(100% - 2rem) !important;
    }
  }
}

@media screen and (max-width: 991px) {
  .tcb-hero-banner_looking-for-wrapper {
    .looking-for {
      &_input {
        min-width: 18.125rem;
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .tcb-hero-banner_bottom-content.tcb-hero-banner_full-width,
  .tcb-hero-banner_full-width ul {
    margin: 0 -20px;
    width: unset;
  }
}

/** End of new hero banner **/

body.dark {
  .tcb-hero-banner_content {
    .tcb-hero-banner_body {
      .tcb-hero-banner_customize-content {
        .external-component {
          label.tcb-hero-banner_tag {
            color: var(--priority-text);
          }

          a.tcb-button {
            padding: unset;
            background-color: unset;
            border: none;
            justify-content: start;
            gap: 0.75rem;

            span.white-space {
              color: white;

              &:hover {
                text-decoration: underline;
              }
            }

            span.tcb-arrow {
              color: white;
            }
          }
        }
      }
    }
  }
}
