.cardlistresult {
  .section__margin-medium {
    margin-bottom: 0.75rem;
  }
}

.credit-card-comparison-result {
  min-height: unset;

  .content-wrapper {
    padding: unset;
  }

  @include maxMd {
    padding-bottom:  3rem;
  }
  .card-compare-header {
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 35px;
    &__wrapper {
      position: relative;
      padding: 48px 24px;
    }
    &__content {
      display: flex;
      h1 {
        font-size: 2rem;
        color: white;
        line-height: 1.25;
        font-weight: 300;
      }
    }
  }

  .background-img {
    position: absolute;
    inset: 0px;
    box-sizing: border-box;
    padding: 0px;
    border: none;
    margin: auto;
    display: block;
    width: 0px;
    height: 0px;
    min-width: 100%;
    max-width: 100%;
    min-height: 100%;
    max-height: 100%;
    object-fit: cover;
    object-position: center center;
    border-radius: 8px;
  }
  
  .compare-product__container {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .compare-product__wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex: 0 0 calc(100%/3);

    @media (max-width: 1199px) {
      flex: 0 0 32%;
      width: 100%;
    }

    @include maxSm {
      flex: unset;
      max-width: 100%;
    }
  }
  
  .compare-product__item {
    width: calc(100% / 3);
    padding: 12px 0;
    &:first-child {
      .compare-product__card-info-content {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        padding-left: 20px !important;
        &:before {
          background-color: #ed1b24;
        }
        @media (max-width: 767px) {
          border-radius: 8px;
        }
      }
    }
    &:not(:first-child):not(.compare-product-no-item) .compare-product__card-info-content::before {
      position: absolute;
      content: "";
      width: 1px;
      height: 90%;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      background-color: #c5c5c5;
    }
    &:not(:first-child) .compare-product__card-info-title p {
      display: none;
      @media (max-width: 767px) {
        display: inline;
      }
    }
  }
  
  .compare-product__card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 1%;

    &-image {
      max-width: 306.5px;
      height: 193px;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        object-position: center center;
        border-radius: 8px;
        overflow: hidden;
        @media  (min-width: 768px) {
          box-shadow: 8px 8px 16px rgba(0,0,0,.15);
        }
      }
    }
    &-label {
      background-color: #ed1b24;
      border-radius: 8px 8px 0 0;
      color: #fff;
      padding: 4px 12px;
      text-align: center;
      width: max-content;
      margin-left: auto;
      margin-right: auto;
      font-weight: 600;
      font-size: 0.875rem;
      line-height: 1.5;
      letter-spacing: 2px;
      &.no-label {
        opacity: 0;
      }
    }
    &-text {
      margin-top: 32px;
      font-weight: 700;
      font-size: 1rem;
      line-height: 1.5;
      text-align: center;
    }
    &-info {
      margin-top: 48px;
    }
    &-info-title {
      margin-bottom: 16px;
      text-align: left;
      font-weight: 600;
      font-size: 0.9rem;
      height: 16px;
      p {
        font-size: 1rem;
        line-height: 1.25;
        font-weight: 600;
      }
    }
    &-section {
      margin-bottom: 28px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    &-info-content {
      padding: 24px;
      background-color: #f5f6f8;
      position: relative;
      font-weight: 400;
      font-size: 0.9rem;
      line-height: 1.5;
      overflow: hidden;
      &:before {
        position: absolute;
        content: "";
        width: 5px;
        height: 100%;
        top: 0;
        left: 0px;
      }
      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
      }
      li {
        color: var(--gray-600);
        padding-left: 20px;
        position: relative;
        margin-bottom: 8px;
        &:before {
          position: absolute;
          content: "";
          width: 5px;
          height: 5px;
          top: 9px;
          left: 0;
          background-color: #c5c5c5;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    &-no-item {
      margin-top: 30px;
      width: 335px;
      height: 223px;
      border: 2px dashed #dedede;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      position: relative;
      a {
        position: absolute;
        width: 100%;
        height: 100%;
      }
      img {
        position: absolute;
        inset: 0px;
        box-sizing: border-box;
        padding: 0px;
        border: none;
        margin: auto;
        display: block;
        width: 32px !important;
        height: 32px !important;
        object-fit: cover;
        object-position: center center;
      }
    }
    &-no-item-background-img {
      width: 72px;
      height: 72px;
      position: relative;
      background-color: #f5f5f5;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--gray-600);
    }
  }
  
  .compare-product__bottom {
    margin-top: 32px;
    padding-left: 20px;
    &-button {
      max-width: 327px;
      margin-bottom: 32px;
      button {
        display: flex;
        justify-content: space-between;
        background-color: transparent;
        outline: 1px solid #000;
        border: none;
        outline-offset: -1px;
        position: relative;
        display: inline-flex;
        padding: 16px 24px;
        border-radius: 8px;
        // outline: none;
        cursor: pointer;
        white-space: nowrap;
        text-decoration: none;
        transition: all 0.3s ease-in;
        align-items: center;
        grid-gap: 12px;
        gap: 12px;
        min-width: max-content;
        width: 100%;
        z-index: 1;
        font-weight: 600;
        font-size: 0.9rem;
        &:hover {
          background-color: black;
          color: white;
          img {
            filter: brightness(0) invert(1);
          }
        }
        span {
          font-size: 1rem;
          line-height: 1.5;
        }
      }
    }
    &-link a {
      font-size: 1rem;
      line-height: 1.5;
      font-weight: 600;
      color: black;
      display: flex;
      position: relative;
      display: inline-flex;
      outline: none;
      border: none;
      cursor: pointer;
      align-items: center;
      text-decoration: none;
      transition: all 0.3s ease-in;
      width: inherit;
      grid-gap: 12px;
      gap: 12px;
      background-color: inherit;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  @media (max-width: 991px) {
    .compare-product__card-no-item {
      height: 160px;
    }
  }

  @media (max-width: 1200px) {

    .compare-product {
      margin-top: 48px;
    }
    
    .compare-product__card .compare-product__card-image {
      max-width: 206px;
      height: 130px;
    }

    .compare-product__card-no-item {
      width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .compare-product__card-no-item {
      height: 160px;
    }
  }
  
  @media (max-width: 767.98px) {
  
    padding-bottom: 64px;

    .compare-product {
      margin-top: 2rem;
    }
  
    .card-compare-header .card-compare-header__content {
      height: unset;
    }
  
    .card-compare-header .card-compare-header__wrapper {
      padding: 40px 16px;
    }
  }
  
  @media (max-width: 767px) {
    .compare-product__card .compare-product__card-image {
      max-width: 242px;
      height: 158px;
    } 

    .compare-product__item {
      width: 100%;
      padding: 0 !important; 
      &:has(.compare-product__card-no-item) {
        .compare-product__card-info {
          display: none;
        }
      }
    }

    .compare-product__card-text {
      margin-top: 24px;
    }
  
    .compare-product__container {
      flex-direction: column;
    }

    .compare-product__bottom {
      padding-left: 0px;
    }
  
    .compare-product__bottom-button {
      max-width: unset;
      button {
        padding: 12px 16px;
      }
    }
  
    .compare-product__bottom-link {
      text-align: center;
    }
  
    .compare-product__card-no-item {
      width: 100%;
      height: 160px;
      display: flex !important; //MS1-79668 | override inline styling display:inline-block
      &-background-img img {
        display: none;
      }
      &-background-img::before {
        font-size: 30px;
        content: "+";
      }
    }
  
    .compare-product-no-item {
      .compare-product__card-info,.compare-product__bottom {
        display: none;
      }
    }
  
    .slick-dots {
      display: flex;
      justify-content: center;
      list-style: inherit;
      li {
        color: #c4c4c4;
        width: 8px;
        font-size: 25px;
        margin-right: 16px;
        &:last-child {
          margin-right: 0;
        }
      }
      .slick-active {
        color: red;
      }
      li::marker {
        color: unset;
      }
      button {
        display: none;
      }
    }
  
    .slick-slide {
      width: 100%;
      margin: 0 1px;
    }
  
    .slick-track {
      display: flex;
    }
  }
}