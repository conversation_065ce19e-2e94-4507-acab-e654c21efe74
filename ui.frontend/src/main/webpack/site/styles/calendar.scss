/** calendar */
.calendar-popup {
  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
    0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  display: none;
  max-width: 325px;
  min-width: 310px;
  min-height: 315px;
  overflow-x: hidden;
  flex-direction: column;
  justify-content: flex-start;
  padding: 32px 0 0;
  position: absolute;
  z-index: 2;
  background: #fff;
  bottom: 0;
  left: -25%;
  top: 36px;
  border-radius: 4px;

  @media (max-width: 320px) {
    &.tcb-date-picker.active {
      max-width: 100%;
      min-width: 95vw;
      left: -1.25rem !important;
    }
  }

  &.active {
    display: flex;
    overflow: hidden;
  }
}

.text-input-wrapper .calendar-popup {
  bottom: unset;
}

@media screen and (max-width: 767px) {
  .calendar-popup {
    left: unset;
  }
}

@media screen and (max-height: 767px) and (max-width: 767px) {
  .calendar-popup {
    left: unset;
    top: -50%;
  }
}

/*end calendar*/

/* datepicker */
.datepicker {
  &-section {
    position: relative;
    display: inline-flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.87);
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    box-sizing: border-box;
    width: 100%;
    cursor: pointer;

    .date-icon {
      position: absolute;
      right: 16px;
      max-height: 2em;
      height: auto;
      margin: 0;
      display: flex;
      align-items: center;
      white-space: nowrap;

      svg,
      img {
        width: 1em;
        height: 1em;
        user-select: none;
        fill: currentColor;
      }

      div {
        padding: 0;
        flex: 0 0 auto;
        color: rgba(0, 0, 0, 0.54);
        font-size: 1.5rem;
        text-align: center;
        border: 0;
        cursor: pointer;
        background-color: transparent;
        display: inline-flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &-input {
    padding: 16px;
    border: 1px solid #e3e4e6;
    border-radius: 8px;
    height: 56px;
    background-color: #fff;
    box-sizing: border-box;
    width: 100%;
    font: inherit;
    color: currentColor;
    outline: none;
    &::-webkit-inner-spin-button,
    &::-webkit-calendar-picker-indicator {
      display: none;
      -webkit-appearance: none;
    }
  }
}

.dropdown-overflow-hidden {
  &.datepicker-showing {
    overflow: auto;
  }
}

.md-ripples {
  &.ui-datepicker-unselectable,
  &.ui-state-disabled {
    pointer-events: none;
  }
}

#ui-datepicker-div {
  z-index: 3 !important;
  padding: 32px 0 0;
  box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%),
    0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
  background-color: #fff;
  border-radius: 5px;

  &.ui-datepicker {
    &.change-year {
      .ui-datepicker-header {
        margin-bottom: 0;
        height: 115px;
      }
      .ui-datepicker-title {
        display: flex;
        flex-direction: column-reverse;
        text-align: left;
        margin: 0;
      }
      .ui-datepicker-month {
        &:before {
          content: var(--yearTitle);
          margin-left: 17px;
          background: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/calendar.svg")
            no-repeat;
          font-weight: 400;
          filter: brightness(0) saturate(100%);
          font-size: 16px;
          padding-left: 33px;
          position: absolute;
          top: 12px;
        }
        &:after {
          content: var(--yearVar);
        }
      }

      span.select-year-button {
        position: relative;
        width: 100%;
        border: 1px solid #e3e4e6;
        border-radius: 8px;
        padding: 12px 40px 12px 17px;
        text-align: right;
        cursor: pointer;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        appearance: none !important;
        outline: none;
        color: rgba(0, 0, 0, 0.87);
        font-size: 16px;
        font-weight: 400;
        background-image: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-dropdown-arrow.svg");
        background-repeat: no-repeat;
        background-position: right 0.7rem top 50%;
        background-size: 22.14px auto;
      }

      select.ui-datepicker-year {
        display: none;
      }

      .ui-datepicker-month {
        border-top: 1px solid #e3e4e6;
        padding-top: 16px;
        font-size: 16px;
      }

      .ui-datepicker-next,
      .ui-datepicker-prev {
        bottom: 0;
        top: unset;
      }

      .ui-datepicker-prev {
        right: 40px;
        left: unset;
      }
    }

    .ui-datepicker-header {
      border: none;
      background: #fff;
      padding: 0;
    }
    .ui-datepicker-days-cell-over .ui-state-highlight {
      background-color: var(--primary-red);
      color: #fff;
      border-radius: 50%;
    }
  }
}

.ui-selectmenu-menu.select-year-dropdown {
  position: absolute;
  z-index: 999;
  background-color: #fff;
  box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04),
    0 13.7866px 75.6175px rgba(0, 0, 0, 0.03),
    0 7.37098px 40.4287px rgba(0, 0, 0, 0.02),
    0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
    0 2.19453px 12.0367px rgba(0, 0, 0, 0.02),
    0 0.913195px 5.00873px rgba(0, 0, 0, 0.01);
  border-radius: 8px;
  // display: flex;
  &.ui-selectmenu-open {
    .ui-menu {
      height: auto;
      margin: 20px 0;
    }
  }

  > .ui-menu {
    height: 0;
    width: 278px;
    max-height: 300px;
    display: flex;
    overflow-x: hidden;
    flex-direction: column;
    width: 100%;
    padding-inline-start: 0;
    > li {
      cursor: pointer;
      line-height: 40px;
      display: flex;
      outline: none;
      justify-content: flex-start;
      color: #000;
      padding-left: 16px;
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
