/* Card List Component*/

.display-webkit-box {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: 48px;
}

.flex-end-icon {
  display: flex;
  align-self: flex-end;
}

.list-event-tile {
  .list-event-tile__container {
    .list-event-tile__list-item {
      position: relative;
      z-index: 1;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-column-gap: 24px;
      margin: 0 auto;
      .list-event-tile__item {
        box-shadow: 0 2px 8px rgba(0,0,0,.15);
        transition: all .2s linear;
        border-radius: 8px;
        &:hover{
          box-shadow: 0 33px 181px rgba(0,0,0,.04), 0 13.7866px 75.6175px rgba(0,0,0,.03), 0 7.37098px 40.4287px rgba(0,0,0,.02), 0 4.13211px 22.664px rgba(0,0,0,.02), 0 2.19453px 12.0367px rgba(0,0,0,.02), 0 0.913195px 5.00873px rgba(0,0,0,.01);
          cursor: pointer;
        }
        @media (min-width: 767px) {
          &:nth-child(n + 4) {
            margin-top: 20px;
          }
        }

        .list-event-tile__item-container {
          height: 100%;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          flex-direction: column;
          background-color: #fff;
          border-radius: 8px;

          .list-event-tile__item-body {
            padding: 24px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;

            .list-event-tile__item-content {
              font-weight: 300;

              .list-event-tile__item-financial-advice {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                padding-bottom: 8px;
                font-weight: 600;
                font-size: 0.875rem;
                line-height: 21px;
                letter-spacing: 2px;
                text-transform: uppercase;
                color: var(--gray-600);
                span:first-child:not(:empty) {
                  padding-right: 16px;
                  margin-right: 16px;
                  border-right: 1px solid #c5c5c5;
                }

                .tcb-date {
                  font-size: 14px;
                  font-weight: 600;
                  line-height: 21px;
                  color: var(--secondary-grey-60);
                }
              }

              .list-event-tile__item-title {
                margin-bottom: 8px;
              }

              .list-event-tile__item-description {
                color: var(--gray-600);
                margin: 8px 0;
                font-size: 1rem;
                font-weight: 400;
                .link-icon {
                  display: none;
                }
              }
            }

            .list-event-tile__item-action {
              flex-shrink: 0;
              margin-top: auto;

              .list-event-tile__item-action-link {
                position: relative;
                display: inline-flex;
                outline: none;
                border: none;
                cursor: pointer;
                align-items: center;
                text-decoration: none;
                transition: all 0.3s ease-in;
                width: inherit;
                grid-gap: 12px;
                gap: 12px;
                background-color: inherit;
                font-weight: 600;

                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }

          .list-event-tile__item-img {
            display: flex;
            position: relative;
            padding-top: 56%;
            overflow: hidden;

            span {
              position: absolute;
              inset: 0px;
            }

            img {
              width: 100%;
              max-width: 100%;
              min-height: 100%;
              object-fit: cover;
              border-radius: 8px 8px 0 0;
              transition: all 0.3s ease-in-out;
              transform: scale(1);
              position: relative;
              max-height: 100%;
            }
          }

          &:hover {
            box-shadow:
              0 33px 181px rgb(0 0 0 / 4%),
              0 13.7866px 75.6175px rgb(0 0 0 / 3%),
              0 7.37098px 40.4287px rgb(0 0 0 / 2%),
              0 4.13211px 22.664px rgb(0 0 0 / 2%),
              0 2.19453px 12.0367px rgb(0 0 0 / 2%),
              0 0.913195px 5.00873px rgb(0 0 0 / 1%);
          }
        }
      }

      @media (max-width: 767px) {
        &::-webkit-scrollbar {
          display: none;
        }
      }

      &.stack-view {
        @media (max-width: 767px) {
          display: flex;
          flex-wrap: wrap;
          a:nth-child(n + 2),
          .list-event-tile__item:nth-child(n + 2) {
            margin-top: 20px;
          }
        }
      }
    }
  }

  @media (max-width: 991px) {
    .list-event-tile__container {
      .list-event-tile__list-item {
        overflow-x: scroll;
          grid-template-columns: repeat(3, 304px);
          grid-column-gap: 16px;
          margin: -8px;
          padding: 8px;
        .list-event-tile__item {
          max-width: unset;
          flex-basis: unset;
          height: 100%;

          .list-event-tile__item-container {
             .list-event-tile__item-img {
              img{
                width: 100%;
              }
             }
            .list-event-tile__item-body {
              padding: 16px;

              .list-event-tile__item-content .list-event-tile__item-financial-advice {
                flex-direction: column;
                align-items: flex-start;
              }
            }
          }
        }
      }
    }
  }
  @media (max-width: 767px) {
    .content-wrapper {
      padding-left: unset;
      padding-right: unset;
    }

    .list-event-tile__container {
      .list-event-tile__list-item {
        &:not(.show-horizontal-mobile) {
          display: flex;
          flex-direction: column;
          padding: 0;
          width: 100%;
          gap: 20px;
        }

        &.show-horizontal-mobile {
          grid-template-columns: unset;
          grid-auto-flow: column;
          grid-auto-columns: 90%;
          position: relative;
          z-index: 1;
          display: grid;
        }

        .list-event-tile__item {
          .list-event-tile__item-container {
            .list-event-tile__item-body {
              display: flex;
              flex-flow: column;
              flex: 1 1 auto;
              border-radius: 0 0 8px 8px;
              background-clip: padding-box;
              padding: 1rem;
              background-color: #fff;

              .list-event-tile__item-content {
                .list-event-tile__item-financial-advice {
                  span:first-child {
                    margin-bottom: 8px;
                    border-right: unset;
                    display: block;
                    padding-left: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.card-link {
  .list-event-tile {
    .list-event-tile__container {
      .list-event-tile__list-item {
        @media (max-width: 991px) {
          overflow-x: scroll;
          grid-template-columns: repeat(3, 304px);
          grid-column-gap: 16px;
        }
        @media (max-width: 767px) {
          display: flex;
          flex-direction: column;
          padding: 0;
          width: 100%;
          gap: 20px;

          &.show-horizontal-mobile {
            grid-template-columns: unset;
            grid-auto-flow: column;
            grid-auto-columns: 90%;
            position: relative;
            z-index: 1;
            display: grid;
          }
        }

        a:nth-child(n + 4) {
          @media (min-width: 767px) {
            margin-top: 20px;
          }
        }

        .list-event-tile__item {
          box-shadow: none;
          transition: none;
          max-width: unset;
          flex-basis: unset;
          height: 100%;

          .list-event-tile__item-container {
            .list-event-tile__item-img {
              margin: 30px;
              padding-top: 70%;
              img{
                border-radius: 0;
              }
              @media (max-width: 767px) {
                margin: 16px;
              }
            }

            .list-event-tile__item-body {
              padding: 32px 33px 32px 24px;
              @media (max-width: 767px) {
                padding: 32px 24px;
                padding-top: 4px;
              }

              .list-event-tile__item-content {
                display: flex;
                flex-direction: column;
                font-size: 1.5rem;
                line-height: 1.5;
                height: 100%;
                .list-event-tile__item-financial-advice {
                  display: flex;
                  justify-content: flex-start;
                  padding-bottom: unset;

                  > span {
                    font-size: 14px;
                    letter-spacing: 2px;
                    font-weight: 600;
                    margin-bottom: 0;
                    text-transform: uppercase;
                    line-height: 21px;
                    color: #616161;
                    width: 100%;
                    padding: unset;
                    margin-right: unset;
                    border-right: unset;
                    &:first-child {
                      border-right: none;
                      padding-right: 0;
                      padding-bottom: 0;
                    }
                  }
                }

                .list-event-tile__item-title {
                  font-size: 24px;
                  font-weight: 600;
                  margin: 16px 0;
                  line-height: 36px;
                  cursor: pointer;
                  letter-spacing: -0.01em;
                  transition: all 0.3s ease;
                  text-decoration: none;
                }

                .list-event-tile__item-description {
                  letter-spacing: 0;
                  display: flex;
                  justify-content: space-between;
                  max-height: unset;
                  margin: unset;
                  flex: 1;
                  span:nth-child(1) {
                    align-self: flex-start;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.card-link-icon {
  .list-event-tile {
    .list-event-tile__container {
      .list-event-tile__list-item {
        .list-event-tile__item {
          .list-event-tile__item-container {
            .list-event-tile__item-img {
              border-radius: 8px;
            }
            .list-event-tile__item-body {
              justify-content: space-between;
              .list-event-tile__item-content {
                height: 100%;
                

                .list-event-tile__item-description {
                  font-size: 16px;
                  font-weight: 400;
                  text-overflow: ellipsis;
                  align-items: flex-end;
                  .link-icon {
                    display: flex;
                  }

                  span:nth-child(2) {
                    margin-left: 5px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
