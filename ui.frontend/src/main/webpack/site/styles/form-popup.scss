.form-failed .popup {
  flex-shrink: 0;
  background: rgba(0, 0, 0, 0.70);
  .popup__container {
    display: inline-flex;
    padding: 48px;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    margin: auto;

    border-radius: 8px;
    background: #FFF;
    box-shadow: 0px 0.91319px 5.00873px 0px rgba(0, 0, 0, 0.01), 0px 2.19453px 12.03668px 0px rgba(0, 0, 0, 0.02), 0px 4.13211px 22.66401px 0px rgba(0, 0, 0, 0.02), 0px 7.37098px 40.42872px 0px rgba(0, 0, 0, 0.02), 0px 13.78661px 75.61747px 0px rgba(0, 0, 0, 0.03), 0px 33px 181px 0px rgba(0, 0, 0, 0.04);
  }
  .popup__container-item {
    position: relative;
    display: flex;
    flex-direction: column;
    border: none;
    .popup__header {
      display: none;
    }
  }
  .popup__content {
    padding: 0;
  }
  .popup__close-icon {
    position: absolute;
    top: -24px;
    right: 0;
    display: block;
    width: 24px;
    height: 24px;
    opacity: 1;
    img {
      width: 100%;
      height: 100%;
      object-fit: scale-down;
      object-position: center center;
      filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(270deg) brightness(98%) contrast(103%);
    }
  }
  .form-popup-body {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .image {
    display: flex;
    justify-content: center;
    .cmp-image {
      height: 120px;
      width: 120px;
      /*margin-bottom: 24px;*/ // changed by adobe
      
      img {
        width: 100%;
        height: 100%;
        object-fit: scale-down;
        object-position: center center;
      }
    }
  }
  .form-popup-title {
    color: #616161;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-bottom: 16px;
  }
  .form-popup-message {
    color: #616161;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
  .column-container {
    display: flex;
    gap: 24px;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    .cta-button {
      margin: 0;
      width: 100%;
    }
  }

  @media (max-width: 767px) {
    .popup__container {
      margin: auto 16px;
      padding: 48px 16px 24px;
    }

    .column-container {
      margin-top: 16px;
      flex-direction: column;
      gap: 16px;
    }
    .popup__close-icon {
      top: -32px;
    }
  }
}

@media (max-width: 1366px) {
  .popup__container {
    margin: 0px auto !important;
  }
}