.masthead-extend-component {
    max-width: 1920px;
    margin: 0 auto;
}

.masthead-extend {
    .masthead-extend__breadcrumbs {
        position: relative;
        z-index: 2;
    }
    .breadcrumbs__container {
        display: flex;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
    }
    .breadcrumbs__justify-content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 12px;
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
    }
    .breadcrumbs__wrapper {
        margin-top: 16px;
    }
    .breadcrumbs__li {
        a {
            text-decoration: none;
            color: var(--secondary-mid-grey-100);
            font-size: 14px;
            line-height: 20px;
        }
    }
    .breadcrumbs__separator {
        color: var(--secondary-mid-grey-100);
        margin-left: 16px;
        margin-right: 16px;
        user-select: none;
        display: flex;
        span {
            width: 8px;
            height: 12px;
            overflow: hidden;
            background: none;
            opacity: 1;
            border: 0px;
            margin: 0px;
            padding: 0px;
            position: relative;
            max-width: 100%;
            display: block !important;
            img {
                position: absolute;
                inset: 0px;
                box-sizing: border-box;
                padding: 0px;
                border: none;
                margin: auto;
                display: block;
                width: 0px;
                height: 0px;
                min-width: 100%;
                max-width: 100%;
                min-height: 100%;
                max-height: 100%;
                object-fit: cover;
                object-position: center center;
            }
        }
    }
    .breadcrumbs-current {
        p {
            font-size: 14px;
            line-height: 20px;
        }
    }
    .masthead-extend__banner {
        position: relative;
    }
    .banner-center__container {
        padding: 43px 0 248px;
        text-align: center;
    }
    .banner-center__container-title {
        display: inline-flex;
        position: relative;
        p {
            font-weight: 300;
            font-size: 2rem;
            line-height: 1.25;
        }
        padding-top: 43px;
    }
    .container-title__title-arrow {
        display: flex;
        align-items: flex-start;
        width: 100%;
        justify-content: center;
        h1 {
            white-space: nowrap;
            font-weight: 300;
            line-height: 1.25;
            font-size: 2rem;
        }
    }
    .title-arrow__big-icon {
        margin-right: 32px;
        max-width: 62px;
        flex: 0 0 62px;
        height: 80px;
        margin-top: -20px;
        width: 100%;
        span {
            box-sizing: border-box;
            display: inline-block;
            overflow: hidden;
            width: inherit;
            height: inherit;
            background: none;
            opacity: 1;
            border: 0px;
            margin: 0px;
            padding: 0px;
            position: relative;
            max-width: 100%;
            img {
                position: absolute;
                inset: 0px;
                box-sizing: border-box;
                padding: 0px;
                border: none;
                margin: auto;
                display: block;
                min-width: 100%;
                max-width: 100%;
                min-height: 100%;
                max-height: 100%;
                object-fit: cover;
                object-position: center center;
            }
        }
    }
    .banner-center__container-desc {
        max-width: 582px;
        margin: 24px auto 32px auto;
        p {
            font-weight: 400;
            font-size: 16px;
            line-height: 1.5;
        }
    }
    .banner-center__container-button {
        width: 326px;
        margin: 0 auto;
        a {
            justify-content: space-between;
            background-color: transparent;
            border: 1px solid var(--primary-black);
            position: relative;
            display: inline-flex;
            padding: 16px 24px;
            border-radius: 8px;
            outline: none;
            cursor: pointer;
            white-space: nowrap;
            text-decoration: none;
            transition: all 0.3s ease-in;
            align-items: center;
            grid-gap: 12px;
            gap: 12px;
            min-width: max-content;
            width: 100%;
            z-index: 1;
            line-height: 24px;
            &:hover {
                background-color: var(--secondary-grey-100);
                color: var(--primary-white);
            }
        }
    }
    .button-link__icon-svg {
        margin-left: 0px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease-in-out;
    }
    .masthead-extend__card-gallery {
        position: relative;
        z-index: 1;
        padding: 206px 0 64px;
    }
    .card-gallery__image-gallery {
        padding: 0 30px;
        top: -165px;
        position: absolute;
        width: 100%;
    }
    .image-gallery__container {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        margin-left: -12px;
        margin-right: -12px;
    }
    .image-gallery__content-item {
        flex: 0 0 149px;
        max-width: 149px;
        height: 199px;
        padding: 0 12px;
    }
    .image-gallery__center {
        height: 481px;
        flex: 0 0 261px;
        max-width: 261px;
        margin-top: -45px;
        .content-item__img {
            box-shadow: none;
        }
    }
    .content-item__img {
        height: 100%;
        border-radius: 8px;
        box-shadow: 0 0.6912px 1.3824px rgb(0 0 0 / 12%);
        width: 100%;
        display: block;
        overflow: hidden;
        span {
            box-sizing: border-box;
            display: inline-block;
            overflow: hidden;
            width: 100%;
            height: 100%;
            background: none;
            opacity: 1;
            border: 0px;
            margin: 0px;
            padding: 0px;
            position: relative;
            max-width: 100%;
            img {
                position: absolute;
                inset: 0px;
                box-sizing: border-box;
                padding: 0px;
                border: none;
                margin: auto;
                display: block;
                width: 0px;
                height: 0px;
                min-width: 100%;
                max-width: 100%;
                min-height: 100%;
                max-height: 100%;
                object-fit: cover;
                object-position: center center;
            }
        }
    }
    .card-gallery__aticle {
        display: flex;
        justify-content: center;
    }
    .card-gallery__aticle-item {
        width: 213px;
        margin-right: 170px;
        height: 398.5px;
        &:last-child {
            margin-right: 0;
        }
        &:nth-child(2) {
            .transparent-content__img {
                height: 86%;
            }
        }
    }
    .aticle-item__transparent {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
    }
    .aticle-item__transparent-content {
        max-width: 100%;
        position: relative;
        flex: 0 0 263px;
        flex-direction: column;
        justify-content: flex-end;
        display: block;
    }
    .transparent-content__img {
        height: 100%;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
        span {
            box-sizing: border-box;
            display: block;
            overflow: hidden;
            width: 100%;
            height: 100%;
            background: none;
            opacity: 1;
            border: 0px;
            margin: 0px;
            padding: 0px;
            position: relative;
            max-width: 100%;
            img {
                position: absolute;
                inset: 0px;
                box-sizing: border-box;
                padding: 0px;
                border: none;
                margin: auto;
                display: block;
                width: 0px;
                height: 0px;
                min-width: 100%;
                max-width: 100%;
                min-height: 100%;
                max-height: 100%;
                object-position: center center;
            }
        }
    }
    .transparent-content__text {
        margin-top: 32px;
        h3 {
            font-weight: 600;
            font-size: 1rem;
            margin: 0 auto 8px;
        }
    }
    .transparent-content__text-body {
        color: var(--secondary-grey-60);
        margin: 0;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: 1rem;
        line-height: 1.5;
        font-weight: 400;
    }
    .cmp-button__text {
        font-weight: 600;
    }
    .cmp-title__text {
        font-weight: 300;
        font-size: 2rem;
        line-height: 1.25;
    }
}

@media (max-width: 1024px) {
    .masthead-extend {
        .card-gallery__aticle-item {
            margin-right: 40px;
        }
    }
}

@media (max-width: 991px) {
    .masthead-extend {
        .masthead-extend__card-gallery {
            padding-top: 50px;
        }
        .image-gallery__center {
            flex: 0 0 190px;
            max-width: 190px;
            height: 340.89px;
        }
        .card-gallery__image-gallery {
            top: -180px;
        }
        .card-gallery__aticle-item {
            max-width: 190px;
            margin-right: 100px;
            height: 370.14px;
        }
    }
}

@media (max-width: 767px) {
    .masthead-extend {
        .banner-center__container {
            padding: 35px 0 328px;
        }
        .banner-center__container-title {
            display: block;
            padding-top: 35px;
        }
        .container-title__title-arrow {
            display: block;
            h1 {
                margin-bottom: 16px;
                white-space: inherit;
            }
        }
        .title-arrow__big-icon {
            margin: auto auto 16px;
            max-width: 44px;
            flex: 0 0 44px;
            height: 58px;
        }
        .banner-center__container-desc {
            padding: 0 24px;
            margin-bottom: 16px;
        }
        .banner-center__container-button {
            padding: 0 16px;
            a {
                padding: 12px 16px;
                span {
                    max-width: 265px;
                    display: block;
                    white-space: normal;
                }
            }
        }
        .masthead-extend__card-gallery {
            margin-top: 110px;
            padding-top: 160px;
        }
        .card-gallery__image-gallery {
            width: 315px;
            left: 50%;
            transform: translateX(-50%);
            top: -350px;
            padding: 0;
        }
        .image-gallery__container {
            margin: 0;
        }
        .image-gallery__content-item {
            display: none;
            padding: 0;
        }
        .image-gallery__center {
            flex: 0 0 312px;
            max-width: 312px;
            height: 637.2px;
            display: block;
            padding: 0;
        }
        .card-gallery__aticle {
            margin-top: 20px;
            flex-direction: column;
        }
        .card-gallery__aticle-item {
            max-width: unset;
            width: 280px;
            margin: 0 auto 32px;
            height: 457.06px;
            &:first-child {
                order: 2;
            }
            &:nth-child(2) {
                order: 1;
                height: 433.75px;
                .aticle-item__transparent-content {
                    flex: 0 0 297.73px;
                }
                .transparent-content__img {
                    height: 100%;
                }
            }
            &:last-child {
                margin: 0 auto;
                order: 3;
            }
        }
        .aticle-item__transparent-content {
            margin-bottom: 16px;
            flex: 0 0 345.06px;
        }
        .transparent-content__text {
            margin-top: 16px;
        }
    }
}