$breadcrumb-caret-icon: "/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron_right_gray.svg";
/* Light theme */
$breadcrumb-color: #a2a2a2;
$breadcrumb-active-color: #000;
/* Dark theme */
$breadcrumb-color-dark-theme: #a2a2a2;
$breadcrumb-active-color-dark-theme: #ffffff;

.aem-Grid:has(.breadcrumb),.article-content--wrapper:has(.breadcrumb) {
    position: relative;
}

.breadcrumb {
    position: absolute;
    z-index: 3;
    width: 100%;
    left: 0;
    top: 0;
}

.hero-breadcrumb-container {
    padding: 1rem 0;
    overflow: hidden;

    ol {
        display: flex;
        flex-wrap: nowrap;
        list-style: none;
        align-items: center;
        margin: 0;
        padding: 0;
        white-space: nowrap;
        overflow-x: scroll;

        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
        /* Hide scrollbar for Chrome, Safari and Opera */
        &::-webkit-scrollbar {
            display: none;
        }

        & .breadcrumb-item {
            color: $breadcrumb-color;
            font-weight: 400;

            &:not(:first-child) {
                padding-left: 0.5rem;
            }
            
            &:not(:last-child) {
                padding-right: 2rem;
                background: url($breadcrumb-caret-icon) no-repeat 100%;
                filter: brightness(0) saturate(100%) invert(74%) sepia(0%) saturate(512%) hue-rotate(88deg) brightness(89%) contrast(84%);
            }
            &:nth-child(even) {
                display: flex;
            }
            &.cmp-breadcrumb__item--active {
                color: $breadcrumb-active-color;
            }

            & .breadcrumb-text {
                text-decoration: none;
                font-size: 0.875rem;
                line-height: 1.25rem;
                font-weight: 400;
                letter-spacing: 0.009rem;
            }
        }
    }
}
  
.insurance {
    .hero-breadcrumb-container {
        position: absolute;
        top: 0;
    }
}

body.dark {
    .hero-breadcrumb-container {
        ol {
            .breadcrumb-item {
                .cmp-breadcrumb__item-link {
                    color: $breadcrumb-color-dark-theme;
                }

                &.cmp-breadcrumb__item--active {
                    color: $breadcrumb-active-color-dark-theme;
                }
            }
        }
    }
}