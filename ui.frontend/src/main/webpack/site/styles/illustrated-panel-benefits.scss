.illustrated-panel {
  &-title {
    padding-top: 16px;
    padding-bottom: 20px;
    font-weight: 300;
    font-size: 1.75rem;
    line-height: 1.25;
    position: relative;
  }

  &-list {
    display: flex;
    
    &__item {
      flex-grow: 1;
      flex-shrink: 0;
      flex-basis: calc(100% / 3);
      padding: 12px;
    }

    &__card {
      background-color: var(--primary-white);
      padding: 48px;
      border-radius: 8px;
      height: 100%;
  
      &-header {
        img {
          width: 100px;
          height: 100px;
        }
        h3 {
          margin: 0;
          padding: 0;
        }
      }
  
      &-content {
        margin-top: 8px;
        line-height: 24px;
        color: var(--gray-600);
      }
  
      &-body {
        width: 100%;
        margin-top: 40px;
  
        img {
          width: 100%;
          height: 224px;
          max-width: 325px;
          object-fit: cover;
          object-position: center;
        }
  
        h4 {
          margin-bottom: 16px;
        }

        ul {
          padding-inline-start: 0;
        }
  
        li {
          display: flex;
          margin-top: 8px;
          line-height: 24px;
          color: var(--gray-600);
          list-style: none;
  
          &:before {
            content: "◼";
            color: var(--primary-red);
            display: inline-block;
            width: 2em;
            font-size: 11px;
            border-radius: 1px;
          }
        }
      }
    }
  }

  &-plan {
    background-image: linear-gradient(var(--primary-white), var(--secondary-light-grey-60));

    &:has(&__result__wrapper:only-of-type) {
      padding-top: 0;
    }

    ul {
      list-style-type: none;
      >li {
        margin-top: 8px;
        color: var(--gray-600);
        &:before {
          content: "";
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: var(--primary-red);
          display: inline-block;
          margin-right: 10px;
          transform: translateY(-2px);
        }
      }
    }

    &__title {
      font-size: 1.5rem;
      line-height: 1.5;
      margin-bottom: 16px;
      font-weight: 300;
      display: flex;
      justify-content: center;
    }

    &__img {
      min-height: 259px;
      max-width: 1118px;
      margin: auto;
      line-height: 0; // changed by adobe
      img {
        height: 100%;
        width: 100%;
      }
    } 

    &__description {
      margin: 0px 0 28px 16px; // changed by adobe
      p {
        color: var(--gray-600);
      }
      ul{
        padding-left: 0;
      }
      // changed by adobe
      h3{
        margin-top: 0;
      }
    }

    &__result {
      &__wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--primary-white);
        padding: 32px 16px;
      }

      &_left__info {
        display: flex;
        align-items: center;
        margin-right: 50px;
      }

      &_center__info {
        display: flex;
        align-items: center;
        margin-right: 50px;

        .illustrated-panel-plan__result_description{
          text-align: center;
        }
      }
      &__icon {
        max-width: 90px;
        flex: 0 0 90px;
        height: 60px;
        margin-right: 40px;
        position: relative;
        img {
          position: absolute;
        }
      }

      &_right__info h3 {
        font-size: 1.5rem;
        line-height: 1.5;
        color: #0a84ff;
        margin: 0;
        font-weight: 600;
      }

      //changed by adobe
      &_description{
        font-size: 1.5rem;
      }
      &__title{
        font-weight: 600;
        color: var(--gray-900);
      }
    }

    &__benefitRate {
      display: flex;
      justify-content: space-between;
      padding: 0 128px;
      margin-top: 8px;
      margin-bottom: 28px;
    }

    &__beforeRate {
      padding: 8px 0;
      background-color: #fff;
      border-radius: 8px;
      display: flex;
      flex: 1 1;
      flex-direction: row;
      margin-right: 16px;
      align-items: center;
      justify-content: center;
    }

    &__value {
      margin: 0 8px;
    }

    &__afterRate {
      padding: 8px 18px;
      background-color: #fff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      flex-direction: row;
      h3{
        margin:0; //changed by adobe
      }
    }
    
    &__subfix {
      display: inline;
      vertical-align: text-top;
    }
  }

  @media (min-width: 1199px) {
    margin: -12px;
  }

  @media (max-width: 991px) {
    &-list {
      flex-direction: column;
      &__item {
        padding: 12px 0;
      }
    }
    &-plan__benefitRate {
      padding: 0 64px;
    }
  }

  @media (max-width: 767px) {
    &-title {
      padding-bottom: 8px;
    }

    &-list__item {
      padding: 8px 0;
    }

    &-list__card {
      padding: 32px 16px;
    }

    &-plan {
      padding-top: 32px;

      &__result {
        &__icon {
          margin-right: 24px;
          flex: 0 0 60px;
          height: 40px;
          padding-bottom: 70px;
  
          img {
            position: absolute;
            height: 40px;
          }
        }
        &__wrapper {
          flex-direction: column;
          align-items: flex-start;
          padding: 24px 16px;
        }

        &_left__info,&_center__info {
          margin-right: 0;
          align-items: flex-start;
          padding-bottom: 24px;
          margin-bottom: 24px;
          border-bottom: 1px solid var(--secondary-mid-grey-80);
          width: 100%;
        }
      }

      &__img {
        padding: 0px 14px 14px;
      }

      &__description {
        padding: 0 14px;
      }

      &__benefitRate {
        padding: 0;
        p {
          font-size: 10px;
        }
      }

      &__beforeRate {
        margin-right: 8px;
        padding: 10px 16px;
      }

      &__afterRate {
        display: block;
      }

      &__subfix {
        display: inline;
        vertical-align: text-top;
      }

      &__afterRate &__value {
        display: inline;
      }
    }
  }

  @media (max-width: 390px) {
    &-plan__result_left__info &-plan__result__icon {
      padding-bottom: 90px;
    }
  }
}
