.device-look-carousel {

  .device-look-carousel__item-content {
    word-break: break-word;
    h4 {
      line-height: 1.5;
      font-weight: 600;
      font-size: 1rem;
    }

    p a:hover {
      text-decoration: underline;
    }
  }

  &.layout-step-text {
    overflow: hidden;
  }

  &.layout-image-step-text {
    .device-look-carousel__item-image-wrapper {
      max-width: none;
      margin: 0 0.75rem 2.5rem;
    }
  }

  .slick-list {
    display: flex;
  }

  .device-look-carousel__container {
    height: 100%;
    max-width: 1440px;
    margin: 0 auto;

    .title {
      font-weight: 300;
      font-size: 1.75rem;
      line-height: 1.25;
      margin-bottom: 18px;
    }

    .slick-track {
      float: left;
      display: flex;
      margin-left: 0;
    }
  }

  .device-look-carousel__header {
    color: #000;
    padding-bottom: 12px;
  }

  .device-look-carousel__header-title {
    display: flex;
    align-items: center;
    font-weight: 300;
    font-size: 1.75rem;
    line-height: 1.25;
    position: relative;
  }

  .title-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 8px;
    flex-wrap: wrap;
  }

  .device-look-carousel__item {
    padding-top: 20px;
    display: flex !important;
    flex-direction: column;
    height: 100%;
  }

  .device-look-carousel__item-image-wrapper {
    margin-bottom: 56px;
    max-width: 175px;
    flex: 1;

    .device-look-carousel__item-image {
      position: relative;
      width: 100%;
      object-fit: contain;
      object-position: top center;
      border-radius: 0.5rem;
    }
  }

  .device-look-carousel__item-body {
    background-color: white;
    position: relative;
    padding: 2.5rem 0.625rem 1.5rem 1rem;
    width: 100%;
    height: 100%;
    min-height: 7rem;
  }

  .device-look-carousel__item:first-child .device-look-carousel__item-body {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .slick-slide:first-child .device-look-carousel__item-body {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .slick-slide:last-child .device-look-carousel__item-body {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .device-look-carousel__item-number {
    font-size: 24px;
    font-weight: 600;
    position: absolute;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    background-color: #000;
    color: #fff;
    top: -20px;
    justify-content: center;
    border-radius: 50%;
  }

  .device-look-carousel__item-content .item-title {
    line-height: 1.5;
    font-weight: 600;
    font-size: 1rem;
    margin-top: 8px;
    color: var(--primary-black);
  }

  .device-look-carousel__item-heading {
    line-height: 1.5;
  }

  .slick-arrow {
    position: absolute;
    border-radius: 50%;
    transition: all 0.2s ease-in-out;
    box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: hsla(0, 0%, 77%, 0.54);
    border: none;
    z-index: 1;
  }

  .slick-prev {
    top: 50%;
    z-index: 1;
    color: white;
    font-size: 1.25rem;

    &:after {
      content: "❮";
    }
  }

  .slick-next {
    right: 0;
    top: 50%;
    z-index: 1;
    color: white;
    font-size: 1.25rem;

    &:after {
      content: "❯";
    }
  }

  .slick-next:hover,
  .slick-prev:hover {
    color: red;
    background-color: white;
    cursor: pointer;
  }

  .slick-disabled {
    display: none;
  }
}


@media (max-width: 991px) {  
  .device-look-carousel {

    //overflow: unset;
    .device-look-carousel__item-body {
      padding: 40px 32px 40px 16px;
    }

    .device-look-carousel__list-item .device-look-carousel__item:first-child .device-look-carousel__item-body {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      padding: 40px 16px 40px 32px;
    }

    .device-look-carousel__list-item .device-look-carousel__item:last-child .device-look-carousel__item-body{
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }

    .device-look-carousel__list-item {
      display: flex;
      overflow-x: scroll;
    }

    .device-look-carousel__item {
      max-width: 50%;
      flex: 0 0 50%;
    }
  }
}

@media (max-width: 767px) {
  .device-look-carousel {    
    .device-look-carousel__list-item {
      padding-top: 0;
      .device-look-carousel__item:has(.device-look-carousel__item-image-wrapper)  {
        padding-top: unset;
      }
    }
  }
}

@media (max-width: 576px) {
  .device-look-carousel {
    .device-look-carousel__item {
      max-width: 84%;
      flex: 0 0 84%;
    }
  }
}

@media (max-width: 414px) {
  .device-look-carousel {
    .device-look-carousel__item {
      max-width: 100%;
      flex: 0 0 100%;
    }
  }
}

@media (max-width: 300px) {
  .device-look-carousel {
    .device-look-carousel__container {
      margin-left: -16px;
    }
  }
}
