.personal-section {
  position: relative;
  background: #fff;
  &:before {
    position: absolute;
    content: "";
    height: 100%;
    width: 40%;
    right: 0;
    top: 0;
    background: var(--right-panel-background-color);
  }
  
  .personal-container {
    width: 100%;
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
  }
  
  .personal-panel {
    width: calc(100% + 24px);
    margin: -12px;
    justify-content: center;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;
  }
  
  .personal-info {
    padding: 12px;
  }
  
  .personal-info-container {
    height: 100%;
    width: calc(100% + 24px);
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
  }
  
  .personal-info-content {
    position: relative;
    padding-top: 88px;
    padding-left: 40px;
    padding-bottom: 74px;
  }
  
  .personal-info-description {
    position: relative;
    color: #ed1c24;
    margin-bottom: 16px;
    font-weight: 300;
  }
  
  .personal-info-name {
    margin: 0;
  }
  
  .personal-info-position {
    font-size: 16px;
    color: var(--gray-600);
    margin-bottom: 64px;
  }
  
  .personal-button {
    color: #000;
    justify-content: flex-start;
    position: relative;
    display: inline-flex;
    outline: none;
    border: none;
    cursor: pointer;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease-in;
    width: inherit;
    grid-gap: 12px;
    gap: 12px;
    background-color: inherit;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  .bio-button-text {
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: rgb(0, 0, 0);
  }
  
  .personal-button-icon-content {
    display: flex;
    align-items: center;
    margin-left: 0px;
    transition: all 0.3s ease-in-out;
  }
  
  .left-Square {
    height: 24px;
    width: 24px;
    background-color: #ed1c24;
    position: absolute;
    top: 92px;
    transform: rotate(45deg);
    &:before {
      content: "";
      height: 100px;
      bottom: 0;
      left: -80px;
      margin-bottom: 4px;
      width: 1px;
      transform: rotate(-45deg);
      transform-origin: center top;
      background-color: #e3e4e6;
      position: absolute;
      display: block;
    }
    &:after {
      content: "";
      height: 300px;
      margin-top: 31px;
      right: -7.5px;
      width: 1px;
      transform: rotate(-45deg);
      transform-origin: center top;
      background-color: #e3e4e6;
      position: absolute;
      display: block;
    }
  }
  
  .empty-col-bio {
    padding: 12px;
  }
  
  .personal-image {
    display: flex;
    align-items: flex-end;
    padding: 12px;
  }
  
  .personal-image-container {
    display: flex;
    margin-top: 64px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
  }
  
  .personal-image-panel {
    box-sizing: border-box;
    overflow: hidden;
    width: initial;
    height: initial;
    background: none;
    opacity: 1;
    border: 0px;
    margin: 0px;
    padding: 0px;
    position: relative;
    max-width: 100%;
    display: block !important;
  }
  
  .personal-image-space {
    box-sizing: border-box;
    display: block !important;
    width: initial;
    height: initial;
    background: none;
    opacity: 1;
    border: 0px;
    margin: 0px;
    padding: 0px;
    max-width: 100%;
  }
  
  .image-personal {
    z-index: 1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    width: initial;
    height: initial;
  }
  
  .personal-container {
    display: flex;
    max-width: 1440px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 64px;
    padding-right: 64px;
  }
  
  @media (min-width: 768px) {
    .personal-info {
      flex-grow: 0;
      max-width: 50%;
      flex-basis: 50%;
    }
    .empty-col-bio {
      flex-grow: 0;
      max-width: 8.333333%;
      flex-basis: 8.333333%;
    }
    .personal-image {
      flex-grow: 0;
      max-width: 50%;
      flex-basis: 50%;
    }
  }

  @media (min-width: 992px) {
    .personal-info {
      flex-grow: 0;
      max-width: 43.666667%;
      flex-basis: 43.666667%;
    }
  }

  @media (max-width: 1200px) {
    .personal-container {
      padding-left: calc(100vw / 22.5);
      padding-right: calc(100vw / 22.5);
    }
  }

  @media (max-width: 1025px) {
    .left-Square {
      &:after {
        height: 330px;
      }
    }
  }

  @media (max-width: 991px) {
    .empty-col-bio {
      display: none;
    }
  }

  @media (max-width: 767.98px) {
    &.pad-wrapper {
      padding-bottom: 48px;
    }
  
    .image-personal {
      box-sizing: border-box;
      display: block;
      width: 100%;
      height: initial;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      max-width: 100%;
      padding-top: 0;
      border-radius: unset;
      position: relative;
    }
  }
  
  @media (max-width: 767px) {
    &:before {
      display: none;
    }
  
    .personal-image-container {
      width: inherit;
      padding-top: 0;
      border-radius: unset;
      margin-left: calc(100vw / -22.5);
      position: relative;
      margin-top: 64px;
      overflow: hidden;
      padding-bottom: 33px;
      &:before {
        position: absolute;
        content: "";
        width: 90%;
        height: 100%;
        background: #ed1c24;
        right: calc(100vw / -22.5);
      }
    }
  
    .left-Square {
      top: 340px;
      &:after {
        height: 380px;
      }
    }
  
    .personal-info-content {
      padding-top: 27px;
      border-left: 0;
      position: relative;
      margin-left: 15px;
      padding-bottom: 0;
    }
  
    .personal-info-container {
      width: 100%;
    }
    .personal-info {
      width: 100%;
      padding: 8px;
      margin: 0;
      box-sizing: border-box;
    }
    .personal-image {
      width: -webkit-fill-available;
      padding: 8px;
      display: flex;
      align-items: flex-end;
    }
    .personal-panel {
      flex-direction: column-reverse;
      flex-wrap: wrap;
      width: calc(100% + 16px);
      margin: -8px;
      justify-content: center;
    }
  }
  
  @media (max-width: 376px) { 
    .left-Square {
      top: 335px;
    }
  }
}

