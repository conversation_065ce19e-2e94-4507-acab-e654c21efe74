// dark header
.dark .header {
  @include maxLgSemi {
    background-color: var(--primary-black);
  }
  .header_layout {
    .header-navigation,
    .navigation_primary {
      background-color: var(--secondary-grey-80);
    }

    .navigation_secondary,
    .navigation-primary_item-dropdown_list.open {
      background-color: var(--primary-black);
    }

    .navigation_secondary {
      .search-engine- {
        &show, &hidden {
          background-color: var(--primary-black);
        }
      }
    }

    .navigation-primary_left
      .navigation-primary_item-dropdown_list.open
      .header_list_dropdown {
      background-color: var(--primary-black);
      .dropdown-item {
        background-color: unset;
      }
    }

    .navigation-primary_item .link_component-link .link_text,
    .dropdown_holder {
      color: #a2a2a2;
      letter-spacing: 0.28px;
    }

    @media (max-width: 1024px) {
      .language_dropdown_item .dropdown_holder{
        color: #fff;
      }
    }

    .navigation-primary_item.show-active-arrow .link_component-link .link_text,
    .navigation-primary_item .link_component-link .link_text:hover {
      color: var(--secondary-gold);
    }

    .dropdown-arrow.material-symbols-outlined {
      fill: var(--secondary-mid-grey-100);
      @media (max-width: 1024px) {
        filter: brightness(0) saturate(100%) invert(100%) sepia(38%) saturate(2081%) hue-rotate(310deg) brightness(100%) contrast(87%);
        font-size: 20px;
      }
    }

      .navigation-primary_left
      .navigation-primary_item.navigation-primary_item-dropdown_list.show-active-arrow::after {
      display: none;
    }

    .navigation-primary_left .navigation-primary_item.show-active-arrow:after {
      background-color: var(--primary-black);
      left: 50%;
    }

      .navigation-primary_left
      .navigation-primary_item.discover_dropdown_btn {
      border-left: 1px solid rgb(197, 197, 197, 0.2);
    }

    .navigation-primary_item.has-right-border {
      border-right: 1px solid rgb(197, 197, 197, 0.2);
    }

    .navigation-secondary_item:before {
      background-color: var(--secondary-gold);
    }

    .navigation-secondary_item.active span {
      color: var(--primary-white);
    }

    .header-logo .link_component-link img {
      width: auto;
    }

    .language_dropdown a {
      color: var(--primary-black);
    }

    .language_dropdown .active a {
      color: var(--primary-red);
    }
  }
}

.mgm-header-style .header_layout .header-navigation .navigation_secondary .navigation-secondary_menu .navigation-secondary_item {
  max-width: fit-content;
}
.mgm-header-style .header_layout .header-navigation .navigation_secondary .navigation-secondary_actions .link_component-link {
  background-color: #ed1c24;
  border-radius: 8px;
  height: 37px;
  padding: 0 16px;
  font-size: 14px;
  letter-spacing: normal;
  .navigation-secondary_actions-icon img {
    display: none;
  }
}

@media (max-width: 1024px) {
  .header {
    background-color: var(--primary-white);
    .header_layout {
      .navigation_sub {
        .mobile-menu-items {
          .navigation-secondary_menu {
            .navigation-secondary_item:not(.secondary-nav) {
              .icon {
                display: none;
              }

              a {
                width: 100%;
                text-align: start;
              }
            }
          }
        }
      }

      .navigation_secondary {
        box-shadow: 0 5px 20px rgba(0,0,0,.1);

        &:has(.search-engine-hidden) {
          margin-bottom: 0;
        }

        &:has(.search-engine-show) {
          --search-engine-height: 4.625rem;
          margin-bottom: var(--search-engine-height);
        }

        .search-engine-hidden {
          transform: translateY(-100%);
          display: block;
          z-index: 10;
          position: absolute;
          width: 100%;
          transition: transform 0.2s ease, visibility 0.2s ease, opacity 0.2s ease;
          background-color: var(--primary-white);
          visibility: hidden;
          opacity: 0;
        }

        .search-engine-show {
          transform: translateY(0);
          display: block;
          z-index: 10;
          position: absolute;
          width: 100%;
          transition: transform 0.2s ease, visibility 0.2s ease, opacity 0.2s ease;
          background-color: var(--primary-white);
          visibility: visible;
          opacity: 1;
        }
      }
    }
  }

  .dark .header {
    .header_layout {
      .navigation_secondary .navigation-secondary_menu {
        display: none;
      }

      .navigation_secondary .header-logo {
        height: 100%;
      }
  
      .navigation-secondary_actions .link_component-link {
        background-color: unset;
        .link_text {
          color: #b4a393;
        }
      }
  
      .navigation_sub {
        background-color: var(--secondary-grey-80);
        .mobile-menu {
          background-color: var(--secondary-grey-80);
          .mobile-button {
            background-color: unset;
            .dropdown-item {
              background-color: #131313;
              a {
                color: unset;
              }
            }
          }
        }
  
        .mobile-main-nav {
          background-color: var(--primary-black);
          .discover_category {
            .show-active-arrow::after {
              display: none;
            }
            .navigation-primary_item {
              color: unset;
            }
          }
        }
  
        .mobile-menu-items {
          background-color: inherit;
          .navigation-secondary_menu {
            padding: 0;
            .navigation-secondary_item {
              margin: 0;
              .icon {
                display: none;
              }
            }
          }
        }
      }
    }
  }
}
