// styles.scss
#detail-container {
  display: none;
  position: fixed;
  top: var(--header-height);
  left: 0;
  z-index: 9;
  width: 100%;
  height: calc(100% - var(--header-height));
  margin-top: 0;
  overflow: auto;

  ul {
    padding-left: 1.25rem;
    margin-top: 24px;
  }

  &.open {
      display: block;
  }

  @media (min-width: 769px) {
    height: calc(100% - var(--header-height));
  }

  .image-card-container {
    img {
      max-width: 100%;
      max-height: 10.063rem;

      @media (max-width: 768px) {
        --card-width: calc((100vw - 104px) / 3);
        max-height: var(--card-width);
      }
    }
  }
}

.offer-listing-detail {
  ul {
    padding-left: 1.25rem;
    margin-top: 24px;
  }

  &__wrapper {
    background-color: rgb(245, 246, 248);
    width: 100%;
    position: relative;
    overflow: hidden;
  }

  .cta-button-custom {
    min-height: 3.5rem;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-box-align: center;
    -ms-flex-align: center;
    grid-gap: 0.75rem;
    align-items: center;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    display: inline-flex;
    font-weight: 600;
    gap: 0.75rem;
    justify-content: space-between;
    line-height: 1.5;
    min-width: -webkit-max-content;
    min-width: -moz-max-content;
    min-width: max-content;
    outline: none;
    padding: 1rem 1.5rem;
    position: relative;
    text-decoration: none;
    -webkit-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
    white-space: nowrap;
    width: 50%;
    z-index: 1;

    &.primary {
      background-color: black;
      color: white;
    }

    &.secondary {
      background-color: transparent;
      color: black;
      border: 1px solid #404040;
    }

    &.hide {
        display: none;
    }

    &.block {
      width: 100%;

      span {
        max-width: 260px;
        white-space: normal;
      }
    }

    &-icon {
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      margin-left: 0;
      -webkit-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
    }

    img {
        display: block;
    }

    &:hover {
      background-color: var(--secondary-grey-60);

      .load-more__button-icon {
        filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(1%) hue-rotate(232deg) brightness(107%) contrast(101%);
      }
    }

  }

  a.primary-cta-btn,
  a.secondary-cta-btn {
    width: 100%;
  }

  .list-style-position {}

  .height-content {
    height: fit-content !important;
    margin-bottom: 24px;
  }

  .question>img {
    transform: rotate(-180deg);
  }

  .filterpanelcontainer {
    width: 100%;
  }

  .answer {
    margin-top: 8px;
  }

  .detail-back-container {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;

    .tcb-container {
//       padding: 0 64px;
      @media (max-width: 768px) {
        padding: 0;
        margin: 0;
      }
    }

    .detail-back {
      width: 1314px;
      display: inline-flex;
      gap: 4px;
      align-items: center;

      @media (max-width: 768px) {
        width: 100vw;
        padding: 0 16px;
      }

      &:hover {
        cursor: pointer;
      }

      &__text {
        color: var(--secondary-grey-60);
      }
    }
  }

  .offer-detail-container {
    display: flex;
    padding: 48px 0px;
    width: 100%;
    justify-content: center;
    width: 100vw;
    background-size: cover;
    transition: background-color 0.5s ease, padding 0.3s ease;

    &--bg-white {
      background-color: #ffffff;
    }

    &--bg-gray {
      background-color: #E7E7EE;
    }

    @media (max-width: 768px) {
      padding: 40px 16px;

      &.offer-detail-masterhead__wrapper {
        padding: 56px 16px 40px;
      }
    }
  }

  .product-card-list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 36px;
    align-items: center;
    justify-items: center;

    img {
      max-width: 100%;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }

  .product-card-title{
    display: flex;
    font-weight: 600;
    flex-direction: column;
    gap: 4px;
    margin-top: 0px;
  }

  .link-custom {
    color: #0a84ff;
    margin-top: 16px;
    display: block;
  }

  .view-more {
    color: #0a84ff;

    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }

  &__masterhead {
    max-width: 752px;
    width: 100%;
    display: flex;
    gap: 12px;
    flex-direction: column;

    .merchant-tag {
      color: var(--secondary-grey-60);
      font-size: 24px;
      font-weight: 300;
      text-align: center;
      line-height: 32px;
      text-transform: capitalize;
    }

    .description {
      line-height: 40px;
      text-align: center;
    }

    .description>h1>* {
      font-size: 32px;
    }

    .description>* {
      font-size: 32px;
    }

    .exp-and-location {
      display: flex;
      gap: 32px;
      padding-top: 24px;
      justify-content: center;
      align-items: center;

      div {
        display: flex;
        gap: 8px;
        align-items: center;

        @media (max-width: 768px) {
          align-items: flex-start;
          img {
            margin-top: 2px;
          }
        }

        img {
          width: 20px;
          height: 20px;
        }

        p {
          font-weight: 600;
        }
      }
    }
  }

  &__content {
    max-width: 752px;
    width: 100%;

    .filter-panel-container {
      background-color: white !important;
    }

    .question {
      border-bottom: 1px solid var(--secondary-mid-grey-60);
    }
  }

  &__product {
    max-width: 752px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;

    h2 {
      text-align: left;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--secondary-mid-grey-60);
    }

    .content {
      color: #616161;
    }
  }

  .list-images {
    display: grid;
    grid-template-columns: auto auto auto auto;

    .image-card-container {
      width: 160px;
      height: 160px;
      display: flex;
      align-items: center;
    }

    .image-card-product {
      // width: 160px;
      // height: 100px;
    }
  }

  .list-cards {
    padding-inline-start: 18px !important;
    display: flex;
    flex-direction: column;
    gap: 8px;

    li {
      font-weight: 600;
    }
  }

  .btn-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding-top: 16px;

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__target {
    max-width: 752px;
    width: 100%;

    .list-items {
      display: flex;
      gap: 48px;
      flex-direction: column;

      .question-underline {
        border-bottom: 1px solid var(--secondary-mid-grey-60);
      }
    }
  }

  .offer-detail-address__wrapper {
    background-color: #ffffff;
    background-position: center top;
  }

  &__address {
    max-width: 752px;
    width: 100%;

    .question-underline {
      border-bottom: 1px solid var(--secondary-mid-grey-60);
    }

    .offer-address-list {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      row-gap: 4px;
      font-weight: 500;
      color: #000000;

      li {
        color: #000000;
      }
    }
  }

  .bg-address {
    background-image: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/offer-detail-address.svg");
    background-color: #f5f5f5;
  }

  &__info {
    width: 100%;
    max-width: 752px;
  }

  .offer-faq-container{
    display: flex;
    flex-direction: column;
    gap: 48px;
    background-color: #ffffff;
    padding: 48px 0;

    .offer-detail-address__wrapper{
      padding: 0px;
    }

    .offer-detail-applicable__wrapper{
      padding: 0px;
    }

    .offer-detail-note__wrapper{
      padding: 0px;
    }

    @media (max-width: 768px){
      padding: 40px 0;

      .offer-detail-address__wrapper{
        padding: 0px 16px;
      }

      .offer-detail-applicable__wrapper{
        padding: 0px 16px;
      }

      .offer-detail-note__wrapper{
        padding: 0px 16px;
      }
    }
  }
}