$color_1: #FF0000;
$color_2: #1D6D31;

.c-cash-score-board-component {
    .title {
        padding-bottom: 12px;
        width: 100%;
        display: flex;
        justify-content: flex-start;
        h2 {
            color: var(--secondary-grey-100);
            padding-bottom: 12px;
            font-size: 32px;
            line-height: 40px;
        }
    }

    .scoreboard {
        width: 100%;
        background-color: var(--primary-white);
        border-collapse: collapse;
        tr {
            &.total-score-row {
                background-color: var(--primary-ivory);
            }
        }

        th, td {
            border: 1px solid var(--secondary-mid-grey-60);
            padding: 16px 48px 16px 48px;
            width: 33.33%;
            text-align: center;

            &.first-col-cell {
                text-align: start;
            }

            &.low-score {
                color: $color_1;
            }

            &.high-score {
                color: $color_2;
            }
        }
    }

    @media (max-width: 767px) {
        .scoreboard {
            th, td {
                padding: 16px 12px 16px 12px;
            }
        }
    }

    @media (max-width: 280px) {
        .scoreboard {
            th, td {
                padding: 0;
            }
        }
    }
}