/* Accordion inspire */
.accordion-inspire-component {
  .accordion-inspire-container{
    padding: 0 !important; //Change By Adobe
  }
  .item {
    display: flex;
    flex-direction: column;
    width: 100%;
    border-bottom: 1px solid rgb(227 228 230);
    padding: 15px 16px;
    &:last-child {
      border-bottom: none;
    }
  }
  .item-title {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: start;
    cursor: pointer;
    .item-title-text {
      font-size: 24px;
      font-weight: 300;
      margin: 0;
      max-width: 90%;
    }
  }

  .item-description {
    display: flex;
    flex-direction: column;
    transition-duration: 300ms;
    .content {
      padding-top: 0;
    }
    .cols-2 {
      display: flex;
      margin: 16px 0;
    }
  }

  .cols-2 > div {
    flex: 1;
  }

  .cols-2 ul {
    padding-left: 20px;
  }

  .item-description .more-btn {
    margin-top: 24px;
    display: flex;
  }

  .more-btn img {
    margin-left: 12px;
    transition-duration: 300ms;
  }

  .more-btn a:hover img {
    transform: translate(5px);
  }

  .item.hide .item-description {
    opacity: 0;
    height: 0;
    overflow: hidden;
  }

  .item.show .item-description {
    opacity: 1;
    height: auto;
  }

  .expand-icon {
    transition-duration: 300ms;
    .add-icon{
      margin-top: 10px;
    }
    .sub-icon{
      margin-bottom: 17px;
    }
  }

  .item.show .expand-icon {
    transform: rotate(180deg);
  }

  .item.show .add-icon,
  .item.hide .sub-icon {
    display: none;
  }

  .item.hide .add-icon,
  .item.show .sub-icon {
    display: block;
  }

  .faq-panel .right-container {
    width: 100%;
  }

  .answer-question .question .chevron-down {
    transition-duration: 300ms;
    margin-left: 8px;
    margin-top: 3px;
  }

  .right-container .question {
    align-items: flex-start;
  }

  .answer-question:last-child .question {
    padding-bottom: 0;
  }

  .more-btn a {
    display: flex;
    align-items: center;
  }

  @media (max-width: 1023px) {
    .accordion-inspire-container {
      padding: 0;
      .item {
        padding-left: 0;
        padding-right: 0;
      }
    } 
  }
}

// Change by adobe
@media screen and (max-width: 1023px) {
  .accordion-inspire-component {
    .item {
      padding-left: 0;
      padding-right: 0;
    }
  }
}
