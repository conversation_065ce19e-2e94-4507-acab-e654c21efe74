.modal-hidden {
	display: none;
}

.tcb-button.tcb-button--light-black {
  &:not(.priority-link):not(.priority-btn):not(.white-btn) {
    border: 1px solid var(--body);
    background: var(--body);
    color: var(--primary-background);
  }

  .section-font-color & {
    color: inherit;
  }

  &.priority-link {
    color: #fff;
    background: none;
    border: none;
    justify-content: flex-start;

    .tcb-arrow {
      color: #fff;
    }

    &:hover .white-space {
      text-decoration: none;
      cursor: pointer;
    }
  }

  &.priority-btn {
    color: #fff;
    background: linear-gradient(180deg, #8d8175 -24.11%, #35322b 305.36%);
    border-radius: 8px;
    outline: none;
    border: none;
    display: inline-flex;
    padding: 16px 24px;
    cursor: pointer;
    min-width: max-content;

    .tcb-arrow {
      color: inherit;
    }

    &:hover {
      background: #fff;
      color: #616161;
    }
  }

  &.white-btn {
    background-color: #fff;
    color: #000;
    border: none;

    .tcb-arrow {
      color: #ed1c24;
    }

    &:hover {
      background-color: #a2a2a2;
      color: #616161;
      .tcb-arrow {
        color: inherit;
      }
    }
  }

  .dark & {
    color:#000;
    background-color: #dfdfdf;
  }
}

.form-modal {
  .columnContainer {
    margin-top: 24px;
  }
}

.popup {
	display: none;
	position: fixed;
	z-index: 10000;
	top: 0;
	left: 0;
	width: 100%;
	min-height: 100%;
	overflow-x: hidden;
	overflow-y: auto;
	background-color: rgba(0, 0, 0, 0.6);
	margin-top: auto;
	margin-bottom: auto;
	padding: 8px 0;
  align-items: center;

  .popup__container {
    .popup__content {
      iframe {
        aspect-ratio: 16 / 9;
        width: 100%;
      }
    }
  }

  &__container {
    opacity: 1;
    transform: translate(0);
    max-width: 800px;
    margin: 1.75rem auto;
    transition: all 0.3s ease-in-out;
    position: relative;
    z-index: 2;
    width: 100%;
    .form-modal & {
      margin: auto;
      max-width: 500px;
    }
  }

  &__container-item {
    pointer-events: auto;
    position: relative;
    background: #fff;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    outline: 0;
    .form-modal & {
      border-radius: 4.8px;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    background: #f5f6f8;
    &-title {
      margin-bottom: 0;
      line-height: 1.5;
      font-size: 1.25rem;
      word-wrap: break-word;
    }

    .form-modal & {
      display: none;
    }
  }

  &__content {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;

    .popup__close-icon {
      display: none;
      .form-modal & {
        display: block;
      }
    }
  }

  &__iframe {
    position: relative;
    &:before {
      padding-top: 56.25%;
      display: block;
      content: "";
    }
    iframe {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
    }
  }

  &__close-icon {
    cursor: pointer;
    opacity: 0.5;
    font-weight: bolder;
    text-shadow: 0 1px 0 #000;
    margin-top: 0.25rem;
    .form-modal & {
      text-shadow: 0 10px var(--primary-red);
      opacity: unset;
      position: absolute;
      top: 12px;
      right: 16px;
    }
    & img {
      width: 20px;
      height: 20px;
    }
  }
}

@media (max-width: 428px) {
  .popup__container {
    max-width: 90%;
  }
}

@media screen and (max-width: 1024px) and (orientation:portrait) {
  .popup__container {
    max-width: 95%;
  }
}

@media screen and (max-width: 768px) and (orientation:landscape) {
  .popup__container {
    max-width: none;
    width: 500px;
  }
}

@media (min-width: 1025px) {
  .popup-small {
    .popup__container {
      max-width: 300px;
    }
  }

  .popup-medium {
    .popup__container {
      max-width: 800px;
    }
  }

  .popup-large {
    .popup__container {
      max-width: 1000px;
    }
  }
}