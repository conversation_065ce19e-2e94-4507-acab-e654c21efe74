/* row-3 */
.static-grid-panel {
  .icon {
    &.icon-center {
      margin-left: auto;
      margin-right: auto;
    }
    &.icon-left-small {
      img {
        width: 44px;
        height: 44px;
        margin: 1rem 0;
        object-fit: contain;
      }
    }
  }
  &.row-3__container {
    .row-3__bg {
      height: 100%;
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }

    .list__item__wrapper {
      display: flex;
      flex-wrap: wrap;
      width: calc(100% + 24px);
      margin: -12px;
    }

    .static-grid-item {
      display: flex;
      flex-direction: column;
      padding: 0 12px 12px 12px;
      &:hover {
        .icon img {
          transform: scale(1.05);
        }
      }
    }

    .static-grid-item__content_title h3 {
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.5;
      margin-bottom: 16px;
      margin-top: 0;
    }

    .row-3__header {
      padding-left: 12px;
    }

    .row-3__header__title {
      font-weight: 300;
      font-size: 1.75rem;
      line-height: 1.25;
      padding-bottom: 12px;
    }

    .row-3__header__description {
      color: var(--gray-600);
      margin-bottom: 12px;
    }

    .static-grid__container {
      position: relative;
    }

    .icon:not(.icon-left-small) {
      margin-bottom: 32px;
      width: 8.125rem;
      height: 100%;

      @include maxSm {
        width: 5rem;
      }
    }

    .icon:not(.icon-left-small) img {
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: contain;
      object-position: center center;
      transition: all 0.2s ease-in-out;
      transform: scale(1);
    }

  }
  &.row-4__container,
  &.row-5__container {
    .list__item__wrapper {
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      .static-grid-item {
        display: flex;
        flex-direction: column;
        padding: 0 12px 12px 12px;
        &:hover {
          .icon img {
            transform: scale(1.05);
          }
        }
      }
    }
    .list-card-icon__title h2 {
      padding-bottom: 20px;
      margin: 0;
      font-weight: 300;
      font-size: 1.8rem;
      line-height: 1.25;
    }

    .static-grid-item {
      .icon-left {
        margin-bottom: 32px;
      }

      .icon img {
        position: relative;
        transition: transform 0.5s;
      }

      .icon {
        &.icon-left img {
          width: 128px;
          height: 128px;
        }

        &.icon-center {
          img {
            width: 130px;
            height: 100px;
            margin: 16px;
            object-fit: contain;
          }
        }
      }
    }

    /* list-card-icon stack */
    .list__item__wrapper.mobile-stack .static-grid-item {
      align-items: center;
    }

    .list__item__wrapper.mobile-stack .icon {
      height: 100px;
      width: 100px;
      margin: 16px;
    }

    .list__item__wrapper.mobile-stack .content {
      margin: 16px;
    }

    .list__item__wrapper.mobile-stack .content h3 {
      margin-bottom: 8px;
      padding-bottom: 0;
    }

    .list__item__wrapper.mobile-stack .content p {
      color: #000;
    }

    .list__item__wrapper.mobile-stack .static-grid-item:hover .icon {
      transform: scale(1.2);
    }
  }

  &.row-5__container {
    .list__item__wrapper {
      display: grid;
      grid-template-columns: repeat(auto-fit,minmax(200px,1fr));
      grid-auto-rows: minmax(160px,auto);
      gap: 24px;
      grid-gap: 24px;
    }
    
    .static-grid-item {
      width: 100%;

      .icon {
        margin-bottom: 0;
      }

      &:hover {
        .icon img {
          transform: scale(1.2) !important;
        }
      }
    }

    .title {
      margin-bottom: 8px;
    }
  }

  .content {
    height: 100%;
    word-break: break-word;

    h3 {
      font-size: 16px !important;
      font-weight: 600 !important;
      line-height: 24px !important;
    }
  }

  .title {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 16px;
    margin-top: 0;
  }

  .description {
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5;
    margin-top: auto;
    color: var(--gray-600);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }

  .title,
  .description {
    flex-shrink: 0;
  }

  .icon {
    picture {
      &.no-img {
        visibility: hidden;
      }
    }
  }

  &.row-5__container {
    .description {
      -webkit-line-clamp: unset;
    }
  }
}

.SectionHeader_title *{
  position: relative;
  font-weight: 400;
  line-height: 1.5;
  padding-bottom: 8px;
  font-size: 1rem;
}
.staticgridpanel {
  .SectionHeader_title {
    padding-bottom: 12px;
    > :last-child {
      padding-bottom: 12px;
    }
    h2 {
      font-weight: 300;
    }
    p {
      font-size: 16px;
      font-weight: 400;
      color: var(--gray-600);
    }
  }
}

.list__item__wrapper {
  flex-wrap: wrap;
}

.static-grid-row-3-item{
  width: 33%;
}

.static-grid-row-4-item{
  width: 25%;
}

@media (max-width: 1024px) {
  .static-grid-panel {
    &.row-4__container {
      .icon {
        margin-bottom: 0 !important;
        
        img {
          width: 96px !important;
          height: 96px !important;
        }
      }
    }

    .static-grid-item {
      width: 50%;
    }
  }
}

@media (max-width: 991px) {
  .static-grid-panel {
    &.row-5__container {
      .static-grid-row-5-item {
        .icon-center {
          img {
            margin: 24px !important;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .static-grid-panel {
    &.row-3__container {
      .icon {
        margin-bottom: 16px;
      }
    }

    &.row-4__container .static-grid-item {
      .icon img {
        margin-bottom: 16px;
      }
    }

    .title {
      margin-bottom: 8px;
    }
  }
}

@media (max-width: 767px) {
  .static-grid-panel {
    &.row-5__container {
      .list__item__wrapper {
        display: flex;
        gap: 16px;
        grid-gap: 16px;
      }

      .static-grid-row-5-item {
        width: 100%;
        flex-direction: row !important;
        padding: 0 !important;
        .icon-center {
          margin-left: 0;
          margin-right: 0;

          img {
            width: 80px !important;
            height: 65px !important;
          }
        }

        .content {
          .title {
            margin-top: 16px;
          }
        }
      }
    }
  }
}
