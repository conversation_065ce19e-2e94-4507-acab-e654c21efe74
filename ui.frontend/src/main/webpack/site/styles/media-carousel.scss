@import "swiper/css";

/* Inspire Slide Component */
.inspire-slider-component {

	.head-title {
		color: rgb(33 33 33);
		font-weight: 600;
		font-size: 32px;
		margin-bottom: 32px;
	}

	.item-img {
		position: relative;
		border-radius: 16px;

		picture {
			width: 100%;
		}

		img:not(.btn-video-img) {
			width: 100%;
			border-radius: 16px;
			object-fit: contain;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
		}

		.btn-video {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
			cursor: pointer;
			z-index: 20;
		}
	}

	.item-content {
		display: none;
		margin-top: 34px;
		grid-template-columns: repeat(12, minmax(0, 1fr));

		.heading {
			grid-column: span 4 / span 3;

			.text-smallText {
				font-size: 14px;
				line-height: 21px;
				color: rgb(97 97 97);
			}

			p {
				font-size: 24px;
				font-weight: 600;
			}
		}

		.description {
			grid-column: span 5 / span 5;

			p {
				padding-right: 20px;
			}
		}

		.btn {
			display: flex;
			justify-content: center;
			align-items: center;
			grid-column: span 3 / span 3;

			a {
				display: flex;
				align-items: center;
			}

			span {
				font-weight: 600;
				padding-right: 12px;
				text-align: right;
			}
		}
	}

	.primary-tracking:hover img{
		@include transform($transforms: translateX(5px));
	}

	.primary-tracking img{
		transition-duration: 200ms;
		transition-property: all;
		transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	}

	.swiper {
		max-width: 1312px;
		padding-left: 104px;
		padding-right: 104px;
	}

	.swiper-slide-prev {
		opacity: 0.5 !important;
	}

	.swiper-slide-next {
		opacity: 0.5 !important;
	}

	.swiper-slide-active {
		.item-content {
			display: grid;
		}
	}

	.swiper-slide:not(.swiper-slide-active) {
		top: -65px;
	}

	.swiper-button-next {
		right: 80px;
		&::after {
			font-size: 16px;
			width: 24px;
			height: 24px;
			content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-right-red.png);
		}
	}

	.swiper-button-prev {
		left: 80px;
		&::after {
			font-size: 16px;
			width: 24px;
			height: 24px;
			content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-left-red.png);
		}
	}

	.swiper-button {
		cursor: pointer;
		border-radius: 100%;
		background-color: rgb(255 255 255);
		position: absolute;
		height: 48px;
		width: 48px;
		top: 250px;
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;

		&.swiper-button-disabled {
			opacity: 0.35;
			cursor: unset;
			pointer-events: none;
		}

		&:not(.swiper-button-disabled):hover {
			background-color: #ed1b24;

			&.swiper-button-next {
				&::after {
					font-size: 16px;
					width: 24px;
					height: 24px;
					content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-right-white.png);
				}
			}

			&.swiper-button-prev {
				&::after {
					font-size: 16px;
					width: 24px;
					height: 24px;
					content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-left-white.png);
				}
			}
		}

		&.hide {
			display: none;
		}
	}


}

.inspire-slider-component .slider-popup {
	display: none;
	position: fixed;
	z-index: 10;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	padding: 10vh 0;
	background-color: rgba(0, 0, 0, 0.7);	
	overflow-y: auto;
	z-index: 99;
}

@media screen and (max-width: 1024px) and (orientation:portrait) {
	.inspire-slider-component .slider-popup {
		padding: 25vh 0;
	}
}
@media screen and (max-width: 1024px) and (orientation:landscape) {
	.inspire-slider-component .slider-popup {
		padding: 10vh 0;
	}
}

.inspire-slider-component .slider-popup-content {
	padding: 48px;
	background-color: rgb(255 255 255);
	max-width: 1236px;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
	position: relative;

	@include xs {
		height: 28.125rem;
	}

	@include sm {
		height: 32.5rem;
	}

	@include lg {
		height: 42.5rem;
	}

	@include xl {
		height: 45.625rem;
	}
}

.inspire-slider-component .slider-popup-content img {
	position: absolute;
	right: 15px;
	top: 15px;
	cursor: pointer;
}

.inspire-slider-component .slider-popup-content iframe {
	width: 100%;
	height: 100%;
}

.inspire-slider-component .item-content .btn svg {
	transition-duration: 300ms;
}

.inspire-slider-component .item-content .btn a:hover svg {
	transform: translate(5px);
}

@media screen and (max-width: 1024px) {
	.inspire-slider-component .head-title {
		padding-left: 32px;
	}

	.inspire-slider-component .swiper-button-next,
	.inspire-slider-component .swiper-button-prev {
		height: 30px;
		width: 30px;
	}

	.inspire-slider-component .swiper-button-next {
		right: 90px;
	}

	.inspire-slider-component .swiper-button-prev {
		left: 90px;
	}
}

@media (max-width: 976px) {

	/* inspire slider */
	.inspire-slider-component .item-content .heading,
	.inspire-slider-component .item-content .description,
	.inspire-slider-component .item-content .btn {
		grid-column: span 12 / span 12;
	}

	.inspire-slider-component .item-content .heading {
		gap: 34px;
		display: flex;
		align-items: center;
	}

	.inspire-slider-component .item-content {
		margin-top: 8px;
	}

	.inspire-slider-component .item-content .description {
		margin: 20px 0;
	}

	.inspire-slider-component .item-content .btn {
		justify-content: flex-start;
	}

	.inspire-slider-component .item-content .heading p {
		font-size: 18px;
		line-height: 24px;
	}

	.inspire-slider-component .swiper {
		padding-left: 40px;
		padding-right: 40px;
	}

	.inspire-slider-component .head-title {
		padding-left: 16px;
	}

	.inspire-slider-component .swiper-button-prev {
		left: 25px;
	}

	.inspire-slider-component .swiper-button-next {
		right: 25px;
	}

	.inspire-slider-component .slider-popup-content {
		padding: 48px 24px;
	}
}

@media (max-width: 650px) {

	/* inspire slider */
	.inspire-slider-container {
		padding-left: 0 !important;
		padding-right: 0 !important;
	}

	.inspire-slider-component .swiper {
		padding-left: 16px;
		padding-right: 16px;
	}

	.inspire-slider-component .item-content br {
		display: none;
	}

	.inspire-slider-component .item-content .heading {
		display: unset;
	}
}

@media (max-width: 575px) {
	/* inspire slider */
	.inspire-slider-component .swiper {
		margin-left: 0;
		margin-right: 0;
	}

	.inspire-slider-component .head-title {
		padding-left: 16px;
	}
}

@media (max-width: 428px) {
	.inspire-slider-component .swiper-button-prev {
		left: 0px;
	}

	.inspire-slider-component .swiper-button-next {
		right: 0px;
	}
}