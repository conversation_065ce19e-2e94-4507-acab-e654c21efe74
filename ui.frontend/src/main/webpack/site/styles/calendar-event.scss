#ui-datepicker-div {
  display: none;
}
.calendar-event {
  .event-filter {
    margin-top: 0;
    .tcb-date-picker {
      margin-bottom: 24px;
      @media (max-width: 767px) {
        margin-bottom: 0;
      }
      .ui-datepicker {
        .isSelected>a {
          background-color: #ed1b24;
          color: #fff;
        }
        .isPast .ui-state-active {
          background-color: #c5c5c5;
        }
        .isEvent {
          position: relative;
          &:after {
            content:"\A";
            width:7px;
            height:7px;
            border-radius:50%;
            top: 5px;
            right: 10px;
            position: absolute;
            animation: red-flash-point 1s infinite;
          }
          &.isPast:after {
            animation: grey-flash-point 1s infinite;
          }
        }
        :not(.isSelected)>.ui-state-hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
        .isPast {
          color: #c5c5c5;
          &.isSelected>a {
            background-color: #c5c5c5;
            color: #fff;

            &.ui-state-hover {
              background-color: #c5c5c5;
            }
          }
          .ui-state-active {
            background-color: #fff;
            color: #c5c5c5;
          }
          .ui-state-hover {
            background-color: #fff;
          }
        }
        .ui-state-active {
          background-color: #fff;
          color: #000;
        }
      }
    }
    .offer-cards__container {
      padding: 0;
    }
    .news-filter-card_cover-image {
      object-position: center center;
    }
    .news_filter__close-button {
      height: 24px;
      > img {
        filter: brightness(0) saturate(100%) invert(10%) sepia(76%) saturate(5062%) hue-rotate(348deg) brightness(137%) contrast(94%);
        height: 24px;
      }
    }
    .news-filter-card {
      .news-card_type {
        display: flex;
        @media (max-width: 767px) {
          flex-direction: column;
          .news-card_time {
            margin: 8px 0;
          }
          .news-card_type-inner {
            border-right: 0px;
          }
        }
      }
      .news-filter-card_cover {
        flex: 0 0 422px;
        height: auto;
        min-height: 236px;
        @media (max-width: 1999px) {
          flex: 0 0 355px;
        }
        @media (max-width: 767px) {
          flex: 0 0 0;
        }
      }
      .news-card_info {
        line-height: 24px;
        overflow: hidden;
        width: 100%;
        .news-card_title {
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
        }
        .news-card_note {
          margin-top: -8px;
          color: #616161;
          font-weight: 500;
          font-style: italic;
        }
        .news-card_description {
          margin-top: -8px;
          overflow: auto;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
          display: block;
        }
      }
    }
    .news-filter-card_month {
      width: 94px;
      height: 82px;
      top: 32px;
      left: 24px;
      border-radius: 16px;
      background-color: #fff;
      box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04),
        0 13.7866px 75.6175px rgba(0, 0, 0, 0.03),
        0 7.37098px 40.4287px rgba(0, 0, 0, 0.02),
        0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
        0 2.19453px 12.0367px rgba(0, 0, 0, 0.02),
        0 0.913195px 5.00873px rgba(0, 0, 0, 0.01);
      > small {
        color: #a2a2a2;
      }
    }
    .news-filter__wrapper {
      display: flex;
      gap: 24px;
      padding-top: 12px;
      padding-bottom: 12px;
    }
    .news_filter-group {
      max-width: 100%;
      flex-basis: auto;
      padding: 0 !important;
    }
    .offer-filter__checkbox-item {
      width: auto;
    }
    .tcb-button--link:hover {
      color: unset;
      text-decoration: underline;
    }
    .information-filter__load-more {
      text-align: center;
      margin: 24px auto 0;
      max-width: 328px;
      button {
        background-color: transparent;
      }
    }
    .offer-filter__title .offer-filter__title-label {
      line-height: 24px;
      font-weight: 700;
      font-size: 16px;
    }

  }
  .news_filter__header {
    padding: 16px;
    box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04),
      0 13.7866px 75.6175px rgba(0, 0, 0, 0.029),
      0 7.37098px 40.4287px rgba(0, 0, 0, 0.024),
      0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
      0 2.19453px 12.0367px rgba(0, 0, 0, 0.016),
      0 0.913195px 5.00873px rgba(0, 0, 0, 0.011);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    color: #000;
    font-size: 16px;
    line-height: 24px;
    background-color: #fff;
    transform: translateY(0);
    transition: all 0.3s ease;
    font-weight: 700;
  }
  .news_open-filter-button {
    .btn-open-filter-sticky {
      justify-content: space-between;
      align-items: center;
      color: rgb(0, 0, 0);
      font-size: 16px;
      line-height: 24px;
      height: 56px;
      padding: 16px 24px 16px 32px;
      background-color: rgb(255, 255, 255);
      font-weight: 700;
      border-radius: 8px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      box-shadow: rgba(0, 0, 0, 0.04) 0px 33px 181px, rgba(0, 0, 0, 0.027) 0px 13.7866px 75.6175px, rgba(0, 0, 0, 0.024) 0px 7.37098px 40.4287px, rgba(0, 0, 0, 0.02) 0px 4.13211px 22.664px, rgba(0, 0, 0, 0.016) 0px 2.19453px 12.0367px, rgba(0, 0, 0, 0.01) 0px 0.913195px 5.00873px;
      cursor: pointer;
      position: fixed;
      top: 100px;
      z-index: 2;
      left: 0px;
      display: flex;
    }
    &:not(.sticky) .btn-open-filter-sticky {
      display: none;
    }
  }
  @media (min-width: 768px) {
    .mobile-only {
      display: none !important;
    }
  }
  @media (max-width: 767px) {
    padding: 0px 0 48px; //changed by adobe
    .mobile-not-show {
      display: none;
    }
    .event-filter {
      .offer-cards__container {
        padding: 8px 0;
      }
      .news-filter__wrapper {
        display: block;
        // gap: 24px;
      }
      .news_open-filter-button {
        padding: 8px 0;
        font-weight: 700;
        line-height: 24px;
      }
      .news_filter-group {
        position: fixed;
        top: 0;
        left: 0;
        padding: 0;
        width: 100%;
        z-index: 1300;
        .news_filter-group__content {
          background-color: #fff;
          padding: 0 16px 48px;
          height: 100vh;
          overflow-y: auto;
        }
      }
      .news_filter-group {
        display: block;
        opacity: 0;
        visibility: hidden;
        transform: scale(0.75, 0.5625);
        transition: opacity 398ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 265ms cubic-bezier(0.4, 0, 0.2, 1) 133ms;
      }
      &.open {
        .news-filter__wrapper {
          padding: 12px 17px !important;
        }
        .news_filter-group {
          opacity: 1;
          visibility: visible;
          transform: none;
          transition: opacity 398ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 265ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
        }
      }
      .news-card_info {
        .news-card_type-inner {
          line-height: 21px;
        }
        .news-card_title {
          margin-top: 8px;
          line-height: 24px;
        }
        .news-card_description {
          line-height: 24px;
        }
        .news-card_note {
          margin-top: -8px;
        }
      }
      .load-more__button {
        padding: 12px 16px;
      }
      .btn-open-filter {
        font-size: 16px;
        line-height: 24px;
        padding: 12px 24px;
      }
    }

    .offer-filter__apply {
      display: flex;
      margin: 32px auto 150px;
      text-align: center;
      .tcb-button {
        flex: 1;
        padding: 12px 16px;
        line-height: 24px;
      }
    }

    .card-multiple-select__content {
      background-color: transparent !important;
      margin: 0 !important;
      padding: 20px 0 !important;
    }

    .card-multiple-select__container {
      padding: 0;
    }
  }
}
