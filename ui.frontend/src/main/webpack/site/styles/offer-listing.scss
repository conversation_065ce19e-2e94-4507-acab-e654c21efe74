// .offer-listing, .offer-listing-promotions {
//   .offer-filter__dropdown, .category-checkbox, .product-checkbox {
//     margin-bottom: 2.5rem;
//   }
//   &.open {
//     .tcb-modal__wrapper.is-single-filter .offer-filter__header {
//       display: none;
//     }
//     .is-single-filter .filter--border-bottom{
//       padding: 1.5rem 1.5rem 1rem;
//       width: 100vw;
//       position: fixed;
//       margin-left: -1.5rem;
//       top: 20%;
//       background-color: #fff;
//       z-index: 9;
//       border-top-left-radius: 1.25rem;
//       border-top-right-radius: 1.25rem;
//     }
//     .is-single-filter .mobile-filter-section{
//         padding-top: 90px;
//     }

//     .is-single-filter {
//       .card-checkbox-mobile .offer-filter__title-collapse {
//         display: block !important;
//       }
//       .mobile-filter-section {
//         .dropdown__list {
//           @include maxSm {
//             overflow: unset;
//             box-shadow: none;
//             max-height: unset !important;
//             margin-top: 8px;
//             top: 54px!important;
//             border: 0;
//               ul {
//                 border: 1px solid #e3e4e6;
//                 border-radius: 8px;
//               }
//           }
//         }
//       }
//     }

//     .tcb-modal__wrapper.is-single-filter .news_filter-group{
//       padding-top: 0 !important;
//     }

//     .offer-filter__header {
//       padding: 1.5rem 1.5rem 1rem;
//       display: flex;
//       align-items: center;
//       justify-content: space-between;
//       border-bottom: 1px solid #dedede;
//       .title {
//         font-size: 16px;
//         font-weight: 600;
//         line-height: 24px;
//       }
//     }
//     .news_filter-group {
//       display: block;
//       padding: 16px 16px 90px;
//       margin-bottom: auto;
//     }

//     .tcb-modal {
//       position: fixed;
//       top: 0;
//       left: 0;
//       width: 100%;
//       height: 100%;
//       z-index: 1300;
//       background: rgba(0, 0, 0, .6);
//       overflow: hidden;
//       @include maxSm {
//         width: 100vw;
//       }
//     }
//     .tcb-modal__wrapper{
//       display: flex;
//       position: relative;
//       width: 100vw;
//       height: 80%;
//       margin-top: auto;
//       background: white;
//       overflow: hidden;
//       flex-direction: column;
//       justify-content: space-between;
//       box-shadow: 0px 4px 30px 0px #0000004D;
//       border-top-left-radius: 1.25rem;
//       border-top-right-radius: 1.25rem;
//       padding: 0 0 1.5rem;
//       -ms-overflow-style: none;  /* IE and Edge */
//       scrollbar-width: none;  /* Firefox */
//       &::-webkit-scrollbar {
//         display: none;
//       }
//       .news_filter-group{
//         width: 100%;
//         height: calc(100% - 10.5rem);
//         overflow: hidden auto;
//         padding-top: 1.5rem;
//       }
//     }

//     .btn-close {
//       font-weight: 500;
//       position: absolute;
//       right: 16px;
//       color: #ed1c24;
//     }

//     .tcb-modal_header {
//       padding: 16px;
//       position: relative;
//       border-bottom: 1px solid #dedede;

//       .tcb-modal_title {
//         font-weight: 600;
//         color: rgba(0, 0, 0, 0.87); // Change by Adobe
//       }

//       margin-bottom: auto;
//     }

//     .news-filter__wrapper {
//       padding: 0 !important;
//     }

//     .tcb-modal_action-bar {
//       padding: 1.5rem 1.5rem 0 1.5rem;
//       margin-top: auto;

//       .tcb-button {
//         display: flex;
//         justify-content: space-between;

//         img {
//           line-height: 0;
//         }
//       }
//     }
    
//     .tcb-modal {
//       display: flex;
//     }
    
//   }

//   .tcb-modal {
//     .news_filter-group {
//       flex: 1;
//     }

//     .news_filter-group {
//       display: flex;
//       flex-direction: column;
//       padding: 0 1.5rem;
//     }

//     .offer-filter__tab button {
//       padding: 12px 16px;
//     }

//     .offer-filter__tab.big-size button {
//       padding: 12px 24px;
//     }

//     .offer-filter__title {
//       position: relative;
//       .btn-close {
//         right: 0;
//         top: 0;
//       }
//     }
//   }

//   .not-found {
//     padding-top: 28px;
//     display: none;
//     text-align: center;
//     max-width: 490px;
//     margin: 0 auto;
//     .not-found-img {
//       display: flex;
//       justify-content: center;
//       img {
//         line-height: 0;
//       }
//     }
//     .description {
//       padding-top: 24px;
//       font-size: 22px;
//       font-weight: 600;
//       line-height: 34px;
//       letter-spacing: 0.38px;
//     }
//   }

//   .offer-img {
//     display: block;
//     position: absolute;
//     width: 100%;
//     height: 100%;
//     img {
//       border-radius: 8px;
//       width: 100%;
//       height: 100%;
//       object-fit: cover;
//       object-position: center center;
//     }
//   }

//   .offer-img-mb {
//     display: none;
//     position: absolute;
//     width: 100%;
//     height: 100%;
//     img {
//       border-radius: 8px;
//       width: 100%;
//       height: 100%;
//       object-fit: cover;
//       object-position: center center;
//     }
//   }

//   .offer-banner__text {
//     z-index: 2;
//   }
//   .black-text {
//     color: hsl(0,0%,0%);
//   }
//   .offer-text__title {
//     z-index: 2;
//     color: hsl(0,0%,100%);
//     font-size: 24px;
//     font-weight: 300;
//     line-height: 1.5;
//     margin: 16px 0 24px;
//     .text-small {
//       font-size: 14px;
//     }
//   }
//   .banner__description {
//     color: hsl(0,0%,100%);
//     letter-spacing: -0.2px;
//   }

//   .banner__text {
//     color: hsl(0,0%,100%);
//     font-weight: 600;
//     line-height: 1.5;
//   }

//   .offer-listing__wrapper {
//     display: flex;
//     flex-wrap: wrap;
//     margin: -12px;
//   }

//   .offer-filter__container {
//     padding: 12px;
//     max-width: 25%;
//     flex-basis: 25%;
//     @include maxLgSemi {
//       padding: 1rem 0 1rem 1rem;
//     }
//   }

//   .offer-filter__title {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     padding-bottom: 8px;
//     margin-bottom: 24px;
    
//     h6, p {
//       line-height: 20px;
//       font-size: 16px;
//       font-weight: 600;
//     }
//     &--no-border{
//       padding-bottom: 0;
//       margin-bottom: 1rem;
//     }
//     &-collapse{
//       cursor: pointer;
//       .expanded{
//         display: none;
//       }
//     }
//   }
//   .offer-filter__title-close{
//     display: none;
//     width: 1.5rem;
//     height: 1.5rem;
//   }
//   .offer-filter__collapse-wrapper{
//     overflow: hidden;
//     transition: height 200ms;
//   }

//   .filter--border-bottom {
//     border-bottom: 1px solid #dedede;
//   }

//   .autocomplete__wrapper {
//     margin: 0;
//   }

//   .offer-filter__checkbox-item {
//     margin-bottom: 24px;
//     position: relative;

//     .checkbox-item__wrapper div {
//       color: #616161;
//     }
//   }

//   .card-promotion__list {
//     margin: -12px;
//     display: flex;
//     flex-wrap: wrap;
//   }

//   .card-promotion__item-wrapper {
//     padding: 0.75rem;

//     @include maxSm {
//       &.card-promotion__item-empty {
//         padding: 0 1rem;
//       }
//     }
//   }

//   .card-promotion__item {
//     display: flex;
//     flex-direction: column;
//     background-color: #fff;
//     border-radius: 8px;
//     height: 100%;
//     max-width: 400px;
//   }

//   .card-content__title {
//     font-weight: 600;
//   }

//   .offer-cards-title {
//     font-size: 1.75rem;
//     font-weight: 300;
//     line-height: 1.25;
//     margin-bottom: 32px;
//   }

//   // .partner-offer-component .partner-offer-component__container {
//   //   display: flex;
//   //   flex-direction: column;
//   // }

//   .offer-cards__container {
//     padding: 12px;
//     max-width: 75%;
//     flex-basis: 75%;
//     @include maxLgSemi {
//       padding: 1rem;
//     }
//   }

//   .partner-offer-component__container {
//     .title {
//       font-size: 1.75rem;
//       font-weight: 300;
//       line-height: 1.25;
//       margin-bottom: 20px;
//     }

//     .offer-card-list {
//       display: grid;
//       grid-auto-flow: row;
//       grid-template-columns: repeat(3, 1fr);
//       grid-gap: 24px;

//       .offer-card {
//         border-radius: 8px;
//         background-color: var(--primary-white);
//         display: flex;
//         flex-direction: column;
//         overflow: hidden;
//       }

//       .card-list__item-body {
//         background-color: #fff;
//         padding: 24px 24px 32px;
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;
//       }

//       .offer-card {
//         .image {
//           display: flex;
//           justify-content: center;
//           flex-shrink: 0;
//         }

//         .content {
//           padding: 24px 16px;
//           display: flex;
//           flex-direction: column;
//           justify-content: space-between;
//           background-color: white;
//           flex-grow: 1;

//           &:hover {
//             text-decoration: underline;
//           }

//           .card-title {
//             font-size: 1rem;
//             font-weight: 600;
//             margin-bottom: 8px;
//           }

//           .offer-info {
//             padding: 16px;
//             background-color: #f5f5f5;
//             border-radius: 8px;
//             >p:not(.offer-info__title) {
//               color: #616161;
//               font-weight: 300;
//             }
//             .offer-info__title {
//               font-weight: 600;
//             }
//           }

//           .due-date {
//             color: var(--gray-600);
//             font-weight: 600;
//             font-size: 0.875rem;
//             letter-spacing: 2px;
//             text-transform: uppercase;
//             margin-top: 16px;
//           }
//         }
//       }

//     }

//     .read-more {
//       display: flex;
//       justify-content: center;
//       padding: 12px;
//       padding-top: 24px;
//       margin: auto;
//       width: fit-content;
//       align-self: center;

//       &:hover {
//         cursor: pointer;
//         text-decoration: underline;
//       }

//       span {
//         font-size: 16px;
//       }

//       .expand {
//         margin-left: 10px;
//       }
//     }
//   }

//   .offer-cards__wrapper {
//     margin-bottom: 52px;
//     &.not-offer {
//       .read-more {
//         display: none;
//       }
//       .offer-card-list,.card-promotion__list {
//         display: none;
//       }
//       .not-found {
//         display: block;
//       }
//     }
//   }

//   .offer-cards__wrapper:last-child {
//     margin-bottom: 0;
//   }

//   .card-offer__image {
//     display: flex;
//     justify-content: center;
//     flex-shrink: 0;
//     img {
//       border-top-left-radius: 8px;
//       border-top-right-radius: 8px;
//     }
//   }

//   .card-offer__content {
//     padding: 24px 16px;
//     display: flex;
//     flex-direction: column;
//     position: relative;
//     height: 100%;
//     width: 100%;
//     justify-content: space-between;
//   }

//   .card-offer__content:hover {
//     text-decoration: underline;
//     cursor: pointer;
//   }

//   .card-content__title {
//     margin-bottom: 8px;
//     line-height: 1.5;
//   }

//   .card-content__description {
//     color: #616161;
//     font-weight: 300;
//     line-height: 1.5;
//   }

//   .card-content__label {
//     color: #616161;
//     margin-top: 16px;
//     font-weight: 600;
//     font-size: 0.875rem;
//     line-height: 1.5;
//     letter-spacing: 2px;
//     text-transform: uppercase;
//   }

//   .input__radio {
//     background-color: #fff;
//   }

//   .section__margin-medium & {
//     margin-top: 32px;
//     margin-bottom: 32px;
//   }

//   @media (min-width: 768px) {
//     .card-promotion__item-wrapper {
//       max-width: 50%;
//       flex-basis: 50%;
//     }
//   }

//   @media (min-width: 992px) {
//     .card-promotion__item-wrapper {
//       max-width: 33.333333%;
//       flex-basis: 33.333333%;
//     }
//     .section__margin-medium & {
//       margin-top: 48px;
//       margin-bottom: 48px;
//     }
//   }

//   @media (max-width: 991px) {
//     .partner-offer-component .partner-offer-component__container .offer-card-list {
//       grid-template-columns: repeat(2, 1fr);
//     }
//   }

//   // @media (min-width: 768px) {
//   //   .partner-offer-component__container .offer-card-list .offer-card:nth-child(n + 7) {
//   //     display: none;
//   //   }
//   // }

//   @media (max-width: 767px) {
//     .offer-filter__dropdown {
//       margin-bottom: 20px;
//       &.thinner {
//         margin-bottom: 8px;
//       }
//     }

//     .offer-cards-title {
//       margin-bottom: 24px;
//     }

//     .card-promotion__item-wrapper {
//       padding: 8px;
//     }

//     .offer-img {
//       display: none;
//     }

//     .offer-img-mb {
//       display: block;
//     }

//     .offer-banner {
//       min-height: 496px;
//       margin-bottom: 52px;
//       justify-content: flex-start;
//     }
    
//     .offer-banner__text {
//       padding: 32px 16px 32px 16px;
//     }

//     .card-promotion__list {
//       overflow: auto;
//       flex-wrap: nowrap;
//       width: 100%;
//       margin: 0;
//     }

//     .card-promotion__item {
//       width: 280px;
//     }

//     .btn-open-filter {
//       display: flex;
//       font-size: 16px;
//       border: 1px solid #E3E4E5;
//       width: fit-content;
//       border-radius: 2.0625rem;
//       font-weight: 400;
//       justify-content: space-between;
//       align-items: center;
//       padding: 0.5rem 0.75rem;
//       background-color: transparent;
//       gap: 1.25rem;
//       white-space: nowrap;
//       line-height: 1.5rem;

//       @include maxSm {
//         gap: 8px;
//       }


//       &.active{
//         border: 1px solid #212121;
//       }

//       &__total{
//         display: none;
//         width: 1.3125rem;
//         height: 1.3125rem;
//         display: block;
//         border-radius: 50%;
//         background-color: #212121;
//         font-size: 12px;
//         font-weight: 400;
//         line-height: 1.3125rem;
//         color: white;
//       }

//       &.scroll-over {
//         width: auto;
//         line-height: 0;
//         position: fixed;
//         top: 100px;
//         left: 0;
//         background-color: #fff;
//         padding: 17px 28px 17px 34px;
//         border-radius: 0 8px 8px 0;
//         box-shadow:
//           0 33px 181px rgba(0, 0, 0, 0.04),
//           0 13.7866px 75.6175px rgba(0, 0, 0, 0.029),
//           0 7.37098px 40.4287px rgba(0, 0, 0, 0.024),
//           0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
//           0 2.19453px 12.0367px rgba(0, 0, 0, 0.016),
//           0 0.913195px 5.00873px rgba(0, 0, 0, 0.011);
//         display: none;
//         z-index: 1300;
//       }
//     }

//     .tcb-filter-button-icon{
//       display: flex;
//       justify-content: center;
//       align-items: center;
//       transition: all 0.3s ease-in-out;
//     }

//     .tcb-filter-button-title{
//       text-transform: capitalize;
//     }

//     .partner-offer-component__container {
//       margin-top: 0 !important;
//       .offer-card-list {
//         display: flex;
//         width: calc(100% + 16px);
//         margin: -8px;
//         padding: 8px;
//         flex-wrap: nowrap;
//         overflow: auto;
//         gap: 16px;
//         .offer-card {
//           width: 280px;
//           flex-shrink: 0;
//           .image {
//             width: 280px;
//           }
//         }
//       }
//       .read-more {
//         display: none;
//       }
//       .title {
//         margin-bottom: 12px;
//       }
//     }
    

//     .offer-cards__container {
//       max-width: 100%;
//       padding: 8px;
//     }

//     .partner-offer-component .partner-offer-component__container .offer-card-list {
//       display: flex;
//       flex-direction: row;
//       overflow: scroll;
//     }

//     .offer-card-list.no-wrap .offer-card {
//       max-width: 77.2%;
//       flex: 0 0 77.2%;
//       margin: 8px;
//     }
//   }
// }