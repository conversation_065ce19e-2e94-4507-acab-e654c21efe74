/* Fixing Rate Component */
/* Fixing rate border modify */
/* End of Fixing Rate Component */
/* Fixing Rate */
.body {
  -webkit-print-color-adjust: exact !important;
  print-color-adjust: exact !important;
  forced-color-adjust: exact !important;
}

.exchange-rate,
.gold-rate,
.fixing-rate {
  .hidden {
    display: none !important;
  }
  .exchange-rate__empty-label {
    min-height: calc(100% - 315px);
  }

  .date-time-wrapper__input-extra {
    position: relative;
    pointer-events: visible;
    right: unset;
  }
  .exchange-rate__header {
    display: flex;
    align-items: center;
    margin-bottom: 23px;
  }
  .title-cmp {
    padding-bottom: 0;
  }
  .title-cmp__title {
    padding-bottom: 0;
    font-size: 28px;
    font-weight: 300;
    line-height: 36px;
  }
  .exchange-rate__header-select {
    display: flex;
    margin-left: 44px;
    gap: 24px;
    align-items: center;
    position: relative;
    min-width: 0;
  }
  .header-select__data {
    position: relative;
    &.time-select {
      min-width: 0;
    }
  }
  .header-select__data-input {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e3e4e6;
    padding: 16px;
    justify-content: space-between;
    > p {
      display: flex;
      align-items: center;
    }
  }
  .header-select__data-input.no-pointer {
    pointer-events: none;
  }
  .exchange-rate-calendar,
  .gold-rate-calendar,
  .fixing-rate-calendar, {
    position: relative;

    .calendar-popup {
      top: 0;
      bottom: unset;
    }
  }
  .data-input__arrow-icon {
    width: 14px;
    height: 14px;
    margin-left: 8px;
    flex: 1;
    >img {
      position: relative;
      display: block;
      width: 14px;
      height: 14px;
      margin: auto;
    }
  }
  .data-input__prefix {
    display: flex;
    align-items: center;
    > p {
      font-weight: 500;
      margin-left: 10px;
      margin-right: 20px;
    }
  }
  .data-input__suffix {
    display: flex;
    align-items: center;
    min-width: 0;
    .selected-time-slot {
      min-width: 0;
      overflow: hidden;
      word-break: break-word;
    }
  }
  .data-input__calendar-icon {
    box-sizing: border-box;
    display: inline-block;
    overflow: hidden;
    width: 16px;
    height: 16px;
    background: none;
    opacity: 1;
    border: 0px;
    margin: 0px;
    padding: 0px;
    position: relative;
    img {
      position: absolute;
      inset: 0px;
      box-sizing: border-box;
      padding: 0px;
      border: none;
      margin: auto;
      display: block;
      width: 0px;
      height: 0px;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: cover;
      object-position: center center;
    }
  }
  .exchange-rate__table-content {
    background-color: #fff;
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%),
      0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%),
      0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    border-radius: 8px;
  }
  .exchange-rate__table-outer {
    overflow: auto;
    background-color: #fff;
    box-shadow: 0 33px 181px rgb(0 0 0 / 4%),
      0 13.7866px 75.6175px rgb(0 0 0 / 3%),
      0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%),
      0 2.19453px 12.0367px rgb(0 0 0 / 2%),
      0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    border-radius: 8px;
    .exchange-rate__table-content {
      box-shadow: none;
      background-color: transparent;
      
      @media (max-width: 767px) {
        min-width: 500px;
      }
    }
  }

  .exchange-rate__table-records {
    display: flex;
    width: 100%;
  }
  .table-note {
    width: 100%;
    padding-bottom: 24px;
  }
  .table__first-column {
    width: 17%;
    border-bottom: 1px solid #dedede;
    text-align: left;
    padding: 16px 0;
  }
  .table__first-column.one-col {
    width: 100% !important;
    max-width: unset !important;
    border-left-width: 0;
    border-right-width: 0;
    text-align: center !important;
    span{
      padding-left: 5px;
    }
  }
  .table__first-column.left-pos {
    text-align: left;
    padding-left: 40px;
  }
  .table-records__data.strong-par {
    p {
      font-weight: 700;
    }
  }
  .table__first-column.strong-par {
    p {
      font-weight: 700;
    }
  }
  .table-header {
    .table__first-column {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    p {
      font-weight: 700;
    }
    .up-par {
      p {
        letter-spacing: 2px;
        text-transform: uppercase;
        font-weight: 600;
        font-size: 14px;
      }
    }
    .last-column {
      width: 27.66%;
      padding: 16px 0;
      display: flex;
      align-items: center;
      > p {
        width: 100%;
      }
    }
  }
  .table-records__data {
    display: flex;
    width: 75%;
    flex-wrap: wrap;
    flex: auto;
    .group-header {
      display: flex;
      flex-direction: column;
      width: 50%;
      flex: auto;
      .table-records__data-content {
        width: 100%;
        flex: 1;
        .data-content__item {
          width: 50%;
        }
      }
    }
  }
  .table-records__data-content {
    width: 50%;
    text-align: center;
    padding: 16px 0;
    border-bottom: 1px solid #dedede;
    border-left: 1px solid #dedede;
  }
  .width-66-33 {
    .table-records__data-content {
      &:nth-child(odd) {
        width: 66.66%;
      }
      &:nth-child(even) {
        width: 33.33%;
      }
    }
  }
  .width-33-66 {
    .table-records__data-content {
      &:nth-child(odd) {
        width: 33.33%;
      }
      &:nth-child(even) {
        width: 66.66%;
      }
    }
  }
  .table-records__data-content.row-divided {
    display: flex;
    justify-content: center;
    padding: 0;
  }
  .data-content__item {
    width: 50%;
    padding: 16px 0;
    &:nth-child(even) {
      border-left: 1px solid #dedede;
    }
  }
  .first-border {
    .data-content__item {
      &:nth-child(odd) {
        border-right: none;
      }
      &:nth-child(even) {
        border-left: none;
      }
    }
    .table-records__data-content {
      border-left: none;
      border-right: none;
      &:nth-child(odd) {
        border-left: 0.5px solid #dedede;
      }
    }
  }
  .exchange-rate__table-note {
    text-align: left;
    font-style: italic;
    padding-left: 40px;
    width: 100%;
    p{
      margin-top: 16px;
    }
  }
  .exchange-rate__view-more {
    margin-top: 36px;
    text-align: center;
    > button {
      font-family: inherit;
      display: inline-flex;
      gap: 12px;
      font-weight: 600;
      align-items: center;
      border:none;

      &:hover {
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }
  .view-more__icon {
    display: flex;
    align-items: center;
    transition: all 0.3s ease-in-out;
  }
  .time__dropdown {
    display: none;
    transition: opacity 485ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
      transform 323ms cubic-bezier(0.4, 0, 0.2, 1) 154ms;
    min-width: 118px;
    overflow-x: hidden;
    overflow-y: auto;
    position: absolute;
    right: 0;
    top: -5px;
    z-index: 2;
    background: #fff;
    color: rgba(0, 0, 0, 0.87);
    border-radius: 4px;
    text-align: center;
    box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%),
      0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
    ul {
      list-style: none;
      padding-inline-start: unset;
      padding-top: 8px;
      padding-bottom: 8px;
    }
    li {
      padding: 6px 16px;
      min-height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      span {
        vertical-align: middle;
      }
      &:hover, &.selected {
        background-color: rgba(0, 0, 0, 0.08);
        span {
          font-weight: 600;
        }
      }
      
    }
  }
  .time__dropdown.active {
    display: block;
  }
  .first-row {
    border-top: none;
  }
  .first-column {
    border-left: none;
  }
  .last-row {
    border-bottom: none;
  }
  .last-column {
    border-right: none;
  }
  .exchange-rate__popup {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    padding: 48px 64px 12px;
    background-color: #f5f6f8;
    z-index: 1301;
    display: flex;
    flex-direction: column;
    .exchange-rate__table {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      margin-bottom: 24px;
      padding-bottom: 24px;
      background-color: #fff;
      box-shadow: 0 33px 181px rgba(0,0,0,.04), 0 13.7866px 75.6175px rgba(0,0,0,.029), 0 7.37098px 40.4287px rgba(0,0,0,.024), 0 4.13211px 22.664px rgba(0,0,0,.02), 0 2.19453px 12.0367px rgba(0,0,0,.016), 0 0.913195px 5.00873px rgba(0,0,0,.011);
      border-radius: 8px;
    }
    .exchange-rate__table-content {
      flex: 1;
      overflow: auto;
      box-shadow: unset !important;
    }
  }
  .exchange-rate__popup-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 0 auto;
    width: 100%;
    height: 100%;
  }
  .popup__close-button {
    position: absolute;
    right: 60px;
    top: 53px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    display: block;
    &::before {
      position: absolute;
      left: 7px;
      content: " ";
      height: 100%;
      width: 2px;
      background-color: #000;
      transform: rotate(45deg);
    }
    &::after {
      position: absolute;
      left: 7px;
      content: " ";
      height: 100%;
      width: 2px;
      background-color: #000;
      transform: rotate(-45deg);
    }
  }
  .popup__download-button {
    display: flex;
  }
  .download-button__wrapper {
    margin: 0 auto;
    display: inline-flex;
    width: 254px;
    > button {
      background-color: #fff;
      color: #000;
      position: relative;
      display: inline-flex;
      padding: 16px 24px;
      border-radius: 8px;
      outline: none;
      border: none;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all 0.3s ease-in;
      align-items: center;
      grid-gap: 12px;
      gap: 12px;
      width: 100%;
      z-index: 1;
      justify-content: space-between;
    }
  }
  .download-button__icon {
    transition: all 0.3s ease-in-out;
  }
}

.fixing-rate .table-header .table__first-column p:first-child{
  text-align: left;//added by Adobe
}
.exchange-rate__footer {
  display: flex;
  background-color: #000 !important;
  align-items: center;
  justify-content: space-between;
  padding: 32px 16px 32px 16px;
  display: none;
  color: #fff;
  margin-top: 16px;
}

.exchange-rate.width-25-75 {
  .table__first-column {
    width: 25%;
  }
}

.gold-rate {
  .table-header {
    .first-row {
      padding: 16px 0;
    }
    .last-column {
      width: 50%;
    }
  }
  .first-column {
    text-align: center;
  }
}

.fixing-rate, .gold-rate {
  .table-header {
    .last-column {
      width: 50%;
    }
  }
  .table__first-column {
    text-align: center;
  }
}

@media not print {
  .exchange-rate {
    .download-button__wrapper {
      > button {
        &:hover {
          background-color: #a2a2a2;
          color: var(--gray-600);
          img {
            filter: brightness(0) saturate(100%) invert(37%) sepia(0%)
              saturate(1112%) hue-rotate(136deg) brightness(95%) contrast(81%);
            transition: all 0.3s ease-in;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .exchange-rate,
  .gold-rate,
  .fixing-rate {
    .exchange-rate__header {
      display: block;
    }
    .title-cmp {
      margin-bottom: 24px;
    }
    .exchange-rate__header-select {
      flex-direction: column;
      margin-left: 0;
      gap: 16px;

      .header-select__data {
        width: 100%;
      }
    }
    .exchange-rate__table-note {
      padding-left: 8px;
      padding-right: 8px;
    }
    .table__first-column {
      width: 25%;
      min-width: 108px !important;
      &.one-col {
        max-width: unset !important;
        flex: unset !important;

        span:before { content: ' '; display: block; }
      }
    }
    .table-header {
      .table__first-column {
        padding-left: 8px;
        padding-right: 8px;
      }
      .last-column {
        width: 25%;
      }
    }
    .table__first-column.left-pos {
      padding-left: 8px;
    }
    .table-records__data {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: calc(100% - 108px);
      max-width: calc(100% - 108px);
    }
    .group-header {
      width: 50%;
      .first-row {
        width: 100%;
      }
    }
    .exchange-rate__popup {
      padding: 16px 0;
    }
    .popup__close-button {
      top: 22px;
      right: 22px;
    }
  }

  .gold-rate {
    .table-header {
      .first-row {
        padding: 16px 0;
      }
      .last-column {
        width: 50%;
      }
    }
  }

  .fixing-rate {
    .table-header {
      .last-column {
        width: 50%;
      }
      .first-column {
        text-align: left;
      }
    }
  }
}

@page {
  margin: 0;
}

@media print {
  .print {
    -webkit-print-color-adjust: exact !important;

    .popup__download-button {
      margin-top: 36px;
    }
    .exchange-rate__footer {
      display: flex;
      print-color-adjust: exact;
      -webkit-print-color-adjust: exact;
    }
    .exchange-rate {
      .exchange-rate__popup {
        padding: 0;
        padding-top: 44px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: unset;
        .exchange-rate__popup-wrapper {
          overflow: visible;
          .exchange-rate__table {
            overflow: visible;
          }
        }
        .popup__close-button {
          display: none;
        }
        .exchange-rate__header {
          display: block;
          gap: 44px;
          .exchange-rate__header-select {
            margin-left: 0;
            margin-top: 44px;
            >* {
              flex: 1;
            }
          }
        }
        .table__first-column {
          &.one-col {
            span:before { content: ' '; display: block; }
          }
          &.left-pos {
            padding-left: 8px;
          }
        }
        .table-header {
          .table__first-column {
            padding-left: 8px;
          }
        }
      }
    }
  }
}

.exchange-rate {
  .exchange-rate__table-content {    
    @media (max-width: 767px) {
      min-width: 500px;
    }
  }
}
