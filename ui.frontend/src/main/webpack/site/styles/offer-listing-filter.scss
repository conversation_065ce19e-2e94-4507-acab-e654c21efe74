.offer-listing-filter {
  &__header {
    background-color: gray;
    height: 5rem;
    max-height: 5rem;
  }

  &__footer {
    background-color: gray;
    height: 8rem;
    max-height: 8rem;
  }

  &__body {
    padding-left: unset !important;
    padding-right: unset !important;

    @media (max-width: 768px) {
      padding-left: unset !important;
      padding-right: unset !important;
    }

    .input__checkbox:checked {
      border-color: black;
      background-color: black;
    }

    .offer-listing-promotions {
      &__wrapper {
        display: flex;
        flex-wrap: wrap;
        margin: -12px;

        @include maxLgSemi {
          margin: 0;
        }
        

        .tcb-modal_action-bar {
          width: 100%;
          display: flex;
          
          gap: 1rem;
          z-index: 1;
          .tcb-apply-filter-button {
            padding: 1rem 1.5rem;
            display: flex;
            width: 100%;
            background-color: black;
            justify-content: space-between;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 0.5rem;

            &:hover {
              background-color: #616161;

              img {
                filter: brightness(0) saturate(100%) invert(99%) sepia(1%) saturate(2105%) hue-rotate(297deg)
                  brightness(114%) contrast(101%);
              }
            }
          }

          .tcb-close-filter-button {
            width: 100%;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            background-color: transparent;
            align-items: center;
            color: var(--primary-black);
            font-weight: 600;
            font-size: 1rem;
            line-height: 1.25;
            gap: 1.5rem;
            padding-left: 1rem;
            padding-right: 1rem;
            cursor: pointer;
            border-radius: 0.5rem;
            border: 1px solid var(--secondary-gray);
            &.disabled {
              border-color: var(--cta-disabled);
              color: var(--secondary-mid-grey-100);
              pointer-events: none;
            }
            img {
              width: 1rem;
              height: 1rem;
            }
          }
        }

        .offer-cards {
          &__wrapper {
            .pagination {
              width: 100%;
              .pag-container {
                width: 100%;
                display: flex;
                grid-template-columns: 20px 1fr 20px;
                width: 100%;
                align-items: center;
                justify-content: center;

                .pag-number {
                  display: flex;

                  &.pag-scroll {
                    overflow: auto;
                    scrollbar-width: none;
                    @include maxSm {
                      width: 60%;
                      scroll-snap-type: x mandatory;
                    }
                  }
                }

                label {
                  display: flex;
                  width: 24px;
                  height: 24px;
                  align-items: center;
                  justify-content: center;

                  .next {
                    transform: rotate(180deg);
                  }
                  img:first-child {
                    display: none;
                  }
                  img:last-child {
                    display: block;
                  }
                  &.disable {
                    pointer-events: none;
                    img:first-child {
                      display: block;
                    }
                    img:last-child {
                      display: none;
                    }
                  }
                }

                a {
                  cursor: inherit;
                  scroll-snap-align: start;
                }

                a:not(.active) > span:hover {
                  cursor: pointer;
                  background-color: rgba(0, 0, 0, 0.04) !important;
                  color: unset !important;
                  border-radius: 0.25rem !important;
                  color: red;
                }

                a.hide {
                  display: none !important;
                }

                a.hide-left {
                  &:after {
                    content: '...';
                    margin-left: 12px;
                    pointer-events: none;
                  }
                }

                a.hide-right {
                  &:before {
                    content: '...';
                    margin-right: 12px;
                  }
                }
              }
            }
          }
        }

        .mobile-filter-section {
          .dropdown__list {
            max-height: 25.25rem;
          }
        }
      }

      @include maxSm {
        .search-primary-container {
          min-height: unset;
          height: fit-content;
          padding: unset;

          .search-box {
            width: 100%;

            .input-container {
              .search-box_icon {
                position: absolute;
                z-index: 10;
                height: 100%;
                display: flex;
                align-items: center;
                margin-left: 1rem;
              }
            }
          }
        }

        .offer-filter {
          &__dropdown {
            margin-bottom: unset;

            &.merchant-mobile-element {
              margin-bottom: 1.5rem;
            }

            .dropdown__wrapper {
              .dropdown__display.merchant-dropdown-display,
              .dropdown__display.location-dropdown-display {
                padding: 1rem;
              }
            }
            &.merchant-mobile-element {
              margin-bottom: 1.5rem;
            }
            &.filter-mobile-element {
              margin-bottom: 1.5rem;
            }
          }
        }

        .dropdown {
          &__wrapper {
            margin-bottom: unset;
          }

          &__display {
            padding: 1rem;

            .sort-title {
              display: flex;
              flex-direction: column;

              .display {
                &__title {
                  color: var(--secondary-grey-60);
                  font-size: 0.75rem;
                }
              }
            }
          }
        }

        .mobile-filter-section {
          .dropdown__list {
            max-height: 20rem;
          }
        }

        .offer-cards {
          &__container {
            flex-basis: 100% !important;
            padding: 0 !important;
          }
        }
        .card-offer {
          &__image {
            max-width: 33.33%;
            flex-basis: 33.33%;
            img {
              border-radius: 0.5rem !important;
              aspect-ratio: 4 / 3;
            }
          }
          &__content {
            max-width: 66.66%;
            flex-basis: 66.66%;
            padding: 0 !important;
            grid-gap: 0.25rem !important;
            gap: 0.25rem !important;
            &::before,
            &::after {
              width: 1.125rem !important;
              height: 1.125rem !important;
            }

            .card-content {
              &__label {
                font-size: 0.625rem !important;
                line-height: 0.938rem !important;
                height: fit-content !important;
                display: none !important;
              }
              &__title {
                font-size: 1rem !important;
                line-height: 1.5rem !important;
                font-weight: 400;
                height: fit-content !important;
              }

              &__description {
                font-size: 1rem !important;
                line-height: 1.5rem !important;
                font-weight: 400 !important;
                max-height: 4.5rem !important;
                color: #000000 !important;
                & > * {
                  font-size: unset !important;
                  overflow: hidden !important;
                  line-height: unset !important;
                  text-overflow: ellipsis !important;
                  word-break: break-word !important;
                  display: -webkit-box !important;
                  -webkit-line-clamp: 3 !important;
                  -webkit-box-orient: vertical !important;
                }
              }

              &__expired-date {
                column-gap: 0.25rem !important;
                grid-column-gap: 0.25rem !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;

                & > img {
                  width: 0.625rem;
                  height: 0.625rem;
                }

                &--icon {
                  width: 0.75rem;
                  height: 0.75rem;
                }

                &--date-time {
                  font-size: 0.875rem;
                  line-height: 1.3125rem;
                  color: #616161;
                }
              }

              &__expired-date-count-down {
                font-size: 0.625rem;
              }

              &__link {
                display: block;
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;

                a {
                  width: 100%;
                  height: 100%;
                  opacity: 0;
                }

                &--button {
                  .button--text {
                    font-size: 0.625rem;
                    line-height: 0.938rem;
                  }

                  .button--icon {
                    & > img {
                      width: 0.625rem;
                      height: 0.625rem;
                    }
                  }
                }
              }

              &__favorite-promo {
                bottom: 1rem !important;
                right: 1rem !important;
                & > img {
                  width: 1.3125rem !important;
                  height: 1.1875rem !important;
                }
              }
            }
          }
        }

        .expiry-date-section {
          flex: 1;
        }

        .expiry-date-section-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          gap: 0.5rem;
        }

        .card-promotion {
          &__list {
            overflow: unset !important;
            display: flex !important;
            flex-wrap: wrap !important;
            box-sizing: border-box !important;
            flex-direction: column;
            gap: 0.375rem;

            position: relative;
          }

          &__item-wrapper {
            flex-grow: 0 !important;
            max-width: 100% !important;
            flex-basis: 100% !important;
            box-sizing: border-box !important;
            padding: 0;
          }

          &__item {
            width: 100% !important;
            padding: 1rem;
            border-radius: 0 !important;
            gap: 0.5rem !important;
          }
          &__item-body {
            display: flex;
            flex-direction: row !important;
            flex-wrap: nowrap;
            gap: 0.75rem;
          }

          &__item-product-types {
            width: 100%;
            display: flex !important;
            gap: 0.5rem;
            flex-wrap: nowrap;
            overflow-x: auto;
          }

          &__item-product-type {
            width: fit-content;
            padding: 0.25rem 0.375rem;
            border: 1px solid var(--secondary-light-grey-80);
            border-radius: 0.3125rem;
            font-size: 0.8125rem;
            line-height: 1.3125rem;
            color: #ed1c24;
            font-weight: 500;
            text-transform: uppercase;
            white-space: nowrap;
            word-break: keep-all;
          }
        }

        .offer-filter {
          &__checkbox-item-mobile {
            margin-bottom: 1.5rem;
            position: relative;
            line-height: 1.125rem;

            label.checkbox-item__wrapper {
              color: #616161;
            }
          }
        }

        .offer-listing-filter {
          &__button {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            width: 100%;
            overflow-x: scroll;
            padding: 0 1rem !important;
            -ms-overflow-style: none; /* IE and Edge */
            scrollbar-width: none; /* Firefox */
            &::-webkit-scrollbar {
              display: none;
            }
          }
        }
        .offer-listing-filter__button--no-overflow {
          overflow: unset !important;
        }
        .offer-listing-filter__button--cloud {
          mask-image: linear-gradient(
            to right,
            transparent,
            rgb(51, 51, 51) 5%,
            rgb(238, 238, 238) 91%,
            transparent 100%
          );
          mask-size: 100%;
          gap: 8px;
          width: 100vw;
          scroll-snap-type: x mandatory;
          > * {
            scroll-snap-align: center;
          }
        }

        .input-dropdown {
          li {
            min-height: 48px;
          }
        }
      }

      .membership-checkbox, .membership-checkbox-mobile {
        color: var(--secondary-grey-60);
        .offer-filter__checkbox-wrapper {
          margin-bottom: 24px;

          .checkbox-parent {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--secondary-mid-grey-60);
            padding-bottom: 0.5rem;
            .checkbox-item__wrapper {
              .all-checked:checked {
                background-color: var(--primary-black) !important;
              }

              .all-checked:checked::before {
                border-color: var(--primary-white) !important;
              }

              .input__checkbox:checked {
                background-color: var(--primary-white);
                border-color: var(--primary-black);
              }

              .input__checkbox:checked::before {
                border-color: var(--primary-black);
              }
            }
          }

          .checkbox-child {
            .input__checkbox:checked {
              background-color: var(--primary-black);
              border-color: var(--primary-white);
            }
          }

          .toggle-icon:hover {
            cursor: pointer;
          }

          .toggle-icon.expanded {
            display: none;
          }

          .offer-filter__checkbox-child-wrapper {
            flex-direction: column;
            gap: 1rem;
            display: none;

            .checkbox-child {
              margin-bottom: 0px;
              margin-top: 1rem;
            }
          }
        }
      }

      .sort-checkbox, .sort-checkbox-mobile {
        color: var(--secondary-grey-60);
        .offer-filter__checkbox-wrapper {
          margin-bottom: 24px;

          .checkbox-parent {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--secondary-mid-grey-60);
            padding-bottom: 0.5rem;
            .checkbox-item__wrapper {
              .all-checked:checked {
                background-color: var(--primary-black) !important;
              }

              .all-checked:checked::before {
                border-color: var(--primary-white) !important;
              }

              .input__checkbox:checked {
                background-color: var(--primary-white);
                border-color: var(--primary-black);
              }

              .input__checkbox:checked::before {
                border-color: var(--primary-black);
              }
            }
          }

          .checkbox-child {
            .input__checkbox:checked {
              background-color: var(--primary-black);
              border-color: var(--primary-white);
            }
          }

          .toggle-icon:hover {
            cursor: pointer;
          }

          .toggle-icon.expanded {
            display: none;
          }

          .offer-filter__checkbox-child-wrapper {
            flex-direction: column;
            gap: 1rem;
            display: none;

            .checkbox-child {
              margin-bottom: 0px;
              margin-top: 1rem;
            }
          }
        }
      }

      .card-checkbox {
        color: var(--secondary-grey-60);
        .offer-filter__checkbox-wrapper {
          margin-bottom: 24px;

          .checkbox-parent {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--secondary-mid-grey-60);
            padding-bottom: 0.5rem;
            .checkbox-item__wrapper {
              .all-checked:checked {
                background-color: var(--primary-black) !important;
              }

              .all-checked:checked::before {
                border-color: var(--primary-white) !important;
              }

              .input__checkbox:checked {
                background-color: var(--primary-white);
                border-color: var(--primary-black);
              }

              .input__checkbox:checked::before {
                border-color: var(--primary-black);
              }
            }
          }

          .checkbox-child {
            .input__checkbox:checked {
              background-color: var(--primary-black);
              border-color: var(--primary-white);
            }
          }

          .toggle-icon:hover {
            cursor: pointer;
          }

          .toggle-icon.expanded {
            display: none;
          }

          .offer-filter__checkbox-child-wrapper {
            flex-direction: column;
            gap: 1rem;
            display: none;
            @include maxLgSemi {
              padding: 0;
            }

            .checkbox-child {
              margin-bottom: 0px;
              margin-top: 1rem;
            }
          }
        }
      }

      .offer-filter {
        &__checkbox-item {
          margin-bottom: 1.5rem;
          position: relative;
          line-height: 1.125rem;

          label.checkbox-item__wrapper {
            color: var(--secondary-grey-60);
          }
        }
      }

      .search-area {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        gap: 0px;

        @media (max-width: 576px) {
          display: none;
        }

        .search-primary-container {
          min-height: unset;
          height: fit-content;
          padding: 0;

          .search-box {
            width: 100%;

            .input-container {
              .search-input {
                padding: 1rem;
                border-radius: 0.5rem 0 0 0.5rem;
              }

              .search-box_icon {
                position: absolute;
                z-index: 10;
                height: 100%;
                display: flex;
                align-items: center;
                margin-left: 1rem;
              }
            }
          }
        }

        .promotions-filter {
          margin-bottom: 0;

          .information-filter {
            &__load-more {
              margin-top: unset;
              margin-bottom: unset;

              .load-more {
                &__button {
                  background-color: black;
                  color: white;
                  height: 3.5rem;
                  -webkit-box-pack: justify;
                  -ms-flex-pack: justify;
                  -webkit-box-align: center;
                  -ms-flex-align: center;
                  grid-gap: 0.75rem;
                  align-items: center;
                  border: none;
                  border-radius: 0 0.5rem 0.5rem 0;
                  cursor: pointer;
                  display: inline-flex;
                  font-weight: 600;
                  gap: 0.75rem;
                  justify-content: space-between;
                  line-height: 1.5;
                  min-width: -webkit-max-content;
                  min-width: -moz-max-content;
                  min-width: max-content;
                  outline: none;
                  padding: 1rem 1.5rem;
                  position: relative;
                  text-decoration: none;
                  -webkit-transition: all 0.3s ease-in;
                  transition: all 0.3s ease-in;
                  white-space: nowrap;
                  width: 100%;
                  z-index: 1;

                  &-icon {
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    margin-left: 0;
                    -webkit-transition: all 0.3s ease-in-out;
                    transition: all 0.3s ease-in-out;
                  }

                  &:hover {
                    background-color: var(--secondary-grey-60);
                    .load-more__button-icon {
                      filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(1%) hue-rotate(232deg)
                        brightness(107%) contrast(101%);
                    }
                  }
                }
              }
            }
          }
        }
      }

      .input-dropdown {
        ul {
          display: none;
          margin: 0;
          padding: 8px 0;
          overflow: auto;
          list-style: none;
          max-height: 40vh;
          background-color: #fff;
          position: absolute;
          z-index: 10;
          width: 100%;
          li {
            cursor: pointer;
            display: flex;
            outline: 0;
            box-sizing: border-box;
            align-items: center;
            padding-top: 6px;
            padding-left: 16px;
            padding-right: 16px;
            padding-bottom: 6px;
            justify-content: flex-start;
            -webkit-tap-highlight-color: transparent;

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }

            &.input-dropdown__item--active {
              background-color: rgba(0, 0, 0, 0.04);
            }
          }
        }
      }

      .promotion-total-count {
        margin-bottom: 1.5rem;
      }

      .card-promotion {
        &__list {
          margin-bottom: 2rem !important;
        }

        &__item {
          position: relative;
          display: flex;
          flex-direction: column;
          background-color: white;
          border-radius: 0.5rem;
          height: 100%;
          max-width: 100%;
          gap: 0;

          &-body {
            display: flex;
            flex-direction: column;
            height: 100%;
          }

          &-product-types {
            display: none;
          }

          .card-offer {
            &__image {
              flex-shrink: 0;
              display: flex;
              justify-content: center;

              @media (max-width: 768px) {
                height: fit-content;
              }

              &--wrapper {
                box-sizing: border-box;
                display: inline-block;
                overflow: hidden;
                width: initial;
                height: initial;
                background: none;
                opacity: 1;
                border: 0px;
                margin: 0px;
                padding: 0px;
                position: relative;
                max-width: 100%;

                .image-card-offer {
                  box-sizing: border-box;
                  display: block;
                  width: initial;
                  height: initial;
                  background: none;
                  opacity: 1;
                  border: 0px;
                  margin: 0px;
                  padding: 0px;
                  max-width: 100%;

                  & > img {
                    display: block;
                    max-width: 100%;
                    width: initial;
                    height: initial;
                    background: none;
                    opacity: 1;
                    border: 0px;
                    margin: 0px;
                    padding: 0px;
                    aspect-ratio: 4 / 3;
                  }
                }

                & > img {
                  position: absolute;
                  inset: 0px;
                  box-sizing: border-box;
                  padding: 0px;
                  border: none;
                  margin: auto;
                  display: block;
                  width: 0px;
                  height: 0px;
                  min-width: 100%;
                  max-width: 100%;
                  min-height: 100%;
                  max-height: 100%;
                  object-fit: cover;
                  object-position: center center;
                }
              }
            }

            &__content {
              padding: 1rem;
              display: flex;
              flex-direction: column;
              position: static;
              height: 100%;
              width: 100%;
              justify-content: space-between;
              gap: 0.75rem;
              grid-gap: 0.5rem;
              &:hover {
                text-decoration: unset;
                cursor: unset;
              }

              &__wrapper {
                display: flex;
                flex-direction: row;
                flex-direction: column;
                gap: 0.25rem;
              }

              .card-content {
                &__label {
                  font-size: 0.875rem;
                  text-transform: uppercase;
                  font-weight: 600;
                  line-height: 1.313rem;
                  letter-spacing: 0.125rem;
                  color: var(--accent);
                  min-height: 2.625rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-word;
                  margin-top: 0;
                  display: block;
                }

                &__title {
                  display: block;
                  font-weight: 600;
                  line-height: 1.5rem;
                  height: fit-content;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  word-break: break-word;
                  margin-bottom: 0;
                }

                &__description {
                  display: block;
                  color: var(--secondary-grey-60);
                  flex-grow: 1;
                  overflow: hidden;
                  font-weight: 400;

                  & > * {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    line-height: 1.3rem;
                    @include maxSm {
                      -webkit-line-clamp: 3;
                    }
                  }
                }

                &__expired-date {
                  display: flex;
                  grid-column-gap: 0.5rem;
                  column-gap: 0.5rem;
                  color: var(--secondary-grey-60);
                  font-weight: 400;
                  line-height: 1.5rem;
                  align-items: center;
                  &--date-time {
                    font-weight: 400;
                    line-height: 1.3125rem;
                    color: var(--secondary-grey-60);
                  }
                }

                &__link {
                  &--button {
                    position: relative;
                    display: inline-flex;
                    outline: none;
                    border: none;
                    cursor: pointer;
                    align-items: center;
                    text-decoration: none;
                    transition: all 0.3s ease-in;
                    justify-content: space-between;
                    gap: 0.75rem;
                    background-color: inherit;
                    padding: 0.5rem 0;
                    .button--text {
                      font-weight: 600;
                      color: black;

                      &:hover {
                        color: red;
                      }
                    }

                    .button--icon {
                      display: flex;
                      align-items: center;
                      margin-left: 0;
                      transition: all 0.3s ease-in-out;
                    }
                  }
                }

                .expiry-date-section {
                  margin: 0.5rem 0;
                }

                &__expired-date--date-time {
                  white-space: nowrap;
                }

                &__favorite-promo {
                  display: flex;
                  position: absolute;
                  bottom: 0;
                  right: 0;

                  & > img {
                    width: 2rem;
                    height: 1.875rem;
                  }
                }

                .display-none {
                  display: none;
                }

                .display-unset {
                  display: unset;
                }
              }
            }
          }
        }
      }

      .pagination {
        margin-top: 1.5rem;

        .pag-container {
          float: right;
          display: flex;
          align-items: center;
          color: #616161;

          label {
            cursor: pointer;
            font-size: 1.125rem;
            line-height: 1.875rem;
            color: var(--secondary-grey-60);
          }

          &--previous {
            margin-right: 0.5rem;
          }

          &--next {
            margin-left: 0.5rem;
          }
        }

        a {
          color: var(--secondary-grey-60);
          margin-right: 0.375rem;
          display: flex;
          align-items: center;
          text-align: center;

          span {
            padding: 0.25rem 0.5rem;
            font-size: 1.125rem;
            font-style: normal;
            font-weight: 400;
            height: 1.875rem;
          }

          &:last-child {
            margin-right: 0;
          }

          &.active {
            background-color: red;
            color: white;
            border-radius: 0.25rem;
          }
        }
      }
      .offer-filter__count-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;

        @include maxSm {
          gap: 0.5rem;
          padding: 0 1rem;
        }

        @media (max-width: 768px) {
          flex-direction: row;
        }
        @media (max-width: 370px) {
          flex-direction: column-reverse;
          gap: 1rem;
        }

        .sort-desktop-element {
          @media (max-width: 768px) {
            //            width: 100%;
          }
          @include xs{
            display: none;
          }
        }

        .offer-filter__dropdown,
        .dropdown__wrapper,
        .promotion-total-count {
          margin-bottom: 0px;
        }
        .dropdown {
          &__wrapper {
            width: 100%;
          }
          &__display {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            padding: 0px;
            background-color: transparent;
            border: none;
            border-radius: 0px;
            @media (max-width: 780px) {
              gap: 0.5rem;
            }
          }
          &__list {
            min-width: 13rem;
            height: fit-content;
            max-height: none;
            box-shadow: 0px 16px 32px -8px #0c0c0d66;
            border-radius: 0.5rem;
            margin-top: 12px;
            padding: 0.375rem 0;
            right: 0;
            //             @include maxSm {
            //               top: 0 !important;
            //               position: relative;
            //               box-shadow: none;
            //               max-height: unset;
            //               margin-top: 8px;
            //             }
            @media (max-width: 370px) {
              width: calc(-32px + 100vw);
              left: 0;
            }
          }
          &__item {
            height: auto;
            padding: 0.625rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #1e1e1e;
            border: none;
            &:hover {
              font-weight: 400;
              background-color: #f2f2f2;
            }
          }
        }

        .sort-title {
          display: flex;
          flex-direction: row;
          gap: 0.5rem;
          @media (max-width: 767px) {
            flex: 1;
            gap: 0.25rem !important;
          }
          @media (min-width: 370px) and (max-width: 780px) {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
          }
          @media (max-width: 370px) {
            white-space: nowrap;
            max-width: calc(100vw - 32px - 8px - 16px);
          }
        }

        .display {
          &__title {
            color: #212121 !important;
            font-size: 1rem !important;
            line-height: 1.5;
            font-weight: 600;
            @media (max-width: 1024px) {
              white-space: nowrap;
            }
          }
          &__text {
            color: #000000 !important;
            font-size: 1rem !important;
            line-height: 1.5;
            font-weight: 500;
            @media (max-width: 767px) {
              max-width: 10rem;
              width: fit-content;
            }
            @media (max-width: 370px) {
              max-width: unset;
            }
          }
        }
      }
    }
  }
}
