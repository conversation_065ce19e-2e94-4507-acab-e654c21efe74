.title-component {
	.title-cmp {
		width: 100%;
		&.title-cmp--row {
			display: flex;
			align-items: center;
			// Change from Adobe
			@media screen and (max-width: 767px) {
				flex-direction: column;
			}
		}
		// Change from Adobe : alignment of title
		&.align-center {
			justify-content: center;
			.tcb-title {
				text-align: center;
			}
			&.title-cmp--row{
				@media screen and (max-width: 767px) {
					align-items: center;
				}
			}
		}
		&.align-left {
			justify-content: flex-start;
			.tcb-title {
				text-align: left;
			}
			&.title-cmp--row{
				@media screen and (max-width: 767px) {
					align-items: flex-start;
				}
			}
		}
		&.align-right {
			justify-content: flex-end;
			.tcb-title {
				text-align: right;
			}
			&.title-cmp--row{
				@media screen and (max-width: 767px) {
					align-items: flex-end;
				}
			}
		}
		&.title-separator {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			.tcb-title {
				display: inline;
				position: relative;
				padding-right: 24px;
				margin-right: 24px;
				padding-bottom: 0;
				flex-shrink: 0;
				&::before {
					position: absolute;
					content: "";
					width: 1px;
					background: linear-gradient(to bottom, #c4c4c4, #c4c4c4) no-repeat 0 0;
					background-size: 1px 100%;
					right: 0;
					bottom: 0;
					height: 28px;
					top: 50%;
					transform: translateY(-50%);
				}
				// Change from Adobe
				@media screen and (max-width: 767px) {
					padding-right: 0;
					margin-right: 0;
				}
			}
			.cta-button {
				min-width: auto;
			}
		}
		// Change from Adobe : for heading tag varation
		h1 {
			font-size: var(--heading1-font-size);
			line-height: var(--heading1-line-height);
			font-weight: 300;
			@media screen and (max-width: 767px) {
				font-size: var(--heading1-mobile);
			}
		}

		h2 {
			font-size: var(--heading2-font-size);
			line-height: var(--heading2-line-height);
			font-weight: var(--heading2-font-weight);
		}

		h3 {
			font-size: var(--heading3-font-size);
			line-height: var(--heading3-line-height);
			font-weight: 300;
		}
	}
	// Change from Adobe : for alignment of Parsys
	.title-cmp--parsys {
		width: fit-content;
		display: flex;
		align-items: center;
		flex-grow: 1;
		.button {
			width: 100%;
			.cta-button picture {
				width: 24px;
				height: 24px;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
	@media (max-width: 767px) {
		.title-cmp.title-separator {
			.tcb-title {
				&::before {
					content: none;
				}
			}
		}
		.title-cmp--parsys {
			width: 100%;
		}
	}
}

// Theme at Component level
// Change by Adobe
.title-text--dark .title-component .title-cmp,
.title-text--dark .title-component .title-cmp .tcb-title {
	color: #fff;
}
// Change by Adobe
.title-text--light .title-component .title-cmp,
.title-text--light .title-component .title-cmp .tcb-title {
	color: #000;
}
.font-weight-300 {
	*.tcb-title {
		font-weight: 300 !important;
	}
	h1,h2,h3,h4,h5,h6 {
		font-weight: 300 !important;
	}
	.cmp-button__text {
		font-weight: 300 !important;
	}
}

.font-weight-400 {
	*.tcb-title {
		font-weight: 400 !important;
	}
	h1,h2,h3,h4,h5,h6 {
		font-weight: 400 !important;
	}
	.cmp-button__text {
		font-weight: 400 !important;
	}
}

.font-weight-500 {
	*.tcb-title {
		font-weight: 500 !important;
	}
	h1,h2,h3,h4,h5,h6 {
		font-weight: 500 !important;
	}
	.cmp-button__text {
		font-weight: 500 !important;
	}
}

.font-weight-600 {
	*.tcb-title {
		font-weight: 600 !important;
	}
	h1,h2,h3,h4,h5,h6 {
		font-weight: 600 !important;
	}
	.cmp-button__text {
		font-weight: 600 !important;
	}
}

.font-weight-700 {
	*.tcb-title {
		font-weight: 700 !important;
	}
	h1,h2,h3,h4,h5,h6 {
		font-weight: 700 !important;
	}
	.cmp-button__text {
		font-weight: 700 !important;
	}
}
.no-padding {
	.tcb-title {
		padding: 0 !important;
	}
}