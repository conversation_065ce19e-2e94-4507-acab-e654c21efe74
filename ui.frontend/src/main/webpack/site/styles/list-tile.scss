$listtiles-red-arrow: '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow.svg';
$listtiles-white-arrow: '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/white-arrow.svg';

/* List tile */
.list-tile {
  position: relative;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: stretch;
  margin: 0 auto;
  width: 100%;
  display: flex;
  grid-gap: 24px;
  gap: 24px;

  ul {
    padding-inline-start: 0;
  }

  .list-tile__nav-icon {
    &.right-corner {
      position: absolute;
      right: -8px;
      bottom: 0;
    }
  }

  a :hover {
    text-decoration: none;
  }

  &.normal {
    display: grid;
  }

  &.grid-2-1-1,
  &.grid-1-1-2 {
    grid-auto-flow: row;
    grid-auto-rows: 1fr;
    .list-tile__card {
      &-content {
        display: flex;
        flex-wrap: wrap;
      }

      &-description {
        &.have-arrow-sticky {
          width: calc(100% - 28px);
        }        
      }

      &-navtext {
        position: static;
        align-items: flex-end;
      }
    }
  }

  &.grid-2-1-1 {
    grid-template-columns: 2fr 1fr 1fr;
  }

  &.grid-1-1-2 {
    grid-template-columns: 1fr 1fr 2fr;
  }

  &.grid-equal {
    grid-template-columns: 1fr 1fr 1fr;
  }

  /* List Tile Small */
  &.small {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-auto-rows: minmax(160px, auto);

    .list-tile__tile-item {
      &:before {
        padding-bottom: 20%;
      }
    }
  }

  /* List Tile Small Square*/
  &.square {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    .list-tile__tile-item {
      max-height: 500px;
    }

    .list-tile__card-content {
      h3 {
        font-size: 24px;
        line-height: 36px;
        font-weight: 300;
      }
    }

    .list-tile__card-description {
      margin-top: 0.5rem;
      width: 100%;

      ul {
        list-style: none;
      }

      li {
        position: relative;
        padding-left: 24px;
      }
    }

    .list-tile__card-navtext {
      position: relative;
      margin-top: 12px;
    }
  }

  /* List Tile Large */
  &.large {
    display: grid;

    .span-small {
      grid-column-end: span 1;
    }

    .span-wide {
      grid-column-end: span 2;
    }

    .list-tile__card-content {
      h3 {
        font-size: 24px;
        line-height: 36px;
      }
    }

    .list-tile__card-navtext {
      position: relative;
      margin-top: 12px;
    }

    .inline {
      position: absolute;
    }
  }
  /* List Tile Large Horizontal */
  &.horizontal {
    grid-template-columns: repeat(auto-fill, minmax(calc(25% - 24px), 1fr));
    grid-auto-flow: column;
    grid-auto-rows: 1fr 1fr;

    .span-small {
      grid-row-end: span 1;

      &::before {
        padding-bottom: 40%;
      }
    }

    .span-small[class*='full'] {
      grid-column-end: span 2;
    }

    .span-wide {
      grid-row-end: span 2;
    }
  }

  /* List Tile Large Vertical */
  &.vertical {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 238px 238px;
    grid-auto-flow: row;
    grid-auto-rows: 1fr;

    .list-tile__tile-item {
      &::before {
        padding-bottom: 25%;
      }
    }

    .list-tile__content {
      padding: 24px;
    }

    @media screen and (min-width: 768px) and (max-width: 1024px) {
      .tablet-high & {
        grid-template-rows: 270px 270px;
        .list-tile__content-inner {
          padding: 0 24px;
        }
      }
    }
  }

  &__tile-item {
    display: block;
    position: relative;
    width: 100%;

    &.list-tile__tile-item__theme-dark {
      color: white;
      & .list-tile__card-img {
        background: url($listtiles-white-arrow) no-repeat center right;
      }
    }

    &.list-tile__tile-item__theme-light {
      color: black;
      & .list-tile__card-img {
        background: url($listtiles-red-arrow) no-repeat center right;
      }
    }

    &:before {
      content: '';
      width: 1px;
      margin-left: -1px;
      float: left;
      height: 0;
      padding-bottom: 80%;
    }

    &:after {
      content: '';
      display: table;
      clear: both;
    }
  }

  &__tile-link {
    display: block;
    height: 100%;
    width: 100%;
  }

  &__hero {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    overflow: hidden;
    border-radius: 8px;
    width: 100%;
    height: 100%;
    transition: box-shadow 0.3s;

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
      transform: scale(1.008);
    }

    &.light-text {
      color: #fff;
    }
  }

  &__img {
    position: absolute;
    min-width: 100%;
    max-width: 100%;
    min-height: 100%;
    max-height: 100%;
    object-fit: cover;
    object-position: 80% 50%;
  }

  &__content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: inherit;
    z-index: 2;
    height: 100%;
    overflow: hidden;
    overflow-x: hidden;
    overflow-y: hidden;
  }

  &__content-inner {
    padding: 16px 24px;
  }

  &__card-label {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    letter-spacing: 2px;
    text-transform: uppercase;

    p {
      font-size: 14px;
      color: #4D4D4D;
    }
  }

  &__card-content {
    width: 100%;
    position: relative;
    z-index: 1;

    h3 {
      margin: 0;
      font-size: 24px;
      font-weight: 300;
      line-height: 1.5;
      width: calc(100% - 24px);
    }
  }

  &__card-content.font-weight-300 {
    h3 {
      font-weight: 300;
    }
  }

  &__card-description {
    margin-top: 8px;
  }

  &__card-navtext {
    display: inline-flex;
    align-items: center;
    margin-top: 8px;
  }

  &__nav-icon {
    position: relative;
    margin-left: 12px;
    display: flex;
    height: 24px;
    align-items: center;
  }

  &__nav-inner {
    box-sizing: border-box;
    display: inline-block;
    overflow: hidden;
    width: 16px;
    height: 16px;
    background: none;
    opacity: 1;
    border: 0px;
    margin: 0px;
    padding: 0px;
    position: relative;
  }

  &__card-img {
    position: absolute;
    inset: 0px;
    box-sizing: border-box;
    padding: 0px;
    border: none;
    margin: auto;
    display: block;
    width: 0px;
    height: 0px;
    min-width: 100%;
    max-width: 100%;
    min-height: 100%;
    max-height: 100%;
    object-fit: cover;
    object-position: center center;
    background: url($listtiles-red-arrow) no-repeat center right;
  }

  /* List Tile Small Other*/
  &.other {
    grid-auto-rows: minmax(235px, auto);
    display: flex;
    flex-direction: column;

    .list-tile__img {
      object-position: center center;
    }

    .list-tile__content-inner {
      height: 100%;
      padding: 24px;
    }

    .list-tile__card-body {
      height: 100%;
    }

    .list-tile__card-content {
      height: 100%;

      h3 {
        margin: 16px 0;
        font-size: 1.5rem;
      }
    }

    .list-tile__card-description {
      position: absolute;
      bottom: 0;
      display: flex;
      flex-direction: row;
      padding: 32px 0;

      .list-tile__nav-icon {
        align-items: center;
      }

      p {
        white-space: nowrap;
      }
    }

    .list__item.list-tile__tile-item {
      transition: 0.5s;
      height: 235px;
      &:hover {
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
      }
    }

    .list-tile__tile-item {
      &:before {
        padding-bottom: unset;
      }
    }
  }

  .black {
    color: black;
  }
}
.large-card .list-tile__tile-link {
  height: 460px;
  max-height: 500px;
}
.x-small-card .list-tile__tile-link {
  height: 160px;
}
@media (max-width: 991px) {
  .list-tile {
    &.grid-2-1-1,
    &.grid-1-1-2 {
      grid-template-columns: 1fr 1fr 1fr;
    }

    .list-tile__tile-item {
      &:before {
        padding-bottom: 125%;
      }
    }

    &.horizontal {
      grid-template-columns: 1fr 1fr;
      grid-auto-flow: unset;
      grid-auto-rows: unset;

      .span-wide {
        &::before {
          padding-bottom: 80%;
        }
      }
    }
  }
}

@media (min-width: 768px) {
  .list-tile.tall .list-tile__tile-item {
    max-height: 22.5rem;
  }
  .x-small-card .list-tile.square {
    max-height: 160px;
  }
}

@media (max-width: 767px) {
  .list-tile {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 16px;
    gap: 16px;
    grid-template-columns: unset;
    grid-auto-rows: unset;
    display: flex;
    flex-wrap: wrap;
    grid-gap: 16px;
    gap: 16px;
    grid-template-columns: unset;
    grid-auto-rows: unset;

    &__hero {
      picture {
        img {
          inset: 0px;
          box-sizing: border-box;
          padding: 0px;
          border: none;
          margin: auto;
          display: block;
          width: 0px;
          height: 0px;
        }
      }
    }

    &.normal {
      grid-auto-rows: 1fr;
    }

    &.grid-2-1-1,
    &.grid-1-1-2 {
      grid-template-columns: unset;
      grid-auto-flow: column;
      grid-auto-columns: 90%;
      overflow-x: auto;
      overflow-y: hidden;
      -ms-scroll-snap-type: x mandatory;
      scroll-snap-type: x mandatory;
      scroll-snap-stop: always;
      -ms-overflow-style: none;
      scrollbar-width: none;

      .mobile-stack & {
        grid-auto-flow: row;
        grid-auto-columns: 100%;
      }

      &::-webkit-scrollbar {
        display: none;
        display: none;
      }

      .list-tile__tile-item {
        scroll-snap-align: start;
        scroll-snap-align: start;
      }
    }

    &.small {
      grid-auto-rows: 1fr;

      .list-tile__tile-item {
        &:before {
          padding-bottom: 80%;
        }
      }
    }

    &.square {
      grid-template-columns: 1fr;

      .list-tile__card-description {
        li {
          &::before {
            border-radius: 50%;
            background-color: var(--gray-600);
            left: 10px;
          }
        }
      }
    }

    &.large {
      display: flex;
    }

    &.horizontal {
      grid-template-columns: 1fr;

      .list-tile__tile-item {
        &::before {
          padding-bottom: 125%;
        }
      }
    }

    &.vertical {
      grid-template-columns: 1fr;
      grid-template-rows: unset;
      grid-auto-flow: unset;
      grid-auto-rows: unset;

      .mobile-slider & {
        grid-auto-columns: 80%;
        grid-auto-flow: column;
        overflow-x: auto;
        display: grid;
      }

      .list-tile__tile-item {
        &::before {
          padding-bottom: 125%;
        }
      }

      .list-tile__content {
        padding: 16px;
      }
    }

    &.single {
      display: flex;
      grid-template-columns: 1fr;
      grid-auto-rows: unset;

      .list-tile__tile-item {
        &:before {
          padding-bottom: 80%;
        }
      }
    }

    &.equal {
      .list-tile__tile-item {
        max-height: 100%;
      }
    }
  }
  .x-small-card .list-tile__tile-link {
    height: 100%;
  }
}

/* End List tile component */
