.carousel-images {
  .carousel-images__container {
    background-color: transparent;
    position: relative;
  }
  .title-cmp__link {
    transition: unset;
    line-height: 1.5;
    font-weight: 400;
  }
  .title-cmp {
    padding-bottom: unset;
  }
  .title-cmp__link:hover {
    text-decoration: solid underline white 1px;
  }
  .carousel-images__carousel-img {
    margin-top: 24px;
  }
  .carousel-images__carousel-item {
    width: 100%;
    display: inline-block;
    padding: 0 12px;
  }
  .carousel-images__carousel-item img {
    object-fit: cover;
    object-position: center center;
  }
  .carousel-images__carousel-img [class*="slick-track"] {
    display: flex;
    align-items: flex-end;
  }
  .carousel-images__carousel-img:hover .slick-arrow {
    opacity: 1;
  }
  .slick-arrow {
    position: absolute;
    border-radius: 50%;
    transition: all 0.2s ease-in-out;
    box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: hsla(0, 0%, 77%, 0.54);
    border: none;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  .slick-prev {
    top: 50%;
    z-index: 1;
    color: white;
    font-size: 1.25rem;
    left: calc(100vw / 22.5);
  }
  .slick-next {
    right: 0;
    top: 50%;
    z-index: 1;
    color: white;
    font-size: 1.25rem;
    right: calc(100vw / 22.5);
  }
  .slick-next:hover,
  .slick-prev:hover {
    color: red;
    background-color: white;
  }
  @media (max-width: 768px) {
    .carousel-images__carousel-item img {
      height: 200px;
    }
    .slick-dots {
      padding-left: 0;
      display: flex;
      justify-content: center;
      list-style: inherit;

    }

    .slick-dots li {
      color: #c4c4c4;
      width: 8px;
      font-size: 25px;
      margin: 0 5px;
      transform: translateX(20px);
    }

    .slick-dots .slick-active {
      color: red !important;
    }

    .slick-dots button {
      display: none;
    }

    .carousel-images__carousel-item {
      padding: 0 16px;
    }
  }
  @media (max-width: 376px) {
    .carousel-images__carousel-item img {
      height: 190px;
    }
  }

  .slick-dots li::marker {
    color: #c4c4c4;
  }

  .slick-dots .slick-active::marker {
    color: red;
  }
}
