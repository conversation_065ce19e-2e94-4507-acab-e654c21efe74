.button-active {
  color: #fff !important;
  background-color: #212121 !important;
}
.event-filter,
.news-filter {
  margin-bottom: 3rem; // Change by Adobe
  .content-wrapper {
    max-width: 90rem !important;
    padding: 0 4rem;
  }
  .category-filter {
    background: #fff;
    padding: 1.5rem 0px;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    filter: drop-shadow(0 30px 20px #eaeaea);
    .container {
      display: flex;
      align-items: baseline;
      gap: 1rem;
      padding: 0 4rem;
      margin: 0 auto;
      max-width: 90rem;
      width: 100%;

      // @media screen and (max-width: 1199px){
      //   padding: 0 calc(100vw / 22.5);
      // }

      .category-title {
        flex-shrink: 0;
      }
      .slide-button {
        padding: 1.25rem 0;
        display: block;

        button {
          margin: 5px 1px;
          height: 3rem;
          padding: 0.75rem 1.5rem;
          border-radius: 27px;
          border: 1px solid #c5c5c5;
          background-color: unset;
          cursor: pointer;
          font-size: 1rem;
          line-height: 1.5rem;
          color: #616161;
          min-width: max-content;
        }
      }

      .slide-button-new {
        padding: 0px;
      }
    }
  }

  .news-filter__wrapper {
    margin-top: 3rem;
    .news_filter-group {
      .btn-close {
        display: none;
      }
    }
    .story-listing-filter-item {
      display: flex;
      gap: 1.5rem;
      margin-bottom: 2rem;
      flex-direction: column;

      .checkbox {
        color: #616161;
        font-size: 1rem;
        line-height: 1.5;
        display: inline-flex;
        align-items: center;
        font-weight: 400;
        text-transform: none;
        letter-spacing: normal;
        cursor: pointer;
      }
    }
  }
  &.open {
    .news_filter-group {
      display: block;
      padding: 1rem 1rem 5.625rem;
      margin-bottom: auto;
    }
    .tcb-modal {
      position: fixed;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 1300;
      background: white;
      overflow: scroll;
      flex-direction: column;
      justify-content: space-between;
    }

    .tcb-modal_header {
      padding: 1rem;
      position: relative;
      border-bottom: 1px solid #dedede;
      .tcb-modal_title {
        font-weight: 600;
        color: rgba(0, 0, 0, 0.87); // Change by Adobe
      }
      margin-bottom: auto;
    }
    .news-filter__wrapper {
      padding: 0 !important;
    }
    .tcb-modal_action-bar {
      padding: 0 1rem 1rem;
      .tcb-button {
        display: block;
      }
    }
  }

  &.not-filter {
    .information-filter__load-more {
      display: none;
    }
    .offer-cards__container .news-list {
      display: none;
    }
    .offer-cards__container .not-found {
      display: block;
    }
  }

  .tcb-modal {
    display: none;
  }

  .news {
    &-tab-content_container {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    &-filter-card {
      position: relative;
      background: var(--primary-background);
      display: flex;
      flex-direction: column;
      border-radius: 0.5rem;
      flex-direction: row;
      box-shadow: 0 0.125rem 0.5rem rgb(0 0 0 / 15%);

      &_cover {
        width: 40%;
        flex-shrink: 0;
      }
      &_cover-image {
        width: 100%;
        max-height: 25rem;
        aspect-ratio: 1.77;
        object-fit: cover;
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
        max-height: unset;
        border-top-right-radius: 0;
        border-bottom-left-radius: 0.5rem;
        height: 100%;
      }

      &_month {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.5rem 1.25rem;
        background: var(--primary-background);
        box-shadow: 0 33px 181px rgb(0 0 0 / 4%);
        border-radius: 0.5rem;
        text-transform: uppercase;
        font-weight: 600;
        font-size: 1.5rem;
        line-height: 2.25rem;
        letter-spacing: 0.125rem;
        right: unset;
        left: 1.5rem;
        top: 2rem;
        border-radius: 1rem;
        padding: 0.75rem 0.375rem;

        small {
          font-weight: 600;
          font-size: 0.75rem;
          line-height: 0.875rem;
          letter-spacing: 1px;
          font-size: 0.875rem;
          line-height: 21px;
          letter-spacing: 0.125rem;
        }
      }
    }

    &-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      &.temp-placeholder {
        visibility: hidden;
        position: absolute;
      }
    }

    &-item__wrapper {
      display: flex;
      border-radius: 0.5rem;
      background-color: #fff;
      //changed by adobe
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
      min-height: 14.75rem;
    }

    &-image {
      max-width: 355px;
      width: 355px;
      flex-shrink: 0; //changed by adobe
      min-height: 14.75rem;
      background-size: cover;
      display: flex;
      align-items: center;
      > img {
        border-radius: 0.5rem 0 0 0.5rem;
        width: 100%;
        min-height: 14.75rem; //changed by adobe
        height: 236px;
        object-fit: cover;
        object-position: center center;
      }
      &.default-image {
        height: 236px;
        margin: auto;
        > img {
          object-fit: contain;
        }
      }
    }

    &-content {
      width: 100%;
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    &-content__title {
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
    }

    &-content__description {
      letter-spacing: -0.005rem;
      font-weight: 400;
      line-height: 1.5rem;
      color: #616161;
      margin-top: 0.5rem;
      .dark & {
        color: #dfdfdf;
      }
    }

    &-content__header {
      display: flex;
      font-size: 0.875rem;
      letter-spacing: 0.125rem;
      margin-bottom: 0.5rem;

      div:first-child {
        line-height: 1rem;
        color: #ed1c24;
        text-transform: uppercase;
        padding-right: 1rem;
        border-right: 1px solid #c5c5c5;
        font-weight: 600;
      }

      div:last-child {
        color: #616161;
        line-height: 1rem;
        padding-left: 1rem;
        text-transform: uppercase;
        font-weight: 600;
      }
    }

    &-filter__wrapper {
      display: flex;
    }

    &-content__link {
      position: relative;
      display: inline-flex;
      outline: none;
      border: none;
      cursor: pointer;
      align-items: center;
      text-decoration: none;
      transition: all 0.3s ease-in;
      justify-content: flex-start;
      width: inherit;
      grid-gap: 0.75rem;
      gap: 0.75rem;
      font-weight: 600;
      margin-top: 1rem;
      &:hover {
        text-decoration: underline;
      }
    }

    &-content__link-icon {
      display: flex;
      align-items: center;
      margin-left: 0;
      transition: all 0.3s ease-in-out;
    }

    &_open-filter-button {
      display: none;
    }
  }

  .information-filter__load-more {
    max-width: 20.5rem;
    width: 100%;
    margin: auto;
    margin-top: 0.5rem; // Change by Adobe
    margin-bottom: 3rem;
    button {
      border: 1px solid #000;
      position: relative;
      display: inline-flex;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      outline: none;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all 0.3s ease-in;
      justify-content: space-between;
      align-items: center;
      grid-gap: 0.75rem;
      gap: 0.75rem;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      line-height: 1.5;
      font-weight: 600;
      &:hover {
        background-color: #000;
        color: #fff;
      }

      &:hover > .load-more__button-icon {
        filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(1%) hue-rotate(232deg) brightness(107%)
          contrast(101%);
      }
    }

    .load-more__button-icon {
      display: flex;
      align-items: center;
      margin-left: 0;
      transition: all 0.3s ease-in-out;
      // Change by Adobe
      img {
        width: 1rem;
      }
    }
  }

  .offer-filter {
    &__title {
      padding-bottom: 0.5rem;
      margin-bottom: 1.5rem;
      color: #a2a2a2;

      > :nth-child(1) {
        color: rgba(0, 0, 0, 0.87);
      }
      &.margin-bottom__short {
        padding-bottom: 0;
        margin-bottom: 1rem;
      }

      &.mobile-only {
        display: none;
      }
    }
    &__tab {
      display: flex;
      grid-gap: 0.5rem;
      gap: 0.5rem;
      flex-wrap: wrap;
      &.mobile-only {
        display: none;
      }

      &.margin-bottom {
        margin-bottom: 1.25rem;
      }

      button {
        background-color: transparent;
        height: 3rem;
        justify-content: center;
        width: fit-content;
        border-radius: 27px;
        border: 1px solid #dedede !important;
        position: relative;
        display: inline-flex;
        padding: 1rem 1.5rem;
        outline: none;
        cursor: pointer;
        white-space: nowrap;
        text-decoration: none;
        transition: all 0.3s ease-in;
        align-items: center;
        grid-gap: 0.75rem;
        gap: 0.75rem;
        min-width: max-content;
        z-index: 1;

        &.active {
          background-color: #000;
          span {
            color: #fff;
          }
        }
      }

      span {
        font-weight: 400;
        font-size: 1rem;
        color: #616161;
      }
    }

    &__datepicker {
      display: flex;
      box-shadow: 0 0.25rem 1.25rem rgb(0 0 0 / 10%);
      height: 305px;
      flex-direction: column;
      justify-content: flex-start;
      padding: 2rem 0 0;
      border-radius: 5px;
    }

    &__checkbox {
      margin-bottom: 2rem;
      &-item {
        display: flex; //changed by adobe
        margin-bottom: 1.5rem;
        position: relative;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .news_filter-group {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
  }

  .offer-cards__container {
    padding: 0 0.75rem 0.75rem 0.75rem;
    max-width: 75%;
    width: 75%;

    &.full-width {
      margin: auto;
      max-width: 1064px;
      width: unset;
      padding: 0;
      padding-bottom: 0.75rem;
    }
    .partner-offer-component__container {
      margin: -12px;
    }
    .not-found {
      display: none;
      .not-found-img {
        display: flex;
        justify-content: center;
        img {
          line-height: 0;
        }
      }
      .description {
        font-size: 1rem;
        font-weight: 600;
        line-height: 1.5rem;
        color: #000;
      }
    }
  }

  .btn-open-filter {
    font-size: 1rem;
    border: none;
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    display: inline-flex;
    justify-content: space-between;
    align-items: center;

    &.scroll-over {
      width: auto;
      line-height: 0;
      position: fixed;
      top: 6.25rem;
      left: 0;
      background-color: #fff;
      padding: 17px 28px 17px 34px;
      border-radius: 0 8px 8px 0;
      box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04), 0 13.7866px 75.6175px rgba(0, 0, 0, 0.029),
        0 7.37098px 40.4287px rgba(0, 0, 0, 0.024), 0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
        0 2.19453px 12.0367px rgba(0, 0, 0, 0.016), 0 0.913195px 5.00873px rgba(0, 0, 0, 0.011);
      display: none;
      z-index: 1300;
    }

    &__icon {
      display: flex;
      transition: all 0.3s ease-in-out;
    }
  }

  .filter--border-bottom {
    border-bottom: 1px solid #dedede;
    span {
      font-weight: 600;
      font-size: 1rem;
    }
  }

  .checkbox-item__wrapper {
    font-size: 1rem;
    line-height: 1.5;
    display: inline-flex;
    align-items: center;
    width: 100%;
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
    color: #616161;
    .dark & {
      color: #dfdfdf;
    }
  }

  .tcb-modal {
    .news_filter-group {
      flex: 1;
    }
    .news_filter-group {
      display: flex;
      flex-direction: column;
      padding: 1rem 1rem 1.5rem;
    }
    .offer-filter__tab button {
      padding: 0.75rem 1rem;
    }
    .offer-filter__tab.big-size button {
      padding: 0.75rem 1.5rem;
    }
  }
}

@media screen and (max-width: 1024px) {
  .event-filter,
  .news-filter {
    .offer-cards__container {
      padding: 0.75rem 0px 0.75rem 0px;
    }
  }
}

@media screen and (max-width: 991px) {
  .event-filter,
  .news-filter {
    display: flex;
    flex-direction: column;
    padding: 0;
    margin-bottom: 0 !important;
    &.open {
      .tcb-modal {
        display: flex;
      }
    }

    .tcb-title {
      padding-left: 0.5rem;
    }

    .content-wrapper, .container-new {
      margin-left: 0;
      padding-left: calc(100vw / 22.5) !important;
      padding-right: calc(100vw / 22.5) !important;
    }

    .news-filter__wrapper {
      flex-direction: column;
      width: 100%;
    }

    .news_filter-group {
      max-width: unset;
      padding: 0;
    }

    .offer {
      &-filter {
        &__container {
          width: 100%;
        }

        &__datepicker {
          max-width: 19.375rem;
        }
      }
      &-cards__container {
        max-width: none;
        width: 100%;
      }
    }
    .news-filter__wrapper {
      margin-top: 2.25rem;
    }
  }
}

@media screen and (max-width: 767px) {
  .section__margin-medium {
    margin-top: 0px !important;
  }
  .event-filter,
  .news-filter {
    .category-filter {
      display: none;
      .container {
        display: block;
        padding: 2rem 1rem;
      }
    }
    .news {
      &-filter-card {
        flex-direction: column;
        &_cover {
          width: 100%;
        }

        &_cover-image {
          max-height: 14.75rem;
          border-top-right-radius: 0.5rem;
          border-bottom-left-radius: 0;
        }
      }

      &-image {
        max-width: 100% !important;
        min-height: 11.875rem; //changed by adobe
        height: 11.875rem; //changed by adobe
        width: unset !important;
        > img {
          border-radius: 0.5rem 0.5rem 0 0;
          //changed by adobe
          min-height: 11.875rem;
          height: 100%;
        }
      }

      &-content {
        padding: 2rem 1.5rem;
        height: auto;
        border-radius: 0 0 0.5rem 0.5rem;

        &__header {
          display: block;
          margin-bottom: 0.5rem;
          div:first-child {
            border-right: 0;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            line-height: 21px;
            letter-spacing: 2px;
          }

          div:last-child {
            padding-left: 0;
          }
        }

        &__description {
          margin-top: 1rem;
        }

        &__link {
          margin-top: 1rem;

          &:focus {
            text-decoration: none;
          }
        }
      }

      &-item {
        display: block;
        width: 100%;

        &__wrapper {
          flex-direction: column;
          min-height: 190px;
        }
      }

      &_filter-group {
        display: none;
      }

      &_open-filter-button {
        display: flex;
        z-index: 4;
        top: 0.5rem;
        padding: 0.75rem 0;
        &.sticky {
          .scroll-over {
            display: block;
          }
        }
      }
      &-filter__wrapper {
        margin-top: 2rem !important;
        .btn-close {
          font-weight: 600;
          font-size: 1rem;
          right: 1rem;
          z-index: 1;
          width: 100%;
          background-color: #000;
          color: #fff;
          text-align: left;
          border-radius: 0.5rem;
          padding: 0.75rem 1rem;
          display: unset !important;
        }
      }
    }

    .sticky-filter {
      position: fixed;
      top: 6.25rem;
      left: 0;
      background-color: #fff;
      padding: 17px 28px 17px 34px;
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04), 0 13.7866px 75.6175px rgba(0, 0, 0, 0.029),
        0 7.37098px 40.4287px rgba(0, 0, 0, 0.024), 0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
        0 2.19453px 12.0367px rgba(0, 0, 0, 0.016), 0 0.913195px 5.00873px rgba(0, 0, 0, 0.011);
      z-index: 1300;
    }

    .show {
      display: block !important;
    }

    .offer {
      &-filter {
        &__title.mobile-only,
        &__tab.mobile-only {
          display: flex;
        }

        &__checkbox.flex-show {
          display: flex;
        }
      }
      &-cards__container {
        max-width: 100%;
      }
    }

    .flex-show .offer-filter__checkbox-item {
      margin-bottom: 0;
      margin-right: 1.5rem;
    }

    .btn-open-filter {
      background-color: #fff;
    }

    .tcb-modal {
      .offer-filter__tab button {
        min-width: unset;
        white-space: normal;
      }
    }
    .information-filter__load-more {
      width: 100%;
      max-width: 20.5rem;
      margin-bottom: 2rem;
    }
  }

  .category-filter-mobi {
    background: #fff;
    filter: drop-shadow(0 1.875rem 1.25rem #eaeaea);
    position: fixed;
    top: 0px;
    z-index: 99;
    overflow-y: auto;
    height: 100%;

    .dialog-title {
      padding: 1rem;
      border-bottom: 1px solid #dedede;

      .title {
        font-weight: 600;
        font-size: 1rem;
      }
    }

    .dialog-content {
      padding: 2rem 1rem;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: auto;

      .slide-button {
        padding-bottom: 1.5rem;
        button {
          margin: 5px 4px;
          height: 3rem;
          padding: 0.75rem 1.5rem;
          border-radius: 27px;
          border: 1px solid #c5c5c5;
          background-color: unset;
          cursor: pointer;
          font-size: 1rem;
          line-height: 1.5rem;
          color: #616161;
          min-width: max-content;
        }
      }

      .btn-close {
        font-weight: 600;
        font-size: 1rem;
        right: 1rem;
        position: inherit;
        z-index: 1;
        width: 100%;
        background-color: #000;
        color: #fff;
        text-align: left;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        display: unset !important;
      }
      .category-title {
        font-size: 1rem;
        line-height: 1.25;
        font-weight: 700;
        flex-shrink: 0;
        padding-bottom: 1rem;
      }

      .filter-button {
        background: #000;
        color: #fff;
      }
    }
    .container {
      display: flex;
      align-items: baseline;
      gap: 1rem;
      padding-left: 4rem;
      padding-right: 4rem;
      margin: 0 auto;
      max-width: 90rem;
      .category-title {
        flex-shrink: 0;
      }
      .slide-button {
        display: block;

        button {
          margin: 5px 4px;
          height: 3rem;
          padding: 0.75rem 1.5rem;
          border-radius: 27px;
          border: 1px solid #c5c5c5;
          background-color: unset;
          cursor: pointer;
          font-size: 1rem;
          line-height: 1.5rem;
          color: #616161;
          min-width: max-content;
        }
      }
    }
  }
  .mobile-fit-content.storylisting {
    .event-filter .news-item__wrapper,
    .news-filter .news-item__wrapper {
      height: unset;
    }
  }
}
