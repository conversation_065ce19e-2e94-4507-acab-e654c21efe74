.stock-information {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
  &_summary-details {
    font-weight: 600;
    color: var(--gray-600);
    margin-top: 32px;
    display: flex;
    flex-direction: column;
    gap: 8px;

  }

  &_price {
    margin: 8px 0;
    font-size: 40px;
    line-height: 60px;
    font-weight: 600;
    color: var(--body);
    letter-spacing: -.01em;
  }

  .stock-label {
    font-size: 16px;
    line-height: 18.75px;
    font-weight: 600;
    color: var(--secondary-grey-60);
  }
  .stock-dateLastPrice {
    font-size: 14px;
    line-height: 16px;
    font-weight: 600;
    color: var(--secondary-grey-60);
  }

  &_changes {
    font-size: 16px;
    line-height: 24px;
    font-weight: 600;
    color: var(--green);
    margin-left: 8px;
    letter-spacing: -0.01em;

    &_changes--warning {
      color: var(--accent);
    }
  }

  .stock-chart {
    &_floating-tooltip {
      position: absolute;
      display: none;
      padding: 3px 8px 5px;
      box-sizing: border-box;
      font-size: 12px;
      color: #131722;
      background-color: var(--primary-white);
      text-align: left;
      z-index: 1000;
      top: 12px;
      left: 12px;
      pointer-events: none;
      border: 1px solid #009688;
      border-radius: 2px;
      white-space: nowrap;
      line-height: normal;
    }
    &_total-price {
      font-size: 14px;
      margin: 4px 0;
      color: #21384d;
      font-weight: 600;
    }
    &_tooltip-date {
      color: #21384d;
    }
  }

  &_stock-chart {
    position: relative;
    overflow: auto;
  }

  .tcb-icon {
    img {
      display: none;
    }
    &.decrease {
      .down-icon {
        display: unset;
      }
    }
    &.increase {
      .up-icon {
        display: unset;
      }
    }
    &.equal {
      .equal-icon {
        display: unset;
      }
    }
  }
  .tcb-icon.stock-up,
  .tcb-icon.stock-down {
    position: relative;
    width: 32px;
    height: 24px;
    margin-right: 16px;
  }


  @media screen and (max-width: 991px) {

    .tcb-container,
    .stock-wrapper {
      flex-direction: column;
      gap: 60px;
    }

    &_stock-chart {
      width: 100%;
      margin-right: 0;
      margin-left: auto;
    }
  }

  @media screen and (max-width: 767px) {
    &_summary-details {
      span {
        display: flex;
      }
    }

    &_price {
      font-size: 24px;
      line-height: 28.13px;
      margin-top: 16px;
      margin-bottom: 8px;
    }

    &_changes {
      margin-top: 2.32px;
    }

  }
}
