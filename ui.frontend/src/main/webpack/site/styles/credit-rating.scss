$color_1: #212121;
$color_2: var(--gray-600);
$color_3: #898989;
$color_4: #ed1c24;
$color_5: #a2a2a2;
$color_6: #313131;
$color_7: #000;
$font-family_1: "Roboto", "Helvetica", "Arial", sans-serif;
$background-color_1: #fff;
$background-color_2: rgba(0, 0, 0, 0.04);
$background-color_3: rgba(0, 0, 0, 0.08);
$background-color_4: transparent;
$background-color_5: #f6f6f6;
$border-color_1: transparent;


.credit-ranking-component {
    .select-filter {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        padding: 12px;
        padding-left: 0;
        .select-options {
            display: flex;
            flex-direction: column;
            position: relative;
        }
        h2 {
            margin-right: 32px;
            color: $color_1;
            font-weight: 300;
            line-height: 1.25;
            font-size: 1.75rem;
        }
        .options {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
            transform: scaleY(0);
            transform-origin: top;
            transition: all 0.5s ease-in-out;
            >div {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                opacity: 0;
                padding: 8px 0;
                max-height: 250px;
                overflow: auto;
                border: 1px solid #e3e4e6;
                border-radius: 8px;
                background-color: $background-color_1;
                transition: all 0.5s ease-in-out;
            }
            ul {
                list-style: none;
            }
            li {
                opacity: 1;
                transition: all 0.3s ease-in-out;
                padding: 6px 40px 6px 16px;
                line-height: 1.5;
                font-size: 16px;
                cursor: pointer;
                text-align: right;
                &:not(.selected) {
                    &:hover {
                        background-color: $background-color_2;
                    }
                }
            }
            .selected {
                background-color: $background-color_3;
            }
        }
        .select.showed {
            + {
                .options {
                    z-index: 2;
                    transform: scaleY(1);
                    >div {
                        top: 100%;
                        opacity: 1;
                        &::-webkit-scrollbar {
                            width: 5px;
                        }
                        &::-webkit-scrollbar-thumb {
                            background: #585454;
                        }
                        &::-webkit-scrollbar-track {
                            background: #f1f1f1;
                        }
                    }
                }
            }
            .credit-rating-chevron-icon {
                transform: rotate(180deg);
            }
        }
        .option {
            &:hover {
                font-weight: 400;
            }
        }
        .select {
            display: flex;
            flex-direction: row;
            height: 48px;
            padding: 12px 16px;
            align-items: center;
            background-color: $background-color_1;
            border: 1px solid #e3e4e6;
            border-radius: 8px;
            position: relative;
            cursor: pointer;
            min-height: 48px;
            min-width: 181px;
            z-index: 3;
            h6 {
                font-size: 1rem;
                font-weight: 600;
                font-family: $font-family_1;
                line-height: 1.25rem;
            }
            .fa-calendar-o {
                color: $color_2;
                font-size: 14px;
                margin-right: 10px;
            }
            span {
                color: $color_3;
                margin-left: 26px;
                font-size: 16px;
            }
            .credit-rating-chevron-icon {
                color: $color_4;
                width: 16px;
                height: 10px;
                display: inline-flex;
                align-items: center;
                margin-left: 8px;
            }
        }
    }
    .credits-list {
        display: grid;
        grid-gap: 1rem;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        .credit {
            display: flex;
            flex-direction: column;
        }
        
        @media (max-width: 991px) {
            display: flex;
            flex-direction: column;
        }
    }

    .grid-2-col {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .grid-3-col {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .menu {
        display: flex;
        border-bottom: 1px solid #dedede;
        overflow: auto;
        gap: 24px;
    }
    .menu-item {
        position: relative;
        display: block;
        overflow: hidden;
        color: $color_5;
        min-width: max-content;
        padding: 0;
        text-transform: none;
        min-height: 40px;
        letter-spacing: 0;
        max-width: none;
        text-align: center;
        white-space: normal;
        border: 0;
        cursor: pointer;
        background-color: $background-color_4;
        width: auto;
        &:first-child {
            border-bottom: 4px solid rgb(237, 27, 36);
            color: $color_6;
            font-weight: 700;
        }
    }
    ::-webkit-scrollbar {
        height: 4px;
    }
    ::-webkit-scrollbar-thumb {
        background: $color_5;
    }
    .tab {
        display: flex;
        width: 100%;
        .content {
            width: 100%;
            margin-top: 24px;
            box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%);
            border-radius: 8px;
            overflow: hidden;
            padding-bottom: 24px;
            border: 1px solid #e4e4e4;
        }
    }
    .banner {
        padding: 32px 24px;
        .image {
            position: relative;
            height: 48px;
        }
        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: left center;
        }
    }
    .table {
        width: 100%;
        overflow-x: auto;
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border-color: $border-color_1;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: -0.01em;
            padding: 8px 23px;
            text-align: left;
            font-family: $font-family_1;
            border-bottom: 1px solid rgba(224, 224, 224, 1);
            min-width: 140px;
        }
        thead {
            th {
                background-color: $background-color_5;
                font-weight: 700;
                color: $color_7;
                border-bottom: none;
            }
        }
        tbody {
            td {
                color: var(--secondary-grey-100);
            }
        }
        tr {
            &:last-child {
                td {
                    border-bottom: none;
                }
            }
        }
    }
    .filter-options {
        padding-inline-start: 0;
    }
    .material-symbols-outlined {
        font-variation-settings: "FILL" 1, "wght" 700, "GRAD" 0, "opsz" 48;
        margin-left: 0 !important;
        margin-right: 10px;
        color: $color_3;
        font-size: 16px;
    }
}

.credit {
    .list-items {
        flex: 1;
        display: flex;
    }
}

.display-none {
    display: none !important;
}

.filtered {
    display: none !important;
}

@media (max-width: 1024px) {
    .credit-ranking-component {
        .credits-list {
            .credit {
                flex: 0 0 100% !important;
                max-width: 100% !important;
            }
        }
        .table {
            th, td {
                min-width: unset;
            }
        }
    }
}

// scrollbar for firefox
@-moz-document url-prefix() {
    .credit-ranking-component {
        .menu {
            scrollbar-color: #a2a2a2 transparent;
            scrollbar-width: thin;
        }
    }
}