.table-content {
  margin: 0;
  box-sizing: border-box;
  height: fit-content;
  box-shadow: none;
  color: inherit;
  background-color: transparent;
  left: auto;
  right: 0;
  position: sticky;
  width: 100%;
  display: flex;
  z-index: 10;
  box-sizing: border-box;
  flex-shrink: 0;
  flex-direction: column;

  &__content{
    height: auto;
    background: linear-gradient(0deg,#f5f6f8,#f5f6f8),linear-gradient(201.12deg,#fcfcff 11.28%,#efeff7 86.52%);
    border: 1px solid #e3e4e6;
    border-radius: 8px;
    padding: 24px 16px;
  }

  &__label {
    display: flex;
    align-items: center;
    grid-gap: 8px;
    gap: 8px;
    &:hover {
      cursor: pointer;
    }
  }

  &__labelText {
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    color: #000;
  }

  &__entered {
    min-height: 0px;
    height: auto;
    overflow: visible;
    transition: height 400ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    &-wrapper {
      display: flex;
    }
    &-inner {
      width: 100%;
    }
  }

  &__tableList {
    margin-top: 8px;
    padding: 0;
  }

  &__tableItem {
    margin-top: 8px;
    list-style-type: none !important;

    a span:hover {
      text-decoration: underline;
    }

    &-text {
      justify-content: space-between;
      color: #000;
      display: inline-flex;
      align-items: center;
      transition: all .3s ease-in-out;
      text-decoration: none;
      span {
        font-weight: 400;
        overflow-wrap: anywhere;
        display: inline-flex;
        align-items: center;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  &__listChild {
    margin: 0;
    padding: 0 0 0 1.5rem;

    li {
      display: table;
      list-style-type: none !important;
      margin-top: 8px;
    }
  }

    &__content {
      padding: 12px 16px;
    }
    
    &__label {
      justify-content: space-between;
    }

    &__entered  {
      transition-duration: 300ms;
      &.hide {
        height: 0;
        visibility: hidden;
        overflow: hidden;
        transition-duration: 300ms;
        .table-content__entered-wrapper {
          opacity: 0;
          transition-duration: 300ms;
        }
      }
      .table-content__entered-wrapper {
        opacity: 1;
        transition-duration: 300ms;
      }
    }

    &__icon {
      display: block;
      width: 24px;
      height: 24px;
      transition: all .3s linear;
      transform: rotate(180deg);
      &.click {
        transform: rotate(0deg);
      }
    }
}
