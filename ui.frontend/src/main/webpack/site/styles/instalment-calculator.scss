.instalment-calculator {
  &__container {
    width: 100%;
    margin: 0 auto;

    @media (min-width: 320px) and (max-width: 1199.95px) {
      display: flex;
    }

    @media (min-width: 1200px) {
      display: flex;
    }

    @media (min-width: 1440px) {
      max-width: 1440px;
    }

    &--wrapper {
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      width: calc(100% + 24px);

      .instalment-calculator-group {
        margin-bottom: 0;
        width: 100%;
      }

      .instalment-calculator-body {
        padding: 0.75rem 0 1rem;
        display: flex;
        flex-wrap: wrap;
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;

        @media (max-width: 992px) {
          flex-direction: column;
          padding: unset;
          margin-top: 1.5rem;
        }

        &__input {
          width: 50%;
          display: inline-block;
          padding: 1.5rem;
          border-radius: 0.5rem 0 0 0.5rem;
          background-color: white;

          @media (max-width: 992px) {
            width: 100%;
            border-radius: 0.5rem 0.5rem 0 0;
            padding: 1.25rem;
          }

          &--wrapper {
            display: flex;
            flex-wrap: wrap;
            width: 100%;

            .calculator {
              flex-grow: 0;

              &--label {
                margin: auto;
                padding: 0.5rem 0.25rem;
                display: flex;
                align-items: center;
                max-width: 30%;
                flex-basis: 30%;

                @media (max-width: 992px) {
                  padding: 0.25rem;
                }

                h3 {
                  display: inline-flex;
                  align-items: center;
                  font-size: 0.875rem;
                  font-weight: 700;
                  line-height: 1.5rem;

                  @media (max-width: 992px) {
                    font-size: 0.875rem;
                  }
                }

                img {
                  width: 1rem;
                  height: 1rem;
                  margin-left: 0.5rem;
                }
              }

              &--input {
                padding: 0.5rem 0.25rem;
                max-width: 70%;
                flex-basis: 70%;

                @media (max-width: 992px) {
                  padding: 0.25rem;
                }

                .installment-registration-amount {
                  border: 0;
                  margin: 0;
                  padding: 0;
                  display: inline-flex;
                  position: relative;
                  min-width: 0;
                  width: 100%;
                  flex-direction: column;
                  vertical-align: top;

                  &__wrapper {
                    border-radius: 0.5rem;
                    border: 1px solid #e3e4e6;
                    padding-right: 0.875rem;
                    color: rgba(0, 0, 0, 0.87);
                    cursor: text;
                    display: inline-flex;
                    position: relative;
                    font-size: 1rem;
                    box-sizing: border-box;
                    align-items: center;
                    font-weight: 400;
                    line-height: 1.1876em;
                    letter-spacing: 0.00938em;
                    width: 100%;

                    &:has(input:focus) {
                      border-color: #0a84ff;
                    }

                    &--input-body {
                      font: inherit;
                      color: currentColor;
                      width: 100%;
                      border: 0;
                      height: 1.1876em;
                      margin: 0;
                      display: block;
                      padding: 1.156rem 0.875rem;
                      min-width: 0;
                      background: none;
                      box-sizing: content-box;
                      animation-name: mui-auto-fill-cancel;
                      letter-spacing: inherit;
                      animation-duration: 10ms;
                      outline: 0;
                      line-height: 1.5rem;
                    }

                    &--currency-label {
                      height: 0.01em;
                      display: flex;
                      max-height: 2em;
                      align-items: center;
                      white-space: nowrap;
                      color: #a2a2a2;
                    }
                  }

                  &__invalid-message {
                    color: #ed1b24;
                    font-size: 0.75rem;
                  }
                }

                .offer-filter__dropdown {
                  .dropdown__wrapper {
                    margin-bottom: 0;

                    .dropdown__display {
                      img {
                        transition: all 300ms;

                        &.expanded {
                          transform: rotate(180deg);
                        }

                        &.collapsed {
                          transform: rotate(0deg);
                        }
                      }
                    }

                    .dropdown__list {
                      max-height: max-content;
                    }
                  }
                }
              }
            }
          }
        }

        &__info {
          width: 50%;
          position: relative;
          display: inline-block;
          padding: 1.25rem 6% 1.25rem 1.25rem;

          @media (max-width: 992px) {
            width: 100%;
            padding: 1.25rem;
          }

          &--background {
            box-sizing: border-box;
            display: block;
            overflow: hidden;
            width: initial;
            height: initial;
            background: none;
            opacity: 1;
            border-radius: 0 0.5rem 0.5rem 0;
            margin: 0;
            padding: 0;
            inset: 0;
            position: absolute;

            @media (max-width: 992px) {
              width: 100%;
              border-radius: 0 0 0.5rem 0.5rem;
            }

            img {
              position: absolute;
              inset: 0;
              box-sizing: border-box;
              padding: 0;
              border: none;
              margin: auto;
              display: block;
              width: 0;
              height: 0;
              min-width: 100%;
              max-width: 100%;
              min-height: 100%;
              max-height: 100%;
              object-fit: cover;
              object-position: center center;
            }
          }

          &--content {
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: space-between;

            @media (max-width: 992px) {
              flex-direction: row;
              justify-content: start;
            }

            .fee-content {
              display: flex;
              justify-content: space-between;

              @media (max-width: 992px) {
                flex-direction: column;
                margin-left: 1.875rem;
              }
            }

            .content {
              &__icon {
                position: relative;

                img {
                  width: 4.875rem !important;
                  height: 3.375rem !important;
                }
              }

              &__conversion-fee {
                color: white;
                display: inline-block;

                p {
                  color: var(--primary-white);
                  margin-bottom: 0.5rem;
                  margin-top: 1.5rem;
                  font-weight: 300;
                  font-style: normal;
                  line-height: 1.5rem;

                  @media (max-width: 992px) {
                    margin: unset;
                  }
                }

                h3 {
                  overflow-wrap: break-word;
                  font-size: 1.5rem;
                  line-height: 1.5;
                  font-weight: 700;
                }
              }

              &__monthly-payment-amount {
                color: white;
                display: inline-block;

                p {
                  color: var(--primary-white);
                  margin-bottom: 0.5rem;
                  margin-top: 1.5rem;
                  font-weight: 300;
                  font-style: normal;
                  line-height: 1.5rem;
                }

                h3 {
                  overflow-wrap: break-word;
                  font-size: 1.5rem;
                  line-height: 1.5;
                  font-weight: 700;
                }
              }
            }
          }
        }
      }
    }
  }
  &__note {
    color: #616161;
    font-weight: 300;
  }
}
