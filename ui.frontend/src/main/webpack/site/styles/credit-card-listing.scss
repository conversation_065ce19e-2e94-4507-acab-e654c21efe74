.credit-card-listing__button {
  justify-content: center;
  border: 1px solid var(--secondary-mid-grey-60);
  border-radius: 27px;
  color: var(--secondary-grey-60);
  font-weight: 400;
  padding: 12px 15px;
  background-color: transparent;
  position: relative;
  display: inline-flex;
  outline: none;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
  transition: all 0.3s ease-in;
  align-items: center;
  grid-gap: 12px;
  gap: 12px;
  min-width: max-content;
  z-index: 1;
  line-height: 24px;
  margin: 0;
  &:hover {
    background: var(--primary-black);
    color: var(--primary-white);
  }
}

.credit-card-listing__button.big-size {
  color: var(--body);
  font-size: 16px;
  padding: 12px 24px;
}

.credit-card-listing__button.big-size.filter-selected,
.filter-selected {
  background-color: #000;
  color: var(--primary-white);
}

.credit-card-listing {
  min-height: 90px;

  &__content {
    background-color: var(--primary-white);
    width: 100%;
    padding: 20px 0;
    margin-bottom: 48px;
    filter: drop-shadow(0 5px 22px rgba(0,0,0,.1));
  }

  &__container {
    display: flex;
    box-sizing: inherit;
    flex-grow: 0;
    flex-basis: 100%;
    align-items: center;
    flex-wrap: nowrap;
    overflow: hidden;
    h6 {
      margin-right: 16px;
      white-space: nowrap;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
    }
    .m-r-16 {
      margin-right: 1rem;
    }

    @include maxSm {
      h2 {
        min-width: fit-content;
      }
    }
  }

  &__items {
    display: flex;
    align-items: center;
    flex-grow: 1;
    flex-wrap: nowrap;
    overflow-x:scroll;
    gap: 16px;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .list-card-product {
    &__container {
      width: 100%;
      display: block;
      box-sizing: border-box;
      margin-left: auto;
      margin-right: auto;

      .card-product-banner {
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
      }

      @media (max-width: 991px) {
        gap: 36px;
      }
    }

    &__grid-container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      gap: 24px;
      padding-bottom: 24px;
    }

    &__grid-item {
      flex-grow: 0;
      max-width: calc((100% - 48px) / 3);
      flex-basis: calc((100% - 48px) / 3);
      @media (max-width: 991px) {
        max-width: 48%;
        flex-basis: 48%;
      }

      @include maxLgSemi {
        .card-image {
          max-width: 100%;
        }
      }

      @media (max-width: 767px) {
        max-width: 100%;
        flex-basis: 100%;
      }
    }
  }

  .card-bank {
    &__card {
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      border-radius: 8px;
      transition: all 0.3s ease 0s;
      background-color: rgb(255, 255, 255);
      height: 100%;

      &:hover {
        box-shadow: rgba(0, 0, 0, 0.04) 0 33px 181px, rgba(0, 0, 0, 0.027) 0 13.7866px 75.6175px,
        rgba(0, 0, 0, 0.024) 0 7.37098px 40.4287px, rgba(0, 0, 0, 0.02) 0 4.13211px 22.664px,
        rgba(0, 0, 0, 0.016) 0 2.19453px 12.0367px, rgba(0, 0, 0, 0.01) 0 0.913195px 5.00873px;

        .card-bank__image {
          transform: translateY(-10px);
        }
      }
    }

    &__button {
      position: relative;
      display: inline-flex;
      padding: 16px 24px;
      border-radius: 8px;
      outline: none;
      border: none;
      cursor: pointer;
      white-space: nowrap;
      text-decoration: none;
      transition: all 0.3s ease-in 0s;
      justify-content: space-between;
      align-items: center;
      gap: 12px;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      background-color: inherit;

      &[data-button-compare] {
        &.disabled {
          color: var(--secondary-mid-grey-100);
          cursor: not-allowed;
          background-color: var(--cta-disabled);
          border: 1px solid var(--cta-disabled);
        }

        &.selected {
          background-color: #e0f7e5;
          cursor: not-allowed;
          color: #1d6d30;

          .icon-svg-add {
            display: none;
          }

          .icon-svg-checked {
            display: block;
          }

          &:hover .icon-svg-checked {
            filter: grayscale(0);
          }
        }

        .icon-svg-checked {
          display: none;
        }

        .button__icon-svg {
          display: flex;
          align-items: center;
          transition: all 0.3s ease-in-out 0s;
        }
      }

      &-white {
        background-color: var(--primary-white);
        color: var(--primary-black);

        &:hover {
          background-color: var(--secondary-mid-grey-100);
          color: var(--secondary-grey-60);

          img {
            filter: grayscale(100%);
          }
        }
      }

      &__box-shadow {
        box-shadow: rgba(0, 0, 0, 0.04) 0px 33px 181px, rgba(0, 0, 0, 0.027) 0px 13.7866px 75.6175px,
        rgba(0, 0, 0, 0.024) 0px 7.37098px 40.4287px, rgba(0, 0, 0, 0.02) 0px 4.13211px 22.664px,
        rgba(0, 0, 0, 0.016) 0px 2.19453px 12.0367px, rgba(0, 0, 0, 0.01) 0px 0.913195px 5.00873px;
      }

      @media (max-width: 767px) {
        padding: 12px 16px;
        white-space: normal;
      }
    }

    &__label {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      color: var(--primary-white);
      background-color: var(--primary-red);
      text-transform: uppercase;
      padding: 4px 16px;
      border-radius: 0 0 8px 8px;
      white-space: nowrap;
      z-index: 1;
      font-weight: 600;
      font-size: 0.875rem;
      line-height: 1.5;
      letter-spacing: 2px;
    }

    &__image-wrapper {
      padding: 50px 56px 40px;
      border-radius: 8px 8px 0 0;
      overflow: hidden;
      min-height: 285px;
      max-height: 285px;
      @media (max-width: 767px) {
        padding: 30px 35px 40px;
        min-height: 220px;
        max-height: 220px;
      }
    }

    &__actions {
      margin-top: 1.5rem;
      display: flex;
      gap: 24px;
      @media (max-width: 767px) {
        gap: 0;
      }

      .cta-button {
        min-width: unset;
        max-width: 156px;
        @media (max-width: 767px) {
          padding: 16px;
        }
        @media (max-width: 360px) {
          padding: 16px 24px;
          gap: 0;
        }

        .cmp-button__text {
          font-weight: 600;
          white-space: break-spaces;
          text-align: center;
          font-size: 16px;
          line-height: 24px;

          @include maxSm2 {
            min-width: fit-content;
          }
        }

        &:hover:not(.cta-button--link) .cmp-button__icon {
          filter: brightness(0) invert(1);
        }
      }
      @media screen and (max-width: 1199px) and (min-width: 992px) {
        flex-direction: column;
        gap: 16px
      }
    }

    &__comparing-action {
      text-align: center;
      padding: 0px 24px;
      margin-top: -17px;
      position: relative;
      font-weight: 600;
    }

    &__image {
      display: flex;
      max-width: 100%;
      height: 100%;
      text-align: center;
      transition: all 0.3s ease 0s;
      transform: translateY(0px);
      border-radius: 12px;
      place-content: center;
      img {
        border-radius: 12px;
        object-fit: contain;
      }
    }

    &__name {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
      color: var(--primary-black);
      margin: 0;
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.25;
    }

    &__info {
      padding: 24px 24px 32px;
      flex: 1 1 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      @media (min-width: 1200px) {
        padding: 24px 24px 32px;
      }
      @media (max-width: 767px) {
        padding-left: 16px;
        padding-right: 16px;
      }
    }

    &__body-info {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    &__description {
      line-height: 24px;
      color: var(--secondary-grey-60);
      margin-top: 16px;

      ul {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      li {
        color: var(--secondary-grey-60);
        padding-left: 21px;
        position: relative;

        &::before {
          position: absolute;
          content: "";
          width: 5px;
          height: 5px;
          background-color: var(--secondary-mid-grey-80);
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          border-radius: 1px;
        }
      }
    }

  }

  .compare-choosing {
    &__sticky-card {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 1;
      background-color: var(--primary-white);
      padding: 20px 0;
      transition: all 0.4s ease-in-out;
    }

    &__container {
      width: 100%;
      display: flex;
      box-sizing: inherit;
      margin-left: auto;
      margin-right: auto;
      align-items: center;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    &__list-card {
      display: flex;
      flex-grow: 0;
      max-width: 33.333333%;
      flex-basis: 30%;
    }

    &__list-card .has-card {
      border: none;
      flex: unset;
      max-width: 116px;
      box-shadow: none;
    }

    @media (max-width: 991px) {
      .compare-button {
        max-width: 100%;
        flex-basis: 100%;
        padding-top: 40px;

        &__link {
          padding: 12px 16px;
        }
      }
    }

    @media (max-width: 390px) {
      &__sticky-card {
        padding: 16px 0;
      }

      &__list-card {
        max-width: 100%;
        flex-basis: 100%;
        overflow-x: scroll;
        padding-top: 8px;
      }

      &__list-card::-webkit-scrollbar {
        display: none;
      }

      .remove-button {
        top: -8px;
        right: -8px;
      }
    }
  }

  .list-card__item {
    flex: 0 0 116px;
    max-width: 116px;
    height: 75px;
    border: 2px dashed var(--secondary-mid-grey-60);
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: inherit;

    &:not(:last-child) {
      margin-right: 24px;
    }

    &-image {
      box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%),
      0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
      border-radius: 6px;
      display: none;
      width: 100%;
      overflow: hidden;
      height: 100%;
      box-sizing: inherit;
    }

    &-image span {
      box-sizing: border-box;
      display: inline-block;
      overflow: hidden;
      width: 122.6px;
      height: 77.2px;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      position: relative;
    }

    &-image img {
      position: absolute;
      inset: 0px;
      box-sizing: border-box;
      padding: 0px;
      border: none;
      margin: auto;
      display: block;
      width: 0px;
      height: 0px;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: contain;
      object-position: center center;
    }
  }

  .remove-button {
    width: 24px;
    height: 24px;
    background-color: var(--primary-red);
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
    position: absolute;
    top: -12px;
    right: -12px;
    cursor: pointer;
    box-sizing: inherit;

    img {
      width: 18px;
      height: 18px;
    }
  }

  .add-panel {
    width: 32px;
    height: 32px;
    background-color: var(--secondary-light-grey-60);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    box-sizing: inherit;

    img {
      filter: grayscale(100%);
    }
  }

  .has-card {
    .list-card__item-image {
      display: block;
    }

    .remove-button {
      display: flex;
    }

    .add-panel {
      display: none;
    }
  }

  .compare-button {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 23.6%;
    &__link {
      justify-content: space-between;
      background-color: var(--primary-black);
      color: var(--primary-white);
      position: relative;
      display: inline-flex;
      padding: 16px 24px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease-in;
      align-items: center;
      grid-gap: 12px;
      gap: 12px;
      min-width: max-content;
      width: 100%;
      z-index: 1;
      font-weight: 600;
      line-height: 24px;

      &[disabled] {
        color: var(--secondary-mid-grey-100);
        pointer-events: none;
        background-color: var(--cta-disabled);
        border: 1px solid var(--cta-disabled);
      }

      &:hover {
       background: var(--secondary-grey-60);
      }

      &-icon {
        display: flex;
        align-items: center;
        margin-left: 0;
        transition: all 0.3s ease-in-out;
        color: var(--primary-white);
        filter: brightness(0) invert(1);
      }
    }
  }

}

.credit-card-listing, .credit-card-comparison-result {
  .popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    padding: 144px 0 146px;
    background: rgba(0, 0, 0, .6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all .3s ease;

    &.open {
      opacity: 1;
      visibility: visible;
      pointer-events: auto;
      transition: all .3s ease;
    }

    &-content {
      padding: 50px 0 0;
      background: linear-gradient(90deg, #fdfbfb, #ebedee);
      position: relative;
      max-width: 1000px;
      min-height: 450px;
      border-radius: 16px;
      @media (max-width: 767px) {
        padding-top: 72px;
      }

      img {
        width: 16px;
      }
    }

    .close {
      position: absolute;
      color: red;
      top: 20px;
      right: 30px;
      cursor: pointer;
    }

    .popup-register {
      &_image {
        position: absolute;
        left: 16px;
        top: 50%;
        width: 180px;
        height: auto;
        transform: translateY(-50%);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center center;
        }
      }

      &_content {
        max-width: calc(100% - 196px);
        margin-left: auto;
      }

      &_contentTop {
        background-color: var(--primary-white);
        padding: 16px 24px;
        border-radius: 8px;
      }

      &_steps {
        display: flex;
        margin: 16px -20px 0;
        align-items: stretch;
      }

      &_item {
        padding: 0 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: auto;

        &.popup-register_step0 {
          display: none;
        }
      }

      &_titleDescOuter {
        margin-top: 16px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%), 0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%), 0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);  // Added by Adobe team
        border-radius: 16px;
        img {
          width: 100%;
          object-fit: cover;
          object-position: center center;
          border-radius: 16px;
        }
      }

      &_titleDesc {
        background-color: var(--primary-white);
        box-shadow: 0 33px 181px rgba(0, 0, 0, .04), 0 13.7866px 75.6175px rgba(0, 0, 0, .029), 0 7.37098px 40.4287px rgba(0, 0, 0, .024), 0 4.13211px 22.664px rgba(0, 0, 0, .02), 0 2.19453px 12.0367px rgba(0, 0, 0, .016), 0 0.913195px 5.00873px rgba(0, 0, 0, .011);
        border-radius: 16px;
        overflow: hidden;
        height: 100%;
      }

      &_contentBottom {
        display: flex;
        align-items: center;
        padding: 16px;
      }

      &_title {
        margin-right: 33px;
      }

      &_linkGroup {
        display: flex;
      }

      &_text p {
        font-size: 24px;
        line-height: 36px;
        font-weight: 300;
      }

      &_linkItem {
        border-right: 1px solid #c4c4c4;
        padding-right: 24px;
        margin-right: 24px;

        a {
          justify-content: space-between;
          display: inline-flex;
          align-items: center;
          transition: all .3s ease-in-out;
          .link-text {  // Added by Adobe team
            font-weight: 600;
          }
          &:hover {
            .link-text {
              text-decoration: underline;
            }
          }

          img {
            margin-left: 12px;
          }
        }
      }

      @media (min-width: 768px) {
        &_item {
          &:first-child {
            flex: 0 0 16%;
            max-width: 16%;
          }
          h6{ // Added by Adobe team
            font-size: 16px;
            line-height: 20px;
            font-weight: 600;
          }
          &:nth-child(2) {
            flex: 0 0 24%;
            max-width: 24%;
          }

          &:nth-child(3), &:nth-child(4) {
            flex: 0 0 29%;
            max-width: 29%;
          }
        }
      }
      @media (max-width: 1024px) {
        &_title {
          margin-right: 15px;
          padding: 0;
        }
      }
      @media (max-width: 991px) {
        &_image {
          display: none;
        }
        &_content {
          max-width: 100%;
        }
        &_contentTop {
          .popup-register_title {
            padding: 0;
            border: none;
          }
        }
        &_contentBottom {
          padding: 0 16px 40px;
          display: block;
          background-color: var(--primary-white);
          width: 100%;
        }
        &_title {
          border-top: 1px solid #c4c4c4;
          padding-top: 40px;
        }
        &_linkGroup {
          display: block;
        }
        &_linkItem {
          display: block;
          margin-top: 24px;
          border-right: none;
          padding-right: 0;
          margin-right: 0;
        }
      }
      @media (max-width: 767px) {
        &_contentTop {
          padding: 0;
          background-color: transparent;

          .popup-register_title {
            padding: 0 16px;
          }
        }

        &_desc {
          margin-top: 24px;
          padding: 0 16px;
          display: flex;
          align-items: center;
        }

        &_steps {
          margin: 0;
          overflow: auto;
          padding: 0 16px 40px;
          background-color: var(--primary-white);
          width: 100vw;
        }
        &_item {
          padding: 0;
          margin-right: 16px;
          min-width: 247px;

          &:first-child {
            display: none;
          }

          &.popup-register_step0 {
            display: block;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, .07);
            border-radius: 16px 16px 0 0;
            background-color: var(--primary-white);
            padding: 40px 16px;
            margin-top: 40px;
            margin-right: 0;

            .popup-register_titleDesc1 {
              margin-top: 16px;
            }

            a {
              display: flex;
              padding: 12px 16px;
              border-radius: 8px;

              &:hover {
                background-color: var(--secondary-mid-grey-100);
                color: var(--secondary-grey-60);
              }
            }
          }
        }
      }
    }
  }
}
