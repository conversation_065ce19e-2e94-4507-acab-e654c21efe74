/* Component link-text (Ticket List) */
.link-text {
  padding: 0;
  height: 100%;

  .section-title {
    font-weight: 300;
    font-size: 1.75rem;
    line-height: 1.25;
    position: relative;
    //color: #000;
    padding-left: 12px;
    padding-bottom: 8px;
  }

  .link-text__container {
    height: 100%;
  }

  .ticket-list {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 100%;
    @media screen and (max-width: 767px) {
      margin-top: 0.5rem;
    }
  }

  .ticket-list-wrapper {
    display: grid;
    grid-gap: 24px;
    height: 100%;
    .ticket-item {
      flex-grow: 0;
      .link_component-link {
        display: block;
        height: 100%;
        /* border-radius: 8px; */
      }
    }
    .contact-us__item__wrapper{
        height: 100%;
    }
  }

  .ticket_wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    border: 1px solid #dedede;
    border-radius: 8px;
    cursor: pointer;
    padding: 24px;
    align-items: center;
    background-color: #fff;
    grid-gap: 16px;
    gap: 16px;
    transition: all 0.3s ease-in-out;
  }

  .ticket_wrapper.leftType{
    padding: 16px 20px;
  }

  .float_on_hover{
    &:hover {
      transition: all 0.3s ease-in-out;
      box-shadow: 0 33px 181px rgb(0 0 0 / 4%),
        0 13.7866px 75.6175px rgb(0 0 0 / 3%),
        0 7.37098px 40.4287px rgb(0 0 0 / 2%),
        0 4.13211px 22.664px rgb(0 0 0 / 2%),
        0 2.19453px 12.0367px rgb(0 0 0 / 2%),
        0 0.913195px 5.00873px rgb(0 0 0 / 1%);
    }
  }

  p.ticket-title,
  p.contact-us__item__content__title_text {
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5rem;
    margin-top: 0;
  }

  .ticket-title,
  .ticket-description,
  .contact-us__item__content__title,
  .contact-us__item__content__description{
    line-break: strict;
    word-break: break-word;
  }

  .contact-us__item__content__title{
    display: flex;
  }

  .contact-us__item__content__title_text{
    width: 100%;
  }

  .ticket-description{
    color: #616161;
    margin-top: 8px;
  }

  .ticket-content {
    display: flex;
    flex-direction: column;
    flex: 1 1;
    color: #000;
  }

  .icon-ext {
    margin-left: 0;
    align-self: center;
    line-height: 0;
  }

  icon-img{
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .leftType__icon-img {
    width: 62px;
  }

  .icon-img {
    width: 50px;
    height: 50px;
  }

  .contact-us__icon-img, 
  .contact-us__item__placeholder{
    width: 32px;
    height: 32px;
  }

  .icon-ext img {
    height: 16px;
    width: 16px;
  }

  .ticket_icon.leftType:hover ~ .ticket-content {
    text-decoration: underline;
  }
}

.column-item {
  padding: 0;
}

.ticket-item[hiddenInDesktop='hidden']{
  display: none;
}

.column-item .content-wrapper{
  padding: 0;
  height: 100%;
}

/* Style for view more button */
.link-text-btn{
  margin: 18px 12px;
}

.link-text-button__link {
  border: none;
  background: none;
  white-space: nowrap;
  cursor: pointer;
}

.link-text-view-more__title,
.link-text-view-more__icon {
  display: inline;
}
/* End style for view more button */

/* End component link text*/

@media (max-width: 1024px) {
  .link-text {
    .ticket-list-wrapper {
      width: calc(100% + 1rem);
      gap: 16px;
    }
    .ticket-item {
      grid-column-start: auto !important;
      grid-row-start: auto !important;
    }
  }

  .ticket-item[hiddenInMobile='hidden']{
    display: none;
  }

}

@include maxMd {
  .link-text {
    .section-title {
      padding-bottom: 20px;
      padding-left: 0;
    }

    .ticket-list-wrapper {
        gap: 0px;
        grid-template-columns: 1fr !important;
        &.topType,&.leftType {
            width: calc(100% + 16px);
            margin: 16px -8px;
          }
    }

    .contact-us__item__wrapper,
    .ticket_wrapper {
      padding: 16px;
      overflow: hidden;
    }
  }
  .link-text-btn{
    display: flex;
    justify-content: center;
  }
  .cta-button {
    white-space: unset;
    margin: 12px 0;
    display: flex;
  }
}
@media (max-width: 932px) {
  .ticket-item{
    padding: 8px;
  }
}

@media (max-width: 428px) {
  .ticket-item{
    padding: 8px;
  }

  .link-text-nav {
    margin: 38px 0;
  }

  .ticket_wrapper.leftType{
    padding: 16px !important;
  }
}

@media (max-width: 390px) {
  .link-text {
    .link-text__container {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .section-title {
      padding: 0 0 8px 16px;
    }

    .ticket-list-wrapper {
      width: calc(100% + 16px);
      margin-left: -8px;
    }
  }
}
