.cardinfo:not(:has(.list-card-info-slider)) {
  @media (max-width: 767px) {
    width: 109.756097561% !important;
    margin-left: -4.4444444444vw;
    overflow: hidden;
    position: relative;
    z-index: 1;
    > div {
      display: flex;
      padding-left: calc(100vw / 22.5);
      padding-right: calc(100vw / 22.5);
    }
  }
}
.list-card-info {
  @media (max-width: 767px) {
    width: 100%;
    .cardinfo:not(:has(.list-card-info-slider)) &{
      width: calc(100% + 16px);
      margin: -8px;
    }
  }
    .list-card-info__item-wrapper {
      width: 100%;
      flex: 1;
    }
    &.dark-theme {
      .list-card-info__item-wrapper {
        color: #fff;
        background: linear-gradient(0deg,rgba(0,0,0,.2),rgba(0,0,0,.2)),linear-gradient(0deg,#212121 48.82%,#8d8175 153.41%);
      }
    }
    .cta-button {
      min-width: 0 !important;

      @include xs {
        width: fit-content;
      }
    }
    .right-bottom-icon .cta-button {
      align-items: flex-end;
      picture {
        margin-bottom: 5px;
      }
    }
    &__container {
      z-index: 5;
    }
    .list-card-info {
      &__title {
        color: white;
        font-size: 1.75rem;
        line-height: 1.25;
        font-weight: 300;
        padding-bottom: 8px;
        &.loan {
          color: black;
        }
  
        @media (max-width: 767px) {
          margin-bottom: 0;
        }
      }
  
      &__container {
        .list-card-info {
          &__list-item {
            display: grid;
            grid-template-columns: auto auto auto;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px 24px;
  
            &.column-1 {
              grid-template-columns: repeat(1, 1fr);
            }
  
            &.column-2 {
              grid-template-columns: repeat(2, 1fr);
              .list-card-info {
                &__item {
                    .list-card-info {
                        &__item {
                          &-img {
                              aspect-ratio: 2.85;
                          }
                        }
                    }
                }
              }
            }
  
            &.column-3 {
              grid-template-columns: repeat(3, 1fr);
            }
  
            &.column-4 {
              grid-template-columns: repeat(4, 1fr);
            }

            &.view-more {
              .list-card-info__item-content {
                padding: 24px 16px !important;
              }
            }
            
            .list-card-info {
              &__item {
                color: black;
                position: relative;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-start;
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 1px 4px rgb(0 0 0 / 16%);

                @media screen and (max-width: 767px) {
                  height: var(--mobile-height);
                }
                @media screen and (min-width: 768px) {
                  height: var(--desktop-height);
                }
  
                .list-card-info {
                  &__item {
                    &-img {
                      object-fit: cover;
                      transition: all 0.3s;
                      transform: scale(1);
                      position: relative;
                      max-width: 100%;
                      height: 100%;
                      &.only-image {
                        aspect-ratio: 1;
                        display: flex;
                      }
                      &.small-image {
                        height: 100px;
                        margin: 20px;
                        object-fit: contain;
                      }
                      &.large-image {
                        position: absolute;
                        top: 0;
                        height: 100%;
                        aspect-ratio: calc(16/9);
                        width: 100%;
                      }
  
                      &.loan {
                        pointer-events: none;
                      }
                    }
  
                    &-content {
                      padding: 24px;
                      font-weight: 400;
                      line-height: 1.5;
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      justify-content: space-between;
                      height: 100%;
  
                      @media (max-width: 767px) {
                        padding: 16px;
                      }
  
                      .list-card-info {
                        &__item {
                          &-info {
                            &-description {
                              > h4 {
                                margin-bottom: 8px;
                              }
  
                              &--title {
                                font-weight: 600;
                                font-size: 1rem;
                                margin-bottom: 8px;
                                margin-top: unset;
                              }

                              .content-flex {
                                display: flex;
                                flex-direction: row;
                                align-items: flex-end;
                                justify-content: space-between;
                              }
                            }
  
                            &-footer {
                              font-weight: bold;
                            }
                          }
                        }
                      }
  
                      @media (max-width: 992px) {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                      }

                      h3 {
                        font-size: 24px;
                        font-weight: 300;
                        line-height: 36px;
                      }

                      h4 {
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 24px;
                      }
                    }
                  }
                }
  
                @media (max-width: 767px) {
                  height: 100%;
                  display: flex;
                }

                &.effects-enlarge-shadow {
                  &:hover {
                    box-shadow: 0 0.0625em 0.0625em rgba(0, 0, 0, 0.25),
                      0 0.125em 0.5em rgba(0, 0, 0, 0.25),
                      inset 0 0 0 1px hsla(0, 0%, 100%, 0.1);
    
                    .list-card-info {
                      &__item {
                        &-img {
                          transform: scale(1.05);
                        }
                      }
                    }
                  }
                }

                &.effects-enlarge {
                  &:hover {
                    .list-card-info {
                      &__item {
                        &-img {
                          transform: scale(1.05);
                        }
                      }
                    }
                  }
                }

                .list-card-info__item--box {
                  overflow: hidden;
                  width: 100%;
                  height: auto;
                  min-height: 88px;
                }
  
                &.img-bottom {
                  flex-direction: column-reverse;
  
                  .list-card-info__item--box {
                    display: flex; 
                  }
  
                  .list-card-info__item-content {
                    order: 2;
                  }
                }

                &.card-info-text-align-top {
                  &.overlay-card {
                    min-height: unset;
                  }

                  .list-card-info__item-info-card-description {
                    margin-bottom: 0;
                    margin-top: 0;
                  }
                }

                &.overlay-card {
                  min-height: 350px;
                  overflow: visible;
                  margin-top: 24px;
                  --negativePX: -24px;
                  .list-card-info__item--box{
                    transform: translateY(var(--negativePX));
                    display: flex;
                    justify-content: space-around;
                    .overlay-img {
                      border-radius: 8px;
                      width: calc(100% - 48px);
                      height: 219px;
                      position: relative;
                      overflow: hidden;
                      box-shadow: 0 8px 16px rgba(0,0,0,.1);
                      .overlay-image {
                        position: absolute;
                        inset: 0px;
                        box-sizing: border-box;
                        padding: 0px;
                        border: none;
                        margin: auto;
                        display: block;
                        width: 0px;
                        height: 0px;
                        min-width: 100%;
                        max-width: 100%;
                        min-height: 100%;
                        max-height: 100%;
                        object-fit: cover;
                        object-position: center center;
                        img.list-card-info__item-img {
                          min-width: 100%;
                          min-height: 100%;
                          object-fit: cover;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          &__list-item-slider {
            &.view-more {
              .list-card-info__item-content {
                padding: 24px 16px !important;
              }
            }

            .slick-arrow {
              position: absolute;
              border-radius: 50%;
              transition: all 0.2s ease-in-out;
              box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
              transform: translateY(-50%);
              width: 48px;
              height: 48px;
              display: flex !important;
              align-items: center;
              justify-content: center;
              background: hsla(0, 0%, 77%, 0.54);
              border: none;
              transition: all 0.3s ease;
              cursor: pointer;
            }
            .slick-prev {
              top: 50%;
              z-index: 7;
              color: white;
              font-size: 1.25rem;
              left: -0.875rem;
              &:hover {
                background-color: #fff;
                color: red;
                cursor: pointer;
              }
              &:after {
                content: "❮";
              }
            }
          
            .slick-next {
              right: -0.875rem;
              top: 50%;
              z-index: 7;
              color: white;
              font-size: 1.25rem;
              &:hover {
                background-color: #fff;
                color: red;
                cursor: pointer;
              }
              &:after {
                content: "❯";
              }
            }

            .slick-list {
              padding-top: 0.5rem;
              padding-bottom: 1rem;

              .slick-slide {
                margin: 0 0.75rem;
                .list-card-info {
                  &__item {
                    color: black;
                    position: relative;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: flex-start;
                    background-color: #fff;
                    border-radius: 8px;
                    box-shadow: 0 1px 4px rgb(0 0 0 / 16%);
    
                    @media screen and (max-width: 767px) {
                      height: var(--mobile-height);
                    }
                    @media screen and (min-width: 768px) {
                      height: var(--desktop-height);
                    }
      
                    .list-card-info {
                      &__item {
                        &-img {
                          object-fit: cover;
                          transition: all 0.3s;
                          transform: scale(1);
                          position: relative;
                          max-width: 100%;
                          height: 100%;
                          &.only-image {
                            aspect-ratio: 1;
                            display: flex;
                          }
                          &.small-image {
                            height: 100px;
                            margin: 20px;
                            object-fit: contain;
                          }
                          &.large-image {
                            position: absolute;
                            top: 0;
                            height: 100%;
                            aspect-ratio: calc(16/9);
                            width: 100%;
                          }
      
                          &.loan {
                            pointer-events: none;
                          }
                        }
      
                        &-content {
                          padding: 24px;
                          font-weight: 400;
                          line-height: 1.5;
                          flex: 1;
                          display: flex;
                          flex-direction: column;
                          justify-content: space-between;
                          height: 100%;
      
                          @media (max-width: 767px) {
                            padding: 16px;
                          }
      
                          .list-card-info {
                            &__item {
                              &-info {
                                &-description {
                                  > h4 {
                                    margin-bottom: 8px;
                                  }
      
                                  &--title {
                                    font-weight: 600;
                                    font-size: 1rem;
                                    margin-bottom: 8px;
                                    margin-top: unset;
                                  }
    
                                  .content-flex {
                                    display: flex;
                                    flex-direction: row;
                                    align-items: flex-end;
                                    justify-content: space-between;
                                  }
                                }
      
                                &-footer {
                                  font-weight: bold;
                                }
                              }
                            }
                          }
      
                          @media (max-width: 992px) {
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                          }
    
                          h3 {
                            font-size: 24px;
                            font-weight: 300;
                            line-height: 36px;
                          }
    
                          h4 {
                            font-size: 16px;
                            font-weight: 600;
                            line-height: 24px;
                          }
                        }
                      }
                    }
      
                    @media (max-width: 767px) {
                      height: 100%;
                      display: flex;
                    }
    
                    &.effects-enlarge-shadow {
                      &:hover {
                        box-shadow: 0 0.0625em 0.0625em rgba(0, 0, 0, 0.25),
                          0 0.125em 0.5em rgba(0, 0, 0, 0.25),
                          inset 0 0 0 1px hsla(0, 0%, 100%, 0.1);
        
                        .list-card-info {
                          &__item {
                            &-img {
                              transform: scale(1.05);
                            }
                          }
                        }
                      }
                    }
    
                    &.effects-enlarge {
                      &:hover {
                        .list-card-info {
                          &__item {
                            &-img {
                              transform: scale(1.05);
                            }
                          }
                        }
                      }
                    }
    
                    .list-card-info__item--box {
                      overflow: hidden;
                      width: 100%;
                      height: auto;
                      min-height: 88px;
                    }
      
                    &.img-bottom {
                      flex-direction: column-reverse;
      
                      .list-card-info__item--box {
                        display: flex; 
                      }
      
                      .list-card-info__item-content {
                        order: 2;
                      }
                    }
                    
                    &.overlay-card {
                      min-height: 350px;
                      overflow: visible;
                      margin-top: 24px;
                      --negativePX: -24px;
                      .list-card-info__item--box{
                        transform: translateY(var(--negativePX));
                        display: flex;
                        justify-content: space-around;
                        .overlay-img {
                          border-radius: 8px;
                          width: calc(100% - 48px);
                          height: 219px;
                          position: relative;
                          overflow: hidden;
                          box-shadow: 0 8px 16px rgba(0,0,0,.1);
                          .overlay-image {
                            position: absolute;
                            inset: 0px;
                            box-sizing: border-box;
                            padding: 0px;
                            border: none;
                            margin: auto;
                            display: block;
                            width: 0px;
                            height: 0px;
                            min-width: 100%;
                            max-width: 100%;
                            min-height: 100%;
                            max-height: 100%;
                            object-fit: cover;
                            object-position: center center;
                            img.list-card-info__item-img {
                              min-width: 100%;
                              min-height: 100%;
                              object-fit: cover;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
            
          }
        }
      }
    }
    .borrow {
      .list-card-info {
        &__item {
          justify-content: unset;
  
          .list-card-info {
            &__item {
              &-content {
                height: 50%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
              }
            }
          }
        }
      }
    }
  
    &__item {
      &.item-large-image {
        .list-card-info__item--box {
          display: flex;
          position: relative;
          padding-top: 57%;
          overflow: hidden;
        }
      }
      &-info-card-description {
        margin-bottom: 16px;
        margin-top: 1.5rem;

        .tcb-word-break {
          word-wrap: break-word;
        }
      }
      &-action {
        margin-top: 1rem;

        &:hover {
          text-decoration: underline;
        }
        .tcb-button {
          &--link {
            &:hover {
              color: var(--primary-black);
              text-decoration: underline;
            }
          }
        }
      }
    }

    .list-card-info__view-more-btn {
      display: flex;
      justify-content: center;
      padding: 12px;
      .cta-button {
        padding: 12px;
        .cmp-button__text {
          &:hover {
            text-decoration: underline;
          }
        }

        .cmp-button__icon {
          display: block;
          width: 24px;
          height: 24px;
          -webkit-transition: all 0.3s linear;
          transition: all 0.3s linear;
        }

        &:not(.view-more) {
          .cmp-button__icon {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
          }
        }
      }
    }
  }

  @media (max-width: 991px) {
    .list-card-info {
      .list-card-info {
        &__container {
          &.view-more {
            width: calc(100% + 24px);
            margin: -12px;
          }

          .list-card-info {
            &__list-item {
              &.view-more {
                grid-template-columns: repeat(2, 1fr);
                padding: 12px;
              }
            }
          }
        }
      }
    }
  }
  
  @media (max-width: 767px) {
    .list-card-info {
      .list-card-info {
        &__container {
          &.view-more {
            width: calc(100% + 16px);
            margin: -8px;
          }

          .list-card-info {
            &__list-item {
              &.view-more {
                margin: 0;

                .list-card-info__item {
                  margin: 8px 0;
                }
              }

              .slick-slide {
                .list-card-info__item {
                  display: flex !important;
                }
              }
            }
          }
        }
      }

      .list-card-info__view-more-btn {
        .cta-button {
          padding: 0;
          justify-content: center;
        }
      }
    }

    .list-card-info .list-card-info__container .list-card-info__list-item {
      display: unset;
      &.slick-slider {
        width: 95%;
        display: block;
        .cardinfo:not(:has(.list-card-info-slider)) & {
          width: unset;
          margin: 0 0 0 calc(100vw /(-22.5));
        }
      }
    }
  
    .list-card-info .slick-list {
      overflow: hidden;
      .cardinfo:not(:has(.list-card-info-slider)) & {
        margin-right: calc(100vw /(-22.5));
        padding-left: calc(100vw /(22.5));
      }
    }
  
    .list-card-info .list-card-info__container {
      &.view-more {
        margin: unset;
        width: unset;

        .view-more {
          padding: 0;
        }
      }
    }

    .cardinfo:not(.card-stackable) .list-card-info .list-card-info__container {
      margin-left: -0.5rem;
      
    }
    .list-card-info__container {
      .cardinfo:not(.card-stackable).cardinfo:not(:has(.list-card-info-slider)) & {
        padding: 0.5rem;
        margin-left: unset;
      }
    }
    
    .cardinfo:not(.card-stackable) .list-card-info .list-card-info-slider {
      margin-left: unset;
    }

    .list-card-info.overlay-card-list {
      padding-top: 28px;
    }
  
    .slide-mobile-only .slick-track,
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-track {
      display: flex;
      justify-content: flex-start;
      .cardinfo:not(:has(.list-card-info-slider)) & {
        margin: -8px;
        padding-right: calc(100vw /(22.5));
      }
    }
  
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-slide
      > div {
      height: 100%;
    }
  
    .slide-mobile-only .slick-slide,
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-slide {
      height: auto;
      padding: 8px;
    }
  
    .slide-mobile-only .slick-dots,
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-dots {
      display: flex;
      justify-content: center;
      margin-top: 15px;
    }
  
    .slide-mobile-only .slick-dots li,
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-dots
      li {
      color: #c4c4c4;
      width: 8px;
      font-size: 25px;
      margin: 0 5px;
    }
  
    .slide-mobile-only .slick-active,
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-active {
      font-size: 1rem;
      font-weight: 400;
    }
  
    .slide-mobile-only .slick-dots button,
    .list-card-info
      .list-card-info__container
      .list-card-info__list-item
      .slick-dots
      button {
      display: none;
    }
  
    .list-card-info__list-item .slick-dots {
      li::marker {
        color: #c4c4c4;
      }

      li.slick-active::marker {
        color: var(--primary-red);
      }
    }
    .slide-mobile-only .list__items {
      justify-content: center;
    }
  
    .slide-mobile-only .list-tile__hero {
      min-height: 376px;
    }
  
    .slide-mobile-only .slick-dots {
      position: absolute;
      bottom: -25px;
      left: calc(50% - 30px);
    }
  
    .slide-mobile-only {
      overflow: hidden;
    }
  
    .slide-mobile-only .square .list-tile__tile-item:before {
      padding-bottom: 0;
    }
  
    .background-mobile {
      display: block;
      position: absolute;
      width: 100%;
      top: 0;
    }
  
    .background-mobile img {
      width: 100%;
    }
    .list-card-info__item-action {
      .tcb-button.tcb-button--link.tcb-button--border-on-mobile {
        width: unset;
        padding: unset;
        border: unset;
      }
    }

    .list-card-info {
      .list-card-info__list-item {
        &.stack-view {
          .list-card-info__item {
            margin: 20px 0;
            .no-margin & {
              margin: 0;
            }
          }
        }
      }
    }
    .list-card-info__list-item {
      .slick-arrow {
        position: absolute;
        border-radius: 50%;
        transition: all 0.2s ease-in-out;
        box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
        transform: translateY(-50%);
        width: 48px;
        height: 48px;
        display: flex !important;
        align-items: center;
        justify-content: center;
        background: hsla(0, 0%, 77%, 0.54);
        border: none;
        transition: all 0.3s ease;
        cursor: pointer;
      }
      .slick-prev {
        top: 50%;
        z-index: 1;
        color: white;
        font-size: 1.25rem;
        left:8px;
    
        &:after {
          content: "❮";
        }
      }
    
      .slick-next {
        right: 8px;
        top: 50%;
        z-index: 1;
        color: white;
        font-size: 1.25rem;
    
        &:after {
          content: "❯";
        }
      }
    }
  }
  .image-same .list-card-info .list-card-info__item  .list-card-info__item--box {
    max-height: 170px;
    .list-card-info__item-img {
      width: 100%;
    }
  }
  @media (max-width: 767px) {
  .card-stackable  .list-card-info {
      width: 100%;
      .list-card-info__container .list-card-info__list-item .list-card-info__item {
        margin: 24px 0;
      }
    }
  
  .less-space-button {
    .list-card-info__item-action {
      margin-top: 0 !important;
      margin-bottom: -10px;
    }
    .cardslider-item-description  {
      padding-bottom: 0!important;
    }
  }

  .card-info-w-100 {
    .list-card-info__container {
      .list-card-info__list-item {
        width: 100% !important;
      }
    }
  }
}

.image-card-mobile {
  display: none;

  @media (max-width: 767px) {
    display: block;
    text-align: center;
    background-image: url(https://d1kndcit1zrj97.cloudfront.net/uploads/bg-6f5388cceb-34f9c08757.png);
    background-size: cover;

    img.list-card-info__item-img-mobile {
      max-height: 264px;
      max-width: 100%;
    }
  }
}
