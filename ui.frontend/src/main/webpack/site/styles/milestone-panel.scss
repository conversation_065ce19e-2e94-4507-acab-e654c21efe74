/* Techcombank History component */
.milestone {
  z-index: 2;
  .milestone__header {
    width: 100%;
    &-wrapper {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
      padding-bottom: 12px;
    }
  }

  .header-wrapper__title {
    display: flex;
    align-items: center;

    h2 {
      font-size: 28px;
      font-weight: 300;
      line-height: 35px;
    }
  }

  .milestone__bg-image {
    position: relative;
    min-height: 416px;
    display: flex;
    > img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center center;
    }
  }

  .milestone__body {
    width: 100%;
  }

  .body-wrapper__time-line {
    position: relative;
    z-index: 1;
    justify-content: center;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + 24px);
    margin: -12px;
    margin-top: 64px;
  }

  .time-line__left-content {
    flex-grow: 0;
    max-width: 58.333333%;
    flex-basis: 58.333333%;
    padding: 12px;
  }

  .left-content__wrapper {
    align-items: stretch;
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + 24px);
    margin: -12px;
  }

  .left-content__year-slider {
    height: 100%;
    padding: 12px;
    flex-grow: 0;
    max-width: 16.666667%;
    flex-basis: 16.666667%;

    .slick-slider {
      align-items: center;
      display: flex;
      height: auto;
      max-width: 114px;
      &.slick-vertical {
        min-height: 270px;
      }
    }
  }

  .year-slider__slick .slick-next,
  .slick-prev {
    font-size: 0;
    line-height: 0;
    position: absolute;
    padding: 0;
    cursor: pointer;
    border: none;
    color: transparent;
    outline: none;
    background: transparent;
  }

  .year-slider__slick {
    .slick-arrow {
      width: 15px;
      height: 15px;
      border-top: 2px solid #fff;
      border-left: 2px solid #fff;
      left: calc(50% - 10px);
      transform: translate(-50%);
      display: block;
    }

    .slick-prev {
      bottom: -20px;
      top: auto;
      transform: rotate(225deg);
    }

    .slick-next {
      top: -20px;
      transform: rotate(45deg);
    }

    .slick-list:before {
      width: 100%;
      height: 44px;
      content: "";
      background-color: red;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 33.33px;
      position: absolute;
    }

    .timeline-year {
      padding: 4px 0;
      text-align: center;
      color: #a2a2a2;
      font-weight: 300;
      font-size: 24px;
      line-height: 44px;
      margin: 0;
    }
  }

  .slick-current .timeline-year {
    color: #fff;
    font-weight: 700;
  }

  .left-content__content-slider {
    height: auto;
    flex-grow: 0;
    max-width: 75%;
    flex-basis: 75%;
    padding: 12px;
  }

  .content-slider__item {
    color: #fff;

    @media (max-width: 767px) {
      > h3 {
        margin-bottom: 98px;
        max-width: 35%;
      }
    }

    .timeline-year {
      margin: 0;
      padding: 0;
      margin-bottom: 16px;
      font-weight: 600;
      letter-spacing: -0.03rem;
    }

    .content-slider__item-text {
      letter-spacing: -0.03rem;
      height: 200px;
      overflow: auto;

      ul {
        line-height: 1.5;
      }

      li {
        margin-left: 16px;
        line-height: 1.5;
      }
    }
  }

  .time-line__right-content {
    flex-grow: 0;
    max-width: 33.333333%;
    flex-basis: 33.333333%;
    padding: 12px;
  }

  .milestone__image-card {
    position: absolute;
    top: 50%;
    left: 50%;
    height: calc(100% + 64px);
    transform: translate(-50%, -50%);
    width: 100%;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
  }

  .image-card__left-content {
    height: 100%;
    position: relative;
    flex-grow: 0;
    max-width: 66.666667%;
    flex-basis: 66.666667%;
    padding: 12px;
  }

  .image-card__right-content {
    height: 100%;
    position: relative;
    flex-grow: 0;
    max-width: 33.333333%;
    flex-basis: 33.333333%;
    padding: 12px;

    .image-active {
      opacity: 1;
    }
  }

  .content-slider__image {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 100%;
    transition: all 0.3s ease;
    opacity: 0;
    overflow: hidden;
    text-align: right;
    max-width: 362px;
      img {
        border-radius: 8px;
        object-fit: cover;
        object-position: center top;
        width: 100%;
        height:100%;
        max-height: 480px;
      }
  }
  
}

@media (max-width: 1199px) {
  .milestone .left-content__year-slider {
    max-width: 25%;
    flex-basis: 25%;
  }

  .milestone .image-card__right-content {
    height: 80%;
  }
}

@media (max-width: 991px) {
  .milestone {
    .body-wrapper__time-line {
      margin-top: 105px;
    }
    .milestone__bg-image {
      min-height: 510px;
    }
    .image-card__right-content {
      height: 60%;
    }
  }
}

@media (max-width: 767px) {
  .milestone {
    .body-wrapper__time-line {
      width: calc(100% + 16px);
      margin: -8px;
    }
    .milestone__bg-image {
      min-height: auto;
    }

    .time-line__left-content {
      max-width: 100%;
      flex-basis: 100%;
      padding: 8px;
    }

    .time-line__right-content {
      display: none;
    }

    .left-content__wrapper {
      flex-direction: column-reverse;
    }

    .left-content__year-slider {
      max-width: 100%;
      flex-basis: 100%;
      margin-bottom: 36px;

      .slick-slider {
        align-items: unset;
        display: block;
        height: inherit;
        max-width: 100%;
      }
    }

    .year-slider__slick {
      .slick-arrow {
        left: unset;
        transform: unset;
      }

      .slick-prev {
        top: 50%;
        left: 0;
        bottom: auto;
        transform: translateY(-50%) rotate(-45deg);
      }

      .slick-list {
        padding: 0 !important;
      }

      .slick-list:before {
        width: 80px;
        left: 50%;
        transform: translate(-50%, -50%);
        height: 32px;
      }

      .timeline-year {
        line-height: 34px;
        font-size: 16px;
        padding: 0;
      }

      .slick-next {
        top: 50%;
        right: 0;
        bottom: auto;
        transform: translateY(-50%) rotate(135deg);
      }
    }

    .left-content__content-slider {
      padding-top: 80px !important;
      max-width: 100%;
      flex-basis: 100%;
      padding-bottom: 12px;
    }

    .content-slider__slick {
      height: 100%;
    }

    .content-slider__item {
      .timeline-year {
        display: flex;
        max-width: 35%;
        margin-bottom: 98px;
      }

      .content-slider__item-text {
        ul {
          padding-bottom: 8px;
        }

        li {
          padding-bottom: 8px;
        }
      }

      .content-slider__item-text {
        height: inherit;
        overflow: unset;
      }
    }

    .image-card__left-content {
      height: auto;
      max-width: 50%;
      flex-basis: 50%;
    }

    .image-card__right-content {
      height: auto;
      max-width: 50%;
      flex-basis: 50%;
    }

    .content-slider__image {
      height: 250px;
      left: auto;
      top: 0;
      transform: none;
    }
  }
}


/* End of Techcombank History component */
