tcb-recommend-pre-page-popup {
  .recommend-pre-page-popup {
    display: flex;
    z-index: 1400;
    position: fixed;
    flex-direction: column;
    background-color: var(--primary-background);
    box-shadow: -2px -2px 20px 0px rgba(0, 0, 0, 0.12);
    border-top-left-radius: 0.5rem;

    left: auto;
    right: 0;
    width: fit-content;
    bottom: 0;
    max-width: 500px;
    transform: none;
    padding: 2rem;

    &.hidden {
      display: none;
    }

    @include maxSm {
      width: 100%;
      max-width: unset;
    }

    .recommend-content {
      .recommend-title {
        font-size: 24px;
        color: var(--secondary-grey-60);
        font-weight: 300;
        line-height: 36px;
        display: flex;
        align-items: center;
        gap: 12px;
        @include maxSm {
          font-size: 20px;
          line-height: 30px;
          font-style: normal;
          gap: 8px;
        }
      }

      .recommend-description {
        font-weight: 400;
        line-height: 24px;
        font-size: 16px;
        color: var(--secondary-grey-60);
        margin-top: 16px;
        margin-left: 48px;
        @include maxSm {
          margin-top: 8px;
          margin-left: 38px;
        }

        .page-title {
          color: var(--primary-black);
          font-weight: 500;
          line-height: 24px;
          font-size: 16px;
        }
      }
    }

    .button-block {
      display: flex;
      gap: 24px;
      margin-top: 24px;
      margin-left: 48px;

      button {
        width: 100%;
        border-radius: 8px;
        padding: 16px 24px;
        cursor: pointer;
        align-items: center;
        display: flex;
        gap: 12px;
        justify-content: space-between;
        font-weight: 600;
        border: 1px solid #000;
        background-color: var(--primary-background);

        &.primary {
          background-color: var(--primary-black);
          color: #fff;
        }

        @include maxSm {
          padding: 12px 16px;
        }
      }

      @include maxSm {
        gap: 16px;
        margin-left: 38px;
      }
    }
  }

  @keyframes tilt-shaking {
    0% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(5deg);
    }
    50% {
      transform: rotate(0deg);
    }
    75% {
      transform: rotate(-5deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  .icon-svg {
    position: fixed;
    top: 82%;
    right: 48px;
    animation: tilt-shaking 0.4s infinite;
    z-index: 1500;
    cursor: pointer;

    @include maxLgSemi {
      right: 16px;
      top: calc(100% - 200px);
    }

    &.display {
      display: unset;
    }
  }
}
