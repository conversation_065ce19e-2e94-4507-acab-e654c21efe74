.quick-access {
  position: relative;
  display: flex;
  justify-content: center;
  &__title {
    padding: 12px 0;
    margin: 0;
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
    h2 {
      font-size: 28px;
      font-weight: 300;
      line-height: 1.25;
      padding: 0;
    }
  }
  &__table-desktop {
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
    .quick-access-wrapper {
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 8px;
      flex-grow: 0;
    }
  }
  &__table-desktop-mobile {
    display: none;
  }
  .table-mobile__header {
    display: flex;
    height: 100%;
    flex-direction: column;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
    margin: 0 8px;
  }
  .table-mobile__header-items {
    background-color: #fff;
    border-radius: 8px;
    padding: 24px 16px;
    box-sizing: inherit;
  }
  .header-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:not(:last-child) {
      padding-bottom: 16px;
      margin-bottom: 16px;
      border-bottom: 1px solid #dedede;
    }
  }
  .header-item__img {
    line-height: 0;
    img {
      width: 16px;
      height: 17px;
    }
  }
  .table-header {
    width: 100%;
    box-shadow: none;
    background-color: transparent;
    color: inherit;
    box-sizing: inherit;
    flex-direction: column;
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    padding: 8px 24px 0;
    align-items: flex-end;
    min-height: auto;
    border-bottom: 1px solid #dedede;
    flex: 1 1 auto;
    display: inline-block;
    position: relative;
    white-space: nowrap;
    flex-grow: 1;
    flex-shrink: 0;
    flex-direction: column;

    &__items {
      display: flex;
      column-gap: 24px;
      justify-content: flex-start;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .header__button {
        color: #a2a2a2;
        font-weight: 400;
        font-size: 16px;
        min-height: 40px;
        border: 0;
        cursor: pointer;
        display: inline-flex;
        background-color: transparent;
        align-items: center;
        -webkit-appearance: none;
        -webkit-tap-highlight-color: transparent;

        &:hover {
          background-color: var(--secondary-light-grey-80);
        }

        &:first-child {
          border-bottom: 4px solid #ed1c24;
        }

        h3.table-header__tab {
          font-size: 16px;
          font-weight: unset;
          line-height: 24px;
        }
      }

      .header__selected {
        color: var(--primary-black);
        font-weight: 600 !important;
      }
    }
  }

  .table-records {
    height: 100%;
    .table-records__inner {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      display: none;
      height: 100%;
    }
    .record__show {
      display: flex;
    }

    &__inner-top {
      flex-grow: 1;
      border-bottom: 1px solid #dedede;

      &-header {
        display: flex;
        padding: 8px 0;

        .header__title {
          width: 25%;
          text-align: center;
          text-transform: inherit;
          font-weight: 600;
          font-size: 14px;
          line-height: 1.5;
          letter-spacing: 2px;

          p {
            font-weight: 400;
            font-size: 14px;
          }
        }
      }

      &-body {
        margin-top: 0;
        border-top: 1px solid #dedede;
      }
    }
  
  }
  .simple .table-records__inner-top-body {
    margin-top: 10px;
  }
  .table-records__inner-top-body .body__item {
    padding: 6px 0;
    pointer-events: none;
    justify-content: space-between;
    color: #000;
    align-items: center;
    transition: all 0.3s ease-in-out;
    text-decoration: none;
    display: flex;
    line-height: 1.5;
    font-weight: 600;
    pointer-events: none;
  }
  .simple .table-records__inner-top-body .body__item {
    padding: 8px 48px;
    pointer-events: unset;
  }
  .table-records__inner-top-body .body__item:nth-child(odd) {
    background-color: #f5f6f8;
  }
  .table-records__inner-top-body .body__item-flag {
    width: 25%;
    display: flex;
    align-items: center;
    padding-left: 24px;
    line-height: 1.5;
    pointer-events: none;
    ~ * {
      text-align: center;  
      color: var(--gray-600);
    }
  }
  .body__item-flag img {
    width: 16px;
  }
  .body__item-flag p {
    margin-left: 8px;
    font-weight: 600;
  }
  .body__item-currency-value {
    width: 25%;
    text-align: left;
    color: var(--primary-black);
  }
  .simple .body__item-currency-value {
    width: unset;
  }
  .table-records__inner-footer {
    justify-content: space-between;
    display: flex;
    padding: 16px 28px 32px 24px;
    align-items: center;
  }

  .simple .table-records__inner-footer {
    justify-content: flex-end;
    display: flex;
    padding: 16px 28px 32px 0;
    align-items: center;
  }

  .inner-footer__right-text {
    color: #a2a2a2;
    text-transform: inherit;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 1.5px;
    font-size: 14px;
  }

  .inner-footer__button-link {
    justify-content: space-between;
    width: auto;
    color: #000;
    position: relative;
    display: inline-flex;
    outline: none;
    border: none;
    cursor: pointer;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease-in;
    grid-gap: 12px;
    gap: 12px;
    background-color: inherit;
    line-height: 1.5;
    font-weight: 600;
    .button-link__svg {
      line-height: 0;
    }
  }

  .inner-footer__button-link:hover {
    text-decoration: underline;
  }

  /* pop-up */
  .pop-up {
    display: none;
    position: fixed;
    z-index: 10;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    -webkit-animation-name: curExFadeIn;
    -webkit-animation-duration: 0.6s;
    animation-name: curExFadeIn;
    animation-duration: 0.6s;
    
    &-content {
      position: fixed;
      bottom: 0;
      background-color: #fefefe;
      width: 100%;
      -webkit-animation-name: curExSlideIn;
      -webkit-animation-duration: 0.6s;
      animation-name: curExSlideIn;
      animation-duration: 0.6s;
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;

      .title {
        padding: 16px 24px;
        color: #313131;

        p {
          line-height: 1.5;
          font-size: 16px;
          font-weight: 700;
        }
      }
    }
  }

  @-webkit-keyframes curExSlideIn {
    from {
      bottom: -500px;
      opacity: 0;
    }

    to {
      bottom: 0;
      opacity: 1;
    }
  }

  @keyframes curExSlideIn {
    from {
      bottom: -500px;
      opacity: 0;
    }

    to {
      bottom: 0;
      opacity: 1;
    }
  }

  @-webkit-keyframes curExFadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes curExFadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @media (max-width: 1920px) {
    .quick-access__table-desktop {
      flex-grow: 0;
    }

    .quick-access__tabs {
      flex-grow: 0;
      max-width: 31%;
      flex-basis: 31%;
    }
  }

  @media (max-width: 991px) {
    .quick-access {
      padding-top: 32px;
      padding-bottom: 24px;
    }

    .quick-access__table-desktop {
      flex-grow: 0;
    }

    .quick-access__tabs {
      flex-grow: 0;
      max-width: 49%;
      flex-basis: 49%;
    }

    .quick-access__title {
      padding-top: 4px;
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
    }

    .quick-access__tabs .tabs__item {
      padding: 20px 20px;
    }
  }

  @media (max-width: 768px) {
    .header-item {
      line-height: 19.2px;
    }
    .table-records__inner-top {
      min-height: auto;
    }
    .quick-access__table-desktop {
      flex-grow: 0;
    }

    .quick-access__tabs {
      flex-grow: 0;
      max-width: 48%;
      flex-basis: 48%;
    }

    .header_layout .navigation_primary-wrapper {
      width: calc(100% - 88px);
    }
  }

  @media (max-width: 767px) {
    width: calc(100% + 16px);
    margin: -8px;
    .quick-access__title {
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
    }

    .quick-access__table-desktop {
      display: none;
    }

    .quick-access__table-desktop-mobile {
      display: flex;
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
    }

    .quick-access__tabs {
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
    }

    .quick-access__tabs .item-link__text p {
      display: none;
    }

    .quick-access__tabs .item-link__text {
      width: 68%;
    }

    .quick-access__tabs .item-link__text h6 {
      font-size: 15px;
      font-weight: 500;
    }

    .header_layout .navigation_primary-wrapper {
      width: calc(100% - 68px);
    }
  }
}

