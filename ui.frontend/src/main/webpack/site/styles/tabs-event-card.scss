.tabs-event-card {
  h4.news-card_title {
    font-size: 16px;
  }
  .news-card {
    background-color: var(--primary-white);
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);

    &_month {
      padding: 0;
      width: 94px;
      height: 82px;
    }

    &_cover {
      height: 236px;
    }

    &_cover-image {
      height: 236px;
    }

    &_title {
      margin-top: 16px;
      line-height: 1.5;
    }

    &_description {
      margin-top: 8px;
    }

    &_info {
      gap: 0;
    }

    &_type {
      display: flex;
    }

    &_type-inner,
    &_time {
      display: block;
    }

    &_actions {
      margin-top: 16px;
    }
  }

  .read-more {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 32px;
    padding-bottom: 12px;

    .expand img {
      display: flex;
      justify-content: center;
      width: 16px;
    }

    &:hover {
      cursor: pointer;
      text-decoration: underline;
    }

    button {
      cursor: pointer;
      border: none;
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.5;
      margin-right: 12px;
      background: transparent;
    }

    a {
      padding-right: 12px; //changed by adobe
    }
  }

  @media (max-width: 767px) {
    .news-card {
      &_title {
        min-height: 2.5rem;
      }

      &_type-inner {
        border-right: 0;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 21px;
        letter-spacing: 2px;
      }

      &_type {
        display: flex;
        flex-direction: column;
      }

      &_time {
        margin: 0;
      }

      &_description {
        margin-top: 16px;
        letter-spacing: normal;
      }
    }
  }
}
