.tcb-input-range {
  margin: 24px 0;
  position: relative;

  &_bar {
    position: absolute;
    width: 12px;
    height: 8px;
    background: var(--accent);
    border-radius: 5px;

    &-wrapper {
      height: 8px;
      background: #d3d3d3;
      border-radius: 5px;
      position: relative;
    }
  }

  &_thumb {
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: 12px;
    position: absolute;
    right: 0;
    transform: translate(12px, -8px);
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, .25);
    cursor: pointer;
  }

  &_labels {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-top: 12px;
    font-weight: 400;
    white-space: nowrap;
    color: #a2a2a2;
  }

  &_label-start,
  &_label-end {
    line-height: 1.43;
  }

  &_inline-value {
    position: absolute;
    top: 34px;
    font-size: 0.75rem;
    font-weight: 400;
    background: #ffffff;
    padding: 6px 15px;
    border-radius: 20px;
    left: 50%;
    transform: translate(-50%, 0);
    filter: drop-shadow(0 -1px 3px rgba(0, 0, 0, 0.14));
    white-space: nowrap;

    &::before {
      position: absolute;
      content: "";
      top: -8px;
      left: 50%;
      margin-left: -5px;
      border-color: transparent transparent #fff;
      border-style: solid;
      border-width: 0 5px 8px;
      width: 0;
      height: 0;
      filter: drop-shadow(0 -1px 3px rgba(0, 0, 0, 0.14));
      z-index: 1;
    }
  }

  &[disabled] {
    pointer-events: none;
  }

  &[disabled] &_bar {
    background: rgba(237, 28, 36, 0.3);
  }
}