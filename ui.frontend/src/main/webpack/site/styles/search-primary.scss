.search-primary-btn {
  height: 1.5rem;
  width: 1.5rem;
  margin-right: 30px;
}

.search-primary-modal {
  height: fit-content;
  max-width: 1312px;
  width: calc(100vw - 100vw / 11.25);
  margin-top: 112px;
}

.search-primary-container {
  border-radius: 1rem;
  min-height: 21.375rem;
  padding: 3rem;
  position: relative;
  flex: 1 1 auto;

  .input-container {
    position: relative;

    .icon {
      position: absolute;
      z-index: 1;
      padding: 1rem;

      &.close-icon {
        display: none;
        right: 0;
        padding: 0.8rem;
        cursor: pointer;

        .close-icon-wrapper {
          padding: 0.25rem;
          border-radius: 50%;
          height: 2rem;
          width: 2rem;

          &:hover {
            background: rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    img {
      width: 1.5rem;
      height: 1.5rem;
    }

    .search-input {
      width: 100%;
      padding: 0.75rem 3rem;
      border-radius: 0.5rem;
      background-color: #fff;
      display: inline-flex;
      border: 1px solid #e3e4e6;
      position: relative;
      outline: none;
      height: 3.5rem;
      font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    }
  }

  .search-box_title,
  .link_title {
    margin-top: 1.5rem;
    font-size: 1rem;
    font-style: normal;
    font-weight: 600;
    line-height: 1.5rem;
    color: #000;
  }

  .key-word-list {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;

    .key-word-container {
      position: relative;
      height: 2.5rem;
      border-radius: 27px;
      border: 1px solid #dedede;
      background: #dedede;
      cursor: pointer;
      overflow: hidden;

      &:hover {
        border: 1px solid #616161;
      }

      .key-word-item {
        font-weight: 400;
        font-size: 1rem;
        line-height: 22px;
        color: #616161;
        padding: 0.5rem 2.625rem 0.5rem 1rem;
        display: block;
        max-width: 15.625rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .close-icon {
        width: 0.875rem;
        height: 0.875rem;
        margin-left: 0.75rem;
        z-index: 1;
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        background-color: #a2a2a2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .list-card-info {
    .content-wrapper {
      padding-left: 0px;
      padding-right: 0px;
      margin: 0px;
    }
  }

  .card-list {
    margin-top: 1rem;
    width: 100%;
    height: 100%;
    align-items: center;
    overflow: hidden;

    .slick-slide {
      width: 17.875rem;
      height: 11.875rem;
      margin-right: 1.5rem;
    }

    .card-container {
      position: relative;

      @media (min-width: 933px) {
        width: 286px !important;
        height: 190px;
      }

      img {
        position: absolute;
        max-height: 100%;
        object-fit: cover;
        border-radius: 0.5rem;

        height: 100%;
        width: 100%;
      }

      .card-label {
        position: absolute;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 0 0 0.5rem 0.5rem;
        padding: 0.75rem 1rem;
        width: 100%;
        color: #fff;
        font-weight: 600;
        backdrop-filter: blur(5px);
      }
    }
  }
}

@media (max-width: 932px) {
  .search-primary-container {
    padding: 1rem;

    .slick-slide {
      width: 13.625rem !important;
      height: 9.25rem !important;
    }

    .key-word-list {
      gap: 0.75rem;
    }
  }

  .card-container {
    width: 210px !important;
    height: 140px;

    img {
      min-height: 100%;
      object-fit: cover;
      object-position: center center;
    }

    .card-label {
      bottom: 0px !important;
      padding: 0.75rem 1rem;
    }
  }
}
