.filterpanel {
  background-color: var(--primary-white);
}

.filterpanelcontainer {
  max-width: 1920px;
  margin: auto;
}

.filter-panel {
  min-height: 90px;
  &__content {
    background-color: #fff;
    width: 100%;
    padding: 12px 0;
  }

  &-container {
    padding: 48px 0;

    @include maxMd {
      padding: 32px 0;
    }
  }

  .filter-panel__content.no-margin {
    margin-bottom: 0;
  }

  &__container {
    display: flex;
    box-sizing: inherit;
    flex-grow: 0;
    flex-basis: 100%;
    align-items: center;
    flex-wrap: nowrap;
    overflow: hidden;
  }
  
  &__container h6 {
    margin-right: 16px;
    white-space: nowrap;
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
  }
  
  &__items {
    display: flex;
    align-items: center;
    flex-grow: 1;
    flex-wrap: nowrap;
    overflow-x:scroll;
    &::-webkit-scrollbar {
        display: none;
    }
  }
  
  .filter__item {
    padding: 8px;
  }
  
  &__button {
    justify-content: center;
    border: 1px solid #c5c5c5;
    border-radius: 27px;
    color: var(--secondary-grey-60);
    font-weight: 400;
    padding: 12px 15px;
    background-color: transparent;
    position: relative;
    display: inline-flex;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    text-decoration: none;
    transition: all 0.3s ease-in;
    align-items: center;
    grid-gap: 12px;
    gap: 12px;
    min-width: max-content;
    z-index: 1;
    line-height: 24px;
    margin: 0;
  }
  
  &__button.big-size {
    color: var(--body);
    font-size: 16px;
    padding: 12px 24px;
  }
  
  &__button.big-size.filter-selected,
  .filter-selected {
    background-color: #000;
    color: #fff;
  }
  
  @media (max-width: 767px) {
    &__button {
      max-width: 265px;
      display: block;
      white-space: normal;
    }
    &.mobile-not-show {
      display: none;
    }
  }
}
