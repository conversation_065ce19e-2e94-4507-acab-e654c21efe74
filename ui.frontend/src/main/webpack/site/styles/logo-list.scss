.logo-list {
  padding-left: 0;
  padding-right: 0;

  .logo-list-container {
    width: calc(100% + 16px);
    margin-left: -8px;
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    @media (max-width: 767px) {
      overflow: scroll;
      justify-content: space-between;
      flex-wrap: nowrap;
    }

    .logo {
      padding: 0.5rem;
      margin: 0;
      box-sizing: border-box;
      @media (min-width: 768px) {
        flex-grow: 0;
        max-width: 16.666667%;
        flex-basis: 16.666667%;
      }

      .logo-item {
        border-radius: 8px;
        height: 110px;
        position: relative;
        background-color: #fff;
        box-shadow: 0 0.0625em 0.0625em rgba(0, 0, 0, 0.25),
          0 0.125em 0.5em rgba(0, 0, 0, 0.25),
          inset 0 0 0 1px hsla(0, 0%, 100%, 0.1);

        @media (max-width: 767px) {
          width: 8.75rem;
        }

        .logo-item__span {
          box-sizing: border-box;
          display: block;
          overflow: hidden;
          width: initial;
          height: initial;
          background: none;
          opacity: 1;
          border: 0px;
          margin: 0px;
          padding: 0px;
          position: absolute;
          inset: 0px;

          .logo__img {
            position: absolute;
            inset: 0px;
            box-sizing: border-box;
            padding: 0px;
            border: none;
            margin: auto;
            display: block;
            width: 0px;
            height: 0px;
            min-width: 100%;
            max-width: 100%;
            min-height: 100%;
            max-height: 100%;
            object-fit: contain;
            object-position: center center;
          }
        }
      }
    }
  }
}
