/*  Hero Product */
.container.hero-product {
  position: relative;
}

//Change from Adobe
.tcb-hero-product .content-wrapper {
  position: relative;
}

.tcb-hero-product {
  position: relative;
  max-width: 1920px;
  margin:0 auto;
}

//Change from Adobe
.tcb-hero-product_background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  &-image {
    height: 100%;
    object-fit: cover;
    width: 100%;
  }
}
.hero-product {
  padding-top: 16px;

  max-width: 1312px;
  margin: auto;

  a {
    font-size: 14px;
  }
  .arrow-separator {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  @media (max-width: 768px) {
    position: relative;
    padding-left: 4.44%;
    padding-right: 4.44%;
  }
}

.hero-product__wrapper.right {
  flex-direction: row-reverse;
}

.hero-product__wrapper-image {
  max-width: 510px;
  margin-top: 159px;
  margin-left: 20px;
  img {
    width: 100%;
  }
}
.hero-product__wrapper-content {
  max-width: 450px;
  .wrapper-content-full-width & {
    max-width: unset;
  }
  padding: 40px 0;
  label {
    color: #ed1c24;
    display: block;
    margin-bottom: 0.5rem;

    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    letter-spacing: 2px;
    text-transform: uppercase;

    .section-font-color & {
      color: inherit;
    }
  }
  h1 {
    font-weight: 300;
    line-height: 1.25;
    font-size: 1.75rem;
  }

  .hero-product__wrapper-subtitle {
    font-weight: 700;
    font-size: 1.5rem;
    line-height: 1.5;
    margin: 40px 0;
  }
}
.padding-40 {
  .hero-product__wrapper-content {
    padding: 40px 0;
  }
}
.padding-60 {
  .hero-product__wrapper-content {
    padding: 60px 0;
  }
}

.padding-80 {
  .hero-product__wrapper-content {
    padding: 80px 0;
  }
}

.padding-60,
.padding-80 {
  .hero-product__wrapper-content {
    max-width: 35.5rem;
  }

  h3 {
    margin-top: 16px;
  }

  .qr-button {
    min-width: 240px;
  }
}
.hero-product__wrapper-title {
  padding: 16px;
  border-radius: 8px;
  background: var(--primary-white);
  color: var(--secondary-grey-60);
  h5 {
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.5;
  }
  p {
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.5;
  }
}
.hero-product__wrapper-button {
  margin-top: 24px;
  button {
    background: var(--primary-black);
    color: var(--primary-white);
    display: flex;
    padding: 16px 24px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.3s ease-in;
    justify-content: space-between;
    align-items: center;
    grid-gap: 12px;
    gap: 12px;
    width: 100%;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.5;

    .section-font-color & {
      color: inherit;
    }
  }
  &:hover {
    button {
      background-color: var(--gray-600);
      img {
        filter: brightness(0) invert(1);
      }
    }
    .wrapper-button__icon {
      > path {
        fill: #fff;
        transition: all 0.3s ease-in;
      }
    }
  }
  .forward-arrow {
    color: var(--primary-red);
  }
}
// Change by Adobe
.hero-product__wrapper-button .arrow-hide {
  display: none;
}
.hero-product__wrapper-item {
  //Change from Adobe
  z-index: 1;
  .background-trans {
    button {
      background-color: transparent;
      border: 1px solid #000;
      font-weight: 600;
    }
  }
}
@media (max-width: 991px) {
  .hero-product__container {
    min-height: 467px;
  }
}
@media (min-width: 768px) {
  .hero-product__wrapper {
    display: flex;
    align-items: center;
    width: 100%;
  }
  .hero-product__wrapper-item {
    flex-grow: 0;
    max-width: 50%;
    flex-basis: 50%;
    padding: 12px;
  }
  .hero-product__on-small-screen {
    display: none;
  }
}
@media (max-width: 767px) {
  .hero-product__container {
    min-height: 783px;
  }
  .hero-product__wrapper {
    margin: 0;
    width: 100%;
  }
  .hero-product__wrapper-item {
    display: flex;
    padding: 0;
  }
  .hero-product__wrapper-image {
    margin: 80px 0 10px;
  }
  .hero-product__on-large-screen {
    display: none;
  }
  .hero-product__wrapper-content {
    max-width: 100%;
    width: 100%;
  }
  .hero-product__wrapper-title {
    max-width: 510px;
  }
  .hero-product__wrapper-button {
    max-width: 510px;
  }
}
@media (max-width: 390px) {
  .hero-product__container {
    min-height: 694px;
  }
}
