/* ATM Map component */
.atm-map-component {
    background-color: white;

    .head-title h2 {
      margin-top: 64px;
      margin-bottom: 32px;
      font-size: 1.75rem;
      font-weight: 300;
      line-height: 1.25;
    }

    .atm-map-component__container {
        display: flex;
        flex-direction: column;

        .map-container {
            height: 640px;
            overflow: hidden;
            width: 100%;
            padding-left: 24px;
            @media screen and (max-width: 767px) { 
              padding-left: 0;
            }
          }

          .search-type {
            width: 100%;
            padding-top: 12px;
          }
          
           .search-type .items {
            display: flex;
            flex-direction: row;
          }
          
           .search-type .type {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 12px;
            font-size: 16px;
            line-height: 24px;
            cursor: pointer;
            border: none;
            background-color: transparent;
            @media screen and (max-width: 767px) { 
              padding: 0;
            }
          }
          
           .search-type .type:not(:first-child) {
            margin-left: 24px;
          }
          
           .search-type .icon {
            position: relative;
            height: 48px;
            width: 48px;
            background: #f5f6f8;
            box-shadow: 0 0 10px rgb(0 0 0 / 12%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 4px;
            padding: 8px;
          }
          
           .search-type .icon img {
            position: relative;
            width: 32px;
            height: 32px;
          }
          
           .type.clicked {
            color: #ed1c24;
          }
          
           .type .hide,
           .type.clicked img:not(.hide) {
            filter: grayscale(100%);
          }
          
           .type.clicked .hide {
            filter: unset;
          }
          
           .map-container iframe {
            width: 100%;
            height: 790px;
            margin-top: -150px;
            border: 0;
          }
          
           .show-list .cdm-list,
           .show-list .branch-list {
            display: none;
          }
          
           .show-list .item {
            padding: 16px;
            background-color: #fff;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            flex-direction: row;
            cursor: pointer;
            border: 1px solid #e3e4e6;
          }
          
           .show-list .item .image,
           .detail-item .above .atm div {
            height: 32px;
            width: 32px;
            margin: auto 24px auto 0;
            aspect-ratio: 1;
          }
          
           .show-list .item .content {
            display: flex;
            flex-direction: column;
            row-gap: 4px;
          }
          
           .show-list .image img {
            width: 100%;
            height: auto;
          }
          
           .show-list .item .content .type {
            color: #333;
            font-weight: 400;
            font-size: 0.875rem;
            line-height: 1.5;
            letter-spacing: 2px;
            text-transform: uppercase;
          }
          
           .show-list .item .content .name {
            line-height: 1.5;
            font-weight: 600;
            font-size: 1rem;
          }
          
           .show-list .item .content .address {
            color: #a2a2a2;
            font-weight: 100;
            font-size: 1rem;
            line-height: 1.5;
          }
          
           .detail-item {
            display: none;
            width: 100%;
            border: 1px solid #e3e4e6;
            border-radius: 8px;
          }
          
           .detail-item .above {
            display: flex;
            flex-direction: column;
            background-color: #f5f5f5;
            padding: 16px;
            border-radius: 8px 8px 0 0;
          }
          
           .detail-item .under {
            display: flex;
            flex-direction: column;
            background-color: white;
            padding: 16px;
            border-radius: 0 0 8px 8px;
          }
          
           .detail-item .above .atm {
            display: flex;
            flex-direction: row;
            align-items: center;
          }
          
           .detail-item .above .atm .name {
            font-weight: 600;
          }
          
           .detail-item .above .atm img {
            width: 100%;
          }
          
           .detail-item .above .comeback {
            font-size: 14px;
            line-height: 21px;
            padding-bottom: 16px;
            display: flex;
            align-items: center;
            cursor: pointer;
          }
          
           .detail-item .above .comeback img {
            width: 10px;
            height: 16px;
            margin-right: 12px;
          }
          
           .detail-item .under p:first-child {
            color: #a2a2a2;
            font-weight: 100;
            font-style: italic;
            font-size: 1rem;
            line-height: 1.5;
          }
          
           .detail-item .under p:nth-child(2) {
            color: var(--gray-600);
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5;
          }
          
           .detail-item .under .direction {
            color: #1d6d30;
            text-decoration: underline;
            cursor: pointer;
            margin-top: 4px;
            font-size: 16px;
            line-height: 24px;
          }
          
           .detail-item .under .branch-btn .cta-button{
            margin-top: 8px;
            // background-color: transparent;
            // border: 1px solid #000;
            // padding: 16px 24px;
            border-radius: 8px;
            outline: none;
            cursor: pointer;
            white-space: nowrap;
            text-align: left;
            font-weight: 600;
            font-size: 1rem;
            line-height: 1.5;
            transition: all 0.3s ease-in;
            font-family: SF Pro Display;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
          }
          
           .detail-item .under .branch-btn:hover {
            .cta-button {
              background-color: black;
              color: white;
            }
          }
          
           .detail-item .under .branch-btn:hover path {
            fill: white;
          }
      }
     
      .atm-map_container {
        display: flex;
        position: relative;
      }
      
      .search-container {
        max-width: 32%;
        width: 100%;
        min-width: 421px;
      }
      
      .search-input {
        padding-bottom: 24px;
      }
      
      .search-input .search {
        width: 100%;
        padding: 12px 16px;
        border-radius: 8px;
        background-color: #fff;
        display: inline-flex;
        border: 1px solid #e3e4e6;
        position: relative;
        outline: none;
      }
      
      .search-input .search.focus {
        outline: 1px solid #e3e4e6;
      }
      
      .search-input .search-input_icon {
        width: 24px;
        height: 24px;
        z-index: 2;
      }
      
      .search-input input {
        width: 100%;
        padding: 1.5px 12px 2.5px 56px;//changed by Abobe
        border: none;
        outline: none;
        position: absolute;
        left: 0;
      }
      
      .search-input {
        position: relative;
      }
      
      #atm-map-autocomplete {
        position: absolute;
      }
      
      #atm-map-autocomplete .ui-autocomplete {
        max-height: 300px;
        overflow-x: hidden;
        overflow-y: auto;
        border-radius: 4px;
        background-color: white;
        list-style: none;
        margin-top: 23px;
        z-index: 10;
        border: 1px solid #e3e4e6;
    
        
        padding-inline-start: 0;
        
      }
      
      #atm-map-autocomplete .ui-menu-item {
        min-height: auto;
        width: auto;
        padding: 8px 16px;
        cursor: pointer;
      }
      
      #atm-map-autocomplete .ui-menu-item:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
      
      .search-filter {
        display: flex;
        width: calc(100% + 18px);
        margin: -9px;
        flex-wrap: wrap;
        margin-bottom: 0;
      }
      
      .search-filter .filter {
        padding: 9px;//changed by Abobe
        flex-grow: 0;
        max-width: 50%;
        flex-basis: 50%;
        position: relative;
      }
      
      .filter .option {
        border: 1px solid #e3e4e6;
        border-radius: 8px;
        padding: 12px 16px;
        position: relative;
        z-index: 1;
        height: 48px;
        &:hover {
            font-weight: unset;
        }
        .dropdown-backdrop {
          display: none;
        }
      }
      
      .filter input {
        pointer-events: none;
        border: none;
        background-color: transparent;
        font-size: 16px;
        width: 100%;
        text-overflow: ellipsis;
        max-width: 150px;
        padding-right: 10px;
        @media (max-width: 767px) { 
          padding-right: 20px;
        }
      }
      
      .filter {
        svg, img {
          color: #ed1b24;
          position: absolute;
          top: 43%;
          right: 20px;
          width: 10px;
          height: 10px;
          border-top: 2.5px solid #ed1b24;
          border-left: 2.5px solid #ed1b24;
          transform: translateY(-50%) rotate(225deg);
          z-index: -1;
        }
        img svg * {
          display: none;
        }
      }
      
      .filter path {
        display: none;
      }
      
      .filter.disable .option {
        background-color: #d9d9d9;
        pointer-events: none;
      }
      
      .filter.disable .option {
        svg, img {
          border-color: #616161;
        }
      }
      
      .filter .select-options {
        opacity: 0;
        z-index: -1;
        min-width: 197px;
        max-width: calc(100% + 17px);
        max-height: 400px;
        min-height: 16px;
        position: absolute;
        overflow-x: hidden;
        overflow-y: auto;
        box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
        transition: opacity 337ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 224ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
        border-radius: 4px;
        background-color: white;
        top: -50px;

        @media (max-width: 414px) {
          min-width: unset;
        }
      }
      
      .filter .option.showed + .select-options {
        opacity: 1;
        z-index: 10;
      }
      
      .filter .option.showed {
        svg, img {
          border-color: #616161;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
        }

        .dropdown-backdrop {
          display: block;
        }
      }
      
      .filter .select-options ul {
        padding-top: 8px;
        padding-bottom: 8px;
        list-style: none;
        padding-inline-start: 0;
        padding-right: 17px;
        width: calc(100% + 17px);
      }
      
      .filter .select-options li {
        min-height: auto;
        width: auto;
        font-family: "Roboto", "Helvetica", "Arial", sans-serif;
        line-height: 1.5;
        letter-spacing: 0.00938em;
        padding: 6px 16px;
        transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
        cursor: pointer;
      }
      
      .filter .select-options li.selected {
        background-color: rgba(0, 0, 0, 0.08);
      }
      
      .filter .select-options li:hover {
        background-color: rgba(0, 0, 0, 0.04);
        font-weight: 600;
      }
      
      .search-result {
        padding-top: 24px;
        position: relative;
      }
      
      .search-result::before {
        content: "";
        width: 100%;
        height: 1px;
        background: #a2a2a2;
        position: absolute;
      }
      
      .search-result .result-message {
        padding-top: 24px;
        color: #616161;
        font-size: 16px;
        line-height: 24px;
      }
      
      .results {
        margin-top: 16px;
      }
      
      .results .show-list {
        height: 332px;
        overflow: auto;
      }      
     
      .phone {
        display: none;
      }
      .expand_cards_phone {
        display: none;
      }
  .img-icon {
    width: 24px;
    height: 24px;
  }
}
  

  @media screen and (max-width: 767px) { 
      
  /* atm map cmp */
    .atm-map-component .atm-map_container {
        flex-direction: column;
    }

    .atm-map-component .atm-map-component__container .map-container {
      height: 336px;
      padding-left: 0;
      margin-top: 24px;
      iframe {
        height: 500px;
      }
    }

    .atm-map-component .search-container {
        max-width: unset;
        min-width: unset;
    }

    .atm-map-component__container .search-type .type {
        padding: 0;
    }

    .atm-map-component .search-filter .filter {
        padding: 8px;
    }

    .branches-atm-locate {
        margin-bottom: 48px;
      }
  }

  .ui-helper-hidden-accessible {
      visibility: hidden
  }

  
/* Branches Option */
.branches-option-container {
  background-color: #fff;
  padding-top: 64px;
}

.branches-option-wrapper {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.branches-option-root {
  width: calc(100% + 24px);
  margin: -12px;
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
}

.branches-root-item {
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
}

.branches-root-item > h2 {
  font-size: 1.75rem;
  font-weight: 300;
  line-height: 1.25;
}

.branches-atm-locate {
  margin-top: 32px;
  margin-bottom: 32px;
}

.branches-radio-item {
  justify-content: flex-start;
  flex-direction: row;
  display: flex;
  grid-column-gap: 16px;
  column-gap: 16px;
}

.branches-radio-container {
  max-width: 420px;
  display: block;
  position: relative;
  width: 100%;
  height: 48px;
  cursor: pointer;
  user-select: none;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.5;
  letter-spacing: 2px;
  text-transform: uppercase;
  span {
    display: flex;
  }
}

.branches-radio-container > input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.branches-radio-container > input ~ .branches-radio-cycle {
  position: absolute;
  width: 24px;
  height: 24px;
  border: 1px solid #e3e4e6;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: #fff;
  z-index: 1;
}

.branches-radio-container > input:checked ~ .branches-radio-cycle {
  border: 1px solid #ed1b24;
}

.branches-radio-container > input:checked ~ .branches-radio-cycle::before {
  content: "";
  width: 16px;
  height: 16px;
  background-color: #ed1b24;
  position: absolute;
  top: 3px;
  left: 3px;
  border-radius: 50%;
}

.branches-radio-container > input ~ .branches-radio-checkmark {
  border-radius: 8px;
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  grid-gap: 24px;
  gap: 24px;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: normal;
  font-weight: 400;
  text-align: center;
  text-transform: capitalize;
  padding: 12px 24px;
  background-color: #f5f6f8;
  color: var(--gray-600);
  border: 1px solid #e3e4e6;
}

.branches-radio-container > input:checked ~ .branches-radio-checkmark {
  color: #000;
  background-color: transparent;
  border: 1px solid #212121;
}
.branches-radio-container .branches-radio-checkmark picture{
  height: 24px;
}

.branches-radio-checkmark > span:nth-child(1) {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-right: 10px;
}

.branches-radio-checkmark > h3:nth-child(1) {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-right: 10px;
  font-size: 16px;
  font-weight: 400;
}

.branches-radio-checkmark > span:nth-child(2) {
  color: #000;
}

.branches-root-item > div:last-child {
  margin-right: 8px;
  position: relative;
  display: block;
}

.branches-root-item > div:last-child > div {
  overflow: hidden;
  margin: 0;
  padding: 0;
  transform: translateZ(0);
  position: relative;
  display: block;
}

.branches-root-item > div:last-child > div > div {
  opacity: 1;
  transform: translate3d(0px, 0px, 0px);
  max-width: 435px;
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.branches-root-item > div:last-child > div > div > div {
  outline: none;
  margin-right: 8px;
  display: block;
  float: left;
  height: 100%;
  min-height: 1px;
}

.branches-option-button {
  width: 100%;
  display: inline-block;
  color: #fff;
  background-color: #212121;
  height: 48px;
  padding: 12px 24px;
  border-radius: 27px;
  border: 1px solid #c5c5c5;
  cursor: pointer;
  font-size: 16px;
  line-height: 24px;
}

/* Branches Option Media */
@media (min-width: 390px) {
  .branches-root-item > div:last-child > div > div {
    max-width: 347px;
  }
}

@media (min-width: 576px) {
  .branches-root-item > div:last-child > div > div {
    max-width: 517px;
  }
}

@media (min-width: 768px) {
  .branches-option-wrapper {
    padding-left: 12px;
    padding-right: 12px;
  }

  .branches-root-item > div:last-child > div > div {
    max-width: 231px;
  }
}

@media (min-width: 918px) {
  .branches-root-item > div:last-child > div > div {
    max-width: 276px;
  }
}

@media (min-width: 992px) {
  .branches-root-item > div:last-child > div > div {
    min-width: 299px;
  }

  .branches-root-item > div:last-child > div > div {
    max-width: 299px;
  }
}

@media (min-width: 1200px) {
  .branches-root-item > div:last-child > div > div {
    max-width: 355px;
  }
}

@media (min-width: 1440px) {
  .branches-root-item > div:last-child > div > div {
    max-width: 435px;
  }
}

@media (max-width: 767px) {
  .atm-map-component {
    padding: unset;
  }
  
  .branches-option-container {
    padding-top: 48px;
  }

  .branches-atm-locate {
    margin-bottom: 48px;
  }

  .branches-radio-item {
    grid-gap: 16px;
    gap: 16px;
    flex-direction: column;
  }
}

/* End of Branches Option Media */
/* End of Branches Option */
