.banner-container {
  .slick-dots {
    margin-top: -0.5rem !important;
  }
  .cardslider-carousel-slickwrapper {
    min-width: 100% !important;
  }
  .card-slider-wrapper {
    margin-bottom: 0;
  }
  .slick-slide {
    .tcb-full-width-mobile,
    .cardslider-carousel-slickitem {
      display: block !important;
    }
  }
  @include md {
    .slick-list {
      margin-left: -0.875rem !important;
      width: calc(100% + 0.875rem) !important;
    }
    .slick-slide {
      padding: 0 !important;
      margin-left: 0.875rem !important;
    }
  }
  @include maxSm {
    .slick-slide {
      padding: 0 !important;
      margin: 0 1rem !important;
    }
    .slick-list {
      width: calc(100% + 1rem) !important;
      margin: 0 !important;
    }
    .tcb-sectionContainer {
      width: 100%;
    }
    .mobile-unslide-container .mobile-unslide-items .cardslider-carousel-slickitem > div {
      grid-gap: 0;
    }
  }
}
