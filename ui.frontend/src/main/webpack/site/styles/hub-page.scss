.group-promotion-detail-component{
  &:not(.result-empty-list) {
    padding-top: 3rem;
    border-top: 0.375rem solid var(--secondary-mid-grey-40);
    padding-bottom: 2.625rem;
  }
  .promotion-hub_hub-title {
    margin-bottom: 1.5rem;
  }
  &.recommend-category{
    padding-bottom: 0;
  }
}

.promotion-need-listing-title {
  padding-bottom: 2rem;
  @include xs {
    padding-bottom: 1rem;
  }
}

.promotion-hub_tab-hub-header {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(8, 1fr);
  padding-bottom: 3rem;

  @include xsSemi {
    overflow: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  @include maxMd {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
  }

  @include xs {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }
  &.promotion-detail{
    .promotion-hub_tab-hub-item{
      width: 7.5rem;
      height: 7.5rem;
      @include xs{
        width: auto;
        height: auto;
      }
    }
  }

  .promotion-hub_tab-hub-item {
    background-color: var(--secondary-mid-grey-40);
    text-align: center;
    padding: 1.313rem;
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 0.625rem;
    @include xs {
      padding: 1.313rem 1rem;
    }
    @include xsSemi {
      width: 6.5rem;
      height: 7.5rem;
    }

    &:hover {
      background-color: var(--secondary-light-grey-80);
    }
    img{
      width: 2rem;
      height: 2rem;
    }
  }
}

.promotion-hub_hub-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.promotion-product-listing__see-detail-btn {
  &:not(:last-child) {
    margin-bottom: 3rem;
  }
  a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-top: 1.5rem;
    cursor: pointer;
    gap: 0.5rem;

    @include xs {
      margin-top: 1rem;
    }

    span {
      font-weight: bold;
    }
  }
}