/* Alert Banner component */
.announcement {
  position: relative;
  min-height: unset;
  .announcement__container {
    position: relative;
    .popup {
      display: flex;
      position: fixed;
      z-index: -1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      background-color: transparent;
      transition: all .3s ease-in-out;
      .popup-content {
        position: relative;
        margin: auto;
        //-webkit-animation-name: animatetop;
        //-webkit-animation-duration: 0.3s;
        //animation-name: animatetop;
        //animation-duration: 0.3s;
        transition: all .3s ease-in-out;
        transform: translateY(-25%);
        opacity: 0;
        display: flex;
        flex-direction: column;
        background-color: white;
        border-radius: 0.3rem;
        width: 100%;
        .head {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          padding: 16px;
          border-bottom: 1px solid rgb(233, 236, 239);
          line-height: 1.5;
          font-size: 1.25rem;
          i {
            color: grey;
            opacity: 0.8;
            &:hover {
              opacity: 1;
              cursor: pointer;
            }
          }
          img {
            cursor: pointer;
            opacity: 0.8;
            filter: brightness(0.2);
            &:hover {
              opacity: 1;
              cursor: pointer;
              filter: brightness(0);
            }
          }
        }
        .video {
          padding: 16px;
          iframe {
            width: 100%;
          }
        }
      }
      &.open {
        z-index: 10;
        background-color: rgba(0, 0, 0, 0.6);
        .popup-content {
          display: flex;
          opacity: 1;
          transform: translate(0);
        }
      }
    }
  }
  .background {
    width: 100%;
    height: 100%;
    position: absolute;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .content {
    display: flex;
    flex-direction: row;
    color: white;
    padding: 32px 0;
    .main-content {
      padding-right: 12px;
      @media (max-width:991px){
        padding-right: 0;
      }
      h2 {
        margin: 24px 24px 16px 0;
        font-size: 1.5rem;
        font-weight: 600;
      }
      .title {
        display: flex;
        align-items: center;
        flex-direction: row;
        h3 {
          margin: 0;
          font-size: 1.5rem;
        }
        img {
          margin-right: 16px;
          width: 19px;
          height: 20px;
        }
      }
      p {
        font-size: 1rem;
        margin: 16px 0;
      }
    }
    .btn--wrapper {
      display: flex;
      align-items: center;
      padding-left: 12px;
      @media (max-width:991px){
        padding-left: 0;
        margin-top: 36px;
      }
    }
    .button {
      display: flex;
      align-items: center;
      flex-direction: column;
      padding: 10px;
      .cta-button {
        width: 100%;
        max-width: 328px;
        picture {
          width: 24px;
          height: 24px;
        }
        .cmp-button__icon {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  /* @media (max-width: ) */
// /* Add Animation */
// @-webkit-keyframes animatetop {
//   from {
//     top: -100px;
//     opacity: 0;
//   }

//   to {
//     top: 0;
//     opacity: 1;
//   }
// }

// @keyframes animatetop {
//   from {
//     top: -100px;
//     opacity: 0;
//   }

//   to {
//     top: 0;
//     opacity: 1;
//   }
// }


@media (min-width: 992px) {
  .content .main-content {
    flex-grow: 0;
    max-width: 66.666667%;
    flex-basis: 66.666667%;
  }
  .content .button {
    flex-grow: 0;
    max-width: 33.333333%;
    flex-basis: 33.333333%;
  }
}

@media (min-width: 1200px) {
  .content .main-content {
    flex-grow: 0;
    max-width: 75%;
    flex-basis: 75%;
  }
  .content .button {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
  }
}

@media (max-width: 991px) {
  .announcement__container .content {
    flex-direction: column;
    .btn {
      padding: 0;
    }
  }

  .announcement__container .content .main-content {
    width: 100%;
  }

  .announcement__container .content .btn {
    width: 100%;
    justify-content: flex-start;
    margin-top: 36px;
  }

  .announcement__container .popup .popup-content .video {
    height: 300px;
  }

  .announcement__container .popup .popup-content .video iframe {
    height: 100%;
    overflow: auto;
  }

  .announcement__container .popup .popup-content {
    max-width: 500px;
  }
}

@media (max-width: 767px) {
  .announcement__container .content .btn button {
    max-width: unset;
  }
}

@media (min-width: 992px) {
  .announcement__container {
    .content {
      align-items: center;
    }
    .popup .popup-content {
      max-width: 800px;
    }
  }
}

@media (max-width: 517px) {
  .announcement__container .popup .popup-content {
    margin: auto 0.5rem;
    max-height: 300px;
  }
}
}


/* End of Alert Banner component */