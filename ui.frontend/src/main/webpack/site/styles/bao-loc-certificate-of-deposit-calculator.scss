.bao-loc-certificate-of-deposit-calculator {
  &__container {
    width: 100%;
    margin: 0 auto;

    @media (min-width: 320px) and (max-width: 1199.95px) {
      display: flex;
    }

    @media (min-width: 1200px) {
      display: flex;
    }

    @media (min-width: 1440px) {
      max-width: 1440px;
    }

    &--wrapper {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      box-sizing: border-box;
      width: calc(100% + 24px);

      .certificate-of-deposit-calculator {
        padding: 0.75rem 0;
        display: flex;
        flex-wrap: wrap;
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;

        @media (max-width: 992px) {
          flex-direction: column;
          padding: unset;
          margin-top: 1.5rem;
        }

        &__input {
          width: 59%;
          display: inline-block;
          padding: 1.5rem;
          border-radius: 0.5rem 0 0 0.5rem;
          background-color: white;

          @media (max-width: 992px) {
            width: 100%;
            border-radius: 0.5rem 0.5rem 0 0;
            padding: 2rem 1rem;
          }

          &--wrapper {
            display: flex;
            flex-wrap: wrap;
            width: 100%;

            .calculator {
              flex-grow: 0;

              &--label {
                margin: auto;
                padding: 0.5rem 0.25rem;
                max-width: calc(100% * 5 / 12);
                flex-basis: calc(100% * 5 / 12);

                @media (max-width: 576px) {
                  padding: 0.25rem;
                  max-width: 100%;
                  flex-basis: 100%;
                }

                h3 {
                  display: inline-flex;
                  align-items: center;
                  font-size: 1rem;
                  font-weight: 700;
                  line-height: 1.5rem;
                }
              }

              &--input {
                padding: 0.5rem 0.25rem;
                max-width: calc(100% * 7 / 12);
                flex-basis: calc(100% * 7 / 12);
                position: relative;

                @media (max-width: 576px) {
                  padding: 0.25rem;
                  max-width: 100%;
                  flex-basis: 100%;
                }

                .roi-amount-input {
                  border: 0;
                  margin: 0;
                  padding: 0;
                  display: inline-flex;
                  min-width: 0;
                  width: 100%;
                  flex-direction: column;
                  vertical-align: top;

                  &__wrapper {
                    border-radius: 0.5rem;
                    border: 1px solid #e3e4e6;
                    padding-right: 0.875rem;
                    color: rgba(0, 0, 0, 0.87);
                    cursor: text;
                    display: inline-flex;
                    font-size: 1rem;
                    box-sizing: border-box;
                    align-items: center;
                    font-weight: 400;
                    line-height: 1.1876em;
                    letter-spacing: 0.00938em;
                    width: 100%;

                    &:has(input:focus) {
                      border-color: #0a84ff;
                    }

                    &--input-body {
                      font: inherit;
                      color: currentColor;
                      width: 100%;
                      border: 0;
                      height: 1.1876em;
                      margin: 0;
                      display: block;
                      padding: 1rem 0.875rem;
                      min-width: 0;
                      background: none;
                      box-sizing: content-box;
                      animation-name: mui-auto-fill-cancel;
                      letter-spacing: inherit;
                      animation-duration: 10ms;
                      outline: 0;
                      font-family: "Roboto", "Helvetica", "Arial", sans-serif;
                    }

                    &--currency-label {
                      height: 0.01em;
                      display: flex;
                      max-height: 2em;
                      align-items: center;
                      white-space: nowrap;
                      color: rgba(0, 0, 0, 0.54);
                      font-family: "Roboto", "Helvetica", "Arial", sans-serif;
                    }
                  }
                }

                .calendar_real_estate {
                  align-items: center;
                  border: 0;
                  border-radius: 0.5rem;
                  color: rgba(0, 0, 0, 0.87);
                  cursor: text;
                  display: inline-flex;
                  font-weight: 400;
                  letter-spacing: 0.00938em;
                  line-height: 1.1876em;
                  margin: 0;
                  min-width: 0;
                  padding: 0;
                  position: relative;
                  vertical-align: top;
                  width: 100%;

                  input.date-time-wrapper__input {
                    animation-duration: 10ms;
                    background: none;
                    border-color: lightgray;
                    border-radius: inherit;
                    border-style: solid;
                    border-width: 1px;
                    box-sizing: content-box;
                    color: currentColor;
                    display: block;
                    font: inherit;
                    height: 1.25rem;
                    letter-spacing: inherit;
                    margin: 0;
                    min-width: 0;
                    padding: 1rem 0.875rem;
                    width: 100%;
                    outline: 0;
                    font-family: "Roboto", "Helvetica", "Arial", sans-serif;

                    &:focus {
                      border-color: #0a84ff;
                    }

                    &:hover {
                      border-color: #000000cc;
                    }
                  }

                  .date-time-wrapper__input-extra {
                    p.loan-realestate__icon {
                      flex: 0 0 auto;
                      color: rgba(0, 0, 0, 0.54);
                      padding: 0.75rem;
                      overflow: visible;
                      font-size: 1.5rem;
                      text-align: center;
                      transition: background-color 150ms
                        cubic-bezier(0.4, 0, 0.2, 1) 0ms;
                      border-radius: 50%;
                      width: 3rem;
                      height: 3rem;
                    }

                    &:hover p.loan-realestate__icon {
                      background-color: rgba(0, 0, 0, 0.04);
                    }

                    &.calendar__input-field {
                      outline: 0.1em solid lightskyblue;
                    }
                  }

                  .loan-realestate__input:hover input {
                    border: 1px solid black;
                  }
                }

                .calendar-popup {
                  left: 0;
                }

                .interest-rate,
                .invest-time {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  padding: 1rem;
                  background-color: #d9d9d9;
                  border-radius: 0.5rem;
                  border: 1px solid #e3e4e6;
                }
              }
            }
          }
        }

        &__info {
          width: 41%;
          position: relative;
          display: inline-block;
          padding: 2rem 0;

          @media (max-width: 992px) {
            width: 100%;
          }

          &--background {
            box-sizing: border-box;
            display: block;
            overflow: hidden;
            width: initial;
            height: initial;
            background: none;
            opacity: 1;
            border-radius: 0 0.5rem 0.5rem 0;
            margin: 0;
            padding: 0;
            inset: 0;
            position: absolute;

            @media (max-width: 992px) {
              width: 100%;
              border-radius: 0 0 0.5rem 0.5rem;
            }

            img {
              position: absolute;
              inset: 0;
              box-sizing: border-box;
              padding: 0;
              border: none;
              margin: auto;
              display: block;
              width: 0;
              height: 0;
              min-width: 100%;
              max-width: 100%;
              min-height: 100%;
              max-height: 100%;
              object-fit: cover;
              object-position: center center;
            }
          }

          &--content {
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 0 2.5rem;

            @media (max-width: 576px) {
              padding: 0 1rem;
            }

            .content {
              &__icon {
                position: relative;

                img {
                  width: 5rem !important;
                  height: 3.438rem !important;
                }
              }

              &__profit,
              &__receive {
                color: white;
                display: inline-block;

                p {
                  color: #e3e4e6;
                  margin-bottom: 0.5rem;
                  margin-top: 1.5rem;
                  font-weight: 400;
                  font-style: normal;
                  line-height: 1.5rem;
                }

                h3 {
                  overflow-wrap: break-word;
                  font-size: 1.5rem;
                  line-height: 1.5;
                }
              }
            }
          }
        }
      }
    }
  }
}
