/* Insurance Calculation component */
.insurance-calculation {
  &__content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    box-sizing: inherit;
    justify-content: center;
  }

  &__panel {
    display: flex;
    flex-wrap: wrap;
    flex-grow: 0;
    max-width: 100%;
    flex-basis: 100%;
  }


  .panel-inputs {
    background-color: var(--primary-white);
    width: 60%;
    display: inline-block;
    padding: 24px;
    border-radius: 8px 0 0 8px;
  }

  .input-items {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .item__label {
    margin: auto;
    flex-grow: 0;
    max-width: 41.666667%;
    flex-basis: 41.666667%;
    box-sizing: inherit;
    padding: 8px;
  }

  .label__text {
    display: inline-flex;
    font-weight: 600;
    line-height: 1.5;
    align-items: center;
  }

  .label__footnote {
    color: var(--gray-600);
    max-width: 190px;
    font-weight: 400;
    font-style: italic;
    line-height: 24px;
  }

  .item__input-fields {
    position: relative;
    flex-grow: 0;
    max-width: 58.333333%;
    flex-basis: 58.333333%;
    padding: 8px;

    .tcb-input--save-calc {
      padding: 20px 0;

      .tcb-input-range_bar-wrapper {
        cursor: pointer;

        .tcb-input-range_bar {
          .tcb-input-range_thumb {

            .tcb-input-range_inline-value {
              transform: scale(1) translateY(-10px);
              top: 44px;
              left: calc(-50% - 5px);
              z-index: 1;
              position: absolute;
              font-size: 0.75rem;
              transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
              line-height: 1.2;
              letter-spacing: 0.01071em;
              transform-origin: bottom center;
              padding: 7.8px 21.2px;
            }
          }
        }
      }

      .tcb-input-range_labels {
        margin-top: 2px;
      }
    }
  }

  .input-fields__date-time-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: inherit;
  }

  .date-time-wrapper__input-field {
    width: 100%;
    border: 0;
    margin: 0;
    display: inline-flex;
    padding: 0;
    position: relative;
    min-width: 0;
    vertical-align: top;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    align-items: center;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    input {
      width: 100%;
      height: 19px;
      margin: 0;
      display: block;
      padding: 18.5px 14px;
      min-width: 0;
      background: none;
      box-sizing: content-box;
      letter-spacing: inherit;
      animation-duration: 10ms;
      -webkit-tap-highlight-color: transparent;
      border-color: var(--secondary-light-grey-100);
      border-style: solid;
      border-width: 1px;
      border-radius: 8px;
    }

    &:focus {
      outline: 0.1em solid lightskyblue;
    }
  }

  .date-time-wrapper__separator {
    margin: 0 12px;
  }

  .input-fields__error-msg {
    color: var(--primary-red);
    font-weight: 400;
    line-height: 24px;
  }

  .input-fields__drop-down {
    width: 120px;
    background-color: var(--primary-white);
    box-sizing: inherit;
  }

  .drop-down__container {
    width: 100%;
    border: 0;
    margin: 0;
    display: inline-flex;
    padding: 0;
    position: relative;
    min-width: 0;
    flex-direction: column;
    vertical-align: top;
    box-sizing: inherit;
  }

  .drop-down__controls {
    width: 100%;
    position: relative;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    display: inline-flex;
    font-size: 1rem;
    box-sizing: border-box;
    align-items: center;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    form {
      width: 100%;
    }

    input {
      left: 0;
      width: 100%;
      bottom: 0;
      opacity: 0;
      position: absolute;
      pointer-events: none;
    }
  }

  .drop-down__select {
    padding: 16px;
    border: 1px solid var(--secondary-light-grey-100);
    border-radius: 8px;
    background-color: var(--primary-white);
    position: relative;
    height: 56px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    overflow: hidden;
    min-height: 1.1876em;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    min-width: 16px;
    user-select: none;
    font: inherit;
    color: currentColor;
    width: 100%;
    background: none;
    letter-spacing: inherit;
    animation-duration: 10ms;
    -webkit-tap-highlight-color: transparent;
    appearance: none;

    span {
      margin-right: 14px;
      white-space: nowrap;
    }
  }

  .label__text .icon-info {
    height: 16px;
    width: 16px;
    position: relative;
    margin-left: 12px;

    img:not(.cmp-button__icon) {
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center center;
    }
  }

  .input-field__currency-field {
    width: 100%;
    border: 0;
    margin: 0;
    display: inline-flex;
    padding: 0;
    position: relative;
    min-width: 0;
    vertical-align: top;
    border-radius: 8px;
    color: rgba(0, 0, 0, 0.87);
    cursor: text;
    align-items: center;
    font-weight: 400;
    line-height: 1.1876em;
    letter-spacing: 0.00938em;

    input {
      font: inherit;
      color: currentColor;
      width: 100%;
      height: 19px;
      margin: 0;
      display: block;
      padding: 18.5px 14px;
      min-width: 0;
      background: none;
      box-sizing: content-box;
      letter-spacing: inherit;
      animation-duration: 10ms;
      -webkit-tap-highlight-color: transparent;
      border-color: var(--secondary-light-grey-100);
      border-style: solid;
      border-width: 1px;
      border-radius: inherit;
    }
  }

  .date-time-wrapper__input-extra {
    height: 0.01em;
    display: flex;
    max-height: 2em;
    align-items: center;
    white-space: nowrap;
    position: absolute;
    right: 14px;
  }

  .currency__place-holder {
    color: rgba(0, 0, 0, 0.54);
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.00938em;
  }

  .panel-info {
    position: relative;
    display: inline-block;
    padding: 40px 0;
    width: 40%;

    span {
      display: block;
    }

    img:not(.cmp-button__icon) {
      position: absolute;
      inset: 0px;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: cover;
      border-radius: 0 8px 8px 0;
    }
  }

  .panel-info__content {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    padding: 0 40px;
  }

  .panel-info__content-text {
    display: flex;
  }

  .panel-info__content-button {
    color: var(--primary-white);
    height: auto;
    margin-top: 32px;

    a {
      background-color: var(--primary-white);
      color: var(--primary-black);
      border: 1px solid #404040;
      justify-content: space-between;
      max-width: 328px;
      max-height: 56px;
      /* width: 100%; --> add class width-100 if need*/
      height: 100%;
      border-radius: 8px;
      padding: 16px 24px;
      line-height: 24px;
      display: inline-flex;
      align-items: center;
      transition: all 0.3s ease-in-out;
      text-decoration: none;
      cursor: pointer;
      font-weight: 600;
    }
  }

  .content-button__icon-arrow {
    height: 16px;
    width: 16px;
    position: relative;
    margin-left: 12px;
  }

  .info-content-text__icon {
    position: relative;
  }

  .info-content-text__icon span {
    width: 80px;
    height: 55px;
    position: relative;
  }

  .info-content-text__icon.img-medium span {
    width: 99px;
    height: 69px;
  }

  .mt-auto {
    margin-top: auto;
  }

  .info-content-text__label {
    color: var(--primary-white);
    display: inline-block;
    flex: 1;

    > div {
      color: var(--secondary-light-grey-100);
      margin-bottom: 8px;
      margin-top: 24px;
      font-weight: 300;
    }

    > h3 {
      max-width: 95%;
      overflow-wrap: break-word;
    }
  }


  .info-content-text__label p {
    color: var(--secondary-light-grey-100);
    font-weight: 300;
    margin-bottom: 8px;
  }

  .info-content-text__label .info-title {
    font-weight: 300;
    margin-bottom: 8px;
    margin-top: 24px;
  }

  .info-content-text__label h3 {
    overflow-wrap: anywhere;
    font-size: 24px;
    line-height: 36px;
    margin: 0;
    padding: 0;
  }
}

.insurance-calculation__content input:focus,
.drop-down__select:focus {
  outline: 1px solid #0a84ff;
}

.insurance-calculation input[type="range"] {
  outline: none !important;
}

.insurance-calculation input::-webkit-outer-spin-button,
.insurance-calculation input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

.insurance-calculation input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}

.select-option {
  position: absolute;
  display: none;
  width: 100%;
  background: white;
  padding-top: 8px;
  padding-bottom: 8px;
  z-index: 2;
  border-radius: 4px;
  box-shadow: 0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%);
}

.select-option.opened {
  display: block;
}

.option {
  line-height: 1.5;
  letter-spacing: 0.00938em;
  padding: 6px 16px;
  cursor: pointer;
}

.option.selected {
  background-color: rgba(0, 0, 0, 0.08);
}

.option:hover {
  font-weight: 600;
}

.insurance-calculation .label__text .icon-info:hover {

  .insurance-calculation .info-content-text__label > div {
    height: 37px;
    display: flex;
    align-items: end;
  }
}


@media (max-width: 991px) {
  .insurance-calculation .panel-inputs {
    width: 100%;
    border-radius: 8px 8px 0 0;
  }

  .insurance-calculation .panel-info {
    width: 100%;
  }

  .insurance-calculation .panel-info img:not(.cmp-button__icon) {
    border-radius: 0 0 8px 8px;
  }
  .insurance-calculation .info-content-text__label > div {
    height: auto;
  }
}

@media (max-width: 767px) {
  .insurance-calculation {
    .label__text {
      position: relative;

      .icon-info {
        position: static;
      }
    }
  }

  .insurance-calculation .insurance-calculation__panel {
    padding-top: 8px;

    .panel-inputs {
      padding: 16px;

      .input-items {

        .item__label {
          padding-bottom: 0;
        }
      }
    }
  }

  .insurance-calculation .item__label {
    max-width: 100%;
    flex-basis: 100%;
  }

  .insurance-calculation .item__input-fields {
    max-width: 100%;
    flex-basis: 100%;
  }

  .insurance-calculation .panel-info__content-button a {
    max-width: 100%;
    width: 100%;
  }
}

@media (max-width: 575px) {
  .insurance-calculation .panel-info__content {
    padding: 0 16px;
  }

  .insurance-calculation .panel-info__content-text {
    flex-direction: column;
  }

  .insurance-calculation .info-content-text__label {
    margin-left: 0;
  }

  .insurance-calculation .info-content-text__label > div {
    margin-bottom: 8px;
    margin-top: 24px;
  }
}

@media (max-width: 390px) {
  .insurance-calculation .panel-inputs {
    padding: 24px 8px;
  }

  .insurance-calculation .item__label {
    padding-bottom: 0;
  }
}

/* End Insurance Calculation component */
