.global-announcement {
  &.hidden {
    display: none !important;
  }

  &.light-theme {
    .global-announcement {
      &__bell-icon,
      &__close-button {
        filter: brightness(1);
      }

      &__content--title,
      &__content--description {
        color: var(--primary-white);
      }
    }
  }

  &.dark-theme {
    .global-announcement {
      &__bell-icon,
      &__close-button {
        filter: brightness(0);
      }

      &__content--title,
      &__content--description {
        color: var(--primary-black);
      }
    }
  }

  @include maxLgSemi {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 18;
  }

  &__wrapper {
    display: flex;
    gap: 1.5rem;
    padding: 1rem 4rem;
    cursor: pointer;
    max-width: 90rem;
    margin: 0 auto;

    @include maxLgSemi {
      gap: 0.5rem;
      padding: 1rem;
      max-width: unset;
    }
  }

  &__bell-icon {
    img {
      width: 1.25rem;
      height: 1.25rem;
    }
  }

  &__content {
    display: flex;
    gap: 1.5rem;

    @include maxLgSemi {
      flex-direction: column;
      gap: 0.5rem;
      max-width: calc(100% - 3rem);
    }

    &--title {
      min-width: max-content;
      font-weight: 700;
    }

    &--description {
      @include maxLgSemi {
        & > * {
          text-overflow: ellipsis;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 10;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  &__close-button {
    margin-left: auto;

    img {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
}
