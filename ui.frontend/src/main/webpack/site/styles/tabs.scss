$arrow-icon-link: "/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/right-detail-arrow.svg";

.tcb-tabs_tab-list {
  display: flex;
  flex: 1;
  overflow: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.tcb-tabs {
  display: flex;
  overflow: auto;
  border-bottom: 1px solid var(--light-background-hover);
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.tcb-tabs_item {
  cursor: pointer;
  line-height: 24px;
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-900);
  min-width: 160px;
  min-height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #a2a2a2;
  white-space: nowrap;
  user-select: none;
}

.auto-width {
  .tcb-tabs_item {
    min-width: 100%;
  }
}

.loan-realestate__container {
  .tcb-tabs_item {
    &:hover {
      background-color: #f2f2f2;
    }
  }
}

.tcb-tabs_control {
  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.tcb-tabs_item {
  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.tcb-tabs_indicator {
  position: absolute;
  display: block;
  height: 4px;
  width: 160px;
  background: var(--accent);
  bottom: 0;
  left: 0;
  transition: all 0.3s;
}

.loan-realestate__container {
  .tcb-tabs_indicator {
    height: 5px;
  }
}

.tcb-tabs_item {
  &.tcb-tabs_item--active {
    color: var(--body);
    font-weight: 600;
  }
}

.tcb-tabs_scrollable {
  display: flex;
  flex-wrap: nowrap;
  position: relative;
  gap: 24px;
}

.tcb-tabs_control {
  padding: 20px;
  cursor: pointer;
  background-image: url($arrow-icon-link);
  background-repeat: no-repeat;
  background-position: center;
}

.tcb-tabs_control {
  &.tcb-tabs_control--prev {
    transform: rotate(180deg);
  }
}

.tcb-tabs_item {
  min-width: 40px;
}

.vertical-tabs-button-text {
  overflow-wrap: break-word;
}

.menu-item {
  width: 100%;
}

.tab-vertical-features-component__container .tab-content .tab {
  display: grid;
  height: fit-content;
  column-gap: 25px;
  row-gap: 18px;
}

.tab,
.tab-item {
  word-wrap: break-word;
}

.tab-items {
  position: relative;
  width: 100%;
}

.tab-items-author {
  display: table;
  position: relative;
  width: 100%;
  height: 150px;
}

.tab-items-author > .tab-item:not(.hidden) {
  display: table-cell;
}

.tab-items-author > .tab-item.hidden {
  display: none;
}

.tab-item {
  width: inherit;
}

.tab-item.hidden {
  visibility: hidden;
  max-height: 0;
  overflow: hidden;
}

.cmp-tabs__tablist{
  grid-gap: 24px;
}

.tcb-tabs:not(.can-next) .tcb-tabs_control--next {
  display: none;
}

.tcb-tabs:not(.can-prev) .tcb-tabs_control--prev {
  display: none;
}

@media screen and (max-width: 1024px) {
  .tabs-vertical {
    display: none;
  }
}
@media screen and (min-width: 1025px) {
  .tabs-horizontal {
    display: none;
  }
}
