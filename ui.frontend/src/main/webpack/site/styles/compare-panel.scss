.loan-list {
  .table-item-below {
    overflow-x: auto;
  }

  .dropdown-label__place-holder {
    white-space: nowrap;
    color: var(--secondary-grey-100);
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .label__text {
    display: inline-flex;
    align-items: center; //added by Adobe
    font-weight: 600;
    line-height: 1.5;

    .icon-info {
      height: 16px;
      width: 16px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center center;
      }
    }
  }

  .table-data {
    &.deactivate {
      display: none;
    }
  }

  .table-root {
    width: 100%;
    margin: 0;
  }

  .table-grid {
    padding: 0;
  }

  .table-upper__select-list {
    z-index: 1;
  }

  .select-list__panel {
    display: flex;
    text-align: left;
    position: relative;
    grid-gap: 24px;
    gap: 24px;
    flex-direction: row;
    padding: 32px 24px;
  }

  .select-list__dropdown {
    display: inline-block;
    position: relative;
    border-radius: 8px;
    flex-grow: 0;
    flex-shrink: 0;

    ul {
      list-style: none;
      z-index: 100;
      top: 0;
      border: 1px solid #e3e4e6;
      border-radius: 8px;
      background-color: #fff;
      transition: all 0.5s ease-in-out;
      overflow-x: hidden;
      overflow-y: auto;
      max-height: 250px;
      box-shadow: 0 33px 181px rgb(0 0 0 / 4%),
        0 13.7866px 75.6175px rgb(0 0 0 / 3%),
        0 7.37098px 40.4287px rgb(0 0 0 / 2%),
        0 4.13211px 22.664px rgb(0 0 0 / 2%),
        0 2.19453px 12.0367px rgb(0 0 0 / 2%),
        0 0.913195px 5.00873px rgb(0 0 0 / 1%);
      -webkit-padding-start: 0;
      padding-inline-start: 0;
    }

    li {
      padding: 12px 16px 12px 16px;
      cursor: pointer;
      width: auto;
      flex-wrap: wrap;

      &:hover {
        background-color: #000;
        transition: 0.5s linear;
        color: #fff;
      }

      &.deactivate {
        display: none;
      }
    }
  }

  .select-list__submit {
    justify-content: space-between;
    border: 1px solid #fff;
    position: relative;
    display: inline-flex;
    padding: 16px;
    border-radius: 8px;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    text-decoration: none;
    transition: all 0.3s ease-in;
    align-items: center;
    grid-gap: 12px;
    gap: 12px;
    min-width: max-content;
    width: 100%;
    background-color: inherit;

    &:hover {
      border: 1px solid #a2a2a2;
    }

    >span {
      font-family: inherit;
      cursor: pointer;
      white-space: nowrap;
      font-weight: 600;
      font-size: 16px;
    }
  }

  .select-list__dropdown-display {
    display: flex;
    border: 1px solid #e3e4e6;
    padding: 5px 24px 8px 16px;
    overflow: hidden;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    width: auto;
    height: 56px;
    transition: 0.5s linear;

    &:hover {
      border: 1px solid #0a84ff;
      box-shadow: 0 0 4px 3px #daecff;
      background-color: #fff;
      color: #000;
    }
  }

  .select-list__dropdown-label {
    display: flex;
    flex-direction: column;
    width: calc(100% - 36px);

    >.dropdown-label__prefix-label {
      color: var(--gray-600);
    }
  }

  .font-small {
    font-size: 12px;
  }

  .select-list__arrow-icon {
    font-family: Material Icons;
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
    font-weight: 400;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    color: #ed1c24;
    font-size: 30px;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-top: 4px;
    margin-left: 8px;

    &:hover {
      filter: brightness(0.5) saturate(0%);
    }
  }

  .dropdown-open {
    .select-list__arrow-icon {
      transform: rotate(180deg);
    }

    .select-list__dropdown-list {
      transform: scaleX(1) scaleY(1);
      z-index: 1;
    }
  }

  .select-list__dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    transition: all 0.3s ease-in-out;
    transform: scaleX(0) scaleY(0);
  }

  .table-body-row {
    background-color: #fff;

    td {
      padding: 12px 16px;
    }
  }

  .table-item-body {
    font-size: 14px;
    min-width: 1000px;
    width: 100%;
  }

  .table-label-content {
    font-size: inherit;

    >p {
      font-weight: inherit;
      font-size: inherit;
      line-height: 24px;
    }
  }

  .table-cell-innercontent {
    font-size: inherit;
    font-weight: 400;
  }

  .table-header {
    .table-label-content {
      display: inline-flex;
      position: relative;
    }

    th {
      text-align: start !important;
      padding-top: 44px;
      padding-bottom: 8px;
      font-weight: 700;
      position: relative;
      vertical-align: top;
    }
  }

  .table-label-content-icon {
    top: 0;
    margin-top: -5px;
    right: -3px;
  }

  .content-icon__container {
    height: 16px;
    width: 16px;
    position: relative;
    margin-left: 24px;

    >span {
      box-sizing: border-box;
      display: block;
      overflow: hidden;
      width: initial;
      height: initial;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      position: absolute;
      inset: 0px;
    }

    img {
      position: absolute;
      inset: 0px;
      box-sizing: border-box;
      padding: 0px;
      border: none;
      margin: auto;
      display: block;
      width: 0px;
      height: 0px;
      min-width: 100%;
      max-width: 100%;
      min-height: 100%;
      max-height: 100%;
      object-fit: cover;
      object-position: center center;
    }
  }
}

@media (min-width: 993px) {
  .loan-list {
    .select-list__dropdown {
      width: calc(25% - 18.75px);
    }
  }
}

@media (max-width: 992px) {
  .loan-list {
    .select-list__panel {
      flex-direction: column;
      padding: 24px 16px;
      grid-gap: 8px;
      gap: 8px;
    }
  }
}

@media (max-width: 575px) {
  .label__text {
    position: relative;

    .icon-info {
      position: static;
    }
  }

  .loan-list {
    .table-body-row {
      td {
        &:first-child {
          box-shadow: 7px 0 4px 2px rgb(0 0 0 / 3%);
        }
      }
    }
  }
}