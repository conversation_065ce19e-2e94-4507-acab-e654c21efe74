.article-tag-cloud {
  display: flex;
  padding: 32px 0;
  flex-grow: 0;
  max-width: 100%;
  flex-basis: 100%;
  align-items: flex-start;

  &_heading {
    display: flex;
    align-items: center;
    padding: 8px 0;
  }
  &_icon {
    display: flex;
    margin-right: 10px;
    width: 21px;
    height: 21px;
  }
  &_title {
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: #000;
  }
  &_content {
    display: flex;
    grid-gap: 16px;
    gap: 16px;
    flex-wrap: wrap;
    margin-left: 24px;
    @media (max-width: 991px) {
      grid-gap: 8px;
      gap: 8px;
      margin-left: 8px;
    }
  }
  &_item {
    border: 1px solid #dedede;
    border-radius: 27px;
    padding: 8px 16px;
    color: #616161;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    justify-content: space-between;

    display: inline-flex;
    align-items: center;
    transition: all .3s ease-in-out;

    &:hover {
      background: #000;
      color: #fff;
    }

    &_title {
      line-height: 1.5;
      font-weight: 600;
      font-size: 1rem;
      display: inline-flex;
      align-items: center;
    }
  }
}

//added by adobe
.article-content--wrapper {
	&:has(.article-tag-cloud) {
		margin-top: -80px;
	}
	.tcb-container {
		.article-tag-cloud {
			padding-left: 24px;
		}
	}
}
@media (max-width: 992px) {
  .article-content--wrapper {
    &:has(.article-tag-cloud) {
      margin-top: -48px;
    }
		.tcb-container {
			.article-tag-cloud {
				padding-left: 0;
			}
		}
	}
}