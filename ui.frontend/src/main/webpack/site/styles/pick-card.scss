:root {
  --border-color: #404040;
  --bg-icon: #f2ece0;
  --color-button-added: #1d6d30;
  --bg-button-added: #e0f7e5;
}

$rem04: 0.25rem;
$rem08: 0.5rem;
$rem12: 0.75rem;
$rem16: 1rem;
$rem18: 1.125rem;
$rem20: 1.25rem;
$rem24: 1.5rem;
$rem32: 2rem;
$rem40: 2.5rem;
$rem48: 3rem;

$color-white: var(--primary-white);
$bg-icon: var(--bg-icon);

.listing-pick-card-container {
  width: 100%;
  max-width: 90rem;
  display: flex;
  flex-direction: column;
  gap: $rem24;
  margin: 0 auto;

  .title-pick-card-container {
    font-size: $rem32;
    line-height: $rem40;
    font-weight: 300;
  }

  .pick-card-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: $rem24;
    background-color: var($color-background);
  }

  @include maxSm {
    gap: $rem16;

    .title-pick-card-container {
      font-size: $rem24;
    }

    .pick-card-list {
      flex-direction: column;
    }
  }
}

.pick-card-container {
  width: calc((100% - $rem48) / 3);
  display: flex;
  flex-direction: column;

  .pick-card-top {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    aspect-ratio: 421 / 320;
    object-fit: cover;
    border-radius: $rem08 $rem08 0 0;
    overflow: hidden;
    position: relative;

    .pick-card-top-text {
      position: absolute;
      top: $rem20;
      left: $rem20;
      max-width: calc(100% - $rem40);

      display: flex;
      flex-direction: column;
      z-index: 2;

      .label,
      .title {
        color: var(--body);
        font-size: $rem16;
        line-height: $rem24;
        font-weight: 400;
      }
      .title {
        font-weight: 600;
      }
    }

    .pick-card-top-image {
      width: 100%;
      height: auto;
      max-width: 100%;
      max-height: 100%;

      transition: all 0.2s linear;
      z-index: 1;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .pick-card-bottom {
    flex: 1;
    width: 100%;

    display: flex;
    flex-direction: column;
    gap: $rem20;

    background-color: $color-white;
    padding: $rem24 $rem20;
    border-radius: 0 0 $rem08 $rem08;
    .promotions-iscontinued {
      color: var(--light-secondary-text);
      margin-bottom: 0.25rem;
    }
    .pick-card-bottom-content-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $rem16 $rem12;
      .content-item {
        display: flex;
        gap: $rem08;
        align-items: flex-start;

        .content-item-icon {
          margin-top: $rem04;
          width: $rem40;
          min-width: $rem40;
          height: $rem40;

          display: flex;
          justify-content: center;
          align-items: center;

          border-radius: 50%;
          background-color: $bg-icon;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .content-item-text {
          font-size: $rem16;
          line-height: $rem24;
          color: var(--light-secondary-text);
          font-weight: 400;
          min-height: 4.5rem;
          b,
          strong {
            color: var(--primary-black);
          }
          span {
            font-weight: 600;
          }
        }
      }

      .promotions-link {
        text-decoration: none;

        .content-item-promotions {
          align-items: center;
          .content-item-icon {
            background-color: var(--primary-red);
            margin-top: 0;
          }

          .content-item-text {
            font-weight: 600;
            color: var(--primary-black);
            min-height: auto;
          }
        }
      }
    }

    .pick-card-discontinued-card-issuance {
      font-size: $rem16;
      line-height: $rem24;
      color: var(--light-secondary-text);
      font-weight: 400;
      width: 100%;
    }
    .pick-card-bottom-content-button {
      width: 100%;
      display: flex;
      align-items: center;
      gap: $rem12;
      margin-top: auto;

      .button-item {
        width: calc((100% - $rem12) / 2);
        height: 3.125rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.625rem;
        border: 0.063rem solid var(--cta-disabled);
        border-radius: $rem08;
        font-size: $rem16;
        font-weight: 600;
        line-height: $rem18;
        text-align: start;
        padding: 0.4375rem $rem12;
        background: transparent;
        cursor: pointer;

        svg,
        img {
          width: $rem16;
          min-width: $rem16;
          height: $rem16;
        }
      }

      .button-link {
        width: calc((100% - $rem12) / 2);
        text-decoration: none;

        .button-item-link {
          width: 100%;
          max-width: 100%;
          gap: $rem08;
          border: 0.063rem solid var(--border-color);
          .pick-card__next-white {
            display: none;
          }
          .pick-card__next {
            display: block;
          }
          &:hover {
            color: var(--primary-white);
            background: var(--primary-black);
            .pick-card__next {
              display: none;
            }
            .pick-card__next-white {
              display: block;
            }

            img {
              -webkit-filter: brightness(0) invert(1);
              filter: brightness(0) invert(1);
            }
          }
        }
      }

      .button-item-compare {
        &:hover {
          background-color: var(--secondary-mid-grey-100);
          color: var(--secondary-grey-60);

          img {
            -webkit-filter: grayscale(100%);
            filter: grayscale(100%);
          }
        }
      }

      .button-item-compare-added {
        background-color: var(--bg-button-added);
        color: var(--color-button-added);
        cursor: not-allowed;
        border-color: var(--bg-button-added);

        &:hover {
          color: var(--color-button-added);
          background-color: var(--bg-button-added);

          img {
            -webkit-filter: none;
            filter: none;
          }
        }
      }

      .button-item-disabled {
        background-color: var(--cta-disabled);
        color: var(--secondary-mid-grey-100);
        cursor: not-allowed;

        &:hover {
          color: var(--secondary-mid-grey-100);
          background-color: var(--cta-disabled);
        }
      }
    }
  }

  @include maxLgSemi {
    width: calc((100% - $rem24) / 2);
  }

  @include maxSm {
    width: 100%;

    .pick-card-top {
      .pick-card-top-text {
        top: $rem16;
        left: $rem16;
        max-width: calc(100% - $rem32);

        .label,
        .title {
          font-size: 1rem;
          line-height: 130%;
        }
      }
    }
    .pick-card-bottom {
      gap: $rem24;
      padding: $rem20 $rem16;

      .pick-card-bottom-content-list {
        grid-template-columns: 1fr;
        gap: $rem16 $rem08;

        .content-item {
          align-items: center;
          .content-item-icon {
            margin-top: 0;
          }

          .content-item-text {
            min-height: auto;
          }
        }
      }

      .pick-card-bottom-content-button {
        .button-item {
          gap: $rem08;
          line-height: $rem18;
          img {
            width: $rem16;
            min-width: $rem16;
            height: $rem16;
          }
        }

        .button-link {
          .button-item-link {
            padding: 0 $rem12;
            svg {
              min-width: $rem16;
            }
          }
        }
      }
    }
  }
}
