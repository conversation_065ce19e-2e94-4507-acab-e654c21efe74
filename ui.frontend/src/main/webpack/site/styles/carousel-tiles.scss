.card-slider-container {

  .cardslider-item-arrow {
    position: absolute;
    bottom: 29px;
    right: 24px;
  }

  .bottom-line {
    margin-top: auto !important;
    padding-bottom: 0 !important;
    margin-bottom: 16px !important;
  }

  .carousel-card-wrapper {
    &.card-image-type-foreground {
      background-color: #fff;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.16);

      .carousel-card-article {
        padding-top: 0 !important;
      }

      .cardslider-head-foreground__image {
        width: 100%;
        object-fit: cover;
        object-position: center center;
        aspect-ratio: 1.77;
        border-radius: 8px 8px 0 0;
        transition: all .3s ease-in-out;
      }
    }
  
    &.card-image-type-background {
      .cardslider-item-nudge,
      .cardslider-item-title,
      .cardslider-item-description {
        margin-top: auto;
      }
    }

    &:hover {
      box-shadow: 0 0.0625em 0.0625em rgba(0, 0, 0, 0.25),
        0 0.125em 0.5em rgba(0, 0, 0, 0.25),
        inset 0 0 0 1px hsla(0, 0%, 100%, 0.1);

      .cardslider-head-foreground__image {
        transform: scale(1.02);
      }
    }
  }

  &[num-alias="card4"] {
    .cardslider-carousel-slickitem {
      article {
        min-height: 376px !important;
      }
    }

    .slick-arrow {
      top: 200px !important;
    }

    &.medium-card {
      .slick-arrow {
        top: 140px !important;
      }
    }
  }

  &.mobile-slide {
    @media (max-width: 768px) {
      .card-slider-wrapper {
        .cardslider-carousel-slickwrapper {
          .cardslider-carousel-slickcontainer {
            .cardslider-carousel-slicklist {
              .slick-arrow {
                &.slick-next {
                  right: 0;
                  top: 45%;
                }

                &.slick-prev {
                  left: 0;
                  top: 45%;
                }
              }

              .slick-list {
                .slick-track {
                  .slick-slide {
                    .cardslider-carousel-slickitem {
                      .cardslider-item-wrapper {
                        .cardslider-item-content {
                          .cardslider-item-body {
                            .cardslider-item-inner {
                              .cardslider-item-navtext {
                                width: 100%;

                                .cardslider-item-action {
                                  .tcb-button.tcb-button--link.tcb-button--border-on-mobile {
                                    padding-left: unset;
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    @media (min-width: 768px) and (max-width: 1024px) {
      .card-slider-wrapper {
        .cardslider-carousel-slickwrapper {
          .cardslider-carousel-slickcontainer {
            .cardslider-carousel-slicklist {
              .slick-arrow {
                &.slick-next {
                  right: 25px;
                }

                &.slick-prev {
                  left: 25px;
                }
              }

              .slick-list {
                .slick-track {
                  .slick-slide {
                    .cardslider-carousel-slickitem {
                      .cardslider-item-wrapper {
                        .cardslider-item-content {
                          .cardslider-item-body {
                            .cardslider-item-inner {
                              .cardslider-item-navtext {
                                width: 100%;

                                h4 {
                                  width: calc(100% - 24px);
                                  font-weight: 300;
                                  font-size: 1.5rem;
                                  line-height: 1.5;
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.card-slider-wrapper {
  padding: 0;
  margin-bottom: 32px;
  display: flex;

  .show-more {
    .cta-button {
      img {
        height: 24px;
        width: 24px;
        margin-left: -10px;
      }
    }
  }

  .tcb-button {
    white-space: normal;
  }

  .cardslider-carousel-slickwrapper {
    position: relative;
    z-index: 1;
    min-width: calc(100% + 24px);

    @media (max-width: 320px) {
      min-width: 100%;
    }

    &.small-article {
      .cardslider-carousel-slickcontainer {
        .cardslider-carousel-slicklist {
          .cardslider-carousel-slickitem {
            .carousel-card-wrapper {
              .carousel-card-article {
                min-height: 376px;

                @media screen and (max-width: 767px) {
                  height: var(--mobile-height);
                }
                @media screen and (min-width: 768px){
                  height: var(--desktop-height);
                }

                @include maxSm {
                  min-height: 235px;
                }
              }
            }
          }
        }
      }
    }

    .cardslider-carousel-slickcontainer {
      position: relative;
      z-index: 1;
      flex-grow: 0;
      max-width: 100%;
      flex-basis: 100%;
      padding-top: 12px;

      .cardslider-carousel-slicklist {
        margin-left: auto;
        margin-right: auto;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-top: -12px;
        border-radius: 8px !important;
        gap: 1.5rem;

        .slick-list {
          width: 100%;
          margin: 0 -0.875rem;
          @include maxSm {
            margin: 0 -0.25rem;
          }

          @media (max-width: 320px) {
            margin: 0 -0.08rem;
          }

          .slick-track {
            max-width: none;
            display: flex;

            .slick-slide {
              height: auto;
              padding: 12px;
              flex: 1;

              >div {
                height: 100%;
              }

              .cardslider-carousel-slickitem {
                height: 100%;
                width: 100%;
                border-radius: 8px !important;
                position: relative;

                .card-slider-container.medium-card & {
                  height: 235px;

                  .carousel-card-article {
                    min-height: unset;
                    padding-top: unset;

                    .cardslider-item-navtext {
                      h4 {
                        margin-top: 40px;
                      }

                      .cardslider-item-description {
                        margin-bottom: 48px !important;
                      }
                      .cardslider-item-title {
                        padding-right: 0;
                      }

                      .cardslider-item-action {
                        padding-bottom: 48px;
                        .tcb-button.tcb-button--link {
                          font-weight: 400;
                        }
                      }
                    }
                  }
                }

                .carousel-card-wrapper {
                  border-radius: 8px !important;
                  height: 100%;

                  .carousel-card-link {
                    display: block;
                    height: 100%;
                    width: 100%;
                    border-radius: 8px !important;

                    &.no-link {
                      cursor: unset;
                    }

                    .carousel-card-article {
                      .cardslider-item-wrapper {
                        .cardslider-item-content {
                          .cardslider-item-body {
                            width: 100%;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .slick-next {
          right: -0.125rem;
          top: 50%;
          z-index: 1;
          color: white;
          font-size: 1.25rem;

          &::after {
            content: "❯";
          }
        }

        .slick-prev {
          left: -1.625rem;
          top: 50%;
          z-index: 1;
          color: white;
          font-size: 1.25rem;

          &::after {
            content: "❮";
          }
        }

        .slick-dots {
          margin-top: 1rem;
          display: flex;
          justify-content: center;
          width: 100%;

          @include maxSm {
            margin-top: 0.25rem;
          }

          li {
            width: 8px;
            font-size: 25px;
            margin: 2px 5px;

            &::marker {
              color: #c4c4c4;
            }

            &.slick-active::marker {
              color: #ed1b24;
            }
          }

          button {
            display: none;
          }
        }

        .slick-arrow {
          position: absolute;
          border-radius: 50%;
          transition: all 0.2s ease-in-out;
          box-shadow: 1px 1.5px 4px 0 hsl(218deg 7% 69% / 45%);
          transform: translateY(-50%);
          width: 48px;
          height: 48px;
          display: flex !important;
          align-items: center;
          justify-content: center;
          background: hsla(0, 0%, 77%, 0.54);
          border: none;
          cursor: pointer;

          &:hover {
            background: var(--primary-white);
            color: var(--primary-red);
          }
        }

        &[arrow-hover="white-red"] {
          .slick-arrow:hover {
            background: var(--primary-white);
          }

          .slick-prev:hover {
            color: var(--accent);
          }

          .slick-next:hover {
            color: var(--accent);
          }
        }

        &.mobile-unslide-items {
          article {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            overflow: hidden;
            border-radius: 8px;
            width: 100%;
            height: 100%;
            transition: box-shadow 0.3s;
            min-height: 500px;

            a {
              span {
                box-sizing: border-box;
                display: block;
                overflow: hidden;
                width: initial;
                height: initial;
                background: none;
                opacity: 1;
                border: 0px;
                margin: 0px;
                padding: 0px;
                position: absolute;
                inset: 0px;
              }
            }

            .cardslider-item-navtext {
              width: 100%;
              height: 100%;
              position: relative;
              z-index: 1;

              h4 {
                width: calc(100% - 24px);
              }

              .cardslider-item-description {
                margin-top: 8px;
                font-weight: 400;
                font-size: 1rem;
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}

/* Card Slider */

.cardslider-carousel-slickitem .carousel-card-wrapper .carousel-card-link .carousel-card-article {
  padding-top: 62%;
  min-height: 500px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  overflow: hidden;
  border-radius: 8px !important;
  width: 100%;
  height: 100%;
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  @include maxSm {
    padding-top:  unset;
  }
}

[card-slider-type="card-item"] .cardslider-carousel-slickitem article {
  padding-top: 0;
  justify-content: start;
  height: 540px;
  min-height: 100%;
  box-shadow: 0 1px 4px rgb(0 0 0 / 16%);

  &:hover {
    box-shadow: 0 0.0625em 0.0625em rgb(0 0 0 / 25%),
      0 0.125em 0.5em rgb(0 0 0 / 25%), inset 0 0 0 1px hsl(0deg 0% 100% / 10%);
  }
}

.cardslider-carousel-slickitem .cardslider-head__img-container,
.cardslider-carousel-slickitem .carousel-card-wrapper .carousel-card-link .carousel-card-article .carousel-card-picture {
  box-sizing: border-box;
  display: block;
  overflow: hidden;
  width: initial;
  height: initial;
  background: none;
  opacity: 1;
  border: 0px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  inset: 0px;
}

.cardslider-carousel-slickitem .cardslider-head__img-container {
  position: relative;
}

.cardslider-carousel-slickitem .cardslider-head__image {
  position: absolute;
  inset: 0px;
  box-sizing: border-box;
  padding: 0px;
  border: none;
  margin: auto;
  display: block;
  width: 0px;
  height: 0px;
  min-width: 100%;
  max-width: 100%;
  min-height: 100%;
  max-height: 100%;
  object-fit: cover;
  object-position: 80% 50%;
}

.cardslider-carousel-slickitem .cardslider-head__image {
  position: relative;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease-in-out;
}

.cardslider-item-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: inherit;
  z-index: 2;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

[card-slider-type="card-item"] .cardslider-item-wrapper {
  display: block;
}

.cardslider-item-content {
  height: 100%;
  display: flex;

  .cardslider-item-body {
    display: flex;
  }
}

[card-slider-type="card-item"] .cardslider-item-content {
  color: var(--body);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cardslider-item-inner {
  width: 100%;
  position: relative;
  z-index: 1;

  >h4 {
    width: calc(100% - 24px);
  }
}

[card-slider-type="card-item"] h4>p,
.cardslider-item-inner>h4>p {
  font-weight: 300;
  font-size: 1.5rem;
  line-height: 1.5;
}

.cardslider-item-description {
  display: block;
  flex-direction: row;
  align-items: flex-end;
}

.text-align-bottom {
  .cardslider-item-navtext {
      justify-content: flex-end;
    .cardslider-item-action {
      margin-top: 0;
    }
  }


  .carousel-card-wrapper {
    &.card-image-type-foreground {
      picture {
        flex: 1;
      }
    }
  }
}

.text-align-middle {
  .card-slider-container .carousel-card-wrapper.card-image-type-background {
    .cardslider-item-nudge {
      margin-top: 0;
    }
    .cardslider-item-description, .cardslider-item-title {
      margin-top: 5px;
    }
  }
}
.cardslider-item-navtext {
  display: inline-flex;
  align-items: flex-end;
  line-height: 1.5;
  font-weight: 600;
  font-size: 1rem;

  .cardslider-item-nudge,
  .cardslider-item-title,
  .cardslider-item-description,
  .cardslider-item-action {
    padding-left: 24px;
    padding-right: 24px;
    .tcb-button--border-on-mobile {
      img {
        margin-left: auto;
      }
    }
  }

  .cardslider-item-description {
    padding-bottom: 16px;
  }

  .cardslider-item-action {
    padding-bottom: 24px;
    &.action-bottom-right {
      position: absolute;
      bottom: -3px;
      right: 0px;
    }
  }

  flex-direction: column;

  >h4 {
    align-self: flex-start;
  }

  >p {
    text-transform: uppercase;
    align-self: flex-start;
  }

  .cardslider-item-action {
    align-self: flex-start;
    margin-top: auto;
    padding: 0 24px 16px;
    @media (max-width: 767px) {
      padding: 0 24px 4px 24px;
    }
    
    .tcb-button {
      transition: unset;
      border-bottom-style: none;
      border-bottom-width: 0px;
      border-left-style: none;
      border-left-width: 0px;
      border-right-color: rgb(0, 0, 0);
      border-right-style: none;
      border-right-width: 0px;
      border-top-color: rgb(0, 0, 0);
      border-top-style: none;
      border-top-width: 0px;

      &:hover {
        color: inherit;
        border: unset;
      }
    }

    >div {
      display: flex;
      justify-content: flex-start;
    }
  }

  >span:nth-child(1) {
    line-height: 1.5;
    font-weight: 600;
    font-size: 1rem;
  }

  >span:nth-child(2) {
    position: relative;
    margin-left: 12px;
    display: flex;

    >span {
      box-sizing: border-box;
      display: inline-block;
      overflow: hidden;
      width: 16px;
      height: 17px;
      background: none;
      opacity: 1;
      border: 0px;
      margin: 0px;
      padding: 0px;
      position: relative;
    }
  }

  .navtext__icon-container {
    margin-left: 12px;
  }
}

/* Card Slider Media */
@media (min-width: 768px) {
  .cardslider-header-contain>div {
    padding-right: 24px;
    margin-right: 24px;
  }

  .cardslider-carousel-container {
    padding-left: 12px;
    padding-right: 12px;
  }
}

@media (max-width: 767px) {
  .card-slider-wrapper {
    display: flex;
  }

  .cardslider-carousel-slickwrapper {
    width: calc(100% + 16px);
    margin: -8px;
  }

  .cardslider-carousel-slickcontainer>.cardslider-carousel-slicklist {
    margin-top: -12px !important;
    width: auto !important;
    display: grid;
  }

  .mobile-unslide-container {
    display: flex;
    margin: unset;
    padding: 8px;

    .mobile-slide & {
      padding: 0;
    }

    .mobile-unslide-items {
      width: 100% !important;
      margin: -8px;
      display: flex;

      .show-more {
        width: 100%;
        display: flex;
        place-content: center;

        .cta-button {
          width: 100%;
          place-content: center;
        }

        &:hover {
          cursor: pointer;
        }
      }

      .cardslider-carousel-slickitem {
        flex-grow: 0;
        max-width: 100%;
        flex-basis: 100%;
        height: auto;
        padding: unset;

        &.slickitem-hidden {
          display: none;
        }

        >div {
          grid-gap: 24px;
          gap: 24px;
          display: grid;

          article {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            overflow: hidden;
            border-radius: 8px;
            width: 100%;
            height: 100%;
            transition: box-shadow 0.3s;

            span {
              box-sizing: border-box;
              display: block;
              overflow: hidden;
              width: initial;
              height: initial;
              background: none;
              opacity: 1;
              border: 0px;
              margin: 0px;
              padding: 0px;
              inset: 0px;
            }

            .cardslider-item-wrapper {
              position: relative;
              display: flex;
              flex-direction: column;
              justify-content: flex-end;
              align-items: inherit;
              z-index: 2;
              height: 100%;
              border-radius: 8px;
              overflow: hidden;

              .cardslider-item-content {
                .cardslider-item-body {
                  width: 100%;
                  height: 100%;
                  position: relative;
                  z-index: 1;
                  padding: unset;

                  .cardslider-item-inner {
                    height: 100%;
                  }

                  .tcb-button.tcb-button--link.tcb-button--border-on-mobile {
                    border: unset;
                    padding-left: unset;
                    width: 100%;
                    justify-content: flex-start;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: 992px) {
  .card-slider-wrapper {
    max-width: 1440px;
    margin-left: auto;
    margin-right: auto;
  }
}
