.card-center-listcard {
	display: grid;
	position: relative;
	grid-template-columns: repeat(auto-fit, minmax(calc(20% - 72px), 1fr));
	grid-gap: 24px;
	gap: 24px;
	flex-wrap: nowrap;

	.cardcenter-carditem-content {
		h3 {
			margin-bottom: 8px;
		}
	}

	&.row-display {

		.card-center-carditem {
			article {
				flex-direction: row;
				padding: 16px;
				align-items: flex-start;
			}
		}
	}

	.card-center-carditem {
		box-shadow: 0 33px 181px rgba(0,0,0,.04), 0 13.7866px 75.6175px rgba(0,0,0,.029), 0 7.37098px 40.4287px rgba(0,0,0,.024), 0 4.13211px 22.664px rgba(0,0,0,.02), 0 2.19453px 12.0367px rgba(0,0,0,.016), 0 0.913195px 5.00873px rgba(0,0,0,.011);
    	border-radius: 8px;
		article {
			background-color: var(--bg-card);
		}
		.view-more {
			margin-top: 24px;
			&.edit-mode {
				min-height: 40px;
			}
		}
	}

	@media (min-width: 768px) {
		&.column-3 {
			grid-template-columns: 1fr 1fr 1fr;
		}

		&.column-2 {
			grid-template-columns: 1fr 1fr;
		}

		&.column-5 {
			grid-template-columns: repeat(auto-fit,minmax(17%,1fr));
			grid-auto-rows: minmax(160px,auto);
		}

		&.column-4 {
			grid-template-columns: 1fr 1fr;
			grid-auto-rows: minmax(160px,auto);
		}
	}

	@media (min-width: 1024px) {
		&.column-4 {
			grid-template-columns: repeat(auto-fit,minmax(calc(26% - 72px),1fr));
		}
	}

	@media only screen and (max-width: 980px) and (min-width: 768px)  {
		&.column-5 {
			.cardcenter-carditem-headerimg {
				width: 85px;
			}
		}
	}

	&.merged {
		gap: 0;
		.card-merged-carditem:nth-child(1) {
			h2 {
				font-size: 1.75rem;
				font-weight: 300;
				line-height: 1.25;
			}

			p {
				margin-top: 24px;
			}
		}
		.card-merged-carditem {
			display: flex;
			border-radius: 8px;
			height: 100%;
			flex-direction: column;
			padding: 8px;
			justify-content: stretch;
			align-items: center;

			.cardcenter-carditem-header {
				width: 44px;
				height: auto;
				margin: 0;

				@media (max-width: 767px) {
					align-self: flex-start;
				}
			}

			.cardcenter-carditem-headerimg {
				width: auto;
				img {
					inset: 0px;
					max-width: 100%;
					box-sizing: border-box;
					display: block;
					overflow: hidden;
					width: initial;
					height: initial;
					background: none;
					opacity: 1;
					border: 0px;
					margin: 0px;
					padding: 0px;
					position: relative;
					object-position: center center;
				}
			}

			.cardcenter-carditem-content {
				margin-top: 32px;

				@media (max-width: 767px) {
					margin-top: 24px;
				}

				> div {
					&:nth-child(1) {
						font-size: 24px;
						margin-bottom: 8px;
					}
				}
			}

			article {
				display: flex;
				border-radius: 8px;
				height: 100%;
				flex-direction: column;
				padding: 32px 16px;
				justify-content: stretch;
				align-items: center;
				background-color: var(--secondary-light-grey-60);
				&:hover {
					box-shadow: 2px 3px 18px 0 hsla(0, 6%, 49%, 0.24);
				}
			}
		}

		@media (min-width: 768px) {
			width: calc(100% + 24px);
			margin: -12px;
			justify-content: center;
			display: flex;
			flex-wrap: wrap;
			box-sizing: border-box;
			gap: 0;
			.card-merged-carditem:nth-child(1) {
				flex-grow: 0;
				max-width: 33.333333%;
				flex-basis: 33.333333%;
			}
			.card-merged-carditem {
				display: block;
				flex-grow: 0;
				max-width: 16.666667%;
				flex-basis: 16.666667%;
				padding: 12px;
				height: auto;
			}
			article {
				display: flex;
				border-radius: 8px;
				height: 100%;
				flex-direction: column;
				background-color: #f5f5f5;
				padding: 32px 16px;
				justify-content: stretch;
				align-items: center;
			}
		}

		&.card-dark-theme {
			> article {
				background: linear-gradient(127.93deg, #e2ded7 -86.5%, #8d8175 69.66%);
				box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
					0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%),
					0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
				color: #f5f5f5;
			}
		}
	}

	&.promotion {
		gap: 0;

		&.card-dark-theme {
			article {
				background: linear-gradient(127.93deg, #e2ded7 -86.5%, #8d8175 69.66%);
				box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
					0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%),
					0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
				padding: 16px;
			}
		}
	}
	.card {
		&-center {
			&-carditem {
				display: block;
				position: relative;
				width: 100%;
				border-radius: 8px;
                box-shadow: 0 1px 4px rgb(0 0 0 / 16%);
				> article {
					display: flex;
					border-radius: 8px;
					height: 100%;
					flex-direction: column;
					transition: all 0.3s ease;
					transform: scale(1);
					background-color: var(--primary-background);
				}

				&.left {
					article {
						flex-direction: row;
						padding: 16px;
						align-items: flex-start;
					}
				}
			}
		}
		&center-carditem {
			&-header {
				position: relative;
				width: auto;
				height: 100px;
				margin: 16px;

				&img {
					width: 130px;
					margin: auto;
					height: 100%;
					transition: all 0.3s ease-in-out;
					transform: scale(1);
					> span {
						box-sizing: border-box;
						display: block;
						overflow: hidden;
						width: initial;
						height: initial;
						background: none;
						opacity: 1;
						border: 0px;
						margin: 0px;
						padding: 0px;
						position: relative;
						height: 100% !important;
						> span {
							box-sizing: border-box;
							display: block;
							width: initial;
							height: initial;
							background: none;
							opacity: 1;
							border: 0px;
							margin: 0px;
							padding: 100% 0px 0px;
							height: 100% !important;
						}
						> picture {
							position: absolute;
							inset: 0px;
							box-sizing: border-box;
							padding: 0px;
							border: none;
							margin: auto;
							display: block;
							width: 0px;
							height: 0px;
							min-width: 100%;
							max-width: 100%;
							min-height: 100%;
							max-height: 100%;
							> img {
								inset: 0px;
								box-sizing: border-box;
								padding: 0px;
								border: none;
								margin: auto;
								display: block;
								width: 0px;
								height: 0px;
								min-width: 100%;
								max-width: 100%;
								min-height: 100%;
								max-height: 100%;
								object-position: center center;
								object-fit: contain;
							}
						}
					}
				}
				&-no-image {
					margin: 1rem;
					height: 6.25rem;

					@include maxSm {
						margin: 1.5rem;
					}
				}
			}
			&-content {
				margin: 16px;
				line-height: 1.5;
				margin-left: 24px;
				margin-top: 0;

				&.bottom-content {
					margin-top: auto;
				}
				ul {
					margin-top: 8px;
					padding-inline-start:unset;
				}
				li {
					margin-left: 24px;
				}
				> div {
					&:nth-child(1) {
						font-weight: 600;
					}
					&:nth-child(2) {
						font-weight: 400;
					}
				}
				&.loan {
					padding-top: 10px;
					padding-right: 20px;
					padding-left: 5px;
					padding-bottom: 10px;
				}
			}
		}
		&-promotion {
			&__item-wrapper {
				padding: 12px;
			}

			&__item {
				display: flex;
				flex-direction: column;

				.card-promotion__item_header {
					box-sizing: border-box;
					display: block;
					overflow: hidden;
					width: initial;
					height: initial;
					background: none;
					opacity: 1;
					border: 0px;
					margin: 0px;
					padding: 0px;
					position: relative;
				}

				.card-promotion__item_headerimg {
					box-sizing: border-box;
					display: block;
					width: initial;
					height: initial;
					background: none;
					opacity: 1;
					border: 0px;
					margin: 0px;
					padding: 56.5321% 0px 0px;
				}

				.card-promotion-carditem-content {
					padding: 24px 24px 32px;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex-grow: 1;
					> div {
						&:nth-child(1) {
							margin-bottom: 24px;
							font-weight: 300;
							font-size: 1.5rem;
							line-height: 1.5;
						}
					}
				}

				.card-promotion-btn {
					font-size: inherit;
					line-height: inherit;
					font-weight: 300;
					margin: 0;
					padding: 0;
				}

				.card-promotion-btn a {
					position: relative;
					display: inline-flex;
					outline: none;
					border: none;
					cursor: pointer;
					align-items: center;
					text-decoration: none;
					transition: all 0.3s ease-in;
					justify-content: flex-start;
					width: inherit;
					grid-gap: 12px;
					gap: 12px;
					padding: 0;
				}

				.card-promotion-btn a:hover {
					text-decoration: underline;
				}

				.card-promotion-btn a:after {
					line-height: 0;
					content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/red-arrow-icon.svg);
				}

				> article {
					border-radius: 8px;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					height: 100%;
					box-shadow: 0 33px 181px rgba(0, 0, 0, 0.04), 0 13.7866px 75.6175px rgba(0, 0, 0, 0.03),
						0 7.37098px 40.4287px rgba(0, 0, 0, 0.02), 0 4.13211px 22.664px rgba(0, 0, 0, 0.02),
						0 2.19453px 12.0367px rgba(0, 0, 0, 0.02), 0 0.913195px 5.00873px rgba(0, 0, 0, 0.01);
				}

				img {
					position: absolute;
					inset: 0px;
					box-sizing: border-box;
					padding: 0px;
					border: none;
					margin: auto;
					display: block;
					width: 0px;
					height: 0px;
					min-width: 100%;
					max-width: 100%;
					min-height: 100%;
					max-height: 100%;
					object-fit: contain;
					object-position: center center;
				}
			}
		}
	}

	&.card-dark-theme {
		article {
			background: linear-gradient(127.93deg, #e2ded7 -86.5%, #8d8175 69.66%);
			box-shadow: 0 33px 181px rgb(0 0 0 / 4%), 0 13.7866px 75.6175px rgb(0 0 0 / 3%),
				0 7.37098px 40.4287px rgb(0 0 0 / 2%), 0 4.13211px 22.664px rgb(0 0 0 / 2%),
				0 2.19453px 12.0367px rgb(0 0 0 / 2%), 0 0.913195px 5.00873px rgb(0 0 0 / 1%);
			color: #f5f5f5;
		}
	}

	&.card-grey-theme {
		article {
			background-color: #f5f5f5;
			p {
				color: #616161;
			}
		}
	}

	&.zoom-out-img {
		.cardcenter-carditem-header {
			padding: 27px 0;
		}
		.cardcenter-carditem-headerimg {
			height: 44px;
		}
		img {
			width: auto !important;
			min-width: 0 !important;
		}
	}

	a.card {
		&-center {
			&-carditem {

				&:hover {
					.cardcenter-carditem-headerimg.hover-zoom {
						width: 100%;
						transform: scale(1.03);
					}
					.cardcenter-carditem-headerimg {
						transform: scale(1.15);
					}
				}
			}
		}
	}

	.card-center-carditem.hidden {
		display: none;
	}
}

@media screen and (max-width: 767px) {
	.display-image-in-mobile-view-false {
		.cardcenter-carditem-header {
			display: none;
		}
		.cardcenter-carditem-content {
			margin: 16px !important;
		}
	}

	.view-more.mobile-only {
		display: flex;
		max-width: 328px;
		margin: 12px auto auto;
		&.hidden {
			display: none;
		}
		.cmp-button__text {
			flex: 1;
		}
	}
}

@media (max-width: 991px) {
	.card-center-listcard {
		grid-template-columns: 1fr 1fr;

		&.small {
			display: grid;
			grid-template-columns: repeat(auto-fit,minmax(200px,1fr));
			grid-auto-rows: minmax(160px,auto);
		}

		&.no-padding-ul {
			.cardcenter-carditem-content {
				ul {
					padding-left: 0;
				}
			}
		}

		&.promotion {
			display: flex;
		}
		.card {
			&center-carditem {
				&-content {
					ul {
						list-style-type: disc;
						padding-left: 24px;
						margin-top: 8px;
					}
					margin-left: 16px;
					li {
						margin-left: 0;
					}
				}

				&-header {
					margin: 24px;
				}
			}

			&-promotion__item-wrapper {
				flex-grow: 0;
				max-width: 33.333333%;
				flex-basis: 33.333333%;
			}
		}
	}
}

@media (max-width: 767px) {
	.card-center-listcard {
		grid-template-columns: 1fr;
		grid-gap: 16px;
		gap: 16px;
		flex-wrap: wrap;

		.third & {
			grid-template-columns: 1fr;
		}

		&.promotion {
			overflow: auto;
			flex-wrap: nowrap;
			margin: -8px;
			width: calc(100% + 16px);

			&.stack-display {
				flex-wrap: wrap;
				.card-promotion__item-wrapper {
					width: 100%;
					max-width: unset;
					flex-basis: auto;
				}
			}
		}
		.card {
			&-center-carditem {
				> article {
					flex-direction: row;
					display: flex;
					align-items: center;
				}
			}

			&center-carditem-content {
				margin: 16px;
				margin-left: 0;
				width: 100%;
			}

			&center-carditem-header {
				height: 65px;
				margin: 24px;
			}

			&center-carditem-headerimg {
				width: 80px;
			}
			&-promotion__item {
				max-width: 77.2%;
				flex: 0 0 77.2%;
				flex-direction: column;
			}
		}
	}
}

.cmp-article-card-cross {
	.view-more {
		max-width: 328px;
    	margin: 24px auto auto;
    	text-align: center;
	}

	.load-more__button {
		display: inline-flex;
		outline: none;
		border: none;
		cursor: pointer;
		align-items: center;
		text-decoration: none;
		justify-content: space-between;
		width: inherit;
		grid-gap: 12px;
		gap: 12px;
		background-color: inherit;
		padding: 0;
		color: #000;
		font-weight: 600;
	}
}