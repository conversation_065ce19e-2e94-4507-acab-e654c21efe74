$inspire_color_1: #DF7F45;
$inspire_color_2: #3E8E87;
$inspire_color_3: #9B7C29;
$inspire_color_4: #14254C;

@keyframes slowShow {
  0% {
    transform: translateX(-400px);
    opacity: 0;
  }
  100% {
    transform: translateX(0px);
    opacity: 1;
  }
}

@keyframes fadeInBottom {
  0% {
    -webkit-transform: translateY(40%);
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Inspire Exclusive Component */
.inspire-exclusive {
    .title-cmp__title {
        font-weight: 600;
        line-height: 24px;
        font-size: 32px;
        letter-spacing: 0;
    }

    .tcb-content-container {
      margin: 0 !important;
    }

    .inspire-exclusive__container[responsive-type="desktop"] {
        display: flex;
        justify-content: space-between;
        width: 100%;
      }

       .inspire-exclusive__container[responsive-type="mobile"] {
        display: none;
      }

      .inspire-exclusive__hidden-content {
        display: none;
      }

       .inspire-exclusive__tab-menu {
        display: flex;
        flex-direction: column;
        width: 30%;
      }

      .tab-menu-item {
        width: 100%;
        transition-duration: 300ms;
        padding: 24px;
        border-radius: 20px;
        box-shadow: 0 0 #0000, 0 0 #0000, 0 0 16px rgba(0, 0, 0, 0.05);
        cursor: default;

        &:not(.active) {
          cursor: pointer;
        }
      }

      .tab-menu-item:nth-of-type(4n + 1):hover {
        background-color: $inspire_color_1 !important;
      }

      .tab-menu-item:nth-of-type(4n + 2):hover {
        background-color: $inspire_color_2 !important;
      }

      .tab-menu-item:nth-of-type(4n + 3):hover {
        background-color: $inspire_color_3 !important;
      }

      .tab-menu-item:nth-of-type(4n):hover {
        background-color: $inspire_color_4 !important;
      }

       .tab-menu-item.active, .tab-menu-item.hover {
        box-shadow: none;
      }

       .tab-menu-item.active h3, .tab-menu-item:hover h3 {
      color: var(--primary-background) !important;
      }

       .tab-menu-item h3 {
        line-height: 1.25;
        text-transform: uppercase;
        font-weight: 800;
        font-size: 40px;
        margin: 0;
        max-width: 80%;
      }

       .inspire-exclusive__tab-list {
        width: 65%;
        display: none;
      }

      .inspire-exclusive__tab-list.active {
        display: block;
        opacity: 1 !important;
        margin: -20px 0 -20px 0;
      }

      .inspire-exclusive__tab-list.active section {
        -webkit-animation-duration: 0.5s;
        -webkit-animation-name: fadeInBottom;
      }

      .inspire-privilege__container2 {
        padding-bottom: 0 !important;
      }

      .inspire-exclusive__tab-list:nth-of-type(4n + 2) .bottom-separator {
        border-bottom: 1px solid $inspire_color_1;
      }

      .inspire-exclusive__tab-list:nth-of-type(4n + 3) .bottom-separator {
        border-bottom: 1px solid $inspire_color_2;
      }

      .inspire-exclusive__tab-list:nth-of-type(4n) .bottom-separator {
        border-bottom: 1px solid $inspire_color_3;
      }

      .inspire-exclusive__tab-list:nth-of-type(4n + 1) .bottom-separator {
        border-bottom: 1px solid $inspire_color_4;
      }

      .inspire-exclusive__tab-menu-mobile .tab-menu-mobile-item:nth-of-type(4n + 1) .bottom-separator {
        border-bottom: 1px solid $inspire_color_1;
      }

      .inspire-exclusive__tab-menu-mobile .tab-menu-mobile-item:nth-of-type(4n + 2) .bottom-separator {
        border-bottom: 1px solid $inspire_color_2;
      }

      .inspire-exclusive__tab-menu-mobile .tab-menu-mobile-item:nth-of-type(4n + 3) .bottom-separator {
        border-bottom: 1px solid $inspire_color_3;
      }

      .inspire-exclusive__tab-menu-mobile .tab-menu-mobile-item:nth-of-type(4n) .bottom-separator {
        border-bottom: 1px solid $inspire_color_4;
      }

      div .inspireprivilege:last-child .bottom-separator {
        border: none !important;
      }

       .tab-list__content-item {
        transform: translate(0px, 0px);
        margin-bottom: 24px;
      }

       .content-item__title {
        line-height: 24px;
        font-weight: 600;
        margin-bottom: 32px;
      }

       .content-item__container {
        display: flex;
        align-items: flex-start;

        >.info-link {
            display: none;
        }
      }

      [responsive-type="desktop"] .content-item__container {
        margin-bottom: 20px;
      }

      [responsive-type="mobile"] .content-item__container {
        flex-direction: column-reverse;
      }

       .content-item__multi-picture,
       .content-item__picture {
        border-radius: 8px;
        width: 100%;
        position: relative;
        margin: 0;
      }

       [responsive-type="mobile"] .content-item__multi-picture,
       [responsive-type="mobile"] .content-item__picture {
        width: 100%;
      }

       .content-item__multi-picture picture,
       .content-item__picture picture {
        justify-content: center;
        width: 100%;
      }

       .content-item__picture .swiper-slide {
        width: 300px;
      }

       .content-item__multi-picture .swiper-slide {
        // margin-right: 8px;
        width: auto;
      }

      .swiper-wrapper {
        transition-timing-function: ease-out;
      }

       .content-item__image {
        object-fit: contain;
        border-radius: 8px;
        width: 100%;
        height: auto;
      }

       .content-item__picture[is-swiper="1"] .content-item__image {
        height: 176px;
      }

       .content-item__multi-picture .content-item__image {
        height: 100px;
        transition: all 0.2s ease-in-out;
      }

    .swiper-button {
        display: flex;
        align-items: center;
        justify-content: center;
    }
       .content-item__picture .swiper-button-prev,
       .content-item__picture .swiper-button-next {
        border-radius: 50%;
        transition: all 0.2s ease-in-out;
        width: 30px;
        height: 30px;
        background-color: var(--primary-background);
        color: var(--accent);
        border: none;
        cursor: pointer;
        outline: 0;
      }

       .content-item__picture .swiper-button-prev {
            position: absolute;
            top: 40%;
            z-index: 2;
            left: 0;
            &::after {
                font-size: 16px;
                font-weight: bolder;
                width: 26px;
                height: 25px;
                content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-left-red.png);
            }
       }

       .content-item__picture .swiper-button-next {
            position: absolute;
            top: 40%;
            z-index: 2;
            right: 0;

            &::after {
                font-size: 16px;
                font-weight: bolder;
                content: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/arrow-right-red.png);
                width: 21px;
                height: 24px;
            }
       }

       .content-item__text {
        margin-left: 10%;
      }

       [responsive-type="mobile"] .content-item__text {
        width: 100%;
        margin-left: 0;
        margin-bottom: 24px;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        gap: 15px;
      }

       .inspire-exclusive__tab-list .list-item {
        display: flex;
        margin-bottom: 24px;
        margin-left: 8px;
        margin-right: 8px;
      }

       .list-item__image {
        align-items: center;
        width: 44px;
        height: 44px;
        margin-right: 20px;
        margin-bottom: 8px;
      }

       .list-item__image img {
        object-fit: contain;
        width: 100%;
        width: 100%;
      }

       .list-item__content h4 {
        color: rgb(97 97 97 / 1);
        line-height: 24px;
        font-weight: 400;
        font-size: 16px;
      }

       .info-link {
        display: flex;
        align-items: center;
        line-height: 24px;
        font-weight: 500;
        justify-content: flex-start;
        &:hover {
            color: unset;
            img {
                transform: translate(5px, 0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
            }
        }
        img {
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 300ms;
            max-width: 16px;
            max-height: 15px;
        }
      }

       [responsive-type="mobile"] .info-link {
        margin-top: 20px;
      }

       .info-link__icon-container {
        width: 16px;
        height: 16px;
        margin-left: 12px;
      }

       .info-link__icon-container img {
        width: 100%;
        height: 100%;
      }

       .inspire-exclusive__tab-menu-mobile {
        width: 100%;
      }

      .tab-menu-mobile-item {
        width: 100%;
        position: relative;
        opacity: 0;
        min-height: 200px;
        animation-duration: 1s;
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      }

      .tab-menu-mobile-item.animate-slow-show {
        animation-name: slowShow;
        opacity: 1 !important;
      }

       .tab-menu-mobile-item:nth-child(odd) {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
      }

       .tab-menu-mobile-item:nth-child(even) {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-mobile-content::before {
        content: "";
        border-radius: 20px;
        width: 100%;
        height: 130px;
        z-index: 10;
        position: absolute;
        pointer-events: none;
        background-color: inherit;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-mobile-content::after {
        content: "";
        border-radius: 20px;
        width: 100%;
        height: 130px;
        z-index: 10;
        top: 35%;
        position: absolute;
        pointer-events: none;
        background-color: inherit;
        transform: translate(0, 0) rotate(0) skewX(0) skewY(5deg) scaleX(1) scaleY(1);
      }

       .tab-menu-mobile-item:nth-child(even) .tab-menu-mobile-content::before {
        content: "";
        border-radius: 20px;
        width: 100%;
        height: 130px;
        z-index: 10;
        position: absolute;
        pointer-events: none;
        background-color: inherit;
        transform: translate(0, 0) rotate(0) skewX(0) skewY(5deg) scaleX(1) scaleY(1);
        bottom: 35%;
      }

       .tab-menu-mobile-item:nth-child(even) .tab-menu-mobile-content::after {
        content: "";
        border-radius: 20px;
        width: 100%;
        height: 130px;
        z-index: 10;
        position: absolute;
        pointer-events: none;
        background-color: inherit;
      }

       .tab-menu-mobile-content {
        display: flex;
        width: 100%;
        justify-content: space-between;
        z-index: 12;
        cursor: pointer;
        border-radius: 20px;
        position: absolute;
        color: var(--primary-background);
      }

       .tab-menu-mobile-content h3 {
        margin: 0;
        font-size: 30px;
        line-height: 1.25;
        text-transform: uppercase;
        font-weight: 800;
        z-index: 11;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-mobile-content {
        flex-direction: row-reverse;
        height: 150px;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-mobile-content h3 {
        padding-right: 48px;
        min-height: 150px;
        text-align: right;
        padding-top: 24px;
        font-family: "SFProDisplay", "sans-serif";
        font-size: 32px;
        max-width: 80%;
      }

       .tab-menu-mobile-item:nth-child(even) .tab-menu-mobile-content {
        top: 50px;
      }

       .tab-menu-mobile-item:nth-child(even) .tab-menu-mobile-content h3 {
        padding-left: 48px;
        text-align: left;
        font-family: "SFProDisplay", "sans-serif";
        font-size: 32px;

        @include minXs {
          padding-left: 2rem;
        }
      }

       .dropdown-title-icon {
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        z-index: 11;
      }

       .tab-menu-mobile-item:nth-child(odd) .dropdown-title-icon {
        justify-content: flex-start;
        position: absolute;
        top: 16px;
        left: 0;
        padding: 24px;
      }

       .tab-menu-mobile-item:nth-child(even) .dropdown-title-icon {
        padding-right: 24px;

        @include minXs {
          position: absolute;
          padding-right: 1rem;
          top: 3rem;
          right: 0;
        }
      }

       .dropdown-title-icon p {
        font-size: unset;
        font-weight: 400;
        font-family: "SFProDisplay", "sans-serif";
        line-height: 1.5;
        white-space: nowrap;
        margin-bottom: 4px;
      }

       .tab-list-mobile {
        max-width: 100%;
        position: relative;
        display: none;
        background-color: var(--primary-background);
        z-index: 11;
      }

       .tab-menu-mobile-item:nth-child(even) .tab-list-mobile {
        border-radius: 20px;
      }

       .tab-menu-mobile-item.active .tab-list-mobile {
        display: block;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-list-mobile {
        padding-top: 210px;
        height: auto;
      }

       .tab-menu-mobile-item:nth-child(even) .tab-list-mobile {
        padding-top: 130px;
        padding-bottom: 70px;
        top: 70px;
      }

       .content-item__primary {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
        width: 50%;
      }

       .tab-menu-collapse-icon {
        color: var(--primary-background);
        border-radius: 20px;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-collapse-icon {
        min-height: 100px;
        position: relative;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-collapse-icon::before {
        content: "";
        background-color: var(--primary-background);
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
        width: 100%;
        height: 45px;
        position: absolute;
        pointer-events: none;
      }

       .tab-menu-mobile-item:nth-child(odd) .tab-menu-collapse-icon::after {
        content: "";
        background-color: var(--primary-background);
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
        width: 100%;
        height: 80px;
        position: absolute;
        pointer-events: none;
      }

       .tab-menu-mobile-item.active:nth-child(odd) .tab-menu-collapse-icon::after {
        transform: translate(0, 0) rotate(0) skewX(0) skewY(5deg) scaleX(1) scaleY(1);
      }

       .tab-menu-mobile-item:nth-child(even) .tab-menu-collapse-icon {
        min-height: 80px;
        margin-bottom: 15px;
        position: relative;
      }

       .collapse-icon-content {
        bottom: 24px;
        right: 24px;
        position: absolute;
        z-index: 11;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

       .tab-menu-mobile-item.active .dropdown-title-icon svg,
       .collapse-icon-content svg {
        transform: translate(0, 0) rotate(180deg) skewX(0) skewY(0) scaleX(1) scaleY(1);
        cursor: pointer;
      }

      .cmp-tabs__tablist {
        grid-gap: 0;
        row-gap: 24px;
      }

      .inspire-exclusive__container[responsive-type="mobile"] {

        .tab-list-mobile__content-item {
            padding: 0 16px;
        }

        .content-item__text {
            &.content-row {
                justify-content: center;
                .list-item {
                    flex-direction: row;
                }
            }

            .list-item {
                display: flex;
                flex-direction: column;
                .list-item__content {
                    h4,p {
                        font-size: 14px;
                        text-transform: unset;
                    }
                }
                .list-item__image {
                    margin-right: 10px;
                }
            }
        }
      }
}
@media screen and (max-width: 1025px) {
  .inspire-exclusive .tab-menu-item h3 {
    font-size: 32px !important;
  }
}

@media screen and (max-width: 767px) {
  .inspire-exclusive .title-cmp__title {
    padding: 0;
    margin-bottom: 32px;
    font-size: 24px;
  }

  .inspire-exclusive .inspire-exclusive__container[responsive-type="desktop"] {
    display: none;
  }

  .inspire-exclusive .inspire-exclusive__container[responsive-type="mobile"] {
    display: block;
    margin: 0 -5%;

    .content-item__primary {
      width: 100%;
    }
  }

  /* inspire slider */
  .inspire-slider-component .swiper {
    padding-left: 20px;
    padding-right: 20px;
    margin-left: 36px;
    margin-right: 36px;
  }

  .inspire-slider-component .swiper-button-next {
    right: 5px;
  }

  .inspire-slider-component .swiper-button-prev {
    left: 5px;
  }

  .inspire-slider-component .head-title {
    font-size: 24px;
    padding-left: 6px;
  }

  .inspire-slider-component .item-img .btn-video svg {
    width: 47px;
    height: 47px;
  }

  .tab-item_list.tcb-tab-content[data-tab-style="image-slider"] {
    height: 700px;
  }

  .content-item__container {
    .tcb-button.tcb-button--link.tcb-button--border-on-mobile {
        border: unset;
        display: flex;
        place-content: center;
        border: unset;
    }


    >.info-link {
        display: none;
    }

  }
}
/* End of Inspire Exclusive Component */

@media (max-width: 976px) {
    .inspire-exclusive {
        .inspire-exclusive__container[responsive-type="desktop"] {
            .tab-menu-item {
                h3 {
                    font-size: 24px !important;
                }
            }

            .content-item__container {
                display: flex;
                flex-direction: column-reverse;
                flex: 1;

                >.info-link {
                    display: flex;
                    order: 2;
                    display: flex;
                    align-self: center;
                }

                .content-item__picture, .content-item__multi-picture {
                    order: 1;
                    width: 100%;
                }
                .content-item__text {

                  &.content-row {
                    justify-content: center;
                    .list-item {
                        flex-direction: row;
                    }
                  }
                    order: 0;
                    width: 100%;
                    margin-left: unset;
                    display: flex;
                    justify-content: center;
                    gap: 15px;
                    .info-link {
                        display: none;
                    }

                    .benefit-feature__list-items {
                        display: flex;
                        .list-item {
                            flex: 1;
                        }
                    }
                  .list-item {
                      display: flex;
                  }
                }

                .content-item__primary {
                  width: 100%;
                  display: flex !important;
                  flex-direction: column-reverse;
                  align-items: center;
                  margin-bottom: 25px;
                  .info-link {
                      display: flex;
                      margin-top: 20px;
                  }
                }

                .list-item-count-1 {
                  .list-item {
                      width: 100%;
                  }
                }

                .list-item-count-2 {
                  .list-item {
                      width: 50%;
                  }
                }

                .list-item-count-3 {
                  .list-item {
                      width: 33%;
                      flex-direction: column;
                      align-items: flex-start;
                  }
                }

                .list-item-count-4 {
                  .list-item {
                      width: 25%;
                      flex-direction: column;
                  }
                }
            }
        }
    }
}