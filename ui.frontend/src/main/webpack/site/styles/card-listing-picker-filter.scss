$rem08: 0.5rem;
$rem16: 1rem;
$rem20: 1.25rem;
$rem24: 1.5rem;

.credit-card-listing-compare {
  .card-listing-picker-filter__content {
    margin-bottom: 0;
    padding: 2rem 0;

    .credit-card-listing__container {
      .wrapper-credit-card-listing-title {
        display: flex;
        flex-direction: column;
        gap: $rem08;
        margin-right: $rem16;
        max-width: 14.375rem;

        h6 {
          white-space: normal;
        }

        .credit-card-listing__title {
          font-size: $rem20;
          line-height: $rem24;
          font-weight: 600;
          margin-right: 0;
        }

        .credit-card-listing__desciption {
          font-size: $rem16;
          line-height: $rem24;
          font-weight: 400;
        }
      }

      .credit-card-listing__button {
        padding: 0.75rem 1.5rem;
        @include xs {
          padding: 0.5rem 1.25rem;
        }
      }

      .credit-card-listing__items {
        flex-wrap: wrap;
        @include xs {
          gap: 0.5rem;
        }
      }

      @include maxLgSemi {
        flex-direction: column;
        align-items: flex-start;
        gap: $rem16;

        .wrapper-credit-card-listing-title {
          margin-right: 0;
          max-width: 100%;
        }

        .credit-card-listing__items {
          max-width: 100%;
          flex-wrap: nowrap;
        }
      }
    }
  }

  .compare-choosing {
    position: relative;
    z-index: 100;

    &__sticky-card {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      z-index: 1;
      background-color: var(--primary-white);
      padding: 1.25rem 0;
      transition: all 0.4s ease-in-out;
    }

    &__container {
      width: 100%;
      display: flex;
      box-sizing: inherit;
      margin-left: auto;
      margin-right: auto;
      align-items: center;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    &__list-card {
      display: flex;
      flex-grow: 0;
      max-width: 33.333333%;
      flex-basis: 30%;
    }

    &__list-card .has-card {
      border: none;
      flex: unset;
      max-width: 7.25rem;
      box-shadow: none;
    }

    .list-card__item {
      flex: 0 0 7.25rem;
      max-width: 7.25rem;
      height: 4.688rem;
      border: 0.125rem dashed var(--secondary-mid-grey-60);
      border-radius: 0.5rem;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: inherit;

      &:not(:last-child) {
        margin-right: 1.5rem;
      }

      &-image {
        box-shadow: 0 33px 180.063rem rgb(0 0 0 / 4%),
          0 13.7866px 75.614.688rem rgb(0 0 0 / 3%),
          0 7.37098px 40.4287px rgb(0 0 0 / 2%),
          0 4.1321.063rem 22.664px rgb(0 0 0 / 2%),
          0 2.19453px 12.0367px rgb(0 0 0 / 2%),
          0 0.913195px 5.00873px rgb(0 0 0 / 1%);
        border-radius: 0.375rem;
        display: none;
        width: 100%;
        overflow: hidden;
        height: 100%;
        box-sizing: inherit;
      }

      &-image span {
        box-sizing: border-box;
        display: inline-block;
        overflow: hidden;
        width: 7.6625rem;
        height: 4.825rem;
        background: none;
        opacity: 1;
        border: 0;
        margin: 0;
        padding: 0;
        position: relative;
      }
      &-image span.list-card__item-image-span {
        width: 100%;
      }

      &-image img {
        position: absolute;
        inset: 0;
        box-sizing: border-box;
        padding: 0;
        border: none;
        margin: auto;
        display: block;
        width: 0;
        height: 0;
        min-width: 100%;
        max-width: 100%;
        min-height: 100%;
        max-height: 100%;
        object-fit: contain;
        object-position: center center;
      }
    }

    .remove-button {
      width: 1.5rem;
      height: 1.5rem;
      background-color: var(--primary-red);
      border-radius: 50%;
      display: none;
      align-items: center;
      justify-content: center;
      color: var(--primary-white);
      position: absolute;
      top: -0.75rem;
      right: -0.75rem;
      cursor: pointer;
      box-sizing: inherit;

      img {
        width: 1.125rem;
        height: 1.125rem;
      }
    }

    .add-panel {
      width: 2rem;
      height: 2rem;
      background-color: var(--secondary-light-grey-60);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--gray-600);
      box-sizing: inherit;

      img {
        filter: grayscale(100%);
      }
    }

    .has-card {
      border: none;

      .list-card__item-image {
        display: block;
      }

      .remove-button {
        display: flex;
      }

      .add-panel {
        display: none;
      }
    }

    .compare-button {
      flex-grow: 0;
      max-width: 25%;
      flex-basis: 23.6%;

      &__link {
        justify-content: space-between;
        background-color: var(--primary-black);
        color: var(--primary-white);
        position: relative;
        display: inline-flex;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease-in;
        align-items: center;
        grid-gap: 0.75rem;
        gap: 0.75rem;
        min-width: max-content;
        width: 100%;
        z-index: 1;
        font-weight: 600;
        line-height: 1.5rem;

        &[disabled] {
          color: var(--secondary-mid-grey-100);
          pointer-events: none;
          background-color: var(--cta-disabled);
          border: 0.063rem solid var(--cta-disabled);
        }

        &:hover {
          background: var(--secondary-grey-60);
        }

        &-icon {
          display: flex;
          align-items: center;
          margin-left: 0;
          transition: all 0.3s ease-in-out;
          color: var(--primary-white);
          filter: brightness(0) invert(1);
        }
      }
    }

    @include maxSm {
      &__sticky-card {
        padding: $rem20 0;
      }

      &__list-card {
        max-width: 100%;
        flex-basis: 100%;
        overflow-x: scroll;
        padding-top: 0.5rem;
      }

      &__list-card::-webkit-scrollbar {
        display: none;
      }

      .remove-button {
        top: -0.5rem;
        right: -0.5rem;
      }

      .compare-button {
        max-width: 100%;
        flex-basis: 100%;
        padding-top: 2.5rem;

        &__link {
          padding: 0.75rem 1rem;
        }
      }
    }
  }
}
