
/* Statistics Table Component */
.statistics-table-component, .financialhighlights {
    .select-checkbox-filter {
        padding-bottom: 12px;
      }


    .select-options {
        .select {
            h6 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 20px;
            }
        }
    }

    .list-items {
        padding-top: 12px;
        @media (max-width: 575px) { 
            padding-top: 8px;
        }
    }
    
    .list-items .quarter-item {
    margin-top: 24px;
    }
    
    .list-items .title {
    color: #ed1b24;
    margin-bottom: 24px;
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.2;
    }
    
    .list-items .note {
    margin-top: 8px;
    }
    
    .list-items .note p {
    font-size: 1rem;
    line-height: 16px;
    font-style: italic;
    font-weight: bold;
    }
    
    .table {
    background-color: transparent;
    width: 100%;
    overflow-x: auto;
    box-shadow: 0px 2px 1px -1px rgb(0 0 0 / 20%), 0px 1px 1px 0px rgb(0 0 0 / 14%), 0px 1px 3px 0px rgb(0 0 0 / 12%);
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.87);
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    }
    
    .table table {
    width: 100%;
    display: table;
    border-spacing: 0;
    border-collapse: collapse;
    }
    
    table th,
    table td {
    padding: 16px;
    font-size: 0.875rem;
    text-align: right;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    font-weight: 400;
    line-height: 1.43;
    border-bottom: 1px solid rgba(224, 224, 224, 1);
    letter-spacing: 0.01071em;
    }
    
    table th:first-child,
    table td:first-child {
    text-align: left;
    white-space: nowrap;
    }
    
    table tbody th {
    background-color: #fff;
    color: #ed1d24;
    border-color: #ed1d24;
    font-weight: 500;
    line-height: 1.5;
    text-wrap: nowrap;
    }
    
    table tbody tr {
    background-color: #fff;
    }
    
    table tbody td {
    border-color: #939598;
    }
    
    table .italic {
    font-style: italic;
    }
    
    table .highlight {
    background-color: #fef2f1;
    }
    
    .year-item.showed .quarter-item.showed {
    display: block;
    }
    
    .quarter-item {
    display: none;
    }

    .table tr td:nth-child(6),
    .table tr td:nth-child(8) {
        background: #fef2f1;
    }

    @media (max-width: 575px) {
        .select-checkbox-filter {
            display: flex;
            flex-direction: column;
            padding-bottom: 0px;
            .select-options {
                margin-left: unset;
            }
        }
    }
}

  /* End of Statistics Table Component */
