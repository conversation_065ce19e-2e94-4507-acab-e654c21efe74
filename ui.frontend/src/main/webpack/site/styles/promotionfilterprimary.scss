$rem08: 0.5rem;
$rem16: 1rem;
$rem20: 1.25rem;
$rem24: 1.5rem;

.promotionfilterprimary {
  .card-list-picker-filter__content {
    margin-bottom: 0;

    .credit-card-listing__container {
      gap: $rem20;

      .wrapper-credit-card-listing-title {
        display: flex;
        gap: $rem08;
        max-width: 14.375rem;

        .credit-card-listing__title {
          font-size: $rem16;
          line-height: $rem24;
          font-weight: 400;
          margin-right: 0;
          white-space: nowrap;

          > span {
            text-transform: lowercase;
          }
        }
      }

      .credit-card-listing__items {
        flex-wrap: wrap;
        gap: 0.75rem;

        .filter__item {
          .credit-card-listing__button {
            padding: 0.6875rem 1.4375rem;
            height: 3rem;

            &:not(.filter-selected) {
              background: unset;
              color: var(--secondary-grey-60);
            }

            @include xs {
              padding: 0.4375rem 0.75rem;
              border-radius: 2.0625rem;
              height: 2.5rem;
            }
          }
        }
      }
    }
    @include maxLgSemi {
      .credit-card-listing__container {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;

        .wrapper-credit-card-listing-title {
          max-width: 100%;
        }

        .credit-card-listing__items {
          max-width: 100%;
          flex-wrap: nowrap;
        }
      }
    }
  }

  .credit-card-listing__content {
    background-color: transparent;
  }
}
