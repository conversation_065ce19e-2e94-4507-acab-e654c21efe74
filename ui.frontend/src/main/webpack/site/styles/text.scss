.pl-20 {
  padding-inline-start: 20px;
}

li {
  &::before {
    background-color: var(--primary-black);
  }
  &::marker {
    color: var(--primary-black);
  }
  &.red-bullet::before {
    background-color: var(--primary-red);
  }
  &.red-bullet::marker {
    color: var(--primary-red);
  }
  &.blue-bullet::before {
    background-color: var(--primary-navy-blue);
  }
  &.blue-bullet::marker {
    color: var(--primary-navy-blue);
  }
  &.black-bullet::before {
    background-color: var(--primary-black);
  }
  &.black-bullet::marker {
    color: var(--primary-black);
  }
  &.white-bullet::before {
    background-color: var(--primary-white);
  }
  &.white-bullet::marker {
    color: var(--primary-white);
  }
  &.grey-bullet::before {
    background-color: var(--secondary-grey-60);
  }
  &.grey-bullet::marker {
    color: var(--secondary-grey-60);
  }
  &.gold-bullet::before {
    background-color: var(--primary-gold);
  }
  &.gold-bullet::marker {
    color: var(--primary-gold);
  }
}

li {
  &.marker-font-weight-normal::marker {
    font-weight: 400;
  }
}

.hover-underline:hover {
  text-decoration: underline;
}

.hover-color:hover {
  color: var(--primary-red);
}

.text {
  &-sm {
    font-size: 0.875rem; /* 14px */
    line-height: 1.25rem; /* 20px */
  }
  &-base {
    font-size: 1rem; /* 16px */
    line-height: 1.5rem; /* 24px */
  }
  &-lg {
    font-size: 1.5rem; /* 24px */
    line-height: 2.25rem; /* 36px */
  }
  &-xl {
    font-size: 1.75rem; /* 28px */
    line-height: 2.25rem; /* 36px */
  }
  &-2xl {
    font-size: 2rem; /* 32px */
    line-height: 2.5rem; /* 40px */
  }
}
.font {
  &-weight-unset {
    font-weight: unset;
  }
  &-light {
    font-weight: 300;
  }
  &-normal {
    font-weight: 400;
  }
  &-medium {
    font-weight: 500;
  }
  &-semibold {
    font-weight: 600;
  }
  &-bold {
    font-weight: 700;
  }
  &-extrabold {
    font-weight: 800;
  }
  &-black {
    font-weight: 900;
  }
}

.line-height {
  &-unset {
    line-height: unset;
  }
  &-sm {
    line-height: var(--heading2-line-height);
  }
  &-20 {
    line-height: 1.25rem;
  }
  &-21 {
    line-height: 21px;
  }
}

.heading-style {
  &-h1 {
    *.tcb-title {
      font-size: 2rem !important; /* 32px */
      line-height: 2.5rem !important; /* 40px */
      font-weight: 300 !important;
    }

    h1,h2,h3,h4,h5,h6 {
      font-size: 2rem !important; /* 32px */
      line-height: 2.5rem !important; /* 40px */
      font-weight: 300 !important;
    }
  }
  &-h2 {
    *.tcb-title {
      font-size: 1.75rem !important; /* 28px */
      line-height: 2.25rem !important; /* 36px */
      font-weight: 300 !important;
    }

    h1,h2,h3,h4,h5,h6 {
      font-size: 1.75rem !important; /* 28px */
      line-height: 2.25rem !important; /* 36px */
      font-weight: 300 !important;
    }
  }
  &-h3 {
    *.tcb-title {
      font-size: 1.5rem !important; /* 24px */
      line-height: 2.25rem !important; /* 36px */
      font-weight: 600 !important;
    }

    h1,h2,h3,h4,h5,h6 {
      font-size: 1.5rem !important; /* 24px */
      line-height: 2.25rem !important; /* 36px */
      font-weight: 600 !important;
    }
  }
  &-h4,
  &-h5,
  &-h6,
  &-p {
    *.tcb-title {
      font-size: 1rem !important; /* 16px */
      line-height: 1.5rem !important; /* 24px */
      font-weight: 400 !important;
    }
    h1,h2,h3,h4,h5,h6 {
      font-size: 1rem !important; /* 16px */
      line-height: 1.5rem !important; /* 24px */
      font-weight: 400 !important;
    }
  }
}

//custom css for image on /khach-hang-ca-nhan/chi-tieu/the/the-tin-dung/tra-gop
.custom-image-wrap {
  @include maxSm {
    overflow-x: auto;
  }
  img {
    width: 100%;
    @include maxSm {
      width: 900px;
    }
  }
}


@include maxSm {
  .text {
    &-base-mb-style {
      h1,h2,h3,h4,h5,h6 {
        font-size: 1.125rem !important; /* 18px */
      }
    }
    &-lg-mb-style {
      h1,h2,h3,h4,h5,h6 {
        font-size: 1.5rem !important; /* 24px */
      }
    }
  }
}

@include maxSm {
  .text {
    &-base-mb {
      font-size: 1.125rem; /* 18px */
    }
    &-lg-mb {
      font-size: 1.5rem; /* 24px */
    }
  }

  .font {
    &-light-mb {
      font-weight: 300;
    }
    &-normal-mb {
      font-weight: 400;
    }
    &-medium-mb {
      font-weight: 500;
    }
    &-semibold-mb {
      font-weight: 600;
    }
    &-bold-mb {
      font-weight: 700;
    }
    &-extrabold-mb {
      font-weight: 800;
    }
    &-black-mb {
      font-weight: 900;
    }
  }
}