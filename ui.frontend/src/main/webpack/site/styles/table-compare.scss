$color_1: var(--gray-600);
$background-color_1: var(--secondary-mid-grey-80);
$background-color_2: var(--primary-red);

.table-compare-container {
	h3 {
		font-size: 14px;
		font-weight: 600;
		line-height: 21px;
	}
	.table-compare-content-col {
		display: flex;
		flex-direction: row;
		width: 100%;
		height: 100%;
		column-gap: 20px;
	}
	.table-compare-item-col {
		display: flex;
		flex-direction: column;
		text-align: center;
		width: 100%;
		&.cards-1 {
			.width-30-70 & { 
				flex: 0 0 70%;
				max-width: 70%;
			}
		}
		.table-compare-item-row {
			&:not(.header, .first-row) {
				padding: 16px 0;
				margin: 0 16px;
				border-bottom: 2px solid var(--secondary-light-grey-100);
			}
		}
		.table-compare-item-row.first-row{
			display: flex;
    		align-items: center;
			justify-content: center;
			position: sticky;
			* {
				font-size: 14px;
				margin: 0;
			}
		}

		span {
			position: absolute;
			z-index: 1;
			top: 50%;
			left: 50%;
			transform: translateX(-50%) translateY(-50%);
			width: 100%;
			font-weight: 600;
			font-size: 0.875rem;
			line-height: 1.5;
			letter-spacing: 2px;
			text-transform: uppercase;
		}
		picture {
			display: block;
			overflow: hidden;
			width: 100%;
			height: 100%;
			min-height: 63px;
		}
		img {
			min-width: 100%;
			max-width: 100%;
			min-height: 100%;
			max-height: 100%;
			object-fit: cover;
			object-position: center center;
			border-radius: 8px 8px 0 0;
		}

		.header {
			position: relative;
			text-align: center;
			min-height: 63px;
			top: 0;
			span {
				position: absolute;
				z-index: 1;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
				width: 100%;
				font-weight: 600;
				font-size: 0.875rem;
				line-height: 1.5;
				letter-spacing: 2px;
				text-transform: uppercase;
			}
			picture {
				display: block;
				overflow: hidden;
				width: 100%;
				height: 100%;
				min-height: 63px;
			}
			img {
				min-width: 100%;
				max-width: 100%;
				min-height: 100%;
				max-height: 100%;
				object-fit: cover;
				object-position: center center;
				border-radius: 8px 8px 0 0;
			}
			p {
				font-size: 0.875rem;
				font-weight: 600;
			}
		}
		.moved-title {
			display: none;
		}
	}
	.table-compare-item-col.first-col {
		.first-row {
			border-bottom: none;
			min-height: 63px;
			letter-spacing: 2px;
		}
		.table-compare-item-row {
			&:not(.first-row) {
				text-align: start;
			}
		}
	}
	.first-row-heading-text{
		padding: 0 16px;
		letter-spacing: 1px !important;
	}
	&__note {
		margin-top: 1rem;
		color: #616161;
	}
}
@media (max-width: 991px) {
	.table-compare-container {
		.table-compare-item-col {
			max-width: unset !important;
			.table-compare-item-row {
				&:not(.header, .first-row) {
					display: flex;
					flex-direction: row;
					padding: 0;
					margin: 0 1rem;
					border-bottom: none;

					p {
						flex: 0 0 50%;
						padding: 1rem 0;
						margin-right: 1rem;
						border-bottom: 2px solid var(--secondary-light-grey-100);
					}
				}
				&.last-row {
					p {
						border-bottom: none;
					}
				}
			}
			.moved-title {
				display: unset;
				color: $color_1;
				font-weight: bold;
			}
			.mobile-left{
				text-align: start;
			}
			.header{
				span{
					padding: 0;
				}
			}
		}
		.table-compare-content-col {
			flex-direction: column;
		}
		.slick-dots {
			display: flex;
			flex-direction: row;
			justify-content: center;
			width: 100%;
			text-align: center;
			margin-top: 15px;
			padding-left: 0;
			li {
				button {
					display: none;
				}
				margin: 0 5px;
				width: 8px;
				height: 8px;
				cursor: unset;
				font-size: 0;
				line-height: 0;
				display: block;
				border: 0;
				outline: none;
				border-radius: 50%;
				background-color: $background-color_1;
			}
			.slick-active {
				background-color: $background-color_2;
			}
		}
	}
}
