// TCB System Design: https://www.figma.com/file/mRqRAyrB5ewJCTMLZGt8VO/TCB_System_Design?type=design&node-id=6-5620&mode=design&t=yuzFsOhmlHkYLgD3-0
// Usecases: https://confluence.techcombank.com.vn/display/MS1DM/AEM+-+Text+Style

//== Base
:root {
  --display: 56px;
  --display-line-height: 84px;

  --display-mobile: 40px;
  --display-line-height-mobile: 60px;

  --stats: 40px;
  --stats-line-height: 60px;

  --text-hero: 40px;
  --text-hero-line-height: 60px;

  --heading1-font-size: 32px;
  --heading1-font-weight: 300;
  --heading1-line-height: 40px;

  --heading2-font-size: 28px;
  --heading2-font-weight: 300;
  --heading2-line-height: 125%;

  --heading3-font-size: 24px;
  --heading3-font-weight: 600;
  --heading3-line-height: 36px;

  --heading4-font-size: 24px;
  --heading4-font-weight: 300;
  --heading4-line-height: 36px;

  --mobile-heading1-font-size: 32px;
  --mobile-heading1-font-weight: 300;
  --mobile-heading1-line-height: 40px;

  --mobile-heading2-font-size: 28px;
  --mobile-heading2-font-weight: 300;
  --mobile-heading2-line-height: 35px;

  --mobile-heading3-font-size: 24px;
  --mobile-heading3-font-weight: 300;
  --mobile-heading3-line-height: 36px;

  --mobile-heading4-font-size: 24px;
  --mobile-heading4-font-weight: 300;
  --mobile-heading4-line-height: 36px;

  --highlight: 18px;
  --hightlight-line-height: 27px;

  --default: 16px;
  --default-line-height: 24px;

  --contenttitle: 16px;
  --contenttitle-line-height: 24px;

  --contentbody: 16px;
  --contentbody-line-height: 24px;
  --contentbody-fontweight: 300;

  --subtitle: 20px;
  --subtitle-line-height: 25px;

  --wtypecaptionsmall: 14px;
  --wtypecaptionsmall-line-height: 21px;
  --wtypecaptionsmall-letter-spacing: 2px;

  --contentfootnote: 16px;
  --contentfootnote-line-height: 24px;

  --note: 14px;
  --note-line-height: 14px;

  --subnote: 14px;
  --subnote-line-height: 21px;

  --typeinlinelink: 16px;
  --typeinlinelink-line-height: 24px;

  --subheader: 12px;
  --subheader-line-height: 18px;

  --table-header: 12px;
  --table-header-line-height: 18px;

  --table-default: 14px;
  --table-default-line-height: 21px;

  // Color

  --primary-black: #000000;
  --primary-red: #ed1b24;
  --primary-white: #ffffff;
  --primary-gold: #d6b973;
  --primary-navy-blue: #1b1564;
  --primary-sky-grey: #d6ebec;
  --primary-ivory: #F1EFE9;
  --primary-color-gray-500: #e3e4e5;

  --secondary-grey: #1c2629;
  --secondary-grey-100: #212121;
  --secondary-grey-80: #333333;
  --secondary-grey-60: #616161;
  --secondary-mid-grey-100: #a2a2a2;
  --secondary-mid-grey-80: #c5c5c5;
  --secondary-mid-grey-60: #dedede;
  --secondary-mid-grey-40: #f5f6f8;
  --secondary-light-grey-100: #e3e4e6;
  --secondary-light-grey-90: #f1f1f1;
  --secondary-light-grey-80: #f2f2f2;
  --secondary-light-grey-60: #f5f5f5;
  --secondary-gold: #ecd7b0;

  --cta-disabled: #d9d9d9;
  --cyan-blue: #0A84FF;
  --background-magenta: #f5f6f8;
  --cta-secondary: #404040;
}

// Light
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-Light.woff2")
    format("truetype");
  font-style: normal;
  font-weight: 300;
  font-display: swap;
}

// Regular
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-Regular.woff")
    format("truetype");
  font-style: normal;
  font-weight: 400;
  font-display: swap;
}

// Regular Italic
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-RegularItalic.otf")
    format("truetype");
  font-style: italic;
  font-weight: 400;
  font-display: swap;
}

// Medium
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-Medium.woff2")
    format("truetype");
  font-style: normal;
  font-weight: 500;
  font-display: swap;
}

// SemiBold
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-SemiBold.woff2")
    format("truetype");
  font-style: normal;
  font-weight: 600;
  font-display: swap;
}

// Bold
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-Bold.woff2")
    format("truetype");
  font-style: normal;
  font-weight: 700;
  font-display: swap;
}

// Light Italic
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-Light-Italic.woff2")
  format("truetype");
  font-style: italic;
  font-weight: 400;
  font-display: swap;
}

// Heavy
@font-face {
  font-family: "SF Pro Display";
  src: url("/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/fonts/SF-Pro-Display-Heavy.woff2")
    format("truetype");
  font-style: normal;
  font-weight: 800;
  font-display: swap;
}

html,
body {
  margin: 0;
  font-size: var(--body);
  line-height: var(--default-line-height);
  font-family: $font-family;
  color: var(--primary-black);
  background: $color-background;
  scroll-behavior: smooth;

  .dark {
    color: $color-foreground-dark;
    background: $color-background-dark;
  }
}

h1 {
  font-size: var(--heading1-font-size);
  font-weight: var(--heading1-font-weight);
  line-height: var(--heading1-line-height);
}

h2 {
  font-size: var(--heading2-font-size);
  font-weight: var(--heading2-font-weight);
  line-height: var(--heading2-line-height);
}

h3 {
  font-size: var(--heading3-font-size);
  font-weight: var(--heading3-font-weight);
  line-height: var(--heading3-line-height);
}

h4 {
  font-size: var(--heading4-font-size);
  font-weight: var(--heading4-font-weight);
  line-height: var(--heading4-line-height);
}

@media screen and (max-width: 767px) {
  h1 {
    font-size: var(--mobile-heading1-font-size);
    font-weight: var(--mobile-heading1-font-weight);
    line-height: var(--mobile-heading1-line-height);
  }
  h2 {
    font-size: var(--mobile-heading2-font-size);
    font-weight: var(--mobile-heading2-font-weight);
    line-height: var(--mobile-heading2-line-height);
  }
  h3 {
    font-size: var(--mobile-heading3-font-size);
    font-weight: var(--mobile-heading3-font-weight);
    line-height: var(--mobile-heading3-line-height);
  }
  h4 {
    font-size: var(--mobile-heading4-font-size);
    font-weight: var(--mobile-heading4-font-weight);
    line-height: var(--mobile-heading4-line-height);
  }
}

a {
  color: $color-link;
  text-decoration: none;
  .dark & {
    color: $color-link-dark;
  }
}

button,
input,
optgroup,
select,
textarea {
  font: inherit;
}

ul,
ol {
  padding-inline-start: 40px;
}

.highlight-bold {
  font-weight: bold;
  font-size: var(--highlight);
  line-height: var(--hightlight-line-height);
}

.highlight-regular {
  font-weight: normal;
  font-size: var(--highlight);
  line-height: var(--hightlight-line-height);
}

.default-bold {
  font-weight: 600;
  font-size: var(--default);
  line-height: var(--default-line-height);
}

.default-regular {
  font-weight: normal;
  font-size: var(--default);
  line-height: var(--default-line-height);
}

.content-title-bold {
  font-weight: bold;
  font-size: var(--contenttitle);
  line-height: var(--contenttitle-line-height);
}

.content-title-regular {
  font-weight: normal;
  font-size: var(--contenttitle);
  line-height: var(--contenttitle-line-height);
}

.content-body-bold {
  font-weight: bold;
  font-size: var(--contentbody);
  line-height: var(--contentbody-line-height);
}

.content-body-regular {
  font-weight: normal;
  font-size: var(--contentbody);
  line-height: var(--contentbody-line-height);
}

.subtitle-bold {
  font-weight: 600;
  font-size: var(--subtitle);
  line-height: var(--subtitle-line-height);
}

.subtitle-regular {
  font-weight: normal;
  font-size: var(--subtitle);
  line-height: var(--subtitle-line-height);
}

.wtype-caption-small-bold {
  font-weight: bold;
  font-size: var(--wtypecaptionsmall);
  line-height: var(--wtypecaptionsmall-line-height);
  letter-spacing: var(--wtypecaptionsmall-letter-spacing);
}

.wtype-caption-small-regular {
  font-weight: normal;
  font-size: var(--wtypecaptionsmall);
  line-height: var(--wtypecaptionsmall-line-height);
  letter-spacing: var(--wtypecaptionsmall-letter-spacing);
}

.content-footnote {
  font-style: italic;
  font-size: var(--contentfootnote);
  line-height: var(--contentfootnote-line-height);
}

.content-footnote {
  font-style: italic;
  font-size: var(--note);
  line-height: var(--note-line-height);
}

.subnote {
  font-style: normal;
  font-size: var(--subnote);
  line-height: var(--subnote-line-height);
}

.type-in-linelink {
  font-style: normal;
  font-size: var(--typeinlinelink);
  line-height: var(--typeinlinelink-line-height);
}

.subheader-bold {
  font-weight: 600;
  font-size: var(--subheader);
  line-height: var(--subheader-line-height);
}

.subheader-regular {
  font-weight: normal;
  font-size: var(--subheader);
  line-height: var(--subheader-line-height);
}

.table-header-bold {
  font-weight: 600;
  font-size: var(--table-header);
  line-height: var(--table-header-line-height);
}

.table-header-regular {
  font-weight: normal;
  font-size: var(--table-header);
  line-height: var(--table-header-line-height);
}

.table-default-bold {
  font-weight: 600;
  font-size: var(--table-default);
  line-height: var(--table-default-line-height);
}

.table-default-regular {
  font-weight: normal;
  font-size: var(--table-default);
  line-height: var(--table-default-line-height);
}

@mixin transform($transforms) {
	-webkit-transform: $transforms;
	-moz-transform: $transforms;
	-ms-transform: $transforms;
	transform: $transforms;
}

*[data-emptytext] {
  display: none;
}
