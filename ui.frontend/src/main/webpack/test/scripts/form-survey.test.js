import { describe, expect, jest, test } from '@jest/globals';

class TestFormSurvey {
  constructor() {
    require('../../site/scripts/form-survey');
  }
}

describe('test form survey', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    Array.each = jest.fn();
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        attr: jest.fn().mockReturnValue('multi'),
        length: 1,
        hide: jest.fn(),
        click: (callback) => callback(),
        find: jest.fn(),
      };
    });
    new TestFormSurvey();
    expect(document.readyState).toBe('complete');
  });
});
