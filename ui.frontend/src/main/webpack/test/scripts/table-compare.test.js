import { describe, expect, jest, test } from '@jest/globals';

class TestTableCompare {
  constructor() {
    require('../../site/scripts/table-compare');
  }
}

describe('TableCompare', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: callback => callback(),
        each: callback => callback(),
        width: jest.fn().mockReturnValue('200'),
        height: jest.fn().mockReturnValue('200'),
        find: () => {
          return {
            find: () => {
              return {
                each: callback => callback(),
                html: () => {
                },
                removeClass: () => {
                },
                detach: () => {
                },
                height: jest.fn().mockReturnValue('200'),
              };
            },
            addClass: () => {
            },
            each: callback => callback(),
            height: jest.fn().mockReturnValue('200'),
            html: () => {
            },
          };
        },
        not: () => {
          return {
            slick: () => {
            },
          };
        },
        resize: callback => callback(),
      };
    });

    new TestTableCompare();
    expect(document.readyState).toBe('complete');
  });
});
