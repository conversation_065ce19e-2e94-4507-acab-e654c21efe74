import { describe, expect, jest, test } from '@jest/globals';

class TestTcbDateFormat {
  constructor() {
    require('../../site/scripts/tcb-date-format');
  }
}

describe('TCBDateFormat', () => {
  test('should create an instance', () => {
    window.jQuery = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {};
    });
    window.$ = jest.fn().mockImplementation(() => {
      return {
        each: callback => callback(),
        text: () => {},
        html: () => {},
        find: () => {
          return {
            attr: () => {
              return true;
            },
            text: () => {
              return '%exchangeRateDateTime';
            },
            html: () => {},
            each: callback => callback(),
          };
        },
      };
    });

    new TestTcbDateFormat();
    expect(document.readyState).toBe('complete');
  });
});
