import { describe, expect, jest, test } from '@jest/globals';

class TestDatePicker {
  constructor() {
    require('../../site/scripts/date-picker');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    window.location = {
      href: 'https://techcombank.com/?cardId=1',
    };
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        each: (callback) => callback(),
        datepicker: () => {
          return {
            trigger: jest.fn(),
          };
        },
      };
    });
    new TestDatePicker();
    expect(document.readyState).toBe('complete');
  });
});
