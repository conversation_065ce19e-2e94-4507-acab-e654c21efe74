import { describe, expect, test, jest } from "@jest/globals";

window.validateForm = jest.fn().mockReturnValue(true);
class TestFormSubmit {
  constructor() {
    document.body.innerHTML = `
    <div class="tcbformcontainer">
        <form class="cmp-form">
        
        </form>
    </div>`;
    require("../../site/scripts/form-submit");
  }
}

describe("test form submit", () => {
  test("should document ready", () => {
    JSON.parse = jest.fn().mockReturnValue({
      webInteractions: {
        name: "submit button",
      },
    });
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        submit: (callback) =>
          callback({
            preventDefault: jest.fn(),
          }),
        each: (_, formElement) => {
          return {
            formElement: {
              id: "test",
            },
          };
        },
        attr: jest.fn(),
        find: () => {
          return {
            each: jest.fn(),
            val: jest.fn(),
            text: () => {
              return {
                addClass: jest.fn(),
              };
            },
            removeClass: jest.fn(),
          };
        },
        on: (event, input, callback) => callback(),
        val: () => {
          return {
            trim: jest.fn(),
          };
        },
        hasClass: jest.fn(),
        parents: () => {
          return {
            find: () => {
              return {
                data: () => {
                  return {
                    trackingClickInfoValue: "test",
                    trackingWebInteractionValue: "test",
                    trackingFormInfoValue: "test",
                    trackingUserInfoValue: "test",
                  };
                },
                text: () => {
                  return {
                    trim: jest.fn(),
                  };
                },
                attr: jest.fn(),
                trigger: jest.fn(),
              };
            },
          };
        },
        trigger: jest.fn(),
        prop: jest.fn(),
        serialize: jest.fn(),
      };
    });

    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({ Success: 200 });
        }
        if (typeof callback === "function") {
          error({ error: "test" });
        }
      });
    new TestFormSubmit();
    expect(document.readyState).toBe("complete");
  });
});
