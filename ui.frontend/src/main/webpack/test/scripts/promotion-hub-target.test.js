import $ from 'jquery';
import { PromotionHubTarget } from '../../site/scripts/promotion-hub-target';
import {
  CLASS_HIDDEN_ELEMENT,
  TYPE_PROMOTION_SEARCH,
  TYPE_PROMOTION_FILTER,
} from '../../site/scripts/constants/offer-common';
import { getQueryParam, setURLParams, removeURLParams } from '../../site/scripts/utils/params.util';
import { getParamNames, TARGET_MAP_PARAM } from '../../site/scripts/utils/promotion-generate-endpoint.util';
import { renderViewCard } from '../../site/scripts/utils/get-data-result-page.util';

jest.mock('../../site/scripts/utils/get-data-result-page.util');
jest.mock('../../site/scripts/utils/params.util');
jest.mock('../../site/scripts/utils/promotion-generate-endpoint.util');

describe('tcb-promotion-hub-target', () => {
  beforeAll(() => {
    if (!customElements.get('tcb-promotion-hub-target')) {
      customElements.define('tcb-promotion-hub-target', PromotionHubTarget);
    }
  });

  let element;

  beforeEach(() => {
    document.body.innerHTML = `
      <div class="tcb-promotion-hub-target">
        <div class="dropdown__item" data-param="category" data-value="shoes">Shoes</div>
        <div class="dropdown__item" data-param="category" data-value="clothes">Clothes</div>
      </div>
    `;

    jest.clearAllMocks();

    getQueryParam.mockImplementation((param) => {
      if (param === 'target') {return 'product';}
      if (param === 'category') {return 'shoes';}
      if (param === 'q') {return 'search-keyword';}
      return null;
    });

    setURLParams.mockImplementation(jest.fn());
    removeURLParams.mockImplementation(jest.fn());
    renderViewCard.mockImplementation(jest.fn());

    getParamNames.mockReturnValue({ target: 'target' });
    TARGET_MAP_PARAM.product = ['category'];

    element = new PromotionHubTarget();
  });

  it('attaches click events and handles selection logic', () => {
    const $items = $('.dropdown__item');
    const firstItem = $items.eq(0);
    const secondItem = $items.eq(1);

    firstItem.trigger('click');

    expect(removeURLParams).toHaveBeenCalledWith('category', true);
    expect(setURLParams).toHaveBeenCalledWith('category', 'shoes', true);
    expect(renderViewCard).toHaveBeenCalledWith(TYPE_PROMOTION_SEARCH);

    expect(firstItem.hasClass(CLASS_HIDDEN_ELEMENT)).toBe(true);
    expect(secondItem.hasClass(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  it('falls back to TYPE_PROMOTION_FILTER if q param is missing', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'target') {return 'product';}
      if (param === 'category') {return 'clothes';}
      return null;
    });

    const $items = $('.dropdown__item');
    $items.eq(1).trigger('click');

    expect(renderViewCard).toHaveBeenCalledWith(TYPE_PROMOTION_FILTER);
  });

  it('does not attach click events if no .dropdown__item exists', () => {
    document.body.innerHTML = '<div class="tcb-promotion-hub-target"></div>';
    const spyOn = jest.spyOn($.prototype, 'on');

    const newElement = new PromotionHubTarget();

    expect(spyOn).not.toHaveBeenCalled();
    spyOn.mockRestore();
  });

  it('adds CLASS_HIDDEN_ELEMENT in loadDefault for matching param', () => {
    const $items = $('.dropdown__item');
    expect($items.eq(0).hasClass(CLASS_HIDDEN_ELEMENT)).toBe(true);
    expect($items.eq(1).hasClass(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  it('skips loadDefault if param is missing or invalid', () => {
    document.body.innerHTML = `
      <div class="tcb-promotion-hub-target">
        <div class="dropdown__item"></div>
      </div>
    `;

    const newElement = new PromotionHubTarget();
    const $item = $('.dropdown__item');

    expect($item.hasClass(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });
});
