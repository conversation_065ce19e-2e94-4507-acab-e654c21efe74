import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { TcbHeader } from '../../site/scripts/tcb-header';

const HEADER = `
	<div class="global-header">
		<tcb-header data-alt-label-text="alt label text"
                data-alt-label-link="alt label link"
                data-alt-target="_blank"
                data-sticky-cta-label-text="sticky cta label text"
                data-sticky-cta-label-link="sticky cta label link"
                data-sticky-cta-target="_self"
                data-hide-sticky-cta="false"
    >
    	<div class="header_layout">
    		<tcb-search-engine-mobile></tcb-search-engine-mobile>
    		<div class="navigation-secondary_actions">
    			<div id="alt-btn" class="link_component-link alt-link hide-on-mobile">
    				<span class="alt-label-text"></span>
					</div>
    			<div id="login-btn"></div>
				</div>
				<div class="alternate-button-mobile">
					<div id="alt-btn" class="link_component-link alt-link">
						<span class="alt-label-text"></span>
					</div>
				</div>
				<div class="navigation_secondary">
					<div class="navigation-secondary_menu"></div>
				</div>
				<div class="navigation_primary content-wrapper"></div>
			</div>
    </tcb-header>
    <div class="tcb-hero-banner"></div>
    <div class="footer"></div>
    <tcb-google-search class="search-engine-show"></tcb-google-search>
	</div>
`;

const HEADER_ALT_BUTTON = `
	<div class="global-header">
		<tcb-header data-alt-label-text="alt label text"
                data-alt-label-link="alt label link"
                data-alt-target="_blank"
    >
    	<div class="header_layout">
    		<tcb-search-engine-mobile></tcb-search-engine-mobile>
    		<div class="navigation-secondary_actions">
    			<div id="alt-btn" class="link_component-link alt-link hide-on-mobile">
    				<span class="alt-label-text"></span>
					</div>
    			<div id="login-btn"></div>
				</div>
				<div class="alternate-button-mobile">
					<div id="alt-btn" class="link_component-link alt-link">
						<span class="alt-label-text"></span>
					</div>
				</div>
				<div class="navigation_secondary">
					<div class="navigation-secondary_menu"></div>
				</div>
				<div class="navigation_primary content-wrapper"></div>
			</div>
    </tcb-header>
	</div>
`;

const HEADER_WITHOUT_CTA = `
	<div class="global-header">
		<tcb-header>
    	<div class="header_layout">
    		<tcb-search-engine-mobile></tcb-search-engine-mobile>
    		<div class="navigation-secondary_actions">
    			<div id="alt-btn" class="link_component-link alt-link hide-on-mobile">
    				<span class="alt-label-text"></span>
					</div>
    			<div id="login-btn"></div>
				</div>
				<div class="alternate-button-mobile">
					<div id="alt-btn" class="link_component-link alt-link">
						<span class="alt-label-text"></span>
					</div>
				</div>
				<div class="navigation_secondary">
					<div class="navigation-secondary_menu"></div>
				</div>
				<div class="navigation_primary content-wrapper"></div>
			</div>
    </tcb-header>
	</div>
`;

describe('TCBHeader', () => {
  let component;
  customElements.define('tcb-header', TcbHeader);

  beforeEach(() => {
    document.body.innerHTML = '';
    document.body.innerHTML = HEADER;
    jest.replaceProperty(window, 'innerWidth', 1025);
    jest.replaceProperty(window, 'scrollY', 500);
    component = new TcbHeader();
  });

  it('should document is ready', () => {
    expect(document.readyState).toBe('complete');
  });

  it('should listener when window scroll', () => {
    window.dispatchEvent(new Event('scroll'));
    const altTarget = component.altTarget;
    expect(altTarget).toBe('_self');
  });

  it('should show ALT button', () => {
    jest.replaceProperty(window, 'scrollY', 200);
    document.body.innerHTML = '';
    document.body.innerHTML = HEADER_ALT_BUTTON;
    const spyShowMobileMenu = jest.spyOn(component, 'showMobileMenu');
    expect(spyShowMobileMenu).not.toBeCalled();
  });

  it('should hide CTA', () => {
    document.body.innerHTML = '';
    document.body.innerHTML = HEADER_WITHOUT_CTA;
    const spyAdjustFooter = jest.spyOn(component, 'adjustFooter');
    expect(spyAdjustFooter).not.toBeCalled();
  });
});
