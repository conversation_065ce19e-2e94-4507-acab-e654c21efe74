import { describe, expect, jest, test } from '@jest/globals';

class TestGridPagination {
  constructor() {
    require('../../site/scripts/grid-pagination');
  }
}

describe('GridPagination', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        0: {
          removeChild: () => {},
        },
        empty: () => {},
        append: () => {},
        ready: callback => callback(),
        on: (param, callback) => callback(),
        find: () => {
          return {
            each: callback => callback(),
          };
        },
      };
    });

    new TestGridPagination();
    new GridPagination($('div'), $('div'));
    expect(document.readyState).toBe('complete');
  });
});
