import { PromotionFilterPrimary } from '../../site/scripts/promotion-filter-primary';
import $ from 'jquery';
import { LocationUtil } from '../../site/scripts/utils/location.util.ts';

global.$ = $;

describe('PromotionFilterPrimary - initRenderUIFilter', () => {
  let promotionFilterPrimary;

  beforeEach(() => {
    document.body.innerHTML = '';

    if (!customElements.get('promotion-filter-primary')) {
      customElements.define('promotion-filter-primary', PromotionFilterPrimary);
    }
    promotionFilterPrimary = new PromotionFilterPrimary();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should do nothing if .promotionfilterprimary does not exist', () => {
    const $button = $('.credit-card-listing__button');
    const spy = jest.spyOn(promotionFilterPrimary, 'handleEventClickFilter');
    promotionFilterPrimary.initRenderUIFilter();
    expect($button.hasClass('filter-selected')).toBe(false);
    $button.trigger('click');
    expect(spy).not.toHaveBeenCalled();
  });

  it('should do nothing if .credit-card-listing__button does not exist', () => {
    document.body.innerHTML = '<div class="promotionfilterprimary"></div>';
    const $button = $('.credit-card-listing__button');
    const spy = jest.spyOn(promotionFilterPrimary, 'handleEventClickFilter');
    promotionFilterPrimary.initRenderUIFilter();
    expect($button.hasClass('filter-selected')).toBe(false);
    $button.trigger('click');
    expect(spy).not.toHaveBeenCalled();
  });

  it('should add "filter-selected" class for matching card-types and types', () => {
    document.body.innerHTML = `
         <div class="promotionfilterprimary">
            <button class="credit-card-listing__button" data-value="visa" data-param="card-types"></button>
            <button class="credit-card-listing__button" data-value="laptop" data-param="products"></button>
            <button class="credit-card-listing__button" data-value="reward" data-param="types"></button>
            </div>

        `;
    promotionFilterPrimary.paramCardTypes = 'visa';
    promotionFilterPrimary.paramProducts = 'laptop';
    promotionFilterPrimary.paramTypes = 'reward';

    promotionFilterPrimary.initRenderUIFilter();

    const $buttons = $('.credit-card-listing__button');

    expect($buttons.eq(0).hasClass('filter-selected')).toBe(true);

    expect($buttons.eq(1).hasClass('filter-selected')).toBe(true);

    expect($buttons.eq(2).hasClass('filter-selected')).toBe(true);
  });

  it('should call handleEventClickFilter when button is clicked', () => {
    document.body.innerHTML = `
      <div class="promotionfilterprimary">
        <button class="credit-card-listing__button" data-value="visa" data-param="card-types"></button>
      </div>
    `;
    const handleEventClickFilter = jest
      .spyOn(promotionFilterPrimary, 'handleEventClickFilter')
      .mockImplementation(() => {});

    promotionFilterPrimary.initRenderUIFilter();

    document.querySelector('.credit-card-listing__button').click();

    expect(handleEventClickFilter).toHaveBeenCalledTimes(1);
  });
});

describe('PromotionFilterPrimary - convertStringParamsToArray', () => {
  let promotionFilterPrimary;

  beforeEach(() => {
    document.body.innerHTML = '';

    if (!customElements.get('promotion-filter-primary')) {
      customElements.define('promotion-filter-primary', PromotionFilterPrimary);
    }
    promotionFilterPrimary = new PromotionFilterPrimary();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return an empty array if param is undefined', () => {
    expect(
      promotionFilterPrimary.convertStringParamsToArray(undefined),
    ).toEqual([]);
  });

  it('should return an empty array if param is null', () => {
    expect(promotionFilterPrimary.convertStringParamsToArray(null)).toEqual([]);
  });

  it('should return an empty array if param is empty string', () => {
    expect(promotionFilterPrimary.convertStringParamsToArray('')).toEqual([]);
  });

  it('should return array with one item if no comma', () => {
    expect(promotionFilterPrimary.convertStringParamsToArray('visa')).toEqual([
      'visa',
    ]);
  });

  it('should return array of items if comma-separated string', () => {
    expect(
      promotionFilterPrimary.convertStringParamsToArray('visa,master,jcb'),
    ).toEqual(['visa', 'master', 'jcb']);
  });

  it('should handle extra spaces if needed (optional trim)', () => {
    expect(
      promotionFilterPrimary.convertStringParamsToArray(
        '  visa , master , jcb ',
      ),
    ).toEqual(['  visa ', ' master ', ' jcb ']);
  });
});

describe('PromotionFilterPrimary - handleUpdateParamsUrl', () => {
  let promotionFilterPrimary;

  beforeEach(() => {
    delete window.location;
    window.location = {
      pathname: '/',
      search: '',
    };
    if (!customElements.get('promotion-filter-primary')) {
      customElements.define('promotion-filter-primary', PromotionFilterPrimary);
    }
    promotionFilterPrimary = new PromotionFilterPrimary();

    LocationUtil.getUrlParamObj = jest.fn(() => ({
      cardTypes: 'mockCard',
      products: 'mockProduct',
      types: 'mockType',
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should do nothing if param does not exist', () => {
    const convertStringParamsToArray = jest
      .spyOn(promotionFilterPrimary, 'convertStringParamsToArray')
      .mockImplementation(() => {});

    promotionFilterPrimary.handleUpdateParamsUrl('', 'card-types', false);
    expect(convertStringParamsToArray).not.toHaveBeenCalled();
  });

  it('should call LocationUtil.getUrlParamObj when have param', () => {
    window.location = {
      pathname: '/',
      search: 'card-types=master',
    };
    promotionFilterPrimary.handleUpdateParamsUrl('jcb', 'card-types', false);

    expect(LocationUtil.getUrlParamObj).toHaveBeenCalled();
  });

  it('should remove param from the URL if isRemove is true', () => {
    window.location = {
      pathname: '/',
      search: '',
    };
    promotionFilterPrimary.handleUpdateParamsUrl('visa', 'card-types', true);

    expect(LocationUtil.getUrlParamObj).toHaveBeenCalled();
  });
});

describe('handleEventClickFilter', () => {
  let promotionFilterPrimary;
  let $item;

  beforeEach(() => {
    document.body.innerHTML =
      '<div id="filter" data-value="visa" data-param="card-types"></div>';
    $item = $('#filter');

    if (!customElements.get('promotion-filter-primary')) {
      customElements.define('promotion-filter-primary', PromotionFilterPrimary);
    }
    promotionFilterPrimary = new PromotionFilterPrimary();

    promotionFilterPrimary.typesTilter = {
      cardTypes: 'card-types',
      products: 'products',
      types: 'types',
    };
    promotionFilterPrimary.paramCardTypes = '';
    promotionFilterPrimary.paramProducts = '';
    promotionFilterPrimary.paramTypes = '';

    jest
      .spyOn(promotionFilterPrimary, 'handleUpdateParamsUrl')
      .mockImplementation(() => {});
  });

  it('should toggle filter-selected class and call handleUpdateParamsUrl with correct args', () => {
    promotionFilterPrimary.handleEventClickFilter($item);

    expect($item.hasClass('filter-selected')).toBe(true);

    expect(promotionFilterPrimary.handleUpdateParamsUrl).toHaveBeenCalledWith(
      'visa',
      'card-types',
      false,
    );
  });

  it('should remove filter-selected class and call handleUpdateParamsUrl when already selected', () => {
    promotionFilterPrimary.paramCardTypes = 'visa';
    $item.addClass('filter-selected');
    promotionFilterPrimary.handleEventClickFilter($item);
    expect($item.hasClass('filter-selected')).toBe(false);
    expect(promotionFilterPrimary.handleUpdateParamsUrl).toHaveBeenCalledWith(
      'visa',
      'card-types',
      true,
    );
  });

  it('should handle product param correctly', () => {
    $item.data('value', 'amex');
    $item.data('param', 'products');
    promotionFilterPrimary.paramProducts = '';
    promotionFilterPrimary.handleEventClickFilter($item);
    expect(promotionFilterPrimary.handleUpdateParamsUrl).toHaveBeenCalledWith(
      'amex',
      'products',
      false,
    );
  });

  it('should handle unknown param by not calling handleUpdateParamsUrl with empty typeParam', () => {
    $item.data('value', 'abc');
    $item.data('param', 'unknown');
    promotionFilterPrimary.handleEventClickFilter($item);
    expect(promotionFilterPrimary.handleUpdateParamsUrl).toHaveBeenCalledWith(
      'abc',
      'unknown',
      false,
    );
  });

  it('should handle types param correctly', () => {
    $item.data('value', 'gold');
    $item.data('param', 'types');
    promotionFilterPrimary.paramTypes = '';
    promotionFilterPrimary.handleEventClickFilter($item);
    expect(promotionFilterPrimary.handleUpdateParamsUrl).toHaveBeenCalledWith(
      'gold',
      'types',
      false,
    );
  });
});

// yarn test --coverage --collectCoverageFrom='src/main/webpack/site/scripts/promotion-filter-primary.js' --testPathPattern='src/main/webpack/test/scripts/promotion-filter-primary.test.js' --verbose
