import { describe, expect, jest, test } from '@jest/globals';

class TestInsprireTabs {
  constructor() {
    require('../../site/scripts/inspire-tabs');
  }
}

describe('test inspire tabs', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
      return { articleFilter: 'sample' };
    });
    window.IntersectionObserver = (callback) => callback([{}]);
    global.setTimeout = jest.fn((cb) => cb());
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        ready: (callback) => callback(),
        attr: jest.fn().mockReturnValue('attr'),
        data: jest.fn(),
        on: (event, element, callback) => {
          if (callback) {
            callback({ currentTarget: 'test' });
          } else {
            element({ currentTarget: 'test' });
          }
        },
        each: (callback) => callback(),
        find: () => {
          return {
            each: (callback) => callback(),
            text: jest.fn().mockReturnValue('test string'),
            attr: jest.fn(),
            click: (callback) => callback(),
            find: () => {
              return {
                slick: jest.fn(),
                length: 2,
                get: () => {
                  return {
                    innerHTML: 'test',
                  }
                }
              };
            },
          };
        },
        length: 1,
        height: jest.fn(),
        css: jest.fn(),
        closest: () => {
          return {
            find: () => {
              return {
                trigger: jest.fn(),
              };
            },
          };
        },
      };
    });
    new TestInsprireTabs();
    expect(document.readyState).toBe('complete');
  });
});
