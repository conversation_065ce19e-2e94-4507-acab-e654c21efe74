import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { getSessionCountData, updateSessionCountData } from '../../site/scripts/count-user-sessions';
import { TcbGlobalAnnouncement } from '../../site/scripts/tcb-global-announcement';

const SAMPLE_GA_LIGHT_DATA = [
  {
    'path': '/content/dam/techcombank/master-data/global-announcements/title',
    'announcementId': 'announcement1',
    'announcementBarBackground': '/content/dam/techcombank/adobe-qa/A_black_image.jpg',
    'announcementTitle': 'Notice',
    'announcementContent': '<p>Announcement Content</p>\n',
    'ctaLink': '/content/techcombank/web/vn/en/test2',
    'theme': 'light',
    'highlyImportant': 'yes',
    'reopenAfterHowManySessions': 3,
    'sitewide': 'no',
    'pagesToShowAnnouncement': [
      '/content/techcombank/web/vn/en/toc.html',
    ],
    'publishedDate': 'Sep 19, 2024, 10:01:10 AM',
  },
];

const SAMPLE_GA_DARK_DATA = [
  {
    'path': '/content/dam/techcombank/master-data/global-announcements/title',
    'announcementId': 'announcement2',
    'announcementBarBackground': '/content/dam/techcombank/adobe-qa/A_black_image.jpg',
    'announcementTitle': 'Notice',
    'announcementContent': '<p>Announcement Content</p>\n',
    'ctaLink': '/content/techcombank/web/vn/en/test2',
    'theme': 'dark',
    'highlyImportant': 'no',
    'reopenAfterHowManySessions': 3,
    'sitewide': 'no',
    'pagesToShowAnnouncement': [
      '/content/techcombank/web/vn/en/toc.html',
    ],
    'publishedDate': 'Sep 19, 2024, 10:01:10 AM',
  },
];

const SAMPLE = `
<header class="global-header">
    <div class="header_layout">
        <div class="global-announcement hidden">
            <div class="global-announcement__wrapper">
                <div class="global-announcement__bell-icon">
                    <img alt="global-announcement bell-icon"
                        class="ga-bell-icon"
                        src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-bell-white.svg">
                </div>
                <div class="global-announcement__content">
                    <div class="global-announcement__content--title"></div>
                    <div class="global-announcement__content--description"></div>
                </div>
                <div class="global-announcement__close-button">
                    <img alt="global-announcement close-button"
                        class="ga-close-button"
                        src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon_close_white.svg">
                </div>
            </div>
        </div>
    </div>
    <div class="header-navigation"></div>
    <div class="header-content">
        <div class="navigation_sub"></div>
    </div>
</header>
`;

describe('TCBGlobalAnnouncement', () => {
  let gaElement;
  let component;
  customElements.define('tcb-global-announcement', TcbGlobalAnnouncement);

  beforeEach(() => {
    document.body.innerHTML = ``;
    document.body.innerHTML = SAMPLE;
    document.documentElement.lang = 'en';
    window.open = jest.fn();
    component = new TcbGlobalAnnouncement();
    gaElement = document.querySelector('.global-announcement');
    component.querySelector = jest.fn().mockImplementation((selector) => {
      if (selector === '.global-announcement') {
        return gaElement;
      }
    });
  });

  it('should document is ready', () => {
    expect(document.readyState).toBe('complete');
  });

  describe('should get global announcement data', () => {
    let spyFetchData;

    beforeEach(() => {
      spyFetchData = jest.spyOn(component, 'fetchData');
    });

    it('case success', async () => {
      spyFetchData.mockReturnValue(Promise.resolve({ data: { items: SAMPLE_GA_LIGHT_DATA } }));
      await component.getGlobalAnnouncement();
      expect(component.globalAnnouncementData).toEqual(SAMPLE_GA_LIGHT_DATA[0]);
    });

    it('case failed', async () => {
      spyFetchData.mockReturnValue(Promise.reject(new Error('failed to fetch data')));
      await component.getGlobalAnnouncement();
      expect(component.globalAnnouncementData).toBeUndefined();
    });
  });

  describe('should render global announcement theme', () => {
    it('case light theme', () => {
      component.globalAnnouncementData = SAMPLE_GA_LIGHT_DATA[0];
      component.renderGAStyle();
      expect(component.globalAnnouncementElement.classList).toContain('light-theme');

      component.globalAnnouncementData.announcementBarBackground = null;
      component.renderGAStyle();
      expect(component.globalAnnouncementElement.style.background).toEqual('black');
    });

    it('case dark theme', () => {
      component.globalAnnouncementData = SAMPLE_GA_DARK_DATA[0];
      component.renderGAStyle();
      expect(component.globalAnnouncementElement.classList).toContain('dark-theme');

      component.globalAnnouncementData.announcementBarBackground = null;
      component.renderGAStyle();
      expect(component.globalAnnouncementElement.style.background).toEqual('white');
    });
  });

  describe('should listen click event in wrapper element', () => {
    it('case clicked wrapper content', async () => {
      component.globalAnnouncementData = SAMPLE_GA_DARK_DATA[0];
      const mockEvent = {
        target: component.gaContentElement,
        preventDefault: jest.fn(),
      };
      component.globalAnnouncementClickListener(mockEvent);
      expect(window.open).toHaveBeenCalled();
    });

    it('case clicked close-button inside wrapper', () => {
      const closeButton = component.gaCloseButtonElement.querySelector('img.ga-close-button');
      const mockEvent = {
        target: closeButton,
        preventDefault: jest.fn(),
      };
      component.globalAnnouncementClickListener(mockEvent);
      expect(window.open).not.toHaveBeenCalled();
    });
  });

  it('should listen click event in close button element', () => {
    jest.useFakeTimers();
    component.globalAnnouncementData = SAMPLE_GA_DARK_DATA[0];
    component.closeButtonClickListener();
    jest.advanceTimersByTime(1000);
    expect(component.globalAnnouncementElement.classList).toContain('hidden');
  });

  it('should show global announcement when sessions match with limit', async () => {
    component.globalAnnouncementData = SAMPLE_GA_DARK_DATA[0];
    const sessionCountData = getSessionCountData('announcement2');
    sessionCountData['announcement2'].isClosed = true;
    sessionCountData['announcement2'].count = 3;
    updateSessionCountData(sessionCountData);
    component.isShowGA();
    const spyIsShowGA = jest.spyOn(component, 'isShowGA');
    expect(spyIsShowGA).toBeTruthy();
  });
});
