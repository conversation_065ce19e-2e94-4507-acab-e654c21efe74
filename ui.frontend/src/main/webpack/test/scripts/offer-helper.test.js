import {
  fetchData,
  removeHTMLTags,
  updateSearchParams,
  clearSearchParams,
  addCurrentUrlHistory,
  resetHistory,
  convertPxToRem,
  createSearchUrl,
  handleResetDropdownLanguage,
} from "../../site/scripts/offer-helper";

jest.mock("../../site/scripts/utils");

global.$ = require("jquery");
$.ajax = jest.fn();

describe("fetchData", () => {
  it("should fetch data successfully", async () => {
    const mockResponse = { data: "test" };
    $.ajax.mockImplementationOnce(({ success }) => success(mockResponse));

    const result = await fetchData("/api/test");
    expect(result).toEqual(mockResponse);
  });

  it("should handle fetch data error", async () => {
    const mockError = "Error";
    $.ajax.mockImplementationOnce(({ error }) => error(null, null, mockError));

    await expect(fetchData("/api/test")).rejects.toEqual(mockError);
  });
});

describe("removeHTMLTags", () => {
  it("should remove HTML tags", () => {
    const html = "<div>Test</div>";
    const result = removeHTMLTags(html);
    expect(result).toBe("Test");
  });
});

describe("updateSearchParams", () => {
  it("should update URL search parameters", () => {
    const mockReplaceState = jest.fn();
    delete window.location;
    window.location = new URL("https://example.com");
    window.history.pushState = mockReplaceState;

    updateSearchParams({ foo: "bar", baz: null });
    expect(window.location.search).toBe("");
    expect(mockReplaceState).toHaveBeenCalled();
  });
});

describe("clearSearchParams", () => {
  it("should clear search parameters", () => {
    const mockReplaceState = jest.fn();
    delete window.location;
    window.location = new URL("https://example.com?foo=bar");
    window.history.replaceState = mockReplaceState;

    clearSearchParams(["foo"]);
    expect(window.location.search).toBe("?foo=bar");
    expect(mockReplaceState).toHaveBeenCalled();
  });
});

describe("addCurrentUrlHistory", () => {
  it("should add current URL to session storage history", () => {
    delete window.location;
    window.location = new URL("https://example.com");
    addCurrentUrlHistory();
    expect(window.location.href).toBe("https://example.com/");
  });
});

describe("resetHistory", () => {
  it("should reset session storage history", () => {
    delete window.location;
    resetHistory();
    expect(window.location).toBe(undefined);
  });
});

describe("convertPxToRem", () => {
  it("should convert px to rem", () => {
    const result = convertPxToRem(32);
    expect(result).toBe(2);
  });

  it("should return null if px is not provided", () => {
    const result = convertPxToRem(null);
    expect(result).toBeNull();
  });
});

describe("createSearchUrl", () => {
  it("should create a search URL with parameters", () => {
    const result = createSearchUrl("https://example.com", {
      foo: "bar",
      baz: "qux",
    });
    expect(result).toBe("https://example.com?foo=bar&baz=qux");
  });

  it("should return baseUrl if no params are provided", () => {
    const result = createSearchUrl("https://example.com");
    expect(result).toBe("https://example.com");
  });
});

describe("handleResetDropdownLanguage", () => {
  beforeEach(() => {
    document.body.innerHTML =
      '<header><div class="language_dropdown"><a href="#">Language 1</a></div></header>';
  });

  it("should reset the dropdown language visibility", () => {
    const mockShow = jest.fn();
    $.fn.show = mockShow;

    handleResetDropdownLanguage();

    expect(mockShow).toHaveBeenCalled();
  });
});
