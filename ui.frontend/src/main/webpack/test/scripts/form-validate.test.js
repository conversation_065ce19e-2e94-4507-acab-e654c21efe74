import { beforeEach, describe, expect, jest, test } from '@jest/globals';

class TestFormValidate {
  constructor() {
    document.body.innerHTML = `
     <div class="cmp-form-text__text"></div>
     <input data-type="currency" value="" class="cmp-form-text__text"/>
		`;
    require('../../site/scripts/form-validate');
  }
}

describe('Test Form Validate', () => {
  beforeEach(() => {
    window.scrollTo = jest.fn();
    window.$ = jest.fn().mockImplementation(() => {
      return {
        0: {
          removeChild: jest.fn(),
        },
        forceNumericOnly: jest.fn().mockImplementation(() => {
          return {};
        }),
        append: jest.fn(),
        addClass: jest.fn(),
        removeClass: jest.fn(),
        attr: (params) => {
          if (typeof params === 'string' && params.includes('type')) {
            return 'currency';
          }
        },
        offset: () => {
          return {
            top: '10',
          };
        },
        keypress: (callback) => callback({
          which: 'which',
        }),
        keyup: (callback) => callback({
          target: {
            value: {
              replaceAll: jest.fn().mockReturnValue('123'),
              substring: jest.fn(),
            },
          },
        }),
        parentElement: 'sample',
        find: () => {
          return {
            each: (callback) => callback({}, {
              getAttribute: jest.fn().mockReturnValue('phone'),
              hasAttribute: jest.fn().mockReturnValue(false),
              id: 'registrationIdOrigination',
              querySelector: () => {
                return {
                  value: 'sample',
                };
              },
            }),
            on: (key, callback) => callback({
              getAttribute: jest.fn(),
              hasAttribute: jest.fn(),
              querySelector: jest.fn(),
            }),
          };
        },
        on: (key, callback) => callback({
          preventDefault: jest.fn(),
          originalEvent: {
            clipboardData: {
              getData: () => 'text',
            },
          },
        }),
        each: (callback) => callback(),
      };
    });

    new TestFormValidate();
  });

  test('should document is ready', () => {
    expect(document.readyState).toEqual('complete');

    const isValidForm = window.validateForm(new Event('input'));
    const isValidEmail = window.validateEmail('<EMAIL>');
    expect(isValidForm).toBeTruthy();
    expect(isValidEmail).toBeTruthy();
  });

  test('should handle paste event on input element', () => {
    const currencyInput = document.querySelector('input.cmp-form-text__text');
    currencyInput.dispatchEvent(new Event('paste'));
    expect(currencyInput.getAttribute('value')).toEqual('');
  });
});
