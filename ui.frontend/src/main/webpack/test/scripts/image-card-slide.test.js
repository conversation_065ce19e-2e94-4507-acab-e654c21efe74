import { describe, expect, jest, test } from '@jest/globals';

class TestImageCardSlide {
  constructor() {
    require('../../site/scripts/image-card-slide');
  }
}

describe('test help panel', () => {
  test('should document ready', () => {
    global.setTimeout = jest.fn((callback) => callback());
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        ready: (callback) => callback(),
        width: jest.fn().mockReturnValueOnce(768).mockReturnValueOnce(1201),
        each: (callback) => callback(),
        find: () => {
          return {
            length: 0,
            not: () => {
              return {
                slick: jest.fn(),
              };
            },
          };
        },
        text: jest.fn(),
        css: jest.fn(),
        on: (event, callback) =>
          callback(
            event,
            {
              $slides: {
                length: 3,
              },
            },
            0,
            {},
          ),
        resize: (callback) => callback(),
        hasClass: jest.fn(),
      };
    });
    new TestImageCardSlide();
    expect(document.readyState).toBe('complete');
  });
});
