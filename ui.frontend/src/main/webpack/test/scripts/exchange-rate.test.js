import { describe, expect, jest, test } from '@jest/globals';

class TestExchangeRate {
  constructor() {
    document.body.innerHTML = `
    <div class="exchange-rate">
      <div class="time-select"></div>
      <div class="exchange-rate__view-more">
        <button>click</button>
      </div>
      <div class="exchange-rate__popup"></div>
      <div class="popup__close-button"></div>
      <div class="popup__download-button">
        <button>click</button>
      </div>
      <div class="table-content-container"></div>
      <div class="exchange-rate__empty-label"></div>
      <div class="exchange-rate-table-content"></div>
      <div class="calendar__input-field"></div>
      <div class="calendar_real_estate"></div>
      <div class="calendar-popup active"></div>
    </div>`;
    require("../../site/scripts/exchange-rate");
  }
}

describe("test exchange rate", () => {
  test("should document ready", () => {
    Array.prototype.toReversed = jest.fn().mockReturnValue({
      value: "['2024','07','18']",
      join: jest.fn(),
    });

    Object.groupBy = jest.fn().mockReturnValue(['vi', 'en']);

    window.location.hash = "#test";
    JSON.parse = jest.fn().mockImplementationOnce(() => {
      return { articleFilter: "sample" };
    });
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        attr: () => {
          return "test";
        },
        data: jest.fn(),
        on: (event, selector, callback) => {
          if (callback) {
            callback({ currentTarget: "test" });
          } else {
            selector({ currentTarget: "test" });
          }
        },
        each: (callback) => callback(),
        find: () => {
          return {
            find: () => {
              return {
                click: (callback) => callback(),
              };
            },
            css: jest.fn(),
            // each: (callback) => callback(),
            each: jest.fn(),
            on: (event, callback) => callback(),
            removeClass: jest.fn(),
            addClass: jest.fn(),
            changeOptions: jest.fn(),
            text: jest.fn(),
            append: jest.fn(),
            empty: jest.fn(),
          };
        },
        click: (callback) => callback(),
        css: jest.fn(),
        parents: () => {
          return {
            siblings: () => {
              return {
                css: jest.fn(),
              };
            },
            css: jest.fn(),
          };
        },
        text: jest.fn(),
        next: () => {
          return {
            css: jest.fn(),
            hasClass: jest.fn(),
            removeClass: jest.fn(),
            addClass: jest.fn(),
          };
        },
        outerHeight: jest.fn(),
        empty: jest.fn(),
        append: jest.fn(),
        html: jest.fn(),
      };
    });
    window.$.ajax = jest.fn().mockImplementation(({ success }) => {
      if (typeof success === "function") {
        success({
          exchangeRate: {
            data: [{ test: "test" }],
            updatedTimes: "01/01/2024",
          },
          goldRate: {
            data: "test",
          },
          otherRate: {
            data: [{ test: "test" }],
            central: "central",
            floor: "floor",
            ceiling: "ceiling",
          },
          tenorintRate: {
            data: [{ tenor: "tenor" }],
          },
          tenorRate: {
            data: [{ test: "test" }],
          },
        });
      }
    });
    new TestExchangeRate();
    expect(document.readyState).toBe("complete");
  });
});
