import { describe, expect, test, jest } from "@jest/globals";

class TestCCashCrm {
  constructor() {
    require("../../site/scripts/c-cash-crm");
  }
}

describe("test article rte analytics", () => {
  test("should document ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        find: () => {
          return {
            length: 1,
            click: (callback) => callback(),
            text: jest.fn(),
            val: jest.fn(),
            change: jest.fn(),
            each: (callback) => callback(),
          };
        },
        siblings: () => {
          return {
            text: jest.fn(),
          };
        },
      };
    });
    new TestCCashCrm();
    expect(document.readyState).toBe("complete");
  });
});
