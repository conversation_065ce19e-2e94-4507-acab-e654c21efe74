import { describe, expect, jest, test } from '@jest/globals';

class TestCalendarEvent {
  constructor() {
    require("../../site/scripts/calendar-event");
  }
}

describe("test calendar", () => {
  beforeEach(() => {
    global.setTimeout = jest.fn((cb) => cb());
  });
  test("should document ready", () => {
    JSON.parse = jest.fn().mockReturnValue({ articleFilter: "test" });
    document.documentElement.lang = "en";
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === "function") {
        callback();
      }
      return {
        each: (callback) => callback(),
        find: () => {
          return {
            data: jest.fn().mockReturnValue([
              {
                id: 'credit',
                title: 'credit',
              },
              {
                id: 'debit',
                title: 'debit',
              },
            ]),
            on: (event, callback) => {
              if (event !== "ready") {
                callback({}, ['test']);
              } else {
                callback();
              }
            },
            cardMultipleSelectOnChanged: jest.fn(),
            empty: jest.fn(),
            datepicker: jest.fn(),
            css: jest.fn(),
            append: jest.fn(),
            filter: () => {
              return {
                map: () => {
                  return {
                    get: jest.fn().mockReturnValue("upcoming", "test"),
                  };
                },
              };
            },
          };
        },
        parents: () => {
          return {
            attr: jest.fn(),
          };
        },
        attr: jest.fn(),
        trigger: jest.fn(),
        on: (event, callback) => callback({ target: "test" }),
        hasClass: jest.fn(),
        ready: (callback) => callback(),
        click: (callback) => callback(),
        width: jest.fn().mockReturnValue(768),
        text: jest.fn().mockReturnValue('test'),
        next: () => {
          return {
            text: jest.fn().mockReturnValue('test'),
          };
        },
        data: () => {
          return {
            trackingClickInfoValue: "test",
          };
        },
        addClass: jest.fn(),
        removeClass: jest.fn(),
      };
    });
    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({
            data: {
              eventDetailContentFragmentList: {
                items: [
                  {
                    eventDesription: {
                      html: 'test',
                    },
                    eventTag: ['credit', 'debit'],
                    eventHeading: 'test',
                    eventDate: '2024-08-05',
                    eventTime: '10:11:11',
                    imageForEvent: {
                      path: 'path',
                    },
                    ctaButtonLabel: 'cta label',
                    ctaButtonLink: {
                      _publishUrl: 'techcombank.com',
                    },
                    internalLinkQueryParam: 'query param',
                    ctaExternalLink: 'cta external link',
                    ctaNoFollow: true,
                    ctaTarget: true,
                  },
                ],
              },
            },
          });
        }
        if (typeof callback === "function") {
          error({ error: "test" });
        }
      });

    window.$.datepicker = {
      regional: {
        vi: {},
      },
    };

    new TestCalendarEvent();
    expect(document.readyState).toBe("complete");
  });
});
