import { describe, expect, test, jest } from "@jest/globals";

class TestCreditRating {
  constructor() {
    require("../../site/scripts/credit-rating");
  }
}

describe("Test Credit Rating", () => {
    beforeEach(() => {
        document.body.innerHTML = "";
        document.documentElement.lang = "en";
        window = Object.assign(window, { innerWidth: 500 });
    });

    test("should document is ready", () => {
        window.$ = jest.fn().mockImplementation(() => {
            return {
                ready: (callback) => callback(),
                each: (callback) => callback(),
                find: () => {
                    return {
                        0: {
                            value: "abc",
                            addEventListener: jest.fn(),
                        },
                        find: () => {
                            return {
                                value: "<div class='mockTest'></div>",
                                text: jest.fn(),
                                sort: () => {
                                    return {
                                        value: "<div class='mockTest'></div>",
                                        appendTo: jest.fn(),
                                    }
                                },
                                each: (callback) => {
                                    if (typeof callback === "function") {
                                        callback({});
                                    }
                                },
                                click: (callback) => {
                                    if (typeof callback === "function") {
                                        callback({});
                                    }
                                },
                                length: 3,
                            }
                        },
                        not: () => {
                            return {
                                value: "<div class='mockTest'></div>",
                                css: jest.fn(),
                                addClass: jest.fn(),
                            }
                        },
                        first: jest.fn(),
                        removeClass: jest.fn(),
                        addClass: jest.fn(),
                        each: (callback) => {
                            if (typeof callback === "function") {
                                callback({});
                            }
                        },
                        click: (callback) => {
                            if (typeof callback === "function") {
                                callback({});
                            }
                        },
                        css: jest.fn(),
                        length: 3,
                        index: jest.fn(),
                        text: jest.fn().mockReturnValue("'attrMock'"),
                    }
                },
                css: jest.fn(),
                index: jest.fn(),
                removeClass: jest.fn(),
                attr: jest.fn().mockReturnValue("'attrMock'"),
                text: jest.fn(),
                resize: jest.fn(),
            }
        });
        new TestCreditRating();
        expect(document.readyState).toBe("complete");
    });
});