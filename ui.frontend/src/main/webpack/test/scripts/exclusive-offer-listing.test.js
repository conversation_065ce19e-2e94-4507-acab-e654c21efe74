import { renderCardExclusiveOffer, showPreviewModalListener } from '../../site/scripts/exclusive-offer-listing';
import { renderDayExpired } from '../../site/scripts/utils/day-expired.util';

jest.mock('../../site/scripts/utils/day-expired.util', () => ({
  renderDayExpired: jest.fn(),
}));

jest.mock('jquery', () => {
  const originalModule = jest.requireActual('jquery');
  return {
    ...originalModule,
    fn: {
      ...originalModule.fn,
      trigger: jest.fn(),
      on: jest.fn(function (event, callback) {
        if (event === 'click') {
          callback({ currentTarget: this });
        }
        return this;
      }),
    }
  };
});

jest.useFakeTimers();

describe("Exclusive Offer Listing", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = `
      <div class="tab-card">
        <div class="tab-card__item">
          <a href="#" data-tab="popular" class="active">Popular</a>
        </div>
      </div>
      <div class="tab-content">
        <div class="card" data-tab="popular"></div>
        <div class="card" data-tab="popular"></div>
        <div class="card" data-tab="popular"></div>
        <div class="card" data-tab="popular"></div>
        <div class="card" data-tab="popular"></div>
      </div>
      <button class="card-see-more" data-see-more="2"></button>
      <div class="popup-download"></div>
    `;
  });

  it('should show initial 4 cards for popular tab', () => {
    renderCardExclusiveOffer();

    setTimeout(() => {
      jest.advanceTimersByTime(100);
      const visibleCards = $(".card:visible");
      expect(visibleCards.length).toBe(4);

      const loadMoreVisible = $(".card-see-more").is(":visible");
      expect(loadMoreVisible).toBe(true);
    }, 100);
  });

  it("should show more cards when clicking see more", () => {
    renderCardExclusiveOffer();
    $(".card-see-more").trigger("click");
    setTimeout(() => {
      const visibleCardsAfterClick = $(".card:visible");
      expect(visibleCardsAfterClick.length).toBe(6);
      const loadMoreVisibleAfterClick = $(".card-see-more").is(":visible");
      expect(loadMoreVisibleAfterClick).toBe(false);
    }, 100);
  });

  it("should filter cards when clicking another tab", () => {
    renderCardExclusiveOffer();

    $('.tab-card__item a[data-tab="popular"]').trigger("click");

    setTimeout(() => {
      jest.advanceTimersByTime(100);
      const visibleCards = $(".card:visible");
      expect(visibleCards.length).toBe(4);
    }, 100);
  });

  it('should call renderDayExpired every second', () => {
    const renderDayExpiredMock = jest.spyOn(require('../../site/scripts/utils/day-expired.util'), 'renderDayExpired');

    renderCardExclusiveOffer();
    setTimeout(() => {

      expect(renderDayExpiredMock).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(1000);
      expect(renderDayExpiredMock).toHaveBeenCalledTimes(2);
    }, 100);
  });

  it('should trigger show-preview-modal when clicking PDF link', () => {
    const mockTrigger = jest.spyOn($.fn, 'trigger');

    const pdfLink = $('<a href="file.pdf" data-download-pdf-label="Download PDF">Download PDF</a>');

    showPreviewModalListener({ currentTarget: pdfLink[0], preventDefault: jest.fn() });

    expect(mockTrigger).toHaveBeenCalledWith('show-preview-modal', [
      expect.objectContaining({
        url: 'file.pdf',
        documentType: 'pdf',
        title: 'Preview',
        loadingLabel: 'Loading PDF...',
        downloadLabel: 'Download PDF',
      })
    ]);

    expect(mockTrigger).toHaveBeenCalledTimes(1);
  });
});

describe('scrollText', () => {
  beforeEach(() => {
    document.body.innerHTML = `
      <div class="card" style="width: 100px;">
        <div class="card__title" style="display: inline-block; white-space: nowrap;">Some very long title text</div>
      </div>
    `;
  });

  it('should add marquee class if text overflows container', () => {
    const text = document.querySelector('.card__title');
    Object.defineProperty(text, 'scrollWidth', {
      configurable: true,
      value: 200, // > container width (100)
    });

    setTimeout(() => {
      scrollText();
      expect($('.card__title').hasClass('marquee')).toBe(true);
    }, 100);
  });

  it('should not add marquee class if text fits in container', () => {
    const text = document.querySelector('.card__title');
    Object.defineProperty(text, 'scrollWidth', {
      configurable: true,
      value: 80,
    });

    setTimeout(() => {
      scrollText();
      expect($('.card__title').hasClass('marquee')).toBe(false);
    }, 100);
  });
});
