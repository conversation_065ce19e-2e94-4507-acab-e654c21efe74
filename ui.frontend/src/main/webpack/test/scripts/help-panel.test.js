import { describe, expect, jest, test } from '@jest/globals';

class TestHelpPanel {
  constructor() {
    document.body.innerHTML = `
      <div class="help-panel__tab-control">
          <button class="help-panel__tab-control-btn" data-tab-control="0" data-tracking-click-event="faq" data-tracking-web-interaction-value="{'webInteractions': {'name': 'Help panel','type': 'other'}}" data-tracking-click-info-value="{'question':'Đi<PERSON>u kiện đ<PERSON> bắt đầu sử dụng'}">
            <h3 class="text-base font-normal"><PERSON><PERSON><PERSON><PERSON> kiện đ<PERSON> bắt đ<PERSON>u sử dụng</h3>
            <img alt="red-arrow" src="/content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg" decoding="async" data-nimg="fill" sizes="100vw" srcset="
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 360w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 576w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1080w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1200w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1920w
              ">
          </button>
          <button class="help-panel__tab-control-btn tab-active" data-tab-control="1" data-tracking-click-event="faq" data-tracking-web-interaction-value="{'webInteractions': {'name': 'Help panel','type': 'other'}}" data-tracking-click-info-value="{'question':'Chuyển khoản bằng mã QR'}">
            <h3 class="text-base font-normal">Chuyển khoản bằng mã QR</h3>
            <img alt="red-arrow" src="/content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg" decoding="async" data-nimg="fill" sizes="100vw" srcset="
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 360w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 576w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1080w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1200w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1920w
              ">
          </button>
          <button class="help-panel__tab-control-btn" data-tab-control="2" data-tracking-click-event="faq" data-tracking-web-interaction-value="{'webInteractions': {'name': 'Help panel','type': 'other'}}" data-tracking-click-info-value="{'question':'Điều kiện để bắt đầu sử dụng'}">
            <h3 class="text-base font-normal">Điều kiện để bắt đầu sử dụng</h3>
            <img alt="red-arrow" src="/content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg" decoding="async" data-nimg="fill" sizes="100vw" srcset="
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 360w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 576w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1080w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1200w,
                /content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg 1920w
              ">
          </button>
      </div>
      <div class="help-panel__tab-content">
          <div class="help-panel__article-content" data-tab-content="0" style="display: none;">
            <div class="help-panel__modal-header">
              <div class="help-panel__modal-close"></div>
            </div>
            <div class="help-panel__tab-content-body">
            <p class="font-semibold" style="font-size: 18.0px;line-height: 24.0px;color: rgb(0,0,0);">Điều kiện để có thể bắt đầu sử dụng được ứng dụng Techcombank Mobile?</p>
            <p>Khách hàng cần mở tài khoản thanh toán tại Techcombank, có thiết bị kết nối Internet và chạy hệ điều hành iOS 14.0 trở lên hoặc hệ điều hành Android 7.0 trở lên để có thể cài đặt và sử dụng dịch vụ ngân hàng số của Techcombank.</p>
            <p>Các khách hàng mới chưa sử dụng các sản phẩm, dịch vụ của Techcombank có thể đăng ký mở tài khoản và dịch vụ ngân hàng số trực tuyến ngay trên ứng dụng mà không cần tới quầy.</p>
          </div>
          </div>
          <div class="help-panel__article-content open" data-tab-content="1" style="display: block;">
            <div class="help-panel__modal-header">
              <div class="help-panel__modal-close"></div>
            </div>
            <div class="help-panel__tab-content-body">
            <p class="font-semibold" style="font-size: 18.0px;line-height: 24.0px;color: rgb(0,0,0);">Chuyển tiền bằng mã QR khác gì so với chuyển tiền thông thường?</p>
            <p>Với cách chuyển tiền thông thường sẽ cần nhiều thông tin như số tài khoản, tên người nhận. Nếu chuyển khoản thường không phải chuyển khoản nhanh còn cần đến cả thông tin về ngân hàng nhận và chi nhánh. Đối với QR code thì chỉ cần duy nhất 1 mã QR là bạn có thể thực hiện giao dịch mà không cần nhập thủ công các thông tin tài khoản của người thụ hưởng, giảm thiểu sai sót trong quá trình giao dịch.</p>
            <p>Bạn có thể xem hướng dẫn chuyển tiền bằng mã QR tại video</p>
            <p><iframe style="width: 100.0%;height: 400.0px;" src="https://www.youtube.com/embed/M5xVGLzVKA0" title="YouTube video player" frameborder="0" class="">&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;nbsp;</iframe></p>
            </div>
          </div>
          <div class="help-panel__article-content" data-tab-content="2" style="display: none;">
            <div class="help-panel__modal-header">
              <div class="help-panel__modal-close"></div>
            </div>
            <div class="help-panel__tab-content-body">
            <p class="font-semibold" style="font-size: 18.0px;line-height: 24.0px;color: rgb(0,0,0);">Điều kiện để có thể bắt đầu sử dụng được ứng dụng Techcombank Mobile?</p>
            <p>Khách hàng cần mở tài khoản thanh toán tại Techcombank, có thiết bị kết nối Internet và chạy hệ điều hành iOS 14.0 trở lên hoặc hệ điều hành Android 7.0 trở lên để có thể cài đặt và sử dụng dịch vụ ngân hàng số của Techcombank.</p>
            <p>Các khách hàng mới chưa sử dụng các sản phẩm, dịch vụ của Techcombank có thể đăng ký mở tài khoản và dịch vụ ngân hàng số trực tuyến ngay trên ứng dụng mà không cần tới quầy.</p>
            </div>
          </div>
        </div>
    `;
    require('../../site/scripts/help-panel');
  }
}

describe('test help panel', () => {
  test('should document ready', () => {
    global.setTimeout = jest.fn((callback) => callback());
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        find: () => {
          return {
            click: (callback) => callback({ target: 'test' }),
          };
        },
        width: jest.fn().mockReturnValue(991),
        hide: jest.fn(),
        removeClass: jest.fn(),
        css: jest.fn(),
        resize: (callback) => callback(),
        addClass: jest.fn(),
        data: jest.fn(),
        show: jest.fn(),
        hasClass: jest.fn().mockReturnValue(true),
      };
    });
    new TestHelpPanel();
    expect(document.readyState).toBe('complete');

    const tabContent = document.querySelector('.help-panel__tab-content');
    tabContent.dispatchEvent(new Event('click'));
    const iframe = tabContent.querySelector('.help-panel__article-content.open .help-panel__tab-content-body iframe');
    expect(iframe.src).toEqual('https://www.youtube.com/embed/M5xVGLzVKA0');
  });
});
