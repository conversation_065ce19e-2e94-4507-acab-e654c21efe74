import { beforeEach, describe, expect, it } from '@jest/globals';
import { getSessionCountData, updateSessionCountData } from '../../site/scripts/count-user-sessions';
import { StorageUtils } from '../../site/scripts/utils/storage.util';

const announcementId = 'announcement1';
const sampleInitData = {
  [announcementId]: {
    isClosed: false,
    count: 0,
  },
};

const sampleClosedData = {
  [announcementId]: {
    isClosed: true,
    count: 1,
  },
};

describe('countUserSession', () => {
  beforeEach(() => {
    StorageUtils.remove('session-count-data');
  });

  it('should get session count data', () => {
    const sessionCountData = getSessionCountData(announcementId);
    expect(sessionCountData).toEqual(sampleInitData);
  });

  it('should increase count value', () => {
    const sessionCountData = getSessionCountData(announcementId);
    sessionCountData[announcementId].isClosed = true;
    updateSessionCountData(sessionCountData);

    const updatedSessionCountData = getSessionCountData(announcementId);
    expect(updatedSessionCountData).toEqual(sampleClosedData);
  });
});
