import { describe, expect, jest, test } from '@jest/globals';

class TestInspirePrivilege {
  constructor() {
    require('../../site/scripts/inspire-privilege.js');
  }
}

jest.mock(
  'swiper',
  () =>
    class Mocked {
      snapGrid = {
        length: 500
      };

      translateTo() {
      }
    }
);

describe('inspire-privilege', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        length: 1,
        slick: jest.fn(),
        innerHeight: jest.fn().mockReturnValue(500),
        each: (callback) => callback(),
        on: (param, callback) => callback(),
        find: () => {
          return {
            attr: jest.fn(),
            each: (callback) => callback(),
          };
        },
        parent: () => {
          return {
            find: jest.fn(),
          };
        },
        not: () => {
          return {
            each: (callback) => callback(),
          };
        },
      };
    });

    new TestInspirePrivilege();
    expect(document.readyState).toBe('complete');
  });
});
