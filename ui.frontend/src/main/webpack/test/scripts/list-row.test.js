import { describe, expect, test, jest } from "@jest/globals";

class TestListRow {
  constructor() {
    require("../../site/scripts/list-row");
  }
}

describe("Test List Row", () => {
    test("should document is ready", () => {
        window.$ = jest.fn().mockImplementation((callback) => {
            if (typeof callback === "function") {
                callback({
                    each: (callback) => callback(),
                });
            }
            return {
                each: (callback) => callback(),
                data: jest.fn(),
                find: () => {
                    return {
                        value: "<div class='testClass'></div>",
                        css: jest.fn(),
                    }
                },
            }
        });
        new TestListRow();
        expect(document.readyState).toBe("complete");
    });
});