import { describe, expect, test, jest } from "@jest/globals";

class TestNews {
  constructor() {
    require("../../site/scripts/news");
  }
}

describe("Test News", () => {
  beforeEach(() => {
    document.body.innerHTML = "";
    global.setTimeout = jest.fn((cb) => cb());
  });

  test("should document is ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        0: {
          value: "123",
          getAttribute: jest.fn(),
        },
        find: () => {
          return {
            each: () => {
              return {
                click: () => {
                  return {
                    hasClass: jest.fn(),
                  };
                },
              };
            },
            find: jest.fn(),
            click: jest.fn(),
            on: (event, callback) => callback(),
            empty: jest.fn(),
            css: jest.fn(),
            append: jest.fn(),
            show: jest.fn(),
            html: jest.fn(),
            removeClass: jest.fn(),
            children: () => {
              return {
                length: 0,
              };
            },
            clone: () => {
              return {
                find: () => {
                  return {
                    0: {
                      value: "123",
                      setAttribute: jest.fn(),
                    },
                    addClass: jest.fn(),
                  };
                },
                appendTo: jest.fn(),
              };
            },
          };
        },
        on: (event, element, callback) => {
          if (callback) {
            callback({ target: { value: "123" } });
          } else {
            element({ target: { value: "123" } });
          }
        },
        width: jest.fn(),
        text: jest.fn(),
        html: jest.fn(),
        each: jest.fn(),
        attr: jest.fn().mockReturnValue("'attrMock'"),
        trigger: jest.fn(),
        css: jest.fn(),
        data: () => {
          return {
            value: "'123'",
            trackingClickInfoValue: '{"articleFilter": 123}',
          };
        },
        length: () => {
          return {
            getAttribute: jest.fn(),
          };
        },
        click: (callback) => {
          if (typeof callback === "function") {
            callback({});
          }
        },
        not: () => {
          return {
            removeClass: jest.fn(),
          };
        },
        addClass: jest.fn(),
        hasClass: jest.fn().mockReturnValue(true),
        scroll: jest.fn(),
      };
    });
    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, method, success, error }) => {
        if (typeof success === "function") {
          success({
            results: [
              {
                itemNo: "1",
                itemName: "item1",
                title: "'testTitle",
                articleCategory: "testCategory",
              },
              {
                itemNo: "2",
                itemName: "item2",
                title: "'testTitle2",
                articleCategory: "testCategory2",
                image: "testImg",
                category: "testCategory",
                articleCreationDate: "articleCreationDate",
                description: "description",
                path: "path",
              },
            ],
          });
        }
        if (typeof callback === "function") {
          error({});
        }
      });
    new TestNews();
    expect(document.readyState).toBe("complete");
  });
});
