import { describe, expect, jest, test } from "@jest/globals";

class TestPreviewDocumentModal {
  constructor() {
    document.body.innerHTML = `<body></body>`;
    require('../../site/scripts/preview-document-modal');
    require('pdfjs-dist/legacy/build/pdf.js');
  }
}

describe('PreviewDocumentModal', () => {
  test('should document document modal', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        addClass: jest.fn(),
        removeClass: jest.fn(),
        empty: jest.fn(),
        append: jest.fn(),
        css: jest.fn(),
        attr: jest.fn(),
        is: jest.fn().mockReturnValue(true),
        on: (param, callback) => callback({}, {
          url: 'string',
          documentType: 'media'
        }),
        click: (callback) => callback({
          target: 'sample'
        }),
        keyup: (callback) => callback({
          keycode: 27
        }),
        find: (param) => {
          return {
            text: jest.fn(),
            removeClass: jest.fn(),
            addClass: jest.fn(),
            css: jest.fn(),
            empty: jest.fn(),
            append: jest.fn(),
          };
        }
      };
    });
    new TestPreviewDocumentModal();
    expect(document.readyState).toEqual('complete');
  });
});
