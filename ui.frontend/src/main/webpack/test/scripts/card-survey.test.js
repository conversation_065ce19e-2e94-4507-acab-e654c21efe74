import { describe, expect, jest, test } from '@jest/globals';

class TestCardSurvey {
  constructor() {
    document.body.innerHTML = `<div class="survey-panel-data-container"></div>`;
    require('../../site/scripts/card-survey');
  }
}

const surveyPanelSample = {
  q1QuestionCountText: 'q1QuestionCountText',
  question1: 'question1',
  q1AnswerOption1: 'q1AnswerOption1',
  q1AnswerOption2: 'q1AnswerOption2',
  q1AnswerOption3: 'q1AnswerOption3',
  q1AnswerOption4: 'q1AnswerOption4',
  q1ProgressBarDescriptionTex: 'q1ProgressBarDescriptionTex',
  q2QuestionCountText: 'q2QuestionCountText',
  question2: 'question2',
  q2AnswerOption1: 'q2AnswerOption1',
  q2AnswerOption2: 'q2AnswerOption2',
  q2AnswerOption3: 'q2AnswerOption3',
  q2AnswerOption4: 'q2AnswerOption4',
  q2AnswerOption5: 'q2AnswerOption5',
  q2ProgressBarDescriptionTex: 'q2ProgressBarDescriptionTex',
  q3QuestionCountText: 'q3QuestionCountText',
  question3: 'question3',
  q3AnswerOption1: 'q3AnswerOption1',
  q3AnswerOption2: 'q3AnswerOption2',
  q3AnswerOption3: 'q3AnswerOption3',
  q3ProgressBarDescriptionTex: 'q3ProgressBarDescriptionTex',
  everydayCardImage: 'everydayCardImage',
  webEverydayCardImage: 'webEverydayCardImage',
  mobileEverydayCardImage: 'mobileEverydayCardImage',
  everydayCardImageAltText: 'everydayCardImageAltText',
  everydayCardUrl: 'everydayCardUrl',
  everydayCardWebInteractionTyp: 'everydayCardWebInteractionTyp',
  styleCardImage: 'styleCardImage',
  webStyleCardImage: 'webStyleCardImage',
  mobileStyleCardImage: 'mobileStyleCardImage',
  styleCardImageAltText: 'styleCardImageAltText',
  styleCardUrl: 'styleCardUrl',
  styleCardWebInteractionTyp: 'styleCardWebInteractionTyp',
  sparkCardImage: 'sparkCardImage',
  webSparkCardImage: 'webSparkCardImage',
  mobileSparkCardImage: 'mobileSparkCardImage',
  sparkCardImageAltText: 'sparkCardImageAltText',
  sparkCardUrl: 'sparkCardUrl',
  sparkCardWebInteractionTyp: 'sparkCardWebInteractionTyp',
  signatureCardImage: 'signatureCardImage',
  webSignatureCardImage: 'webSignatureCardImage',
  mobileSignatureCardImage: 'mobileSignatureCardImage',
  signatureCardImageAltText: 'signatureCardImageAltText',
  signatureCardUrl: 'signatureCardUrl',
  signatureCardWebInteractionTyp: 'signatureCardWebInteractionTyp',
  vietnamAirlineCardImage: 'vietnamAirlineCardImage',
  webVietnamAirlineCardImage: 'webVietnamAirlineCardImage',
  mobileVietnamAirlineCardImage: 'mobileVietnamAirlineCardImage',
  vietnamAirlineCardImageAltText: 'vietnamAirlineCardImageAltText',
  vietnamAirlineUrl: 'vietnamAirlineUrl',
  vietnamAirlineCardWebInteractionTyp: 'vietnamAirlineCardWebInteractionTyp',
  tryAgainCta: 'tryAgainCta',
  nextCta: 'nextCta',
};

describe('CardSurvey', () => {
  test('should document is ready', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        length: 1,
        ready: (callback) => callback(),
        resize: (callback) => callback(),
        each: (callback) => callback(),
        click: callback => callback(),
        on: (param, selector, callback) => callback({}),
        append: () => {},
        addClass: () => {},
        css: () => {},
        text: () => {},
        removeClass: () => {},
        val: () => 2,
        attr: () => JSON.stringify(surveyPanelSample),
      };
    });
    new TestCardSurvey();
    expect(document.readyState).toEqual('complete');
  });
});
