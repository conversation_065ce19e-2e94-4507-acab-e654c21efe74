import { describe, expect, jest, test } from '@jest/globals';

class TestRealEstateCalc {
  constructor() {
    document.body.innerHTML = `
    <div class="insurance-calculation__panel">
      <div class="panel-info"></div>
    </div>
    `;
    require("../../site/scripts/real-estate-calc");
  }
}

describe("test real estate calc", () => {
  test("should document ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        on: jest.fn(),
        text: jest.fn(),
        css: jest.fn(),
        attr: jest.fn(),
        find: () => {
          return {
            0: {
              value: {
                toString: jest.fn(),
                replaceAll: () => {
                  return {
                    replace: jest.fn().mockReturnValue("sample"),
                  };
                },
              },
              disabled: true,
            },
            click: (callback) =>
              callback({
                target: {
                  hasClass: jest.fn(),
                },
              }),
            each: (callback) => callback(),
          };
        },
        hasClass: jest.fn(),
        click: jest.fn(),
        length: 1,
        parent: () => {
          return {
            find: () => {
              return {
                trigger: jest.fn(),
              };
            },
          };
        },
      };
    });
    new TestRealEstateCalc();
    expect(document.readyState).toBe("complete");
  });
});
