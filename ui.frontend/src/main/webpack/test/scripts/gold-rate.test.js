import { describe, expect, test, jest } from "@jest/globals";

class TestGoldRate {
  constructor() {
    require("../../site/scripts/gold-rate");
  }
}

describe("Test Gold Rate", () => {
    beforeEach(() => {
        document.body.innerHTML = "";
        Array.prototype.toReversed = jest.fn().mockReturnValue({
            value: "['2024','07','18']",
            join: jest.fn(),
        });

        JSON.parse = jest.fn().mockImplementationOnce(() => {
            return { articleFilter: "sample" };
        });

        window.$ = jest.fn().mockImplementation(() => {
            return {
                ready: (callback) => callback(),
                on: (param, callback) => {
                    if(param == 'updateCal' && typeof callback === "function") {
                        callback({});
                    }

                    if(param == 'click' && typeof callback === "function") {
                        callback({});
                    }
                },
                attr: jest.fn().mockReturnValue("mockUrl"),
                data: jest.fn(),
                text: jest.fn().mockReturnValue("mockUrl"),
                empty: jest.fn(),
                append: jest.fn(),
                next: () => {
                    return {
                        css: jest.fn(),
                        hasClass: jest.fn().mockReturnValue("active"),
                        removeClass: jest.fn(),
                    }
                },
                outerHeight: jest.fn(),
                find: () => {
                    return {
                        each: (callback) => callback({}, {}),
                        on: (param, callback) => {
                            if(param == 'timeChanged' && typeof callback === "function") {
                                callback({});
                            }
                        },
                        find: () => {
                           return {
                            each: (callback) => {
                                if(typeof callback === "function") {
                                    callback({});
                                }

                                return {
                                    text: jest.fn(),
                                    on: (param, callback) => {
                                        if(param == 'click' && typeof callback === "function") {
                                            callback({});
                                        }
                                    },
                                };
                            },
                           };
                        },
                        addClass: jest.fn(),
                        removeClass: jest.fn(),
                        changeOptions: jest.fn(),
                    };
                },
            };
        });
    });

    test("should document is ready", () => {
        window.$.ajax = jest.fn().mockImplementation(({ url, type, dataType, success }) => {
            if (typeof success === "function") {
                success({
                    goldRate: {
                        data: [
                            {
                                itemId: "10469",
                                askRate: "6000010.0",
                                bidRate: "4999990.0"
                            }
                        ],
                        updatedTimes: [
                            {
                                time: "12:39:07.659951"
                            }
                        ]
                    }
                });
            }
        });
        new TestGoldRate();
        expect(document.readyState).toBe("complete");
    });
});