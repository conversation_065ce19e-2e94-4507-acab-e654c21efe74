import { describe, expect, test, jest } from "@jest/globals";

class TestConvertOneLink {
  constructor() {
    require("../../site/scripts/convert-one-link");
  }
}

describe("Test Convert One Link", () => {
  const prefixOneLink = "https://tcbmobile.onelink.me";

  beforeEach(() => {
    document.body.innerHTML = "";
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
      };
    });
  });

  test("should document is ready", () => {
    const anchor = document.createElement("a");
    anchor.setAttribute("href", `${prefixOneLink}?param1=test1&param2=test2`);
    document.body.append(anchor);
    new TestConvertOneLink();
    expect(document.readyState).toBe("complete");
  });
});
