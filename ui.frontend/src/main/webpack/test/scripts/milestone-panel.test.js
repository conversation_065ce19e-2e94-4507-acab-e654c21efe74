import { describe, expect, jest, test } from '@jest/globals';

class TestMilestonePanel {
  constructor() {
    require('../../site/scripts/milestone-panel');
  }
}

describe('MilestonePanel', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        addClass: () => {},
        find: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    find: () => {
                      return {
                        not: () => {
                          return {
                            slick: () => {},
                          };
                        },
                        eq: () => {
                          return {
                            addClass: () => {},
                          };
                        },
                        removeClass: () => {},
                        on: (param, callback) => callback({}, {}, {}, 1),
                        each: callback => callback(1),
                      };
                    },
                  };
                },
              };
            },
          };
        },
      };
    });

    new TestMilestonePanel();
    expect(document.readyState).toBe('complete');
  });
});
