import { describe, expect, jest, test } from '@jest/globals';

class TestTooltipPosition {
  constructor() {
    require('../../site/scripts/tooltip-position');
  }
}

describe('TooltipPosition', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        length: 0,
        hover: (callbackA, callbackB) => {
          callbackA();
          callbackB();
        },
        on: (param, callback) => callback(),
        empty: () => {},
        append: () => {},
        visible: () => {},
        invisible: () => {},
        get: () => {
          return {
            getBoundingClientRect: () => {
              return {
                left: 500,
              };
            },
          };
        },
        width: jest.fn().mockReturnValue(200),
        height: jest.fn().mockReturnValue(200),
        find: () => {
          return {
            text: () => {},
            get: () => {
              return {
                getBoundingClientRect: () => {
                  return {
                    left: 500,
                  };
                },
              };
            },
            css: () => {},
            outerWidth: jest.fn().mockReturnValue(200),
            width: jest.fn().mockReturnValue(100),
          };
        },
      };
    });

    window.$.fn = jest.fn().mockImplementation(() => {
      return {};
    });

    new TestTooltipPosition();
    expect(document.readyState).toBe('complete');
  });
});
