import { CardPicker } from '../../site/scripts/pick-card-listing';
import $ from 'jquery';
global.$ = $;

describe('initDataAndUpdateParamURLSearch', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = '';
    window.history.pushState({}, '', '/');
    jest.clearAllMocks();
    jest
      .spyOn(CardPicker.prototype, 'handleRenderComponentCompare')
      .mockImplementation(() => {});
    if (!customElements.get('card-picker')) {
      customElements.define('card-picker', CardPicker);
    }
    cardPicker = new CardPicker();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should do nothing if cardIdsParam is empty', () => {
    const spyPushState = jest.spyOn(window.history, 'pushState');
    cardPicker.initDataAndUpdateParamURLSearch([]);

    expect(spyPushState).not.toHaveBeenCalled();
    expect(cardPicker.handleRenderComponentCompare).not.toHaveBeenCalled();
  });

  it('should only keep max 3 unique cardIds and update URL correctly', () => {
    const spyPushState = jest.spyOn(window.history, 'pushState');

    document.body.innerHTML = `
        <div class="button-item-compare" data-id="1" data-image="img1.png"></div>
        <div class="button-item-compare" data-id="2" data-image="img2.png"></div>
        <div class="button-item-compare" data-id="3" data-image="img3.png"></div>
        <div class="button-item-compare" data-id="4" data-image="img4.png"></div>
      `;

    cardPicker.initDataAndUpdateParamURLSearch(['1', '2', '3', '4', '2']);

    expect(spyPushState).toHaveBeenCalled();
    expect(window.location.search).toBe('?cardId=1&cardId=2&cardId=3');
  });

  it('should push correct data to listDataImageCompare', () => {
    document.body.innerHTML = `
        <div class="button-item-compare" data-id="1" data-image="img1.png"></div>
        <div class="button-item-compare" data-id="2" data-image="img2.png"></div>
        <div class="button-item-compare" data-id="3" data-image="img3.png"></div>
      `;

    cardPicker.initDataAndUpdateParamURLSearch(['1', '2']);

    expect(cardPicker.listDataImageCompare).toEqual([
      { id: '1', img: 'img1.png' },
      { id: '2', img: 'img2.png' },
    ]);
  });

  it('should call handleRenderComponentCompare if cardIdsGlobal and buttons exist', () => {
    document.body.innerHTML = `
  <div class="button-item-compare" data-id="1" data-image="img1.png"></div>
    <div class="button-item-compare" data-id="2" data-image="img2.png"></div>
      `;
    cardPicker.initDataAndUpdateParamURLSearch(['1', '2']);
    expect(cardPicker.handleRenderComponentCompare).toHaveBeenCalled();
  });

  it('should not duplicate items in listDataImageCompare', () => {
    document.body.innerHTML = `
        <div class="button-item-compare" data-id="1" data-image="img1.png"></div>
        <div class="button-item-compare" data-id="2" data-image="img2.png"></div>
      `;
    cardPicker.listDataImageCompare.push({ id: '1', img: 'img1.png' }); // giả có sẵn 1 item
    cardPicker.initDataAndUpdateParamURLSearch(['1', '2']);
    expect(cardPicker.listDataImageCompare).toEqual([
      { id: '1', img: 'img1.png' },
      { id: '2', img: 'img2.png' },
    ]);
  });
});

describe('handleGetLabelButtonSelectCompare', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = `
        <button class="button-item-compare"
                data-add-label="Thêm để so sánh"
                data-added-label="Đã thêm"
                data-id="2"
                >
          Add to compare
          <img alt="icon-arrow" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/add-icon.svg">
        </button>
      `;

    cardPicker = new CardPicker();
    cardPicker.checkIconSvg = '/path/to/check-icon.svg';
    cardPicker.addIconSvg = '/path/to/add-icon.svg';
  });

  it('should set contentBTNCompare and contentBTNCompareAdded based on data attributes', () => {
    cardPicker.handleGetLabelButtonSelectCompare();

    expect(cardPicker.contentBTNCompare).toBe(
      'Thêm để so sánh <img alt="icon-arrow" src="/path/to/add-icon.svg">',
    );

    expect(cardPicker.contentBTNCompareAdded.replace(/\s+/g, ' ').trim()).toBe(
      'Đã thêm <img alt="/path/to/check-icon.svg" class="icon-svg-checked" src="/path/to/check-icon.svg">',
    );
  });
});

describe('handleRenderDataFilter', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = '';
    window.history.pushState({}, '', '/');
    jest.clearAllMocks();
    jest
      .spyOn(CardPicker.prototype, 'handleRenderPickCardForFilter')
      .mockImplementation(() => {});
    cardPicker = new CardPicker();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should populate listDataFilterPicker with data-type from pick-card-container', () => {
    document.body.innerHTML = `
      <div class="credit-card-listing__items">
        <div class="pick-card-container" data-type="visa"></div>
        <div class="pick-card-container" data-type="amex"></div>
      `;

    cardPicker.handleRenderDataFilter();
    expect(cardPicker.listDataFilterPickerGLobal).toEqual({
      visa: 'visa',
      amex: 'amex',
    });
  });

  it('should hide filter items not in listDataFilterPicker', () => {
    document.body.innerHTML = `
        <div class="pick-card-container" data-type="visa"></div>
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="visa">Visa</button>
        </div>
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="mastercard">MasterCard</button>
        </div>
      `;
    cardPicker.handleRenderDataFilter();
    const mastercardFilterItem = document.querySelector(
      '.filter__item button[data-card-type="mastercard"]',
    );
    expect(mastercardFilterItem.closest('.filter__item').style.display).toBe(
      'none',
    );
  });

  it('should add or remove filter-selected class on button click', () => {
    document.body.innerHTML = `
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="visa">Visa</button>
        </div>
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="mastercard">MasterCard</button>
        </div>
      `;
    cardPicker.handleRenderDataFilter();
    cardPicker.listDataFilter['visa'] = 'visa';
    document
      .querySelector('.filter__item button[data-card-type="visa"]')
      .click();
    expect(cardPicker.listDataFilter['visa']).toBeUndefined();
    expect(
      document
        .querySelector('.filter__item button[data-card-type="visa"]')
        .classList.contains('filter-selected'),
    ).toBeFalsy();

    document
      .querySelector('.filter__item button[data-card-type="mastercard"]')
      .click();
    expect(cardPicker.listDataFilter['mastercard']).toBe('mastercard');
    expect(
      document
        .querySelector('.filter__item button[data-card-type="mastercard"]')
        .classList.contains('filter-selected'),
    ).toBeTruthy();
  });

  it('should not add duplicate entries to listDataFilter', () => {
    document.body.innerHTML = `
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="visa">Visa</button>
        </div>
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="mastercard">MasterCard</button>
        </div>
      `;

    cardPicker.handleRenderDataFilter();

    cardPicker.listDataFilter['visa'] = 'visa';

    document
      .querySelector('.filter__item button[data-card-type="visa"]')
      .click();
    document
      .querySelector('.filter__item button[data-card-type="visa"]')
      .click();

    expect(
      Object.keys(cardPicker.listDataFilter).filter((key) => key === 'visa')
        .length,
    ).toBe(1);
  });

  it('should call handleRenderPickCardForFilter when filter button is clicked', () => {
    document.body.innerHTML = `
        <div class="filter__item">
          <button class="credit-card-listing__button" data-card-type="visa">Visa</button>
        </div>
      `;

    cardPicker.handleRenderDataFilter();

    document
      .querySelector('.filter__item button[data-card-type="visa"]')
      .click();
    expect(cardPicker.handleRenderPickCardForFilter).toHaveBeenCalled();
  });
});

describe('handleRenderPickCardForFilter', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = '';
    window.history.pushState({}, '', '/');
    jest.clearAllMocks();
    jest
      .spyOn(CardPicker.prototype, 'handleRenderPickCardForFilter')
      .mockImplementation(() => {});
    if (!customElements.get('card-picker')) {
      customElements.define('card-picker', CardPicker);
    }
    cardPicker = new CardPicker();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return early if no .listing-pick-card-container exists', () => {
    const spy = jest.spyOn($.fn, 'css');
    cardPicker.handleRenderPickCardForFilter();
    expect(spy).not.toHaveBeenCalled();
  });

  it('should do nothing if there is no .listing-pick-card-container', () => {
    expect(() => {
      cardPicker.handleRenderPickCardForFilter();
    }).not.toThrow();
  });

  it('handleRenderPickCardForFilter should called when button click', () => {
    document.body.innerHTML = `
      <div class="listing-pick-card-container">
        <div class="pick-card-container" data-type="visa,mastercard" ></div>
        <div class="pick-card-container" data-type="amex" ></div>
      </div>

      <div class="filter__item">
        <button class="credit-card-listing__button" data-card-type="visa">Visa</button>
      </div>
    `;
    const spy = jest.spyOn(cardPicker, 'handleRenderPickCardForFilter');
    expect($('.listing-pick-card-container').length).toBe(1);

    cardPicker.handleRenderDataFilter();
    const buttonVisa = document.querySelector('[data-card-type="visa"]');
    buttonVisa.click();
    expect(spy).toHaveBeenCalled();
  });
});

describe('handleRenderComponentCompare', () => {
  let cardPicker;
  beforeEach(() => {
    document.body.innerHTML = '';
    cardPicker = new CardPicker();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should do nothing if .compare-choosing does not exist', () => {
    cardPicker.handleRenderComponentCompare();
    const toggleCompareButtonState = jest
      .spyOn(cardPicker, 'toggleCompareButtonState')
      .mockImplementation(() => {});

    const toggleCompareComponentVisibility = jest
      .spyOn(cardPicker, 'toggleCompareComponentVisibility')
      .mockImplementation(() => {});

    const renderCompareContent = jest
      .spyOn(cardPicker, 'renderCompareContent')
      .mockImplementation(() => {});

    expect(toggleCompareButtonState).not.toHaveBeenCalled();
    expect(toggleCompareComponentVisibility).not.toHaveBeenCalled();
    expect(renderCompareContent).not.toHaveBeenCalled();
  });

  it('should call handleRenderComponentCompare', () => {
    const handleRenderComponentCompare = jest
      .spyOn(cardPicker, 'handleRenderComponentCompare')
      .mockImplementation(() => {});
    document.body.innerHTML = `
   <div class="button-item-compare" data-id="1" data-image="img1.png"></div>
     <div class="button-item-compare" data-id="2" data-image="img2.png"></div>
    `;
    cardPicker.initDataAndUpdateParamURLSearch(['1', '2']);
    expect(handleRenderComponentCompare).toHaveBeenCalled();
  });

  it('should call renderCompareContent if .list-card__item exists and listDataImageCompare is not empty', () => {
    const toggleCompareButtonState = jest
      .spyOn(cardPicker, 'toggleCompareButtonState')
      .mockImplementation(() => {});
    const toggleCompareComponentVisibility = jest
      .spyOn(cardPicker, 'toggleCompareComponentVisibility')
      .mockImplementation(() => {});
    const renderCompareContent = jest
      .spyOn(cardPicker, 'renderCompareContent')
      .mockImplementation(() => {});

    document.body.innerHTML = `
      <div class="compare-choosing"></div>
      <div class="list-card__item"></div>
    `;

    cardPicker.listDataImageCompare.push({ id: '1', img: 'img1' });

    cardPicker.handleRenderComponentCompare();

    expect(toggleCompareButtonState).toHaveBeenCalled();
    expect(toggleCompareComponentVisibility).toHaveBeenCalled();
    expect(renderCompareContent).toHaveBeenCalled();
  });
});

describe('handleRenderCompareCard', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = '';
    cardPicker = new CardPicker();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should disable compare button when 3 cards are already selected', () => {
    document.body.innerHTML = `
      <div class="pick-card-container" data-id="999">
        <button class="button-item-compare"></button>
      </div>
    `;

    cardPicker.cardIdsGlobal = ['111', '222', '333'];

    cardPicker.handleRenderCompareCard();

    const button = document.querySelector('.button-item-compare');

    expect(button.classList.contains('button-item-disabled')).toBe(true);
    expect(button.classList.contains('button-item-compare-added')).toBe(false);
  });
});

describe('handleUpdateParamsUrl', () => {
  let cardPicker;
  let mockPushState;
  let mockHandleRenderCompareCard;
  let mockHandleRenderComponentCompare;
  const originalPushState = window.history.pushState;

  beforeEach(() => {
    document.body.innerHTML = '';

    global.urlParams = new URLSearchParams();
    global.listDataImageCompare = [];
    mockPushState = jest.fn();
    window.history.pushState = mockPushState;
    cardPicker = new CardPicker();
    mockHandleRenderCompareCard = jest.fn();
    mockHandleRenderComponentCompare = jest.fn();
    cardPicker.handleRenderCompareCard = mockHandleRenderCompareCard;
    cardPicker.handleRenderComponentCompare = mockHandleRenderComponentCompare;
  });

  afterEach(() => {
    window.history.pushState = originalPushState;
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should do nothing if params is not provided', () => {
    cardPicker.handleUpdateParamsUrl(null, 'image1.jpg', false);

    expect(global.urlParams.getAll('cardId')).toEqual([]);
    expect(global.listDataImageCompare).toEqual([]);
    expect(mockHandleRenderCompareCard).not.toHaveBeenCalled();
    expect(mockHandleRenderComponentCompare).not.toHaveBeenCalled();
  });

  it('should remove param and image when isRemove is true', () => {
    cardPicker.listDataImageCompare = [
      { id: '1', img: 'img1' },
      { id: '2', img: 'img2' },
      { id: '3', img: 'img3' },
    ];
    cardPicker.cardIdsGlobal = ['1', '2', '3'];
    cardPicker.handleUpdateParamsUrl('2', '', true);

    expect(cardPicker.listDataImageCompare).toEqual([
      { id: '1', img: 'img1' },
      { id: '3', img: 'img3' },
    ]);
  });
});

describe('updateCompareButtonLink', () => {
  let button;
  let cardPicker;
  beforeEach(() => {
    document.body.innerHTML = '';
    global.cardIdsGlobal = [];
    button = document.createElement('a');
    button.setAttribute('href', 'https://example.com/page?oldQuery=123');
    button.className = 'compare-button__link';
    document.body.appendChild(button);

    cardPicker = new CardPicker();
  });

  it('should do nothing if compareButtonLink is null', () => {
    expect(() => cardPicker.updateCompareButtonLink(null)).not.toThrow();
  });

  it('should not update if href does not exist', () => {
    $(button).removeAttr('href');
    cardPicker.updateCompareButtonLink($('.compare-button__link'));
    const updatedHref = $('.compare-button__link').attr('href');
    expect(updatedHref).toBeUndefined();
  });
});

describe('handleEventClickButtonCompareCard', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = `
        <button
          class="button-item-compare"
          data-id="123"
          data-image="img1.jpg"
        >
          Compare
        </button>
        <button
          class="button-item-compare button-item-compare-added"
          data-id="456"
          data-image="img2.jpg"
        >
          Compare
        </button>
        <button
          class="button-item-compare button-item-disabled"
          data-id="789"
          data-image="img3.jpg"
        >
          Compare
        </button>
      `;
    jest.clearAllMocks();

    cardPicker = new CardPicker();
  });

  it('should not call handleUpdateParamsUrl for buttons with "button-item-compare-added" class', () => {
    const handleUpdateParamsUrl = jest
      .spyOn(cardPicker, 'handleUpdateParamsUrl')
      .mockImplementation(() => {});

    cardPicker.handleEventClickButtonCompareCard();

    document.querySelector('.button-item-compare-added').click();

    expect(handleUpdateParamsUrl).not.toHaveBeenCalled();
  });

  it('should not call handleUpdateParamsUrl for buttons with "button-item-disabled" class', () => {
    const handleUpdateParamsUrl = jest
      .spyOn(cardPicker, 'handleUpdateParamsUrl')
      .mockImplementation(() => {});

    cardPicker.handleEventClickButtonCompareCard();

    document.querySelector('.button-item-disabled').click();

    expect(handleUpdateParamsUrl).not.toHaveBeenCalled();
  });

  it('should bind and unbind click events correctly', () => {
    const button = document.querySelector('.button-item-compare');
    const mockOff = jest.spyOn($.fn, 'off');
    const mockOn = jest.spyOn($.fn, 'on');

    cardPicker.handleEventClickButtonCompareCard();

    expect(mockOff).toHaveBeenCalledWith('click');
    expect(mockOn).toHaveBeenCalledWith('click', expect.any(Function));

    button.click();
  });
});

describe('toggleCompareButtonState', () => {
  let compareButtonLink;
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = `
        <a class="compare-button__link" href="#"></a>
      `;
    compareButtonLink = document.querySelector('.compare-button__link');

    cardPicker = new CardPicker();

    jest.clearAllMocks();
  });

  it('should enable the compare button when lenDataImageCompare > 1', () => {
    cardPicker.toggleCompareButtonState(2);

    expect(compareButtonLink.hasAttribute('disabled')).toBe(false);
  });
});

describe('renderCompareContent', () => {
  let cardPicker;

  beforeEach(() => {
    document.body.innerHTML = '';
    if (!customElements.get('card-picker')) {
      customElements.define('card-picker', CardPicker);
    }
    cardPicker = new CardPicker();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should return early if no .list-card__item exists', () => {
    const spy = jest.spyOn(cardPicker, 'handleUpdateParamsUrl');
    cardPicker.listDataImageCompare = [{ id: '1', img: 'img1.png' }];

    cardPicker.renderCompareContent();

    expect(spy).not.toHaveBeenCalled();
  });

  it('should render image compare data and bind remove event', () => {
    document.body.innerHTML = `
    <div class="list-card__item">
      <img class="img-card-compare" src="old.png" />
      <button class="remove-button" data-id="old-id"></button>
    </div>
    <div class="list-card__item">
      <img class="img-card-compare" src="old2.png" />
      <button class="remove-button" data-id="old-id2"></button>
    </div>
  `;

    cardPicker.listDataImageCompare = [
      { id: '1', img: 'img1.png' },
      { id: '2', img: 'img2.png' },
    ];

    const handleUpdateParamsUrl = jest
      .spyOn(cardPicker, 'handleUpdateParamsUrl')
      .mockImplementation(() => {});

    cardPicker.renderCompareContent();

    const listItems = document.querySelectorAll('.list-card__item');
    expect(listItems.length).toBe(2);

    listItems.forEach((item, index) => {
      expect(item.classList.contains('has-card')).toBe(true);

      const img = item.querySelector('.img-card-compare');
      expect(img.getAttribute('src')).toBe(`img${index + 1}.png`);

      const btn = item.querySelector('.remove-button');
      expect(btn.getAttribute('data-id')).toBe(`${index + 1}`);

      // Trigger click to test bound function
      btn.click();
      expect(handleUpdateParamsUrl).toHaveBeenCalledWith(
        `${index + 1}`,
        '',
        true,
      );
    });
  });

  it('should render normal compare button when isMaxCompare is true', () => {
    cardPicker.cardIdsGlobal = ['1', '2', '3'];
    document.body.innerHTML = `
      <div class="pick-card-container" data-id="123">
        <button class="button-item-compare"></button>
      </div>
    `;
    cardPicker.contentBTNCompare = '<span>Compare</span>';
    cardPicker.handleRenderCompareCard();

    const button = document.querySelector('.button-item-compare');

    expect(button.classList.contains('button-item-compare-added')).toBe(false);
    expect(button.classList.contains('button-item-disabled')).toBe(true);
    expect(button.innerHTML).toContain('<span>Compare</span>');
  });
});

describe('cardPicker - init()', () => {
  let cardPicker;

  beforeEach(() => {
    cardPicker = new CardPicker();
    jest
      .spyOn(cardPicker, 'handleGetLabelButtonSelectCompare')
      .mockImplementation(() => {});
    jest
      .spyOn(cardPicker, 'handleRenderDataFilter')
      .mockImplementation(() => {});
    jest
      .spyOn(cardPicker, 'initDataAndUpdateParamURLSearch')
      .mockImplementation(() => {});
    jest
      .spyOn(cardPicker, 'handleRenderCompareCard')
      .mockImplementation(() => {});
    jest
      .spyOn(cardPicker, 'handleEventClickButtonCompareCard')
      .mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should call all methods inside init()', () => {
    cardPicker.init();

    expect(cardPicker.handleGetLabelButtonSelectCompare).toHaveBeenCalled();
    expect(cardPicker.handleRenderDataFilter).toHaveBeenCalled();
    expect(cardPicker.initDataAndUpdateParamURLSearch).toHaveBeenCalledWith(
      cardPicker.cardIdsGlobal,
    );
    expect(cardPicker.handleRenderCompareCard).toHaveBeenCalled();
    expect(cardPicker.handleEventClickButtonCompareCard).toHaveBeenCalled();
  });

  it('should call init() without throwing error (no mocking)', () => {
    expect(() => cardPicker.init()).not.toThrow();
  });
});

// yarn test --coverage --collectCoverageFrom='src/main/webpack/site/scripts/pick-card-listing.js' --testPathPattern='src/main/webpack/test/scripts/pick-card-listing.test.js' --verbose
