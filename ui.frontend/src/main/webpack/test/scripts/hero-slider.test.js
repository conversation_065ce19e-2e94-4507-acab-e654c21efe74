import { describe, expect, jest, test } from '@jest/globals';

class TestHeroSlider {
  constructor() {
    require('../../site/scripts/hero-slider');
  }
}

describe('HeroSlider', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        find: () => {
          return {
            find: () => {
              return {
                0: {
                  classList: {
                    add: () => {},
                    remove: () => {},
                  },
                },
              };
            },
            text: () => {},
            each: callback => callback({}),
            append: () => {},
            length: 1,
            0: {
              classList: {
                add: () => {},
                remove: () => {},
              },
            },
          };
        },
      };
    });

    new TestHeroSlider();
    new HeroSlider($('div'));
    expect(document.readyState).toBe('complete');
  });
});
