import { describe, expect, test, jest } from "@jest/globals";

class TestArticleRteAnalytics {
  constructor() {
    require("../../site/scripts/articles-rte-analytics");
  }
}

describe("test article rte analytics", () => {
  beforeEach(() => {
    global.setTimeout = jest.fn((cb) => cb());
  });
  test("should document ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        attr: () => {
          return {
            value: true,
            startsWith: jest.fn().mockReturnValue(".http"),
            endsWith: jest.fn().mockReturnValue(".pdf"),
            includes: jest.fn().mockReturnValue("test"),
          };
        },
        text: jest.fn(),
      };
    });
    new TestArticleRteAnalytics();
    expect(document.readyState).toBe("complete");
  });
});
