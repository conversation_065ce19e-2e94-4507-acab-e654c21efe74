import { TcbGlossary } from "../../site/scripts/glossary";
import { expect, jest } from "@jest/globals";

jest.mock("jquery");

const glossary = document.createElement("div");
glossary.setAttribute("data-key-word-per-page", 5);
const listAlphabet = document.createElement("a");
listAlphabet.innerText = "test";
const title = document.createElement("h3");
title.classList.add("container-title");

const glossaryCard = document.createElement("div");
glossaryCard.classList.add("glossary-card");
const lineBreak = document.createElement("div");
lineBreak.classList.add("line-break");
glossaryCard.appendChild(lineBreak);

glossary.appendChild(listAlphabet);
glossary.appendChild(title);

class GlossaryTestComponent extends TcbGlossary {
  constructor() {
    super();
  }

  querySelector(className) {
    const mockGetAttribute = (className) => {
      if (className === "data-key") {
        return "a";
      }
      if (className === "data-term") {
        return "test";
      }
      if (className === "data-page-length") {
        return "10";
      }
      if (className === "data-page-index") {
        return "1";
      }
    };

    const mockEventListener = () => {
      return;
    };

    if (className === ".glossary-container") {
      return {
        querySelector: () => {
          return {
            value: "1223",
          };
        },
        querySelectorAll: () => {
          return [
            {
              getAttribute: (className) => mockGetAttribute(className),
              setAttribute: () => {},
              removeAttribute: () => {},
            },
          ];
        },
      };
    }

    if (className === ".glossary-alphabet") {
      return {
        style: {
          setProperty: () => {},
        },
        querySelector: () => {
          return {};
        },
        querySelectorAll: () => {
          return [
            {
              getAttribute: (className) => mockGetAttribute(className),
              setAttribute: () => {},
              removeAttribute: () => {},
              classList: {
                add: () => {},
              },
              addEventListener: () => {},
            },
          ];
        },
      };
    }
    return glossary;
  }

  querySelectorAll(className) {
    const mockGetAttribute = (className) => {
      if (className === "data-key") {
        return "a";
      }
      if (className === "data-term") {
        return "test";
      }
      if (className === "data-page-length") {
        return "10";
      }
      if (className === "data-page-index") {
        return "1";
      }
    };
    if (className === ".glossary-card:not([hidden])") {
      return [
        {
          getAttribute: (className) => mockGetAttribute(className),
          querySelector: () => {
            return {
              classList: {
                add: () => {},
              },
            };
          },
        },
      ];
    }
    return [glossaryCard];
  }
}
customElements.define("tcb-glossary", GlossaryTestComponent);
describe("TcbGlossary", () => {
  let tcbGlossary = new GlossaryTestComponent();

  it("should work", () => {
    expect(tcbGlossary).toBeTruthy();
  });

  it("should get page size", () => {
    expect(tcbGlossary.pageSize).toBe("5");
  });

  it("should run function displayPageIndex", () => {
    tcbGlossary.onClickAlphabet("a");
    expect(tcbGlossary.pageLength).toBe(2);
  });

  it("should run function initGlossary", () => {
    tcbGlossary.url.searchParams.set("term", "test");
    const mockInitGlossaryFn = jest.spyOn(tcbGlossary, "initGlossary");
    tcbGlossary.initGlossary();
    expect(mockInitGlossaryFn).toHaveBeenCalledTimes(1);
  });
});
