import { describe, expect, test, jest } from "@jest/globals";

class TestCustomCodeComp {
  constructor() {
    require("../../site/scripts/custom-code-comp");
  }
}

describe("Test Custom Code Comp", () => {
    beforeEach(() => {
        document.body.innerHTML = "";
        document.body.innerHTML = `<div data-tracking-click-info-value="{'linkClick' : '@textIn(.mock-test)'}"></div>`;
    });

    test("should document is ready", () => {

        window.$ = jest.fn().mockImplementation((callback) => {
            if (typeof callback === "function") {
                callback({
                    attr: jest.fn(),
                    indexOf: jest.fn(),
                });
            }

            return {
                each: (callback) => {
                    if (typeof callback === "function") {
                        callback({
                            attr: jest.fn(),
                        });
                    }
                },
                attr: () => {
                    return {
                        indexOf: jest.fn(),
                        substring: jest.fn().mockReturnValue('(.mock-test)'),
                        replaceAll: jest.fn(),
                        substr: jest.fn(),
                        text: jest.fn(),
                        trim: jest.fn(),
                    }
                },
                find: () => {
                    return {
                        text: jest.fn().mockReturnValue('mock-test'),
                    }
                },
                parent: () => {
                    return {
                        find: () => {
                            return {
                                text: jest.fn().mockReturnValue('mock-test')
                            }
                        },
                    }
                },
            }
        });
        new TestCustomCodeComp();
        expect(document.readyState).toBe("complete");
      });
});