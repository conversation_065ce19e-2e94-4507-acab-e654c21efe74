import { describe, expect, test, jest } from "@jest/globals";

class TestUtmParam {
  constructor() {
    require("../../site/scripts/utm_parameters");
  }
}

describe("Test Utm Parameters", () => {
  beforeEach(() => {
    document.body.innerHTML = "";
    delete window.location;
    window.location = new URL(
      "http://localhost/test2?utm_source=1&af_sub1=test2&af_sub2=test3"
    );
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        forEach: (callback) => callback(),
      };
    });
    const anchor = document.createElement("a");
    anchor.setAttribute("href", "http://localhost/test2?utm_source=1");
    document.body.append(anchor);
  });

  test("should document is ready", () => {
    new TestUtmParam();
    expect(document.readyState).toBe("complete");
  });
});
