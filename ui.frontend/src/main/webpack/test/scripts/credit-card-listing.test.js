import { describe, expect, jest, test } from '@jest/globals';

class TestCreditCardListing {
  constructor() {
    require('../../site/scripts/credit-card-listing');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    window.location = {
      href: 'https://techcombank.com/?cardId=1',
    };
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        attr: jest.fn().mockReturnValue([{}]),
        data: () => {
          return {
            split: () => {
              return {
                map: () => {
                  return {
                    includes: jest.fn(),
                  };
                },
              };
            },
          };
        },
        on: (event, input, callback) => callback({ currentTarget: 'test' }),
        find: () => {
          return {
            find: () => {
              return {
                insertAfter: jest.fn(),
                append: jest.fn(),
                click: (callback) => callback(),
                find: jest.fn(),
                each: jest.fn(),
              };
            },
            data: jest.fn(),
            attr: jest.fn(),
            eq: jest.fn(),
            click: jest.fn(),
            each: (callback) => callback(),
            removeClass: jest.fn(),
            length: true,
          };
        },
        width: jest.fn().mockReturnValue(993),
        resize: (callback) => callback(),
        each: (callback) => callback(),
        addClass: () => {
          return {
            attr: () => {
              return {
                attr: () => {
                  return {
                    text: () => {
                      return {
                        on: (event, callback) => callback(),
                      };
                    },
                  };
                },
              };
            },
            append: () => {
              return {
                attr: () => {
                  return {
                    attr: () => {
                      return {
                        attr: jest.fn(),
                      };
                    },
                  };
                },
              };
            },
          };
        },
        toggleClass: jest.fn(),
        hide: jest.fn(),
        length: 1,
        removeClass: jest.fn(),
      };
    });

    window.$.ajax = jest
      .fn()
      .mockImplementation(
        ({ url, method, data, contentType, success, error }) => {
          if (typeof success === 'function') {
            success({
              data: {
                credit_debitCardDetailContentFragmentList: {
                  items: [
                    {
                      addedLabelText: 'addedLabelText',
                      addToCompareLabel: 'addToCompareLabel',
                      id: 123,
                      cardNudgeType: [{}],
                      cardImage: {
                        _path: 'path',
                      },
                      registerNowLabel: true,
                      displaySpecialOffer: true,
                    },
                  ],
                },
              },
            });
          }
          if (typeof callback === 'function') {
            error({ error: 'test' });
          }
        },
      );
    new TestCreditCardListing();
    expect(document.readyState).toBe('complete');
  });
});
