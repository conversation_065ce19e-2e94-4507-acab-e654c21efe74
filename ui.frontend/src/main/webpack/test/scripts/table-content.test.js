import { describe, expect, jest, test } from '@jest/globals';

class TestTableContent {
  constructor() {
    require('../../site/scripts/table-content');
  }
}

describe('TableContent', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: callback => callback(),
        each: callback => callback(),
        find: () => {
          return {
            on: (param, callback) => callback({
              preventDefault: () => {},
            }),
            height: jest.fn().mockReturnValue('200'),
            toggleClass: () => {},
            hasClass: jest.fn().mockReturnValue(true),
          };
        },
        width: jest.fn().mockReturnValue('800'),
        height: jest.fn().mockReturnValue('200'),
        length: 2,
        css: () => {},
        attr: () => {},
        offset: () => {
          return {
            top: 2,
          };
        },
        removeClass: () => {},
        scrollTop: () => {
          return 200;
        },
        on: (param, callback) => callback(),
      };
    });

    window.scrollTo = jest.fn();

    new TestTableContent();
    expect(document.readyState).toBe('complete');
  });
});
