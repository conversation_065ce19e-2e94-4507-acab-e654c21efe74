import { describe, expect, test, jest } from "@jest/globals";

class TestSaveCalc {
  constructor() {
    require("../../site/scripts/save-calc");
  }
}

describe("Test Save Calc", () => {
  beforeEach(() => {
    document.body.innerHTML = "";
  });

  test("should document is ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        0: {
          value: "123",
          addEventListener: (param, callback) => {
            if (param === "setvaluemouseup" && typeof callback === "function") {
              callback({});
            }
          },
        },
        ready: (callback) => callback(),
        each: (callback) => callback(),
        length: 10,
        append: jest.fn(),
        attr: jest.fn(),
        data: jest.fn(),
        on: jest.fn(),
        find: () => {
          return {
            0: {
              value: "123",
              innerText: "100.000.000",
            },
            find: () => {
              return {};
            },
          };
        },
        closest: () => {
          return {
            find: () => {
              return {
                forceNumericOnly: jest.fn(),
                html: jest.fn(),
                keypress: () => {
                  return `<div class="save-calculation">
                                        <input name='depositMoney' value="1234">
                                        <input name='depositMonth' value="1234">
                                    </div>`;
                },
                on: (param, callback) => {
                  if (param == "input" && typeof callback === "function") {
                    callback({
                      val: () => {
                        return "1,000,000,000";
                      },
                    });
                  }

                  if (param == "focusout") {
                    callback({
                      trackAnalytics: () => {
                        return {
                          find: jest.fn(),
                        };
                      },
                    });
                  }
                },
                keyup: (callback) => {
                  if (typeof callback === "function") {
                    callback({
                      val: () => {
                        return "1,000,000,000";
                      },
                    });
                  }

                  return {
                    value: jest.fn().mockReturnValue("mockUrl"),
                  };
                },
                val: () => {
                  return "1,000,000,000";
                },
                addEventListener: (param, callback) => {
                  if (
                    param == "setvaluemouseup" &&
                    typeof callback === "function"
                  ) {
                    return "";
                  }
                },
                keypress: (callback) => {
                  if (typeof callback === "function") {
                    callback({});
                  }
                },
              };
            },
            val: () => {
              return "1,000,000,000";
            },
          };
        },
        val: () => {
          return "";
        },
      };
    });
    new TestSaveCalc();
    expect(document.readyState).toBe("complete");
  });
});
