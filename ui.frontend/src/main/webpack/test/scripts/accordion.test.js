import { describe, expect, test, jest } from "@jest/globals";

class TestAccordion {
  constructor() {
    require("../../site/scripts/accordion");
  }
}

describe("test accordion", () => {
  test("should document ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        find: () => {
          return {
            each: (callback) => callback(),
            click: (callback) => callback(),
            attr: jest.fn().mockReturnValue(1),
            parent: jest.fn(),
            hide: jest.fn(),
            show: jest.fn(),
            text: jest.fn(),
            eq: () => {
              return {
                find: () => {
                  return {
                    parent: () => {
                      return {
                        parent: () => {
                          return {
                            find: () => {
                              return {
                                find: () => {
                                  return {
                                    outerHeight: jest.fn(),
                                    css: jest.fn().mockReturnValue("hidden"),
                                  };
                                },
                                outerHeight: jest.fn(),
                                css: jest.fn(),
                                hide: jest.fn(),
                                show: jest.fn(),
                              };
                            },
                            css: jest.fn(),
                          };
                        },
                      };
                    },
                    find: () => {
                      return {
                        hide: jest.fn(),
                        show: jest.fn(),
                      };
                    },
                  };
                },
              };
            },
          };
        },
        parent: () => {
          return {
            parent: () => {
              return {
                find: () => {
                  return {
                    find: () => {
                      return {
                        outerHeight: jest.fn(),
                        css: jest.fn().mockReturnValue("show"),
                      };
                    },
                    outerHeight: jest.fn(),
                    css: jest.fn(),
                    hide: jest.fn(),
                    show: jest.fn(),
                  };
                },
                css: jest.fn(),
              };
            },
          };
        },
        data: jest.fn(),
        attr: jest.fn(),
      };
    });
    new TestAccordion();
    expect(document.readyState).toBe("complete");
  });
});
