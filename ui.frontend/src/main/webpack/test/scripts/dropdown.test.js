import { describe, expect, jest, test } from '@jest/globals';

class TestDropdown {
  constructor() {
    require('../../site/scripts/dropdown');
  }
}

describe('test dropdown', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    window.location = {
      href: 'https://techcombank.com/?cardId=1',
    };
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        each: (callback) => callback(),
        find: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    each: (callback) => callback(),
                  };
                },
                on: (event, element, callback) =>
                  callback({
                    target: {
                      textContent: 'test',
                      getAttribute: jest.fn(),
                    },
                  }),
                text: () => {
                  return {
                    length: 1,
                    toLowerCase: jest.fn(),
                  };
                },
                css: jest.fn(),
                html: jest.fn(),
              };
            },
            offset: () => {
              return {
                top: 0,
              };
            },
            outerHeight: jest.fn().mockReturnValue(2),
            css: jest.fn(),
            text: jest.fn(),
            val: jest.fn(),
            change: jest.fn(),
            click: (callback) => callback(),
            each: (callback) => callback(),
            on: jest.fn(),
          };
        },
        outerHeight: jest.fn().mockReturnValue(1),
        offset: () => {
          return {
            top: 0,
          };
        },
        css: jest.fn(),
        on: (event, callback) => callback({ target: 'test' }),
        parents: () => {
          return { length: 1 };
        },
        attr: jest.fn(),
        text: jest.fn(),
        hasClass: jest.fn(),
        hide: jest.fn(),
      };
    });
    new TestDropdown();
    expect(document.readyState).toBe('complete');
  });
});
