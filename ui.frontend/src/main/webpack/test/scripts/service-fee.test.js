import { describe, jest } from '@jest/globals';
import { renderYoutube } from '../../site/scripts/service-fee';

describe('ServiceFee', () => {
  test('should create an instance', () => {
    window.jQuery = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {};
    });

    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        css: () => {},
        addClass: () => {},
        attr: () => {},
        find: () => {
          return {
            text: () => {},
            addClass: () => {},
            removeClass: () => {},
            css: () => {},
            empty: () => {},
            append: () => {},
          };
        },
      };
    });

    renderYoutube($('div.youtube'), 'title', 'https://youtu.be/CLeZyIID9Bo?si=0OcebUEj2BNnbfvI');
    expect(document.readyState).toBe('complete');
  });
});
