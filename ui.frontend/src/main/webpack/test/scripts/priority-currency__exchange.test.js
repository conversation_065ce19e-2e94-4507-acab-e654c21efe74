import { describe, expect, test, jest } from "@jest/globals";

window.ScrollCurrencyConverter = class TestScrollCurrencyConverter {
  constructor(element) {
    this.wrapper = element;
  }
};

class TestLoanCalc {
  constructor() {
    document.body.innerHTML = `
        <div class="currency-converter"></div>
    `;

    require("../../site/scripts/priority-currency__exchange");
  }
}

describe("Test Priority Currency Exchange", () => {
  test("should document is ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) =>
          callback(
            {},
            {
              getAttribute: () => {
                return {};
              },
            }
          ),
        attr: jest.fn(),
        data: jest.fn(),
        next: () => {
          return {
            length: 10,
            hasClass: jest.fn().mockReturnValue(false),
            removeClass: jest.fn().mockReturnValue(false),
            addClass: jest.fn().mockReturnValue(false),
            css: jest.fn().mockReturnValue(false),
            parents: () => {
              return {
                hasClass: jest.fn().mockReturnValue(false),
              };
            },
          };
        },
        toggleClass: jest.fn(),
        prev: jest.fn(),
        hasClass: jest.fn().mockReturnValue(true),
        addClass: jest.fn(),
        css: jest.fn(),
        click: (callback) => callback(),
        val: jest.fn().mockReturnValue("7.3"),
        removeAttr: jest.fn(),
        keypress: (callback) =>
          callback({
            which: "ABC",
            keyCode: "ABC",
            target: {
              value: "A",
            },
            getAttribute: () => {
              return "day";
            },
          }),
        keyup: (callback) => callback(),
        on: (parms, callback1, callback2) => {
          if (typeof callback1 === "function") {
            callback1({
              target: {
                value: "123",
              },
            });
          }

          if (typeof callback2 === "function") {
            callback2({
              currentTarget: {},
            });
          }
        },
        removeClass: jest.fn(),
        html: jest.fn(),
        find: () => {
          return {
            keypress: (callback) =>
              callback({
                which: "ABC",
                keyCode: "ABC",
                target: {
                  value: "A",
                },
                getAttribute: () => {
                  return "day";
                },
              }),
            keyup: (callback) => callback(),
            val: () => {
              return "1,000,000,000";
            },
            attr: jest.fn(),
            text: jest.fn().mockReturnValue(true),
            css: jest.fn(),
            html: jest.fn(),
            removeClass: jest.fn(),
            on: (params, callback) => {
              callback({
                originalEvent: {
                  clipboardData: {
                    getData: () => {
                      return {
                        match: jest.fn().mockReturnValue(true),
                      };
                    },
                  },
                },
                target: {
                  value: "123",
                },
                preventDefault: jest.fn(),
              });
            },
            click: (callback) =>
              callback({
                target: {},
              }),
            0: {
              value: "123",
              scrollWidth: 200,
              clientWidth: 100,
            },
            find: () => {
              return {
                removeClass: jest.fn(),
                each: (callback) => callback(),
                find: () => {
                  return {
                    each: (callback) => callback(),
                    removeClass: jest.fn(),
                    find: () => {
                      return {
                        each: (callback) => callback(1),
                        addClass: jest.fn(),
                        find: () => {
                          return {
                            each: (callback) => callback(),
                            removeClass: jest.fn(),
                          };
                        },
                      };
                    },
                  };
                },
              };
            },
            not: jest.fn().mockReturnValue({
              slick: jest.fn(),
            }),
            last: jest.fn().mockReturnValue({
              slick: jest.fn(),
              get: jest.fn().mockReturnValue({
                getBoundingClientRect: jest.fn().mockReturnValue({
                  right: 1,
                }),
              }),
            }),
          };
        },
        length: 10,
        parent: () => {
          return {
            outerHeight: () => {
              return 100;
            },
          };
        },
        first: jest.fn().mockReturnValue({
          text: jest.fn().mockReturnValue("unit-test"),
        }),
        text: jest.fn().mockReturnValue("unit-test"),
        get: jest.fn().mockReturnValue({
          getBoundingClientRect: jest.fn().mockReturnValue({
            right: 3,
          }),
        }),
      };
    });

    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({
            exchangeRate: {
              data: {
                filter: () => {
                  return {
                    sort: (callback) => callback(2, 2),
                    length: 3,
                  };
                },
              },
            },
          });
        }
        if (typeof callback === "function") {
          error({});
        }
      });

    new TestLoanCalc();
    expect(document.readyState).toBe("complete");
  });
});
