import { describe, expect } from "@jest/globals";
import { OfferAccordionElement } from "../../site/scripts/offer-accordion-element";
const SAMPLE = `
    <div id="detail-container">
        <tcb-offer-accordion>
        </tcb-offer-accordion>
    </div>
    <template id="offer-detail-accordion-template">
    </template>
`;

const SAMPLE_MOCK_ELEMENT = `<div class="question">
      <h3></h3>
      <div class="answer"></div>
    </div>
    `;

global.$ = jest.fn(() => ({
  attr: jest.fn(),
  width: jest.fn().mockReturnValue(1440),
  appendChild: jest.fn(),
  closest: jest.fn(),
  find: jest.fn(() => ({
    remove: jest.fn(),
    each: (callback) =>
      callback(
        {},
        {
          lastElementChild: jest
            .fn()
            .mockReturnValue(document.createElement("div")),
          nextElementSibling: jest
            .fn()
            .mockReturnValue(document.createElement("div")),
          classList: {
            toggle: jest.fn(),
          },
          addEventListener: jest.fn(),
        }
      ),
    find: () => {
      return {
        on: (type, callback) => {
          if (typeof type === "string" && type.includes("click")) {
            callback();
          }
          return jest.fn().mockReturnValue((callback) => callback());
        },
        hide: jest.fn(),
        length: 1,
        addClass: jest.fn(),
      };
    },
  })),
  toggleClass: jest.fn(),
}));

describe("OfferAccordionElement", () => {
  let offerAccorElement;
  let mockElement1;
  let mockElement2;

  let mockWrapper;
  customElements.define("tcb-offer-accordion", OfferAccordionElement);
  beforeEach(() => {
    document.body.innerHTML = SAMPLE;
    offerAccorElement = new OfferAccordionElement();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("accordionElement", () => {
    it("should document is ready", () => {
      expect(document.readyState).toBe("complete");
    });

    it("should toggle content and open answer section", () => {
      mockElement1 = document.createElement("div");
      mockElement1.innerHTML = SAMPLE_MOCK_ELEMENT;

      mockElement2 = document.createElement("div");
      mockElement2.innerHTML = SAMPLE_MOCK_ELEMENT;

      mockWrapper = document.createElement("div");
      mockWrapper.appendChild(mockElement1);
      mockWrapper.appendChild(mockElement2);

      mockWrapper.attr = jest.fn().mockReturnValue("bglink");
      mockWrapper.css = jest.fn((name, value) => {
        Object.defineProperty(mockWrapper, "style", {
          [name]: value,
        });
      });
      offerAccorElement.toggleContent(mockElement1, mockWrapper, true, true);
      expect(mockElement1.style.paddingBottom).toBe("1.25rem");
    });

    it("should return early if there is no answer element", () => {
      mockElement1 = document.createElement("div");
      mockWrapper = document.createElement("div");

      offerAccorElement.toggleContent(mockElement1, mockWrapper, true, true);
      expect(mockElement1.style.paddingBottom).toBe("");
    });

    it("should return isLocation as false when data-location is false", () => {
      offerAccorElement.setAttribute("data-location", "false");
      expect(offerAccorElement.isLocation).toBe(false);
    });
  });
});
