import { describe, expect, jest, test } from '@jest/globals';

class TestTcb {
  constructor() {
    require('../../site/scripts/tcb');
  }
}

describe('TCB', () => {
  test('should create an instance', () => {
    window.scrollTo = jest.fn();
    window.TcbTab = jest.fn();
    window.GridPagination = jest.fn();
    window.$ = jest.fn().mockImplementation((param) => {
      if (param === '.tcb-tabs') {
        return Array(1);
      }
      return {
        ready: callback => callback(),
        each: callback => callback(),
        off: (param, callback) => callback(),
        resize: callback => callback(),
        click: callback => callback({
          target: 'sample',
        }),
        on: (param, callback) => callback(
          {
            originalEvent: {
              clipboardData: {
                getData: () => {
                  return {
                    match: () => true,
                  };
                },
              },
            },
            preventDefault: () => {},
          },
          {
            $slides: {
              length: 1,
            },
          },
          0,
        ),
        length: 1,
        attr: () => {
          return {
            startsWith: () => true,
          };
        },
        addClass: () => {},
        removeClass: () => {},
        text: () => {},
        index: () => {},
        css: () => {},
        hide: () => {},
        show: () => {},
        toggleClass: () => {},
        offset: () => {
          return {
            top: {},
          };
        },
        filter: () => {
          return {
            slick: () => {},
          };
        },
        first: () => {
          return {
            addClass: () => {},
          };
        },
        last: () => {
          return {
            addClass: () => {},
          };
        },
        not: () => {
          return {
            slick: () => {},
          };
        },
        parents: () => {
          return {
            is: () => false,
          };
        },
        is: () => false,
        hasClass: () => false,
        width: jest.fn().mockReturnValue(200),
        data: jest.fn().mockReturnValue(200),
        clone: () => {
          return {
            appendTo: () => {},
          };
        },
        parent: () => {
          return {
            parent: () => {
              return {
                parent: () => {
                  return {
                    parent: () => {
                      return {
                        find: () => {
                          return {
                            removeClass: () => {},
                            css: () => {},
                          };
                        },
                      };
                    },
                    remove: () => {},
                  };
                },
              };
            },
            toggleClass: () => {},
          };
        },
        find: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    find: () => {
                      return {
                        each: callback => callback(0),
                        removeClass: () => {},
                        scrollCenter: () => {},
                        css: () => {},
                      };
                    },
                    remove: () => {},
                    each: callback => callback(0),
                  };
                },
                not: () => {
                  return {
                    slick: () => {},
                  };
                },
                css: () => {},
                hasClass: () => false,
                text: () => {},
              };
            },
            3: {
              style: {
                display: 'sample',
              },
            },
            4: {
              style: {
                display: 'sample',
              },
            },
            5: {
              style: {
                display: 'sample',
              },
            },
            last: () => {
              return {
                text: () => {},
              };
            },
            length: 5,
            attr: () => {},
            remove: () => {},
            removeClass: () => {},
            before: () => {},
            scrollCenter: () => {},
            text: () => {},
            css: () => {},
            not: () => {
              return {
                addClass: () => {},
                removeClass: () => {},
                slick: () => {},
              };
            },
            each: callback => callback(),
            on: () => {},
            click: callback => callback({
              stopPropagation: () => {},
            }),
          };
        },
      };
    });

    window.jQuery.fn = jest.fn().mockImplementation(() => {
      return {
        scrollCenter: () => {},
      };
    });

    new TestTcb();
    expect(document.readyState).toBe('complete');
  });
});
