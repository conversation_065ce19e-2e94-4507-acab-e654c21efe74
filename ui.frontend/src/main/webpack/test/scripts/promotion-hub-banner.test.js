import { TcbPromotionBannerCarousel } from '../../site/scripts/promotion-hub-banner';

beforeAll(() => {
  customElements.define('tcb-promotion-banner-carousel', TcbPromotionBannerCarousel);
});

describe('TcbPromotionBannerCarousel', () => {
  let component;
  let mockSlick;

  beforeAll(() => {
    global.$ = jest.fn((selector) => {
      return {
        slick: jest.fn((options) => {
          mockSlick = options;
          return this;
        })
      };
    });
  });

  beforeEach(() => {
    component = new TcbPromotionBannerCarousel();
    
    component.querySelector = jest.fn(() => {
      return {
        classList: {
          contains: jest.fn()
        }
      };
    });
    
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default autoplaySpeed', () => {
      expect(component.autoPlaySpeed).toBe(false);
    });

    it('should initialize with dataset autoplaySpeed if provided', () => {
      const testComponent = new TcbPromotionBannerCarousel();
      testComponent.autoPlaySpeed = '3000';
      expect(testComponent.autoPlaySpeed).toBe('3000');
    });
  });

  describe('connectedCallback', () => {
    it('should call initCarousel when connected to DOM', () => {
      component.initCarousel = jest.fn();
      component.connectedCallback();
      expect(component.initCarousel).toHaveBeenCalled();
    });
  });

  describe('initCarousel', () => {
    it('should initialize slick carousel with correct options', () => {
      component.querySelector = jest.fn(() => document.createElement('div'));
      
      component.initCarousel();
      
      expect($).toHaveBeenCalled();
      
      expect(mockSlick).toEqual({
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: false,
        dots: true,
        arrows: false,
        customPaging: expect.any(Function)
      });
    });

    it('should pass autoplay speed when provided', () => {
      component.autoPlaySpeed = 5000;
      component.querySelector = jest.fn(() => document.createElement('div'));
      
      component.initCarousel();
      
      expect(mockSlick.autoplay).toBe(5000);
    });

    it('should use custom paging function that limits to 4 dots', () => {
      component.querySelector = jest.fn(() => document.createElement('div'));
      component.initCarousel();
      
      const pagingFn = mockSlick.customPaging;
      
      expect(pagingFn(null, 0)).toBe('<button>1</button>');
      expect(pagingFn(null, 1)).toBe('<button>2</button>');
      expect(pagingFn(null, 2)).toBe('<button>3</button>');
      expect(pagingFn(null, 3)).toBe('<button>4</button>');
      
      expect(pagingFn(null, 4)).toBe('');
      expect(pagingFn(null, 5)).toBe('');
    });
  });

  describe('custom element registration', () => {
    it('should be registered as a custom element', () => {
      expect(customElements.get('tcb-promotion-banner-carousel')).toBe(TcbPromotionBannerCarousel);
    });
  });
});