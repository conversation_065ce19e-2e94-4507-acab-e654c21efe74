import { describe, expect, jest, test } from '@jest/globals';

class TestBreadcrumb {
  constructor() {
    require('../../site/scripts/breadcrumb');
  }
}

describe('Test Breadcrumb', () => {
  beforeEach(() => {
    document.body.innerHTML = '';
    document.body.innerHTML = `<div class="cmp-breadcrumb__list"></div>`;
    navigator.__defineGetter__('userAgent', function () {
      return 'Firefox';
    });
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback({
          indexOf: () => {
            return {
              css: jest.fn(),
            };
          },
        });
      } else if (callback === '.hero-breadcrumb-container') {
        return {
          length: 0,
        };
      }
      return {
        length: 0,
        css: jest.fn(),
        closest: () => {
          return {
            remove: jest.fn(),
          }
        },
      };
    });
  });

  test('should document is ready', () => {
    window = Object.assign(window, { innerWidth: 500 });
    new TestBreadcrumb();
    expect(document.readyState).toBe('complete');
  });
});
