import { OfferDetailElement } from "../../site/scripts/offer-detail-element";
import { describe, expect } from "@jest/globals";
import { fetchData } from "../../site/scripts/offer-helper";
import DOMPurify from "dompurify";
const SAMPLE = `
    <div id="detail-container">
        <div class="promotion-hub-secondary">
            <div class="offer-listing-promotions" data-url="test-url" data-language-label="en"></div>
        </div>
    </div>
    <template id="offer-listing-detail-template">
    </template>
`;

// Mock document.getElementsByTagName
global.document.getElementsByTagName = jest.fn((tagName) => {
  if (tagName === 'footer') {
    return [{ cloneNode: jest.fn().mockReturnValue(document.createElement('footer')) }];
  }
  if (tagName === 'tcb-offer-listing-detail') {
    return [];
  }
  return [];
});

const mockJQueryObject = {
  attr: jest.fn((key) => {
    if (key === "data-url") return "test-url";
    if (key === "data-language-label") return "en";
    if (key === "data-init") return '{"test": "data"}';
    return "test-value";
  }),
  width: jest.fn().mockReturnValue(1440),
  animate: jest.fn(),
  empty: jest.fn(),
  fadeIn: jest.fn(),
  fadeOut: jest.fn(),
  appendChild: jest.fn(),
  hasClass: jest.fn().mockReturnValue(false),
  addClass: jest.fn(),
  removeClass: jest.fn(),
  length: 1,
  map: jest.fn(() => ({
    get: jest.fn().mockReturnValue([]),
  })),
  val: jest.fn().mockReturnValue("test-value"),
  find: jest.fn(() => mockJQueryObject),
  off: jest.fn(),
  remove: jest.fn(),
  on: jest.fn(),
  append: jest.fn(),
  click: jest.fn(),
  first: jest.fn(() => ({
    text: jest.fn(),
  })),
  children: jest.fn(() => ({
    first: jest.fn(() => ({
      text: jest.fn(),
    })),
  })),
  toggleClass: jest.fn(),
  css: jest.fn(),
};

global.$ = jest.fn(() => mockJQueryObject);

jest.mock("dompurify", () => ({
  sanitize: jest.fn((content) => content),
}));

jest.mock("../../site/scripts/offer-helper", () => ({
  clearSearchParams: jest.fn(),
  fetchData: jest.fn(),
  removeHTMLTags: jest.fn((content) => content),
  resetHistory: jest.fn(),
  updateSearchParams: jest.fn(),
  disableBodyScroll: jest.fn(),
  handleResetDropdownLanguage: jest.fn(),
  convertPxToRem: jest.fn((px) => px / 16),
  listenerElement: jest.fn(),
}));

let detailDialogData = {
  imageMasthead: "imageMasthead.jpg",
  productsPrimaryCTALabel: "Buy now",
  labelLocationViewmore: "View more locations",
};

let detailData = {
  description: "Special offer",
  cardTypes: [
    {
      key: "platinum",
      path: "techcombank:promotions/card-types/card-type-2/platinum",
      title: "Thẻ Platinum",
      thumbnail:
        "/content/dam/techcombank/public-site/promo/img/Debit-Card-Priority.png",
    },
  ],
  expiryDate: "2024-12-31",
  merchant: [{ title: "Merchant A" }, { title: "Merchant B" }],
  locations: [
    { title: "Location 1" },
    { title: "Location 2" },
    { title: "Location 1" },
  ],
  locationSectionCTALabel: "locationSectionCTALabel",
  promotionContent: "<p>Promotion details</p>",
  locationSubtitle: "<p>locationSubtitle</p>",
  productsSubtitle: "<p>productsSubtitle</p>",
  applicableContent: "Applicable in all stores",
  productTypes: [{ key: "product1", title: "Product 1" }],
  categories: [{ key: "category1", title: "Category 1" }],
  name: "Offer Name",
  productsCTAPrimaryLink: "https://example.com/buy-now",
  url: "https://example.com/buy-now/content/dam/techcombank/public-site/promo/pdf",
};

let utils = {
  renderPromotionCardItem: jest.fn(),
  renderExpiryDate: jest.fn(),
  getPromotions: jest.fn(),
  openDetailModal: jest.fn(),
  showPreviewModalListener: jest.fn(),
};

let dataURL = "https://example.com/offerlisting";
let dataLanguageLabel = "en";

describe("OfferDetailElement", () => {
  let offerDetailElement;

  beforeEach(() => {
    document.body.innerHTML = SAMPLE;

    // Mock window.location
    delete window.location;
    window.location = new URL("https://example.com");

    // Reset global $ mock
    global.$ = jest.fn(() => mockJQueryObject);

    // Create a proper DOM element that extends HTMLElement
    offerDetailElement = document.createElement('div');
    Object.setPrototypeOf(offerDetailElement, OfferDetailElement.prototype);

    // Initialize required properties manually
    offerDetailElement.detailPlaceholder = mockJQueryObject;
    offerDetailElement.offerListingComponent = mockJQueryObject;
    offerDetailElement.dataElement = mockJQueryObject;
    offerDetailElement.timer = null;
    offerDetailElement.footer = document.getElementsByTagName('footer')[0];
    offerDetailElement.dialogDetailData = null;
    offerDetailElement.headerJQuery = mockJQueryObject;
    offerDetailElement.headerElement = null;
    offerDetailElement.detailPlaceholderElement = null;
    offerDetailElement.windowJQuery = mockJQueryObject;
    offerDetailElement.cardTypeInputsJQuery = mockJQueryObject;
    offerDetailElement.offerDetailElementJQuery = mockJQueryObject;
    offerDetailElement.shadowRootJQuery = null;
    offerDetailElement.imageCardProductImgJQuery = mockJQueryObject;
    offerDetailElement.detailName = null;
    offerDetailElement.productTypes = [];
    offerDetailElement.cardTypes = [];
    offerDetailElement.categories = [];
    offerDetailElement.renderPromotionCardItem = jest.fn();
    offerDetailElement.renderExpiryDate = jest.fn();
    offerDetailElement.showPreviewModalListener = jest.fn();
    offerDetailElement.dataURL = 'test-url';
    offerDetailElement.dataLanguageLabel = 'en';
    offerDetailElement.selectorPdfLink = '.pdf-link';
    offerDetailElement.selectorDetailLink = '.detail-link';

    // Mock shadowRoot property
    Object.defineProperty(offerDetailElement, 'shadowRoot', {
      value: document.createElement('div'),
      writable: true,
      configurable: true
    });
  });

  describe("appendSlotContent", () => {
    it("should document is ready", () => {
      expect(document.readyState).toBe("complete");
    });

    it("should append text content", () => {
      offerDetailElement.appendSlotContent(
        "a",
        "locationSectionCTALabel",
        detailData.locationSectionCTALabel
      );
      const addedElement = offerDetailElement.querySelector(
        '[slot="locationSectionCTALabel"]'
      );
      expect(addedElement.textContent).toBe(detailData.locationSectionCTALabel);
    });

    it("should append HTML content when isHTML is true", () => {
      offerDetailElement.appendSlotContent(
        "p",
        "promotionContent",
        detailData.promotionContent,
        true
      );
      const addedElement = offerDetailElement.querySelector(
        '[slot="promotionContent"]'
      );
      expect(addedElement.innerHTML).toBe(detailData.promotionContent);
    });

    it("should append tag HTML", () => {
      offerDetailElement.appendSlotHTMLContent(
        "p",
        "productsSubtitle",
        detailData.productsSubtitle,
        true
      );
      const addedElement = offerDetailElement.querySelector(
        '[slot="productsSubtitle"]'
      );
      expect(addedElement.innerHTML).toBe(detailData.productsSubtitle);
    });
  });

  describe("getRelatedEndpoint", () => {
    it("should generate the correct related endpoint URL", () => {
      offerDetailElement.cardTypes = [{ key: "credit-cards/key1" }];
      offerDetailElement.productTypes = [{ key: "product1" }];
      offerDetailElement.categories = [{ key: "category1" }];
      const result = offerDetailElement.getRelatedEndpoint(dataURL);
      expect(result).toBe(
        "https://example.com/offerlisting.offerlisting.json?limit=5&offset=0&sort=related&card-types=credit%20cards/key1&products=product1&types=category1"
      );
    });
  });

  describe("getCardTypesPrefix", () => {
    it("should received prefix", () => {
      const result = offerDetailElement.getCardTypesPrefix(
        detailData.cardTypes
      );
      expect(result).toEqual([
        {
          key: "card-type-2/platinum",
          path: "techcombank:promotions/card-types/card-type-2/platinum",
          title: "Thẻ Platinum",
          thumbnail:
            "/content/dam/techcombank/public-site/promo/img/Debit-Card-Priority.png",
        },
      ]);
    });

    it("should received encoded url", () => {
      const result = offerDetailElement.getKeyAsString(
        offerDetailElement.getCardTypesPrefix(detailData.cardTypes)
      );
      expect(result).toEqual("card type 2/platinum");
    });
  });

  describe("getRelatedPromotions", () => {
    it("should fetch related promotions and render them", async () => {
      const mockResponse = {
        results: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }],
        total: 5,
      };
      fetchData.mockResolvedValue(mockResponse);
      offerDetailElement.renderRelatedPromotionCard = jest.fn();
      offerDetailElement.renderPromotionRelatedCount = jest.fn();

      await offerDetailElement.getRelatedPromotions("https://example.com");

      expect(offerDetailElement.renderRelatedPromotionCard).toBeCalled();
      expect(offerDetailElement.renderPromotionRelatedCount).toBeCalled();
    });

    it("renderPromotionRelatedCount", () => {
      offerDetailElement.renderPromotionRelatedCount(5);
      expect(document.readyState).toBe("complete");
    });
  });

  describe("initializeElements", () => {
    it("should initialize header element when headerJQuery exists", () => {
      const mockHeaderElement = document.createElement('header');
      offerDetailElement.headerJQuery = {
        length: 1,
        0: mockHeaderElement
      };
      offerDetailElement.initializeElements();
      expect(offerDetailElement.headerElement).toBe(mockHeaderElement);
    });

    it("should initialize detail placeholder element when detailPlaceholder exists", () => {
      const mockPlaceholderElement = document.createElement('div');
      offerDetailElement.detailPlaceholder = {
        length: 1,
        0: mockPlaceholderElement
      };
      offerDetailElement.initializeElements();
      expect(offerDetailElement.detailPlaceholderElement).toBe(mockPlaceholderElement);
    });

    it("should initialize shadowRoot jQuery when shadowRoot exists", () => {
      offerDetailElement.shadowRoot = document.createElement("div");
      offerDetailElement.initializeElements();
      expect(offerDetailElement.shadowRootJQuery).toBeDefined();
    });

    it("should initialize data URL and language label when dataElement exists", () => {
      offerDetailElement.dataElement = {
        attr: jest.fn((key) => {
          if (key === "data-url") return "test-url";
          if (key === "data-language-label") return "en";
          return null;
        }),
      };
      offerDetailElement.initializeElements();
      expect(offerDetailElement.dataURL).toBe("test-url");
      expect(offerDetailElement.dataLanguageLabel).toBe("en");
    });
  });

  describe("init", () => {
    it("should call initialization methods", () => {
      offerDetailElement.initDialogDetail = jest.fn();
      offerDetailElement.handleBackBrowserDetail = jest.fn();
      offerDetailElement.handleSetHeaderHeight = jest.fn();

      offerDetailElement.init();

      expect(offerDetailElement.initDialogDetail).toHaveBeenCalled();
      expect(offerDetailElement.handleBackBrowserDetail).toHaveBeenCalled();
      expect(offerDetailElement.handleSetHeaderHeight).toHaveBeenCalled();
    });
  });

  describe("getCardTypesFilter", () => {
    it("should return empty array when cardTypeInputsJQuery is null", () => {
      offerDetailElement.cardTypeInputsJQuery = null;
      const result = offerDetailElement.getCardTypesFilter();
      expect(result).toEqual([]);
    });

    it("should return empty array when cardTypeInputsJQuery length is 0", () => {
      offerDetailElement.cardTypeInputsJQuery = { length: 0 };
      const result = offerDetailElement.getCardTypesFilter();
      expect(result).toEqual([]);
    });

    it("should return filtered card types", () => {
      const mockMap = jest.fn((callback) => {
        callback(0, { value: "test-card" });
        callback(1, { value: "" });
        callback(2, { value: "another-card" });
        return {
          get: jest.fn().mockReturnValue(["test-card", null, "another-card"]),
        };
      });

      offerDetailElement.cardTypeInputsJQuery = {
        length: 3,
        map: mockMap,
      };

      // Mock $ for this specific test
      const originalGlobal$ = global.$;
      global.$ = jest.fn((input) => ({
        val: jest.fn().mockReturnValue(input && input.value ? input.value : "test-value"),
      }));

      const result = offerDetailElement.getCardTypesFilter();
      expect(result).toEqual(["test-card", "another-card"]);

      // Restore original mock
      global.$ = originalGlobal$;
    });
  });

  describe("addCardType", () => {
    beforeEach(() => {
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.getCardTypesFilter = jest.fn().mockReturnValue(["platinum"]);
      offerDetailElement.createCardImages = jest.fn().mockReturnValue([]);
      offerDetailElement.removeCardThumbWrapper = jest.fn();
      offerDetailElement.appendChild = jest.fn();
      offerDetailElement.appendSlotHTMLContent = jest.fn();
      offerDetailElement.appendSlotContent = jest.fn();
      offerDetailElement.productTypes = [];
    });

    it("should remove product section when no card types and no subtitle", () => {
      offerDetailElement.getCardTypesFilter = jest.fn().mockReturnValue([]);
      offerDetailElement.addCardType([], null);
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith('.offer-detail-product__wrapper');
    });

    it("should render products subtitle when provided", () => {
      const subtitle = "<p>Products subtitle</p>";
      offerDetailElement.addCardType(detailData.cardTypes, subtitle);
      expect(offerDetailElement.appendSlotHTMLContent).toHaveBeenCalledWith('p', 'productsSubtitle', subtitle);
    });

    it("should render card type titles", () => {
      offerDetailElement.addCardType(detailData.cardTypes, null);
      expect(offerDetailElement.appendSlotContent).toHaveBeenCalledWith('li', 'cardTypeTitle', 'Thẻ Platinum');
    });
  });

  describe("addContent", () => {

    it("should handle missing promotionContent by removing offer type section", () => {
      offerDetailElement.shadowRoot = document.createElement("div");
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.getRelatedPromotions = jest.fn();
      offerDetailElement.addCardType = jest.fn();
      offerDetailElement.addLocation = jest.fn().mockReturnValue([]);
      offerDetailElement.handleClickClose = jest.fn();
      offerDetailElement.handleClickAllRelated = jest.fn();
      offerDetailElement.createBackground = jest.fn();
      offerDetailElement.renderProductsButtons = jest.fn();
      offerDetailElement.appendSlotHTMLContent = jest.fn();
      offerDetailElement.appendSlotContent = jest.fn();
      offerDetailElement.getCardTypesPrefix = jest.fn().mockReturnValue([]);

      const dataWithoutPromotion = { ...detailData, promotionContent: null };

      offerDetailElement.addContent(
        detailDialogData,
        dataWithoutPromotion,
        dataURL,
        dataLanguageLabel,
        utils
      );

      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith('.offer-detail-offer-type__wrapper');
    });
  });

  describe("addLocation", () => {
    beforeEach(() => {
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.appendSlotContent = jest.fn();
      offerDetailElement.appendSlotHTMLContent = jest.fn();
      offerDetailElement.appendChild = jest.fn();
    });

    it("should handle locations with more than 2 items", () => {
      const locationData = {
        locations: detailData.locations,
        labelLocationViewmore: "View more",
        locationSubtitle: "<p>Location subtitle</p>",
        pdfLink: "test.pdf",
        locationSectionCTALabel: "Download",
        showPreviewModalListener: jest.fn(),
      };

      const result = offerDetailElement.addLocation(locationData);
      expect(result).toEqual(["Location 1", "Location 2", "..."]);
      expect(offerDetailElement.appendSlotContent).toHaveBeenCalledWith('li', 'locationContentItem', 'Location 1');
    });

    it("should remove location info when no locations", () => {
      const locationData = {
        locations: [],
        labelLocationViewmore: "View more",
        locationSubtitle: null,
      };

      offerDetailElement.addLocation(locationData);
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith('.location-info-container');
    });

    it("should create location link when subtitle and pdf link provided", () => {
      const locationData = {
        locations: [{ title: "Location 1" }],
        locationSubtitle: "<p>Location subtitle</p>",
        pdfLink: "test.pdf",
        locationSectionCTALabel: "Download",
        showPreviewModalListener: jest.fn(),
      };

      offerDetailElement.addLocation(locationData);
      expect(offerDetailElement.appendSlotHTMLContent).toHaveBeenCalledWith('p', 'locationSectionSubtitle', '<p>Location subtitle</p>');
    });
  });

  describe("getDetailEndpoint", () => {
    it("should generate correct detail endpoint", () => {
      offerDetailElement.dataURL = "https://example.com/data";
      const result = offerDetailElement.getDetailEndpoint("test-offer");
      expect(result).toBe("https://example.com/data.offerdetail.json?name=test-offer");
    });
  });

  describe("initDialogDetail", () => {
    beforeEach(() => {
      delete window.location;
      window.location = new URL("https://example.com?detail=test-detail");
      offerDetailElement.offerDetailElementJQuery = {
        length: 1,
        attr: jest.fn().mockReturnValue('{"test": "data"}'),
      };
      offerDetailElement.openDetailModal = jest.fn();
    });

    it("should return early when no offerDetailElementJQuery", () => {
      offerDetailElement.offerDetailElementJQuery = { length: 0 };
      offerDetailElement.initDialogDetail();
      expect(offerDetailElement.openDetailModal).not.toHaveBeenCalled();
    });

    it("should parse dialog data and open modal when detail param exists", () => {
      offerDetailElement.initDialogDetail();
      expect(offerDetailElement.dialogDetailData).toEqual({ test: "data" });
      expect(offerDetailElement.openDetailModal).toHaveBeenCalledWith("test-detail");
    });

    it("should not open modal when no detail param", () => {
      window.location = new URL("https://example.com");
      offerDetailElement.initDialogDetail();
      expect(offerDetailElement.openDetailModal).not.toHaveBeenCalled();
    });
  });

  describe("handleBackBrowserDetail", () => {
    it("should handle popstate event", () => {
      offerDetailElement.detailPlaceholder = {
        length: 1,
        hasClass: jest.fn().mockReturnValue(true),
        removeClass: jest.fn(),
        empty: jest.fn(),
      };
      offerDetailElement.offerListingComponent = {
        fadeIn: jest.fn(),
      };

      offerDetailElement.handleBackBrowserDetail();

      // Simulate popstate event
      const popstateEvent = new PopStateEvent('popstate');
      window.dispatchEvent(popstateEvent);

      expect(offerDetailElement.detailPlaceholder.removeClass).toHaveBeenCalledWith('open');
      expect(offerDetailElement.detailPlaceholder.empty).toHaveBeenCalled();
      expect(offerDetailElement.offerListingComponent.fadeIn).toHaveBeenCalledWith(400);
    });
  });

  describe("setVariableHeaderHeight", () => {
    it("should set header height variable", () => {
      const mockElement = {
        offsetHeight: 100,
      };
      offerDetailElement.detailPlaceholderElement = {
        style: {
          setProperty: jest.fn(),
        },
      };

      offerDetailElement.setVariableHeaderHeight(mockElement);
      expect(offerDetailElement.detailPlaceholderElement.style.setProperty).toHaveBeenCalled();
    });

    it("should handle element without offsetHeight", () => {
      const mockElement = {};
      offerDetailElement.detailPlaceholderElement = {
        style: {
          setProperty: jest.fn(),
        },
      };

      offerDetailElement.setVariableHeaderHeight(mockElement);
      expect(offerDetailElement.detailPlaceholderElement.style.setProperty).toHaveBeenCalled();
    });
  });

  describe("setBackground", () => {
    beforeEach(() => {
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.windowJQuery = global.$();
    });

    it("should set background image for desktop", () => {
      const bgData = {
        desktop: "desktop-bg.jpg",
        mobile: "mobile-bg.jpg",
        bgColor: null,
      };

      offerDetailElement.setBackground(".test-element", bgData);
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith(".test-element");
    });

    it("should set background color when no images", () => {
      const bgData = {
        desktop: null,
        mobile: null,
        bgColor: "#ffffff",
      };

      offerDetailElement.setBackground(".test-element", bgData);
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith(".test-element");
    });
  });

  describe("createBackground", () => {
    it("should call setBackground for all sections", () => {
      offerDetailElement.setBackground = jest.fn();

      const bgData = {
        imageMasthead: "masthead.jpg",
        imageAppliedProducts: "products.jpg",
        imageLocationSection: "location.jpg",
        imageMobileMasthead: "mobile-masthead.jpg",
        imageMobileAppliedProducts: "mobile-products.jpg",
        imageMobileLocationSection: "mobile-location.jpg",
        bgColorAppliedProducts: "#ffffff",
        bgColorLocationSection: "#f0f0f0",
        bgColorMasthead: "#000000",
      };

      offerDetailElement.createBackground(bgData);
      expect(offerDetailElement.setBackground).toHaveBeenCalledTimes(3);
    });
  });

  describe("closeDetailModal", () => {
    it("should close detail modal and clean up", () => {
      offerDetailElement.detailPlaceholderElement = {
        classList: {
          remove: jest.fn(),
        },
      };
      offerDetailElement.detailPlaceholder = {
        empty: jest.fn(),
      };
      offerDetailElement.offerListingComponent = {
        fadeIn: jest.fn(),
      };
      offerDetailElement.shadowRootJQuery = {
        find: jest.fn().mockReturnValue({
          off: jest.fn(),
        }),
      };
      offerDetailElement.selectorDetailLink = ".detail-link";
      offerDetailElement.selectorPdfLink = ".pdf-link";
      offerDetailElement.handleClickDetailRelated = jest.fn();
      offerDetailElement.handleClickPdfRelated = jest.fn();

      offerDetailElement.closeDetailModal();

      expect(offerDetailElement.detailPlaceholderElement.classList.remove).toHaveBeenCalledWith('open');
      expect(offerDetailElement.detailPlaceholder.empty).toHaveBeenCalled();
      expect(offerDetailElement.offerListingComponent.fadeIn).toHaveBeenCalledWith(400);
    });
  });

  describe("openDetailModal", () => {
    beforeEach(() => {
      offerDetailElement.getDetailEndpoint = jest.fn().mockReturnValue("test-endpoint");
      offerDetailElement.offerListingComponent = {
        fadeOut: jest.fn(),
      };
      offerDetailElement.dialogDetailData = { test: "data" };
      offerDetailElement.dataURL = "test-url";
      offerDetailElement.dataLanguageLabel = "en";
      offerDetailElement.renderPromotionCardItem = jest.fn();
      offerDetailElement.renderExpiryDate = jest.fn();
      offerDetailElement.showPreviewModalListener = jest.fn();
      offerDetailElement.headerElement = { offsetHeight: 100 };
      offerDetailElement.detailPlaceholderElement = {
        setAttribute: jest.fn(),
        classList: { add: jest.fn() },
        appendChild: jest.fn(),
        scrollTop: 0,
      };
    });

    it("should open detail modal with valid response", async () => {
      const mockResponse = {
        detail: { name: "Test Offer" },
        locales: ["en", "vi"],
      };
      fetchData.mockResolvedValue(mockResponse);

      // Mock document.createElement and custom element
      const mockElement = {
        addContent: jest.fn(),
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockElement);
      jest.spyOn(document, 'getElementsByTagName').mockReturnValue([]);

      await offerDetailElement.openDetailModal("test-detail");

      expect(fetchData).toHaveBeenCalledWith("test-endpoint");
      expect(offerDetailElement.offerListingComponent.fadeOut).toHaveBeenCalledWith(300);
      expect(mockElement.addContent).toHaveBeenCalled();
    });

    it("should not open modal when no detail data", async () => {
      const mockResponse = { detail: null };
      fetchData.mockResolvedValue(mockResponse);

      await offerDetailElement.openDetailModal("test-detail");

      expect(offerDetailElement.offerListingComponent.fadeOut).not.toHaveBeenCalled();
    });
  });

  describe("getRelatedExcludeCurrent", () => {
    beforeEach(() => {
      offerDetailElement.detailName = "Current Offer";
    });

    it("should return empty array for null promotions", () => {
      const result = offerDetailElement.getRelatedExcludeCurrent(null);
      expect(result).toEqual([]);
    });

    it("should exclude current promotion and set isFavorite", () => {
      const promotions = [
        { name: "Current Offer" },
        { name: "Other Offer 1" },
        { name: "Other Offer 2" },
      ];

      const result = offerDetailElement.getRelatedExcludeCurrent(promotions);
      expect(result).toHaveLength(2);
      expect(result[0].isFavorite).toBe(true);
      expect(result.find(item => item.name === "Current Offer")).toBeUndefined();
    });

    it("should remove last item when exactly 5 items", () => {
      const promotions = Array.from({ length: 5 }, (_, i) => ({ name: `Offer ${i}` }));
      offerDetailElement.detailName = "Different Offer";

      const result = offerDetailElement.getRelatedExcludeCurrent(promotions);
      expect(result).toHaveLength(4); // 5 items minus 1 (last removed)
    });
  });

  describe("renderRelatedPromotionCard", () => {
    it("should render related promotion cards", () => {
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.renderPromotionCardItem = jest.fn().mockReturnValue("<div>Card</div>");
      offerDetailElement.renderExpiryDate = jest.fn();

      const response = [{ id: 1 }, { id: 2 }];
      offerDetailElement.renderRelatedPromotionCard(response);

      expect(offerDetailElement.renderPromotionCardItem).toHaveBeenCalledTimes(2);
      expect(offerDetailElement.renderExpiryDate).toHaveBeenCalledWith(true, offerDetailElement.shadowRootJQuery);
    });
  });

  describe("handleClickPdfRelated", () => {
    it("should handle PDF click events", () => {
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.selectorPdfLink = ".pdf-link";
      offerDetailElement.showPreviewModalListener = jest.fn();

      offerDetailElement.handleClickPdfRelated();
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith(".pdf-link");
    });
  });

  describe("handleClickDetailRelated", () => {
    it("should handle detail click events", () => {
      offerDetailElement.shadowRootJQuery = global.$();
      offerDetailElement.selectorDetailLink = ".detail-link";

      offerDetailElement.handleClickDetailRelated();
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith(".detail-link");
    });
  });

  describe("removeCardThumbWrapper", () => {
    it("should remove card thumb wrapper", () => {
      offerDetailElement.shadowRootJQuery = global.$();

      offerDetailElement.removeCardThumbWrapper();
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith('.product-card-list');
    });
  });

  describe("renderPromotionRelatedCount with edge cases", () => {
    beforeEach(() => {
      offerDetailElement.shadowRootJQuery = global.$();
    });

    it("should remove see-all button when total is less than limit", () => {
      offerDetailElement.renderPromotionRelatedCount(3);
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith('.see-all-btn');
    });
  });

  describe("Additional coverage tests", () => {
    it("should test handleSetHeaderHeight method", () => {
      offerDetailElement.headerElement = { offsetHeight: 100 };
      offerDetailElement.detailPlaceholderElement = { style: { setProperty: jest.fn() } };
      offerDetailElement.setVariableHeaderHeight = jest.fn();

      jest.useFakeTimers();
      offerDetailElement.handleSetHeaderHeight();
      jest.advanceTimersByTime(300);

      expect(offerDetailElement.setVariableHeaderHeight).toHaveBeenCalled();
      jest.useRealTimers();
    });

    it("should test getRelatedPromotions with insufficient related items", async () => {
      const mockResponse = {
        results: [{ id: 1 }, { id: 2 }], // Only 2 items, less than required 4
        total: 2,
      };
      fetchData.mockResolvedValue(mockResponse);
      offerDetailElement.getRelatedEndpoint = jest.fn().mockReturnValue("test-api");
      offerDetailElement.getRelatedExcludeCurrent = jest.fn().mockReturnValue([{ id: 1 }]);
      offerDetailElement.shadowRootJQuery = {
        find: jest.fn().mockReturnValue({ remove: jest.fn() })
      };

      await offerDetailElement.getRelatedPromotions("test-url");

      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith('.offer-detail-related');
    });

    it("should test handleExpandAccordion method", () => {
      const mockAccordionElement = {
        shadowRoot: {
          querySelector: jest.fn((selector) => {
            if (selector === '.question') return { classList: { add: jest.fn() }, style: {} };
            if (selector === '.answer') return { style: {}, scrollHeight: 100 };
            if (selector === 'img') return { style: {} };
            return null;
          })
        }
      };

      offerDetailElement.shadowRoot = {
        querySelector: jest.fn((selector) => {
          if (selector === '#offer-address tcb-offer-accordion') return mockAccordionElement;
          if (selector === '#offer-address') return { style: {}, getAttribute: jest.fn().mockReturnValue('url(test.jpg)') };
          return null;
        })
      };
      offerDetailElement.windowJQuery = { width: jest.fn().mockReturnValue(1200) };

      offerDetailElement.handleExpandAccordion();

      expect(offerDetailElement.shadowRoot.querySelector).toHaveBeenCalledWith('#offer-address tcb-offer-accordion');
    });

    it("should test handleClickLocationMore method", () => {
      const mockEvent = { preventDefault: jest.fn() };
      const mockShadowRoot = document.createElement('div');
      const mockLocationSection = { offsetTop: 100 };

      offerDetailElement.windowJQuery = { width: jest.fn().mockReturnValue(1200) };
      offerDetailElement.detailPlaceholder = { animate: jest.fn() };
      offerDetailElement.handleExpandAccordion = jest.fn();

      global.$ = jest.fn(() => ({
        find: jest.fn().mockReturnValue({
          length: 1,
          0: mockLocationSection
        })
      }));

      offerDetailElement.handleClickLocationMore(mockEvent, mockShadowRoot);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(offerDetailElement.detailPlaceholder.animate).toHaveBeenCalled();
      expect(offerDetailElement.handleExpandAccordion).toHaveBeenCalled();
    });

    it("should test initFooter method", () => {
      const mockShadowRoot = {
        querySelector: jest.fn(),
        find: jest.fn().mockReturnValue({
          on: jest.fn()
        })
      };

      global.$ = jest.fn(() => ({
        find: jest.fn().mockReturnValue({
          on: jest.fn()
        })
      }));

      offerDetailElement.initFooter(mockShadowRoot);
      expect(global.$).toHaveBeenCalledWith(mockShadowRoot);
    });

    it("should test mobile background selection", () => {
      offerDetailElement.windowJQuery = { width: jest.fn().mockReturnValue(500) }; // Mobile width
      offerDetailElement.shadowRootJQuery = {
        find: jest.fn().mockReturnValue({
          attr: jest.fn(),
          css: jest.fn()
        })
      };

      const bgData = {
        desktop: "desktop-bg.jpg",
        mobile: "mobile-bg.jpg",
        bgColor: null,
      };

      offerDetailElement.setBackground(".test-element", bgData);
      expect(offerDetailElement.shadowRootJQuery.find).toHaveBeenCalledWith(".test-element");
    });
  });
});
