import { describe, expect, it, jest } from '@jest/globals';
import CurrencyHelper from '../../site/scripts/currency-helper';

describe("Test Currency Helper ", () => {
    it('should call CurrencyHelper', () => {
        const currencyHelper = new CurrencyHelper();
        expect(currencyHelper).toEqual({});
    });

    it('should call numberWithDot function', () => {
        const testData = '***************';
        jest.spyOn(CurrencyHelper, 'numberWithDot');
        CurrencyHelper.numberWithDot(testData);
        expect(CurrencyHelper.numberWithDot).toBeCalled();
    });

    it('should call numberWithCommas function', () => {
        const testData = 0;
        jest.spyOn(CurrencyHelper, 'numberWithCommas');
        CurrencyHelper.numberWithCommas(testData, 'en-US');
        expect(CurrencyHelper.numberWithCommas).toBeCalled();
    });

    it('should call numberWithCommas function with no number', () => {
        jest.spyOn(CurrencyHelper, 'numberWithCommas');
        CurrencyHelper.numberWithCommas('', 'en-US');
        expect(CurrencyHelper.numberWithCommas).toBeCalled();
    });

    it('should call numberWithCommas function with no locale', () => {
        const testData = '***************';
        jest.spyOn(CurrencyHelper, 'numberWithCommas');
        CurrencyHelper.numberWithCommas(testData, undefined);
        expect(CurrencyHelper.numberWithCommas).toBeCalled();
    });
});