import { describe, expect, jest, test } from '@jest/globals';

class TestColumnPanel {
  constructor() {
    require('../../site/scripts/compare-panel');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        find: () => {
          return {
            addClass: jest.fn(),
            each: (callback) => callback(),
            removeClass: jest.fn(),
            find: () => {
              return {
                each: (callback) => callback(),
              };
            },
            eq: () => {
              return {
                find: () => {
                  return {
                    attr: () => {
                      return {
                        value: true,
                        replace: jest.fn(),
                      };
                    },
                    addClass: jest.fn(),
                    text: jest.fn(),
                    removeAttr: jest.fn(),
                    removeClass: jest.fn(),
                  };
                },
              };
            },
            text: jest.fn(),
            removeAttr: jest.fn(),
            hasClass: jest.fn().mockReturnValue(true),
            click: (callback) => callback(),
          };
        },
        click: (callback) => callback(),
        hasClass: jest.fn(),
        removeClass: jest.fn(),
        addClass: jest.fn(),
        text: () => {
          return {
            trim: jest.fn(),
          };
        },
        attr: jest.fn().mockReturnValue('techcombank'),
      };
    });
    new TestColumnPanel();
    expect(document.readyState).toBe('complete');
  });
});
