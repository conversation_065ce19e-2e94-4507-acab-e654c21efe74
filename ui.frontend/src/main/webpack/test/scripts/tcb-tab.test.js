import { describe, expect, jest, test } from '@jest/globals';

class TestTcbTab {
  constructor() {
    require('../../site/scripts/tcb-tab');
  }
}

describe('TcbTab', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        0: {
          append: () => {},
          classList: {
            remove: () => {},
          },
          style: {
            left: 0,
            width: 0,
          },
          scrollWidth: 40,
          clientWidth: 30,
          scrollLeft: 20,
        },
        classList: {
          add: () => {},
          remove: () => {},
        },
        attr: () => {},
        append: () => {},
        prepend: () => {},
        animate: () => {},
        on: (param, callback) => callback({
          target: {
            dataset: {
              tabindex: '0',
            },
            classList: {
              add: () => {},
            },
            parentElement: {
              parentElement: {
                offsetWidth: 0,
              },
            },
            offsetLeft: 10,
            clientWidth: 50,
          },
        }),
        data: () => {
          return {
            split: () => {
              return {
                forEach: (callback) => callback({}, 0),
              };
            },
          };
        },
      };
    });

    new TestTcbTab();
    new TcbTab($('div'), () => {});
    expect(document.readyState).toBe('complete');
  });
});
