import { TcbResultPage } from '../../site/scripts/tcb-result-page';
import {
  ID_PROMOTION_FILTER,
  ID_PROMOTION_SEARCH,
  CLASS_HIDDEN_ELEMENT,
  KEY_RECENT_SEARCH,
} from '../../site/scripts/constants/offer-common';
import { getDataResultPage } from '../../site/scripts/utils/get-data-result-page.util';
import { getEndpoint } from '../../site/scripts/utils/promotion-generate-endpoint.util';
import { LocationUtil } from '../../site/scripts/utils/location.util';
import { StorageUtils } from '../../site/scripts/utils/storage.util';
import { setURLParams, removeURLParams } from '../../site/scripts/utils/params.util';
import DOMPurify from 'dompurify';

jest.mock('../../site/scripts/utils/get-data-result-page.util');
jest.mock('../../site/scripts/utils/promotion-generate-endpoint.util');
jest.mock('../../site/scripts/utils/location.util');
jest.mock('../../site/scripts/utils/storage.util');
jest.mock('../../site/scripts/utils/params.util');

if (!customElements.get('tcb-result-page')) {
  customElements.define('tcb-result-page', TcbResultPage);
}

describe('TcbResultPage', () => {
  let instance;

  beforeEach(() => {



    LocationUtil.getPageUrlParams.mockReturnValue('mock-url-params');
    LocationUtil.getUrlParamObj.mockReturnValue({
      sort: 'most-popular',
      q: 'laptop',
    });
    getEndpoint.mockImplementation((url, params) => 'mock-endpoint-url');
    getDataResultPage.mockImplementation(jest.fn());
    StorageUtils.get.mockReturnValue([]);
    StorageUtils.set.mockImplementation(jest.fn());
    setURLParams.mockImplementation(jest.fn());
    removeURLParams.mockImplementation(jest.fn());

    document.body.innerHTML = `
      <tcb-result-page id="${ID_PROMOTION_FILTER}" data-lang="en" data-day-text="Day" data-label-expired="Expired" data-label-expired-count-down="Countdown"
        data-card-label="true" data-url="https://example.com" data-id-display-page="page-class" data-read-more="Read More"
        data-search-count="results" data-id-root="${ID_PROMOTION_FILTER}">
        <div class="tcb-result-page-hero-notfound ${CLASS_HIDDEN_ELEMENT}"></div>
        <div class="tcb-result-page-content">
          <tcb-promotion-card class="tcb-promotion-card"></tcb-promotion-card>
          <div class="promotion-total-count"></div>
          <div class="keywork-notfoud"></div>
          <div class="add-more-text"></div>
          <button class="button-more-promotion"></button>
          <ul class="offer-filter__dropdown">
            <li class="dropdown__item" value="most-popular" data-sort="Most Popular"></li>
            <li class="dropdown__item" value="latest" data-sort="Latest"></li>
          </ul>
        </div>
      </tcb-result-page>
      <div id="page-class"></div>
      <div class="display-result-page"></div>
      <div id="${ID_PROMOTION_SEARCH}">
        <div class="group-card"></div>
      </div>
      <tcb-need-promotion-search></tcb-need-promotion-search>
    `;

    instance = document.querySelector('tcb-result-page');
    instance.id = ID_PROMOTION_FILTER;
    instance.connectedCallback();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize elements correctly', () => {
    expect(instance.resultNotfound).not.toBeNull();
    expect(instance.resultPage).not.toBeNull();
    expect(instance.countPromotion).not.toBeNull();
    expect(instance.addKeyWork).not.toBeNull();
    expect(instance.countMorePage).not.toBeNull();
    expect(instance.addMoreResult).not.toBeNull();
    expect(instance.selectSortFilter).not.toBeNull();
    expect(instance.toggleAllPage).not.toBeNull();
    expect(instance.toggleResultPage).not.toBeNull();
  });

  test('should bind events correctly', () => {
    const clickEvent = new Event('click');
    instance.addMoreResult.dispatchEvent(clickEvent);
    expect(getDataResultPage).toHaveBeenCalled();

    const sortItem = instance.selectSortFilter.querySelector('li[value="latest"]');
    const event = new MouseEvent('click', { bubbles: true });
    Object.defineProperty(event, 'target', {
      writable: false,
      value: sortItem,
    });
    instance.selectSortFilter.dispatchEvent(event);
    expect(setURLParams).toHaveBeenCalled();
    expect(instance.paramsValue.sort).toBe('');
  });

  test('handleSortItemClick should handle "most-popular" correctly', () => {
    const event = {
      target: instance.selectSortFilter.querySelector('li[value="most-popular"]'),
    };
    instance.handleSortItemClick({ target: event.target });
    expect(removeURLParams).toHaveBeenCalled();
    expect(instance.paramsValue.sort).toBe(instance.DEFAULT_SORT_PARAMS);
  });

  test('handleValueSearch should process search text correctly', () => {
    instance.handleValueSearch();
    expect(instance.textInputSearch).toBe('laptop');
    expect(instance.addKeyWork.innerHTML).toBe(DOMPurify.sanitize('laptop'));
  });

  test('handleDisplayPageResult should toggle classes based on search text', () => {
    instance.textInputSearch = '';
    instance.handleDisplayPageResult();
    expect(instance.toggleResultPage.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(true);
    expect(instance.toggleAllPage.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);

    instance.textInputSearch = 'laptop';
    instance.handleDisplayPageResult();
    expect(instance.toggleResultPage.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
    expect(instance.toggleAllPage.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(true);
  });

  test('handleParamFilter should reset offset and call handleParamUrl', () => {
    const spy = jest.spyOn(instance, 'handleParamUrl');
    instance.handleParamFilter();
    expect(instance.paramsValue.offset).toBe(0);
    expect(spy).toHaveBeenCalled();
  });

  test('handleParamUrl should call getPromotions', () => {
    const spy = jest.spyOn(instance, 'getPromotions');
    instance.handleParamUrl();
    expect(spy).toHaveBeenCalled();
  });

  test('getPromotions should call getDataResultPage with correct parameters', () => {
    instance.getPromotions();
    expect(getDataResultPage).toHaveBeenCalledWith({
      dataUrl: 'mock-endpoint-url',
      classGroupCardName: instance.classGroupCardName,
      dataExpiryDate: instance.dataExpiryDate,
      isCardlabel: instance.isCardlabel,
      totalPromotionData: expect.any(Function),
    });
  });

  test('handleCountMore should handle total = 0 correctly', () => {
    const spy = jest.spyOn(instance, 'renderTcbCardNotFound');
    instance.handleCountMore(0);
    expect(spy).toHaveBeenCalled();
  });

  test('handleCountMore should update countPromotion and addMoreResult correctly', () => {
    instance.handleCountMore(10);
    expect(instance.countPromotion.innerHTML).toBe(DOMPurify.sanitize('10 results'));
    expect(instance.addMoreResult.parentElement.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  test('renderTcbCardNotFound should show resultNotfound and hide resultPage', () => {
    instance.renderTcbCardNotFound();
    expect(instance.resultNotfound.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
    expect(instance.resultPage.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(true);
  });

  test('hideResultPage should add CLASS_HIDDEN_ELEMENT to resultPage', () => {
    instance.hideResultPage();
    expect(instance.resultPage.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(true);
  });

  test('setRecentSearchLocal should update local storage and call renderRecentSearchFromLocalStorage', () => {
    const mockRender = jest.fn();
    customElements.define(
      'tcb-need-promotion-search',
      class extends HTMLElement {
        renderRecentSearchFromLocalStorage = mockRender;
      },
    );
    document.body.innerHTML += '<tcb-need-promotion-search></tcb-need-promotion-search>';
    instance.textInputSearch = 'laptop';
    instance.setRecentSearchLocal();
    expect(StorageUtils.set).toHaveBeenCalledWith(KEY_RECENT_SEARCH, ['laptop']);
    expect(mockRender).toHaveBeenCalled();
  });
});
