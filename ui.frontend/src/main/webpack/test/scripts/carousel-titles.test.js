import { describe, expect, jest, test } from '@jest/globals';

class TestCarouselTitles {
  constructor() {
    require('../../site/scripts/carouserl-titles');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    global.setTimeout = jest.fn((callback) => callback());
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        attr: jest.fn().mockReturnValue(0),
        hasClass: jest.fn().mockReturnValue(true),
        find: () => {
          return {
            length: 5,
            remove: jest.fn(),
            not: () => {
              return {
                not: () => {
                  return {
                    slick: jest.fn(),
                  };
                },
              };
            },
            find: () => {
              return {
                empty: jest.fn(),
                css: jest.fn(),
              };
            },
            on: (event, callback) => callback(event, {}, 0, {}),
            append: jest.fn(),
          };
        },
        on: (event, callback) => callback(),
        width: jest.fn().mockReturnValue(700),
        empty: jest.fn(),
        resize: (callback) => callback(),
        not: () => {
          return {
            slick: jest.fn(),
          };
        },
      };
    });
    new TestCarouselTitles();
    expect(document.readyState).toBe('complete');
  });
});
