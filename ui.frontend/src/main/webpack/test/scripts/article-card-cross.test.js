import { describe, expect, test, jest } from "@jest/globals";

class TestArticleCardCross {
  constructor() {
    require("../../site/scripts/article-card-cross");
  }
}

describe("test article card cross", () => {
  beforeEach(() => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
      return { articleFilter: "sample" };
    });
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === "function") {
        callback();
      }
      return {
        ready: (callback) => callback(),
        attr: jest.fn().mockReturnValue(1),
        hide: jest.fn(),
        data: jest.fn(),
        on: (event, element, callback) => callback({ currentTarget: "test" }),
        each: (callback) => callback(1),
        find: () => {
          return {
            text: jest.fn().mockReturnValue("test string"),
            each: (callback) => callback(),
            show: jest.fn(),
            length: 2,
          };
        },
        parent: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    on: (event, callback) => callback(),
                  };
                },
                hide: jest.fn(),
                show: jest.fn(),
              };
            },
          };
        },
        width: jest.fn().mockReturnValue(767),
        slice: () => {
          return {
            show: jest.fn(),
          };
        },
        resize: (callback) => callback(),
        show: jest.fn(),
      };
    });
  });
  test("should document ready", () => {
    new TestArticleCardCross();
    expect(document.readyState).toBe("complete");
  });
});
