/**
 * @jest-environment jsdom
 */

import $ from 'jquery';

// Mock constants and utils
jest.mock("../../site/scripts/constants/offer-common", () => ({
  SELECTOR_DETAIL_LINK: '[href^="?detail"]',
}));

jest.mock("../../site/scripts/utils/day-expired.util", () => ({
  renderDayExpired: jest.fn(),
}));

// Mock BaseComponent
jest.mock("../../site/scripts/base.ts", () => ({
  BaseComponent: class { },
}));

// Mock RecommendCategory
jest.mock("../../site/scripts/recommend-category", () => ({
  RecommendCategory: class {
    constructor() {
      this.init = jest.fn();
    }
  },
}));

// Import sau khi mock
import { renderDayExpired } from "../../site/scripts/utils/day-expired.util";
import { OfferCardComponent } from "../../site/scripts/hub-page";

describe('OfferCardComponent', () => {
  let component;

  beforeEach(() => {
    document.body.innerHTML = `
      <div class="popup-download"></div>
      <div class="card">
        <a class="promotion-product-listing__link" href="file.pdf" data-download-pdf-label="PDF Label">PDF</a>
      </div>
      <div class="promotion-hub_hub-card"></div>
    `;

    $.fn.slick = jest.fn(); // mock slick
    component = new OfferCardComponent();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  test('showPreviewModalListener should prevent default and call triggerShowPreviewModal', () => {
    const preventDefault = jest.fn();
    const event = {
      currentTarget: $('.promotion-product-listing__link')[0],
      preventDefault,
    };

    const spy = jest.spyOn(component, 'triggerShowPreviewModal');

    component.showPreviewModalListener(event);

    expect(preventDefault).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith(
      'file.pdf',
      'Preview',
      'Loading PDF...',
      'PDF Label'
    );
  });

  test('triggerShowPreviewModal should trigger event with correct data', () => {
    const $modal = $('.popup-download');
    const handler = jest.fn();

    $modal.on('show-preview-modal', handler);
    component.triggerShowPreviewModal('url.pdf', 'Test', 'Loading...', 'Download Label');
    $modal.trigger('show-preview-modal');

    expect(handler).toHaveBeenCalled();
  });

  test('bindPdfClickListener should attach click handler to PDF links', () => {
    const spy = jest.spyOn(component, 'showPreviewModalListener');
    component.bindPdfClickListener($(document));

    $('.promotion-product-listing__link').trigger('click');

    expect(spy).toHaveBeenCalled();
  });

  test('startDayExpiredInterval should call renderDayExpired repeatedly', () => {
    jest.useFakeTimers();
    component.startDayExpiredInterval();

    setTimeout(() => {
      expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 1000);
      jest.advanceTimersByTime(3000);

      expect(renderDayExpired).toHaveBeenCalledTimes(3);
    }, 10);
  });

  test('stopDayExpiredInterval should clear interval', () => {
    jest.useFakeTimers();
    component.startDayExpiredInterval();
    component.stopDayExpiredInterval();

    setTimeout(() => {
      expect(clearInterval).toHaveBeenCalled();
      expect(component.intervalId).toBeNull();
    }, 10);
  });

  test('renderCardExclusiveOffer should call renderDayExpired and start interval', () => {
    const spy = jest.spyOn(component, 'startDayExpiredInterval');
    component.renderCardExclusiveOffer();

    expect(renderDayExpired).toHaveBeenCalled();
    expect(spy).toHaveBeenCalled();
  });

  test('initOfferCard should bind click and initialize slick if width <= 768', () => {
    window.innerWidth = 768;
    const bindSpy = jest.spyOn(component, 'bindPdfClickListener');
    const renderSpy = jest.spyOn(component, 'renderCardExclusiveOffer');

    component.initOfferCard(window, $(document));

    expect(bindSpy).toHaveBeenCalled();
    expect(renderSpy).toHaveBeenCalled();
    expect($.fn.slick).toHaveBeenCalled();
  });

  test('connectedCallback should trigger initOfferCard and initRecommendCategory', () => {
    const initOfferCardSpy = jest.spyOn(component, 'initOfferCard');
    const initRecommendCategorySpy = jest.spyOn(component, 'initRecommendCategory');
    component.connectedCallback();

    expect(initOfferCardSpy).toHaveBeenCalled();
    expect(initRecommendCategorySpy).toHaveBeenCalled();
  });

  test('initRecommendCategory should create RecommendCategory instance if not exists', () => {
    expect(component.recommendCategory).toBeNull();
    component.initRecommendCategory();
    expect(component.recommendCategory).toBeTruthy();
  });

  test('initRecommendCategory should not create new instance if already exists', () => {
    component.initRecommendCategory();
    const firstInstance = component.recommendCategory;
    component.initRecommendCategory();
    expect(component.recommendCategory).toBe(firstInstance);
  });
});
