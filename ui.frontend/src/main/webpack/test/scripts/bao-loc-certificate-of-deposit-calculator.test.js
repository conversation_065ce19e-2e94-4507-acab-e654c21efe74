import { describe, expect, jest, test } from "@jest/globals";

class TestBaoLocCertificateOfDepositCalculator {
  constructor() {
    document.body.innerHTML = `<div class="bao-loc-certificate-of-deposit-calculator"></div>`;
    require('../../site/scripts/bao-loc-certificate-of-deposit-calculator');
  }
}

describe('BaoLocCertificateOfDepositCalculator', () => {
  test('should document is ready', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        0: {
          selectionStart: 1,
          selectionEnd: 1,
        },
        length: 1,
        ready: (callback) => callback(),
        each: (callback) => callback(),
        keyup: (callback) => callback(),
        keypress: (callback) => callback({
          which: 1,
        }),
        keydown: (callback) => callback({
          key: 1,
        }),
        val: () => {
          return '20-11-2024';
        },
        on: (param, callback) => callback({
          originalEvent: {
            data: '1',
          }
        }),
        bind: (param, callback) => callback({
          originalEvent: {
            clipboardData: {
              getData: jest.fn().mockReturnValue('1'),
            },
          },
          preventDefault: jest.fn(),
        }),
        click: (callback) => callback(),
        next: () => {
          return {
            css: jest.fn(),
            hasClass: jest.fn(),
            addClass: jest.fn(),
            datepicker: jest.fn(),
          };
        },
        hasClass: jest.fn(),
        parent: () => {
          return {
            outerHeight: jest.fn(),
          };
        },
        attr: (param) => {
          return 'sample';
        },
        find: (param) => {
          return {
            val: jest.fn().mockReturnValue('20-11-2024'),
            on: (param, callback) => callback({}),
          };
        },
        text: jest.fn().mockReturnValue(1),
      };
    });
    new TestBaoLocCertificateOfDepositCalculator();
    expect(document.readyState).toEqual('complete');
  });
});
