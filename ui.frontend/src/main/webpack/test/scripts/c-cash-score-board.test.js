import { describe, expect, test, jest } from "@jest/globals";

class TestCCashScoreBoard {
  constructor() {
    document.body.innerHTML = `
        <div class="c-cash-score-board-component">
            <div class="multistepform">
                <div class="multi-step-form__step">
                    <input name="company_name">
                    <input class="input__radio">
                    <input type="hidden" name="avgScore">
                </div>
                <div class="multi-step-form__step">
                    <input name="company_name">
                    <input class="input__radio">
                    <input type="hidden" name="avgScore">
                </div>
                <div class="multi-step-form__step">
                    <input name="company_name">
                    <input class="input__radio">
                    <input type="hidden" name="avgScore">
                </div>
                <input name="company_name">
                <div class="form-survey__single-choice"></div>
            </div>
            <div class="scoreboard">
                <div class="total-score-title"></div>
                <tbody>
                    <tr></tr>
                    <tr></tr>
                    <tr class="total-score-row></tr>
                </tbody>
            </div>
        </div>
        `;
    require("../../site/scripts/c-cash-score-board");
  }
}

describe("test c cash score board", () => {
  test("should document ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        closest: () => {
          return {
            find: () => {
              return {
                change: (callback) => callback(),
                each: (callback) => callback(),
                find: jest.fn(),
              };
            },
          };
        },
        find: () => {
          return {
            change: (callback) => callback(),
            find: () => {
              return {
                text: jest.fn(),
                before: jest.fn(),
                find: () => {
                  return {
                    text: jest.fn(),
                    find: jest.fn(),
                    each: (callback) => callback(),
                  };
                },
              };
            },
            text: () => {
              return {
                replace: jest.fn(),
              };
            },
            click: (callback) => callback(),
            each: (callback) => callback(),
          };
        },
        attr: jest.fn().mockReturnValue("English"),
        val: () => {
          return {
            replace: () => {
              return {
                trim: jest.fn(),
              };
            },
          };
        },
      };
    });
    new TestCCashScoreBoard();
    expect(document.readyState).toBe("complete");
  });
});
