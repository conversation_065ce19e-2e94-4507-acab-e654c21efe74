import {beforeEach, describe, expect, jest, test} from "@jest/globals";

jest.mock("jquery-ui/ui/widgets/autocomplete", () => {
  return {
    source: jest.fn(),
  };
});

class TestAtmMap {
  constructor() {
    require("../../site/scripts/atm-map");
  }
}

const body = `
  <div class="recaptcha">
    <div class="g-recaptcha"></div>
  </div>
  <div class="recaptcha"></div>
  <div class="recaptcha"></div>
  <div class="recaptcha"></div>
  <div class="recaptcha"></div>
`;

describe("test atm map", () => {
  beforeEach(() => {
    global.navigator.geolocation = {
      getCurrentPosition: jest.fn().mockImplementationOnce((success) => Promise.resolve(success({
        coords: {
          latitude: 51.1, longitude: 45.3,
        },
      }))),
    };
    JSON.parse = jest.fn().mockReturnValue(() => {
      return {branchSearchType: "branchSearchType"};
    });

    JSON.stringify = jest.fn().mockImplementation(() => {
      return {replace: jest.fn()};
    });
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        on: (event, callback) => callback(),
        parent: () => {
          return {
            find: () => {
              return {
                text: jest.fn(), append: jest.fn(),
              };
            },
          };
        },
        data: () => {
          return {
            trackingClickInfoValue: "{'branchSearchType':true}",
          };
        }, // attr: jest.fn().mockReturnValue("result message"),
        attr: (param) => {
          if (param === "class") {
            return {
              includes: jest.fn().mockReturnValue(true),
            };
          } else {
            return "result message";
          }
        },
        prop: jest.fn(),
        each: (callback) => callback(),
        find: (param) => {
          if (param === "#phone") {
            return {
              on: jest.fn(), parent: () => {
                return {
                  removeClass: jest.fn(), find: () => {
                    return {
                      hide: jest.fn(),
                    };
                  },
                };
              }, val: () => {
                return {
                  trim: jest.fn().mockReturnValue(""),
                };
              },
            };
          }

          if (param === ".client-list .item") {
            return {
              click: (callback) => callback(),
            };
          }

          if (param === ".btn-submit") {
            return {
              click: jest.fn(),
            };
          }

          if (param === "span") {
            return {
              text: jest.fn().mockReturnValue("ATM"),
            };
          }

          if (param === "input[name=\"selectedService\"]") {
            return {
              val: jest.fn(), empty: jest.fn(),
            };
          }

          if (param === ".schedule .date") {
            return {
              find: () => {
                return {
                  click: (callback) => callback(), css: jest.fn(), removeClass: jest.fn(),
                };
              }, css: jest.fn(),

            };
          }

          if (param === ".enable-time") {
            return {
              length: 1,
            };
          }
          return {
            click: (callback) => callback(),
            find: () => {
              return {
                find: () => {
                  return {
                    append: jest.fn(), click: (callback) => callback(), removeClass: jest.fn(), find: () => {
                      return {
                        remove: jest.fn(),
                        attr: jest.fn().mockReturnValue(true),
                        click: (callback) => callback(),
                        removeClass: jest.fn(),
                        off: () => {
                          return {
                            click: (callback) => callback(),
                          };
                        },
                        append: jest.fn(),
                        length: 1,
                        text: jest.fn(),
                      };
                    },
                  };
                },
                empty: jest.fn(),
                html: jest.fn(),
                autocomplete: jest.fn(),
                css: jest.fn(),
                attr: jest.fn(),
                text: jest.fn(),
                append: jest.fn(),
                focus: (callback) => callback(),
                addClass: jest.fn(),
                focusout: (callback) => callback(),
                removeClass: jest.fn(),
                val: jest.fn(),
                children: () => {
                  return {
                    first: () => {
                      return {
                        length: 1, find: () => {
                          return {
                            val: jest.fn(),
                          };
                        },
                      };
                    },
                  };
                },
                click: (callback) => {
                  if (callback) {
                    return callback();
                  } else {
                    return jest.fn();
                  }
                },
                closest: () => {
                  return {
                    value: true, click: (callback) => callback({
                      currentTarget: "currentTarget", preventDefault: jest.fn(),
                    }), closest: () => {
                      return {
                        find: () => {
                          return {
                            attr: jest
                              .fn()
                              .mockReturnValue("currentBranchSelect"),
                          };
                        },
                      };
                    },
                  };
                },
                each: (callback) => callback({}),
              };
            },
            children: () => {
              return {
                first: () => {
                  return {
                    length: 1, find: () => {
                      return {
                        val: jest.fn(),
                      };
                    },
                  };
                },
              };
            },
            on: (event, element, callback) => {
              if (callback) {
                callback();
              } else {
                element();
              }
            },
            css: jest.fn(),
            attr: () => {
              return {
                slice: jest.fn(),
              };
            },
            text: jest.fn().mockReturnValue("prevSelected"),
            val: () => {
              return {
                trim: () => {
                  return {
                    length: 1,
                  };
                },
              };
            },
            append: jest.fn(),
            removeClass: jest.fn(),
            each: (callback) => callback(),
            addClass: jest.fn(),
            parent: () => {
              return {
                removeClass: jest.fn(), find: () => {
                  return {
                    hide: jest.fn(),
                  };
                }, addClass: jest.fn(),
              };
            },
            show: jest.fn(),
            hide: jest.fn(),
            empty: jest.fn(),
            length: 1,
            scrollTop: jest.fn(),
            remove: jest.fn(),
            html: jest.fn(),
          };
        },
        hasClass: jest.fn().mockReturnValue(true),
        removeClass: jest.fn(),
        click: (callback) => callback({target: {}}),
        is: jest.fn(),
        parents: () => {
          return {
            is: jest.fn(),
          };
        },
        addClass: jest.fn(),
        text: jest.fn(),
        closest: () => {
          return {
            attr: jest.fn(), addClass: jest.fn(), length: 1,
          };
        },
        hide: jest.fn(),
        css: jest.fn(),
        val: jest.fn(),
      };
    });

    window.$.ajax = jest
      .fn()
      .mockImplementation(({url, method, data, contentType, success, error}) => {
        if (typeof success === "function") {
          success({
            data: {
              branchFragmentList: {
                items: [{
                  adrLine: {
                    plaintext: "plainText",
                  },
                }],
              }, atmCdmFragmentList: {
                items: [{
                  cityCode: 1,
                  city: "city",
                  cityName: "Hà Nội",
                  districtCode: 1,
                  districtCd: 1,
                  districtName: "districtName",
                  districtNm: "districtNm",
                  atmName: "ATM - 208 Lê Lợi - Sơn Tây, Hà Nội",
                  branchNm: "Techcombank 29-3",
                }, {
                  cityCode: 2,
                  city: "city",
                  cityName: "Hồ Chí Minh",
                  districtCode: 2,
                  districtCd: 2,
                  districtName: "districtName",
                  districtNm: "districtNm",
                  atmName: "ATM - 208 Lê Lợi - Sơn Tây, Hà Nội",
                  branchNm: "Techcombank 29-3",
                }],
              },
            }, servicesList: [{
              branchid: null, serviceInf: {
                servid: 61, servnm: "Deposit-withdrawal, Transfer, Statement, Cheque",
              }, subServiceInf: [{
                srvIdent: 71, srvName: "Cash Deposit", amountLimit: "-1", mainSrvIdent: 61, refData: [],
              }, {
                srvIdent: 72, srvName: "Withdrawal", amountLimit: "-1", mainSrvIdent: 61, refData: [],
              }, {
                srvIdent: 73, srvName: "Cheque withdrawal", amountLimit: "-1", mainSrvIdent: 61, refData: [],
              }, {
                srvIdent: 74, srvName: "Intrabank transfer", amountLimit: "-1", mainSrvIdent: 61, refData: [],
              }],
            }],
          });
        }
        if (typeof callback === "function") {
          error({error: "test"});
        }
      });

    window.$.each = (event, callback) => callback();
    window.grecaptcha = {
      enterprise: {
        getResponse: jest.fn(), reset: jest.fn(),
      },
    };
    document.body.innerHTML = body;
    new TestAtmMap();
  });
  test("should document ready", () => {
    expect(document.readyState).toBe("complete");
  });
});
