import { describe, expect, jest, test } from '@jest/globals';

class TestCarouselImages {
  constructor() {
    require('../../site/scripts/carousel-images');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    global.setTimeout = jest.fn((callback) => callback());
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        on: (event, callback) => callback(),
        slick: jest.fn(),
        length: 1,
        text: jest.fn(),
      };
    });
    new TestCarouselImages();
    expect(document.readyState).toBe('complete');
  });
});
