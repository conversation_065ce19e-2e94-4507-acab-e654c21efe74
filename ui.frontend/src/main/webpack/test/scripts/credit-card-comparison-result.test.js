import { describe, expect, test, jest } from "@jest/globals";

class TestCreditCardComparisonResult {
  constructor() {
    require("../../site/scripts/credit-card-comparison-result");
  }
}

describe("Test Credit Card Comparison Result", () => {
    beforeEach(() => {
        document.body.innerHTML = "";
        window.$ = jest.fn().mockImplementation(() => {
            return {
                ready: (callback) => callback(),
                each: (callback) => callback(),
                ajax: (callback) => callback(),
                find: () => {
                    return {
                        find: () => {
                            return {
                                each: () => {
                                    return {
                                        includes: jest.fn(),
                                    };
                                },
                                attr: jest.fn(),
                                click: (callback) => {
                                    if (typeof callback === "function") {
                                        callback({});
                                    }
                                },
                            };
                        },
                        on: (param, param2, callback) => {
                            if(param == 'click' && param2 == '.compare-product__bottom-button button' && typeof callback === "function") {
                                callback({
                                    preventDefault: jest.fn(),
                                    stopPropagation: jest.fn(),
                                });
                            }
                        },
                        addClass: jest.fn(),
                        filter: () => {
                            return {
                                slick: jest.fn(),
                            }
                        },
                        empty: jest.fn(),
                        append: jest.fn(),
                        removeClass: jest.fn(),
                        not: () => {
                            return {
                                value: "abc",
                                slick: jest.fn(),
                            }
                        },
                    };
                },
                parent: () => {
                    return {
                        find: () => {
                            return {
                                trigger: jest.fn(),
                            };
                        },
                    };
                },
                attr: jest.fn(),
                data: jest.fn(),
                css: jest.fn(),
                addClass: jest.fn(),
                removeClass: jest.fn(),
                map: () => {
                    return {
                        innerHeight: jest.fn(),
                        get: jest.fn(),
                    };
                },
                resize: (callback) => {
                    if (typeof callback === "function") {
                        callback({
                            css: jest.fn(),
                        });
                    }
                },
                click: (callback) => {
                    if (typeof callback === "function") {
                        callback({
                            target: {
                                value: "123",
                            },
                        });
                    }
                },
                is: jest.fn().mockReturnValue(false),
                parents: () => {
                    return {
                        is: jest.fn().mockReturnValue(false),
                    }
                },
                width: jest.fn().mockReturnValue(769),
            };
        });
    });

    test("should document is ready", () => {
        window.open = jest.fn();
        window.$.ajax = jest.fn().mockImplementation(({ url, method, data, contentType, success, error }) => {
            if (typeof success === "function") {
                success({
                    data: {
                        credit_debitCardDetailContentFragmentList: {
                            items: [
                                { itemNo: "1", itemName: "item1", title: "testTitle", cardId: "1", cardType: ["credit-card"]},
                                { itemNo: "2", itemName: "item2", title: "testTitle2", cardId: "2", cardType: ["debit-card"]}
                            ],
                        }
                    }
                });
            }
            if (typeof callback === "function") {
                error({});
            }
        });
        new TestCreditCardComparisonResult();
        expect(document.readyState).toBe("complete");
    });
});