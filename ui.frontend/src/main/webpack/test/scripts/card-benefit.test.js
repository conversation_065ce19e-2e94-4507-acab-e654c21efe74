import $ from 'jquery';
import { CardBenefitComponent } from '../../site/scripts/card-benefit';

customElements.define('tcb-card-benefit', CardBenefitComponent);

describe('CardBenefitComponent', () => {
  let instance;

  beforeEach(() => {
    document.body.innerHTML = `
      <tcb-card-benefit>
        <div id="modalCard" class="modal"></div>
        <button class="close-btn">Close</button>
        <div class="card-benefits-container layout-1">
          <div class="card-benefits-modal item-4"
               data-image-mb="mobile.jpg"
               data-bg-color="#FFF1D6"
               data-title="Card Title"
               data-content="Card Content"
               data-image="img.jpg"
               data-btn-label="Click me"
               data-btn-type="button"
               data-btn-link="https://example.com"
               data-btn-ref="1"
               data-btn-target="_blank">
            <img class="card-benefits-item-image" />
            <div class="card-benefits-content"></div>
          </div>
        </div>
        <div class="modal-card-benefits-content">
          <div class="content"></div>
          <a class="btn-card"><span></span></a>
        </div>
        <img class="banner-modal-img" />
      </tcb-card-benefit>
    `;

    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    window.dispatchEvent(new Event('resize'));

    instance = document.querySelector('tcb-card-benefit');
  });

  afterEach(() => {
    jest.restoreAllMocks();
    document.body.innerHTML = ''; // Reset DOM
  });

  test('should replace image src on mobile', () => {
    const $img = $('.card-benefits-item-image');
    setTimeout(() => {
      expect($img.attr('src')).toBe('mobile.jpg');
    }, 10);
  });

  test('should apply correct background gradient', () => {
    const background = $('.card-benefits-content').css('background');
    setTimeout(() => {
      expect(background).toContain('#FFF1D6');  
    }, 10);
    
  });

  test('should open modal and populate content on click', () => {
    $('.card-benefits-modal').trigger('click');

    const modal = document.getElementById('modalCard');
    setTimeout(() => {
      expect(modal.classList.contains('show')).toBe(true);
      expect($('.banner-modal-img').attr('src')).toBe('img.jpg');
      expect($('.modal-card-benefits-content .content').html()).toBe('Card Content');
      expect($('.btn-card').attr('href')).toBe('https://example.com');
      expect($('.btn-card').attr('ref')).toBe('nofollow');
      expect($('.btn-card').attr('target')).toBe('_blank');
      expect($('.btn-card span').html()).toBe('Click me');
    }, 10);
  });

  test('should close modal on close button click', () => {
    $('#modalCard').addClass('show');
    $('.close-btn').trigger('click');
    expect($('#modalCard').hasClass('show')).toBe(false);
  });
});
