import { describe, expect, test, jest } from "@jest/globals";

class TestFormHidden {
  constructor() {
    require("../../site/scripts/form-hidden");
  }
}

describe("Test Form Hidden", () => {
  beforeEach(() => {
    document.body.innerHTML = "";
    delete window.location;
    window.location = new URL(
      "https://test.com/en/test.html?param1=test1&param2=test2&param3=test3"
    );
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        length: jest.fn(),
        val: jest.fn(),
      };
    });
  });

  test("should document is ready", () => {
    document.body.innerHTML = `
        <div class="container">
          <input type='hidden' value="1234">
          <input type='hidden' value="4567">
          <input type='hidden' value="8912">
        </div>`;
    new TestFormHidden();
    expect(document.readyState).toBe("complete");
  });
});
