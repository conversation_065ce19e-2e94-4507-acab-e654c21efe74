import { describe, expect, test, jest } from "@jest/globals";

class TestQuickAccess {
  constructor() {
    require("../../site/scripts/quick-access");
  }
}

describe("Test Quick Access", () => {
    test("should document is ready", () => {
        window.$ = jest.fn().mockImplementation(() => {
            return {
                ready: (callback) => callback(),
                each: (callback) => callback(),
                find: () => {
                    return {
                        find: () => {
                            return {
                                find: () => {
                                    return {
                                        each: (callback) => callback(0),
                                        removeClass: jest.fn(),
                                        scrollCenter: (callback) => {
                                            if (typeof callback === "function") {
                                                callback({}, {});
                                            }
                                        },
                                        css: jest.fn(),
                                        remove: jest.fn(),
                                    }
                                },
                                each: (callback) => callback(0),
                                text: jest.fn(),
                            }
                        },
                        each: (callback) => {
                            if (typeof callback === "function") {
                                callback({});
                            }
                        },
                        click: (callback) => {
                            if (typeof callback === "function") {
                                callback({
                                    stopPropagation: jest.fn(),
                                });
                            }
                        },
                        css: jest.fn(),
                        text: jest.fn().mockReturnValue("mockTest"),
                    }
                },
                click: (callback) => {
                    if (typeof callback === "function") {
                        callback({});
                    }
                },
                text: jest.fn(),
                addClass: jest.fn(),
                removeClass: jest.fn(),
                css: jest.fn(),
                width: jest.fn().mockReturnValue(500),
                index: jest.fn(),
                length: 2,
                clone: () => {
                    return {
                        value: "<div class='testClass'></div>",
                        appendTo: jest.fn(),
                    }
                },
                resize: (callback) => {
                    if (typeof callback === "function") {
                        callback({
                            target: () => {
                                return {
                                    value: "abc",
                                }
                            },
                        });
                    }
                },
                is: jest.fn(),
                parents: () => {
                    return {
                        is: jest.fn(),
                    }
                },
            }
        });

        new TestQuickAccess();
        expect(document.readyState).toBe("complete");
    });
});