import { describe, expect, jest, test } from '@jest/globals';

class TestYoutubeEmbed {
  constructor() {
    require('../../site/scripts/youtube-embed');
  }
}

describe('YoutubeEmbed', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        length: 1,
        find: (param) => {
          if (param === '#embed_url') {
            return {
              val: () => 'https://youtu.be/CLeZyIID9Bo?si=0OcebUEj2BNnbfvI',
              attr: () => {},
            };
          }
          if (param === '#embed_type') {
            return {
              val: () => 'URL',
              attr: () => {},
            };
          }
          return {
            val: () => 'fixed',
            attr: () => {},
          };
        },
      };
    });

    window.YT = {
      ready: (callback) => callback(),
    };

    new TestYoutubeEmbed();
    document.querySelector(`script`).dispatchEvent(new Event('load'));
    expect(document.readyState).toBe('complete');
  });
});
