import { TcbRecommendPrePagePopup } from '../../site/scripts/tcb-recommend-pre-page-popup';
import { StorageUtils } from '../../site/scripts/utils/storage.util';

const PREVIOUS_PAGE_KEY = 'previousPage';
const SAMPLE_BODY = `
    <body data-tracking-page-name="English">
        <tcb-recommend-pre-page-popup>
            <div class="icon-svg" hidden></div>
            <div class="recommend-pre-page-popup hidden">
                <div class="recommend-content">
                    <div class="recommend-title">
                        <div class="recommend-description">Trang bạn truy cập gần nhất:</div>   
                    </div>
                    <div class="button-block">
                        <button class="primary"></button>
                        <button class="cancel"></button>    
                    </div>
                </div>
            </div>
        </tcb-recommend-pre-page-popup>
    </body>
`;

describe('tcb-recommend-pre-page-popup', () => {
  customElements.define('tcb-recommend-pre-page-popup', TcbRecommendPrePagePopup);

  beforeEach(() => {
    StorageUtils.set(PREVIOUS_PAGE_KEY, {
      slug: '/en',
      title: 'Title',
    });
    document.body.innerHTML = '';
    document.body.innerHTML = SAMPLE_BODY;
  });

  it('should document is ready', () => {
    expect(document.readyState).toBe('complete');
  });
});
