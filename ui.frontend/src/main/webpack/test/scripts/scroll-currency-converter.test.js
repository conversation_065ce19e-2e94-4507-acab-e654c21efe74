import { describe, expect, jest, test } from '@jest/globals';

class TestScrollCurrencyConverter {
  constructor() {
    require('../../site/scripts/scroll-currency-converter');
  }
}

describe('ScrollCurrencyConverter', () => {
  test('should create an instance', () => {
    window.ScrollCurrencyConverter = (callback) => callback([{}]);
    window.setTimeout = (callback) => callback([{}]);
    window.$ = jest.fn().mockImplementation(() => {
      return {
        find: () => {
          return {
            0: {
              children: {
                length: 4,
                0: {
                  getAttribute: () => {
                    return false;
                  }
                }
              }, getBoundingClientRect: () => {
                return {
                  right: '20',
                };
              }, click: () => {
              }
            }, on: (param, callback) => callback(),
          };
        }, get: () => {
          return {
            getBoundingClientRect: () => {
              return {
                right: '22',
              };
            },
          };
        }, on: (param, callback) => {
          callback();
        }, append: jest.fn(),
      };
    });

    new TestScrollCurrencyConverter();
    new ScrollCurrencyConverter(document.createElement('div'));
    expect(document.readyState).toBe('complete');
  });
});
