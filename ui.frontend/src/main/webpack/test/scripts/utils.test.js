import { formatString, getElementText, parseStringToJson } from '../../site/scripts/utils';
import { reloadYTSession } from '../../site/scripts/utils.js';

describe('formatString', () => {
  test('should remove diacritic characters and trim whitespace', () => {
    const input = 'ĩts a tést string  ';
    const expectedOutput = 'its a test string';
    expect(formatString(input)).toEqual(expectedOutput);
  });

  test('should handle null input', () => {
    const input = null;
    const expectedOutput = null;
    expect(formatString(input)).toEqual(expectedOutput);
  });
});

describe('getElementText', () => {
  test('should return plain text content of a jQuery element', () => {
    const elem = {
      text: () => 'Hello, World!',
    };
    const expectedOutput = 'Hello, World!';
    expect(getElementText(elem)).toEqual(expectedOutput);
  });

  test('should handle null input', () => {
    const elem = null;
    const expectedOutput = '';
    expect(getElementText(elem)).toEqual(expectedOutput);
  });
});

describe('parseStringToJson', () => {
  test('should parse a valid JSON string', () => {
    const input = '{"name": "John", "age": 30}';
    const expectedOutput = { name: 'John', age: 30 };
    expect(parseStringToJson(input)).toEqual(expectedOutput);
  });
});

describe('reloadYTSession', () => {
  const source = 'https://www.youtube.com/embed/M5xVGLzVKA0';
  beforeEach(() => {
    document.body.innerHTML = `<iframe class="sample" src="${source}"></iframe>`;
  });

  test('should reload the iframe', () => {
    const iframe = document.querySelector('iframe.sample');
    reloadYTSession(iframe);
    expect(iframe.getAttribute('src')).toEqual(source);
  });
});
