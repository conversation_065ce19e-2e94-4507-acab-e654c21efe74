import { describe, expect, jest, test } from '@jest/globals';

class TestInsprireExclusive {
  constructor() {
    require('../../site/scripts/inspire-exclusive');
  }
}

jest.mock(
  'swiper',
  () =>
    class Mocked {
      snapGrid = {
        length: 2,
      };

      setTranslate() {}

      translateTo() {}
    },
);

describe('test help panel', () => {
  test('should document ready', () => {
    jest.spyOn(document.body, 'addEventListener');
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        length: true,
        find: () => {
          return {
            eq: () => {
              return {
                addClass: jest.fn(),
                css: jest.fn(),
                find: () => {
                  return {
                    css: jest.fn(),
                  };
                },
                toggleClass: jest.fn(),
                hasClass: jest.fn(),
              };
            },
            find: () => {
              return {
                find: () => {
                  return {
                    find: () => {
                      return {
                        each: (callback) => callback(),
                      };
                    },
                  };
                },
                each: (callback) => callback(),
                removeClass: jest.fn(),
              };
            },
            each: (callback) => callback(),
            attr: jest.fn().mockReturnValue(4),
            css: jest.fn(),
            click: (callback) => callback({ currentTarget: 'test' }),
            removeClass: jest.fn(),
          };
        },
        attr: jest.fn().mockReturnValue(4),
        css: jest.fn(),
        hasClass: jest.fn().mockReturnValue(false),
        text: jest.fn().mockReturnValue('English'),
        toggleClass: jest.fn(),
        is: jest.fn(),
        parents: () => {
          return {
            siblings: () => {
              return {
                find: () => {
                  return {
                    text: jest.fn().mockReturnValue('English'),
                    scrollIntoView: jest.fn(),
                  };
                },
              };
            },
          };
        },
        click: (callback) => callback(),
        each: (callback) => callback(),
        prev: () => {
          return {
            length: false,
          };
        },
        scrollTop: jest.fn(),
        innerHeight: jest.fn(),
      };
    });
    new TestInsprireExclusive();
    document.body.dispatchEvent(new Event('scroll'));
    expect(document.readyState).toBe('complete');
  });
});
