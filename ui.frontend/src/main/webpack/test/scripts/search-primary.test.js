import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { TcbSearchPrimary } from '../../site/scripts/search-primary';

const GSC_CONTENT = `
  <div class="search-primary-container">
    <div class="search-engine__wrapper">
        <div id="___gcse_1"><div class="gsc-control-searchbox-only gsc-control-searchbox-only-vi" dir="ltr"><form class="gsc-search-box gsc-search-box-tools" accept-charset="utf-8"><table cellspacing="0" cellpadding="0" role="presentation" class="gsc-search-box"><tbody><tr><td class="gsc-input"><div class="gsc-input-box" id="gsc-iw-id2"><table cellspacing="0" cellpadding="0" role="presentation" id="gs_id51" class="gstl_51 gsc-input" style="width: 100%; padding: 0px;"><tbody><tr><td class="gsc-input-prefix-icon"><img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg"></td><td id="gs_tti51" class="gsib_a"><input autocomplete="off" type="text" size="10" class="gsc-input" name="search" title="tìm kiếm" aria-label="tìm kiếm" id="gsc-i-id2" dir="ltr" spellcheck="false" placeholder="Tìm kiếm sản phẩm, dịch vụ, thông tin của Techcombank" style="width: 100%; padding: 0px; border: none; margin: -0.0625em 0px 0px; height: 1.25em; outline: none; --searchIconUrl: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg);"></td><td class="gsib_b"><div class="gsst_b" id="gs_st51" dir="ltr"><a class="gsst_a" href="javascript:void(0)" title="Xóa nội dung trong hộp tìm kiếm" role="button" style="display: none;"><div><span></span></div></a></div></td></tr></tbody></table></div></td><td class="gsc-search-button"><button class="gsc-search-button gsc-search-button-v2"><svg width="13" height="13" viewBox="0 0 13 13"><title>tìm kiếm</title><path d="m4.8495 7.8226c0.82666 0 1.5262-0.29146 2.0985-0.87438 0.57232-0.58292 0.86378-1.2877 0.87438-2.1144 0.010599-0.82666-0.28086-1.5262-0.87438-2.0985-0.59352-0.57232-1.293-0.86378-2.0985-0.87438-0.8055-0.010599-1.5103 0.28086-2.1144 0.87438-0.60414 0.59352-0.8956 1.293-0.87438 2.0985 0.021197 0.8055 0.31266 1.5103 0.87438 2.1144 0.56172 0.60414 1.2665 0.8956 2.1144 0.87438zm4.4695 0.2115 3.681 3.6819-1.259 1.284-3.6817-3.7 0.0019784-0.69479-0.090043-0.098846c-0.87973 0.76087-1.92 1.1413-3.1207 1.1413-1.3553 0-2.5025-0.46363-3.4417-1.3909s-1.4088-2.0686-1.4088-3.4239c0-1.3553 0.4696-2.4966 1.4088-3.4239 0.9392-0.92727 2.0864-1.3969 3.4417-1.4088 1.3553-0.011889 2.4906 0.45771 3.406 1.4088 0.9154 0.95107 1.379 2.0924 1.3909 3.4239 0 1.2126-0.38043 2.2588-1.1413 3.1385l0.098834 0.090049z"></path></svg></button></td><td class="gsc-clear-button"><div class="gsc-clear-button" title="xóa các kết quả">&nbsp;</div></td></tr></tbody></table></form></div></div>
    </div>
    <input type="hidden" data-search-placeholder="Tìm kiếm sản phẩm, dịch vụ, thông tin của Techcombank">
    <div class="search-box_title">Các tìm kiếm gần đây của bạn</div>
    <div class="key-word-list" data-primary-search-result-slug="/tim-kiem">
        <div class="key-word-container">
            <a class="key-word-item" href="/tim-kiem?q=Hoàn tiền 50% hấp dẫn cho lần đầu quẹt thẻ tín dụng 111">Hoàn tiền 50% hấp dẫn cho lần đầu quẹt thẻ tín dụng 111</a>
            <div class="close-icon">
                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-grey.svg">
            </div>
        </div>

        <div class="key-word-container">
            <a class="key-word-item" href="/tim-kiem?q=card">card</a>
            <div class="close-icon">
                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-grey.svg">
            </div>
        </div>

        <div class="key-word-container">
            <a class="key-word-item" href="/tim-kiem?q=car">car</a>
            <div class="close-icon">
                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-grey.svg">
            </div>
        </div>

        <div class="key-word-container">
            <a class="key-word-item" href="/tim-kiem?q=apple">apple</a>
            <div class="close-icon">
                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-grey.svg">
            </div>
        </div>

        <div class="key-word-container">
            <a class="key-word-item" href="/tim-kiem?q=ưu đãi">ưu đãi</a>
            <div class="close-icon">
                <img src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/icon-close-grey.svg">
            </div>
        </div>
    </div>
    <div class="link_title">Liên kết hữu ích</div>
    <div class="card-list slick-initialized slick-slider"><div class="slick-list draggable"><div class="slick-track" style="opacity: 1; width: 20000px; transform: translate3d(0px, 0px, 0px);"><div class="slick-slide slick-current slick-active" data-slick-index="0" aria-hidden="false"><div><div class="card-container" style="width: 100%; display: inline-block;">
        <a href="/khach-hang-ca-nhan/chi-tieu/the/the-tin-dung" tabindex="0">
            <div>
                <img alt="card-search-popup" src="/content/dam/techcombank/public-site/imported-assets/card-Search-Popup-28e93cec3c-59bdec5495.png">
            </div>
            <div class="card-label">Thẻ tín dụng</div>
        </a>
    </div></div></div><div class="slick-slide" data-slick-index="1" aria-hidden="true" tabindex="-1"><div><div class="card-container" style="width: 100%; display: inline-block;">
        <a href="/khach-hang-ca-nhan/vay/vay-tieu-dung" tabindex="-1">
            <div>
                <img alt="card-search-popup" src="/content/dam/techcombank/public-site/imported-assets/card-2-2856cb5f58-4aa44f7fac.png">
            </div>
            <div class="card-label">Vay tiêu dùng</div>
        </a>
    </div></div></div><div class="slick-slide" data-slick-index="2" aria-hidden="true" tabindex="-1"><div><div class="card-container" style="width: 100%; display: inline-block;">
        <a href="/khach-hang-ca-nhan/ngan-hang-truc-tuyen/ngan-hang-so/techcombank-mobile" tabindex="-1">
            <div>
                <img alt="card-search-popup" src="/content/dam/techcombank/public-site/imported-assets/card-3-850f67eade-a75c15204d.png">
            </div>
            <div class="card-label">Techcombank Mobile</div>
        </a>
    </div></div></div><div class="slick-slide" data-slick-index="3" aria-hidden="true" tabindex="-1"><div><div class="card-container" style="width: 100%; display: inline-block;">
        <a href="/cong-cu-tien-ich/ty-gia" tabindex="-1">
            <div>
                <img alt="card-search-popup" src="/content/dam/techcombank/public-site/imported-assets/card-4-52d69999dc-3203f14d68.png">
            </div>
            <div class="card-label">Tỷ giá</div>
        </a>
    </div></div></div></div></div></div>
</div>
`;

describe('TCBGoogleSearch', () => {
  const localStorageMock = (() => {
    let store = {};
    return {
      getItem: (key) => store[key] || null,
      setItem: (key, value) => store[key] = value.toString(),
      clear: () => store = {},
    };
  })();
  Object.defineProperty(window, 'localStorage', { value: localStorageMock });
  customElements.define('tcb-search-primary', TcbSearchPrimary);

  beforeEach(() => {
    document.body.innerHTML = '';
    document.body.innerHTML = GSC_CONTENT;
    localStorage.setItem('historyKeywords', JSON.stringify(['keyA', 'keyA', 'keyA']));
  });

  it('should document is ready', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        not: () => {
          return {
            slick: () => {
            },
          };
        },
        click: jest.fn(),
        closest: () => {
          return {
            find: () => {
              return {
                text: jest.fn().mockReturnValue('sample'),
              };
            }
          };
        },
        parent: () => {
          return {
            remove: () => {
            },
          };
        },
        find: () => {
          return {
            hide: () => {
            },
            attr: () => {
            },
            append: () => {
            },
            each: (callback) => callback(),
            find: () => {
              return {
                each: (callback) => callback(),
              };
            },
          };
        }
      };
    });

    new TcbSearchPrimary();
    expect(document.readyState).toBe('complete');
  });
});
