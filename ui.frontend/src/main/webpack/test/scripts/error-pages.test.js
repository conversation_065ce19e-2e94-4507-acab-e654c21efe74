import { describe, expect, jest, test } from '@jest/globals';

class TestErrorPages {
  constructor() {
    require('../../site/scripts/error-pages');
  }
}

describe('test error pages', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    window.location = {
      href: 'https://techcombank.com/?cardId=1',
    };
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        ready: (callback) => callback(),
        length: 1,
        data: () => {
          return {
            trackingClickInfoValue: 'test',
            errorMessage: 'error message',
          };
        },
        attr: jest.fn(),
        on: (event, element, callback) => callback({ currentTarget: 'test' }),
        trigger: jest.fn(),
      };
    });
    new TestErrorPages();
    expect(document.readyState).toBe('complete');
  });
});
