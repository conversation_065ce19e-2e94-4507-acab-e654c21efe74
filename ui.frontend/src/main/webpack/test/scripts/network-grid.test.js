import { describe, expect, jest, test } from '@jest/globals';

class TestNetworkGrid {
  constructor() {
    require('../../site/scripts/network-grid');
  }
}

describe('NetworkGrid', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        each: callback => callback(),
        addClass: () => {},
        attr: () => {},
        find: (param) => {
          if (param === '.panel-list .panel') {
            return {
              click: () => {},
            };
          }
          return {
            find: () => {
              return {
                find: () => {},
                0: {},
              };
            },
            each: callback => callback(),
            text: () => {},
          };
        },
      };
    });

    new TestNetworkGrid();
    window.dispatchEvent(new Event('message'));
    expect(document.readyState).toBe('complete');
  });
});
