import { TcbTracker } from '../../site/scripts/tcb-tracker';

const SAMPLE_TRACKER = `
    <tcb-tracker
        event="event"
        value="value"
        download="download"
        app-download="app-download"
        cta="cta"
        faq="faq"
        dest-path="dest-path"
        video-name="video-name"
        contact-us="contact-us"
    >
    </tcb-tracker>
`;

const SAMPLE_TRACKER_WITHOUT_ATTRIBUTE = `
  <tcb-tracker></tcb-tracker>
`;

describe('tcb-tracker', () => {
  customElements.define('tcb-tracker', TcbTracker);

  it('should tracker data with attribute', () => {
    document.body.innerHTML = '';
    document.body.innerHTML = SAMPLE_TRACKER;
    const trackerElement = document.querySelector('tcb-tracker');
    trackerElement.dispatchEvent(new Event('click'));
    expect(document.readyState).toBe('complete');
  });

  it('should tracker data without attribute', () => {
    document.body.innerHTML = '';
    document.body.innerHTML = SAMPLE_TRACKER_WITHOUT_ATTRIBUTE;
    const trackerElement = document.querySelector('tcb-tracker');
    trackerElement.dispatchEvent(new Event('click'));
    expect(document.readyState).toBe('complete');
  });
});
