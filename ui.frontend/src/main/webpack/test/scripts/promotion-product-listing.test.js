import { TcbPromotionProductList } from '../../site/scripts/promotion-product-listing';
import { getDataResultPage } from '../../site/scripts/utils/get-data-result-page.util';
import { getEndpoint } from '../../site/scripts/utils/promotion-generate-endpoint.util';

customElements.define('tcb-promotion-product-listing', TcbPromotionProductList);

jest.mock('../../site/scripts/utils/get-data-result-page.util', () => ({
  getDataResultPage: jest.fn(),
}));

jest.mock('../../site/scripts/utils/promotion-generate-endpoint.util', () => ({
  getEndpoint: jest.fn(() => 'https://test.com/data'),
}));

describe('TcbPromotionProductList', () => {
  let instance;
  let rootElement;

  beforeEach(() => {
    document.body.innerHTML = `
    <tcb-promotion-product-listing>
      <div class="tcb-promotion-product-list" data-url="https://api.example.com" data-lang="en">
        <a class="tab-card__link" data-param="types" data-value="A"></a>
        <a class="tab-card__link" data-param="types" data-value="B"></a>
        <div class="group-card"></div>
      </div>
      </tcb-promotion-product-listing>
    `;
    instance = new TcbPromotionProductList();
    rootElement = document.querySelector('tcb-promotion-product-listing');
    rootElement.init();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with correct defaults', () => {
    expect(instance.PARAM_PROMOTION_LIST).toEqual({});
    expect(instance.paramsValue).toEqual({ limit: 4, offset: 0, sort: 'most popular' });
  });

  test('addParamValue adds value to PARAM_PROMOTION_LIST', () => {
    instance.addParamValue('types', 'A');
    expect(instance.PARAM_PROMOTION_LIST).toEqual({ types: ['A'] });

    instance.addParamValue('types', 'B');
    expect(instance.PARAM_PROMOTION_LIST).toEqual({ types: ['A', 'B'] });

    instance.addParamValue('types', 'A'); // Không thêm lại giá trị đã có
    expect(instance.PARAM_PROMOTION_LIST).toEqual({ types: ['A', 'B'] });
  });

  test('removeParamValue removes value from PARAM_PROMOTION_LIST', () => {
    instance.PARAM_PROMOTION_LIST = { types: ['A', 'B'] };
    instance.removeParamValue('types', 'A');
    expect(instance.PARAM_PROMOTION_LIST).toEqual({ types: ['B'] });

    instance.removeParamValue('types', 'B');
    expect(instance.PARAM_PROMOTION_LIST).toEqual({});
  });

  test('getParamPromotionListAsQuery generates correct query string', () => {
    instance.PARAM_PROMOTION_LIST = { types: ['A', 'B'], products: ['X'] };
    const query = instance.getParamPromotionListAsQuery();
    expect(query).toContain('types=A,B&products=X');
  });

  test('handleButtonClick toggles param value and updates DOM', () => {
    const button = rootElement.querySelector('[data-value="A"]');

    expect(button.classList.contains('active')).toBe(false);

    button.click();
    expect(button.classList.contains('active')).toBe(false);

    button.click();
    expect(button.classList.contains('active')).toBe(false);
    expect(instance.PARAM_PROMOTION_LIST.types).toBeUndefined();
    expect(getDataResultPage).toHaveBeenCalledTimes(4);
  });

  test('renderProductList calls getDataResultPage with correct params', () => {
    instance.PARAM_PROMOTION_LIST = { types: ['A'] };
    instance.renderProductList();
    expect(getEndpoint).toHaveBeenCalledWith(instance.dataUrl, instance.paramsValue);
    expect(getDataResultPage).toHaveBeenCalledWith(
      expect.objectContaining({
        dataUrl: expect.stringContaining('types=A'),
        classGroupCardName: instance.classGroupCardName,
        dataExpiryDate: instance.dataExpiryDate,
      }),
    );
  });
  test('init does not initialize if root is not found', () => {
    document.body.innerHTML = '<div class="other-element"></div>';
    const newInstance = new TcbPromotionProductList();
    expect(newInstance.root).toBeNull();
  });

  test('init sets dataExpiryDate with fallback values when dataset fields are missing', () => {
    document.body.innerHTML = `
    <div class="tcb-promotion-product-list" data-url="https://api.example.com"></div>
  `;
    const newInstance = new TcbPromotionProductList();
    expect(newInstance.dataExpiryDate).toBeUndefined();
  });

  test('bindEvents does nothing when no tabCardItem elements', () => {
    document.body.innerHTML = `
    <div class="tcb-promotion-product-list" data-url="https://api.example.com"></div>
  `;
    const newInstance = new TcbPromotionProductList();
    newInstance.tabCardItem = null; // Force no tabCardItem
    expect(() => newInstance.bindEvents()).not.toThrow();
  });

  test('handleButtonClick does nothing if button missing data-param or data-value', () => {
    const button = document.createElement('button');
    document.body.appendChild(button);
    const event = new Event('click');
    Object.defineProperty(event, 'currentTarget', { value: button });
    expect(() => instance.handleButtonClick(event)).not.toThrow();
  });

  test('getParamPromotionListAsQuery returns empty string when PARAM_PROMOTION_LIST is empty', () => {
    instance.PARAM_PROMOTION_LIST = {};
    expect(instance.getParamPromotionListAsQuery()).toBe('');
  });

  test('getParamPromotionListAsQuery encodes special characters', () => {
    instance.PARAM_PROMOTION_LIST = { types: ['A&B', 'C D'] };
    const query = instance.getParamPromotionListAsQuery();
    expect(query).toContain('types=A%26B,C%20D');
  });
});
