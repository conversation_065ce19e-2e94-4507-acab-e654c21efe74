import {beforeEach, describe, expect, jest, test} from "@jest/globals";
import {TcbSearchEngineMobile} from "../../site/scripts/tcb-search-engine-mobile";

class TestTcbSearchEngineMobile extends TcbSearchEngineMobile {
  constructor() {
    super();
  }

}

customElements.define("tcb-search-engine-mobile", TestTcbSearchEngineMobile);

describe("Test Tcb Search Engine Mobile", () => {
  let tcbSearchEngineMobile;
  beforeEach(() => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        find: jest.fn(),
        addClass: jest.fn(),
        removeClass: jest.fn(),
      };
    });
    // window.scrollY = -1;
    window.addEventListener = (event, callback) => callback();
    tcbSearchEngineMobile = new TestTcbSearchEngineMobile();

  });

  test("should document is ready", () => {
    expect(tcbSearchEngineMobile).toBeTruthy();
  });

  test("should show search engine when scroll up", () => {
    window.scrollY = -1;
    window.dispatchEvent(new Event("scroll"));
    expect(tcbSearchEngineMobile).toBeTruthy();
  });

  test("should hide search engine when scroll down", () => {
    window.scrollY = 1;
    let searchEngineMobileTest = new TestTcbSearchEngineMobile();
    window.dispatchEvent(new Event("scroll"));
    expect(searchEngineMobileTest).toBeTruthy();
  });

});
