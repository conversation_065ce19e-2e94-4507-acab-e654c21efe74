import { describe, expect, jest, test } from '@jest/globals';
import _ from 'lodash';

class TestDeviceLookCarousel {
  constructor() {
    require('../../site/scripts/device-look-carousel');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    _.debounce = (callback) => callback();
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        attr: jest.fn().mockReturnValue(1),
        width: jest.fn().mockReturnValue(993),
        find: () => {
          return {
            not: () => {
              return {
                slick: jest.fn(),
              };
            },
            text: jest.fn(),
            css: jest.fn(),
            on: (event, callback) => callback(event, {}, 0, {}),
            each: (callback) => callback(),
          };
        },
        on: jest.fn(),
        innerHeight: jest.fn().mockReturnValue(1),
        css: jest.fn(),
      };
    });
    new TestDeviceLookCarousel();
    expect(document.readyState).toBe('complete');
  });
});
