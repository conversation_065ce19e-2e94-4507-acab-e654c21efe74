import { describe, expect, test, jest } from "@jest/globals";

class TestListViewDocuments {
  constructor() {
    document.body.innerHTML = `
    <div class="mySlides"></div>
    `;
    require("../../site/scripts/list-view-documents");
  }
}

jest.mock("moment", () => {
  return () => jest.requireActual("moment")("2020-01-01T00:00:00.000Z");
});

describe("test list view documents", () => {
  test("should document ready", () => {
    document.documentElement.lang = "en";
    window.setTimeout = (callback) => callback([{}]);
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === "function") {
        callback();
      }
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        find: () => {
          return {
            0: {
              textContent: "textContent",
            },
            on: (event, element, callback) => {
              if (callback) {
                callback();
              } else {
                element({ stopPropagation: jest.fn() });
              }
            },
            find: () => {
              return {
                addClass: jest.fn(),
                attr: jest.fn(),
                click: (callback) => callback(),
                each: (callback) => callback(),
                find: () => {
                  return {
                    empty: jest.fn(),
                    on: (event, callback) => callback(),
                  };
                },
                map: jest
                  .fn()
                  .mockReturnValue({ get: jest.fn().mockReturnValue([{}]) }),
                add: () => {
                  return {
                    filter: () => {
                      return {
                        each: (callback) => callback(),
                      };
                    },
                  };
                },
                on: (event, callback) =>
                  callback({ stopPropagation: jest.fn() }),
                filter: () => {
                  return {
                    filter: () => {
                      return {
                        map: jest.fn().mockReturnValue({
                          get: jest.fn().mockReturnValue([{}]),
                        }),
                      };
                    },
                  };
                },
              };
            },
            map: jest
              .fn()
              .mockReturnValue({ get: jest.fn().mockReturnValue([{}]) }),
            empty: jest.fn(),
            append: jest.fn(),
            each: (callback) => callback(),
            toArray: jest.fn(),
            css: jest.fn(),
            click: (callback) => callback({ preventDefault: jest.fn() }),
            attr: jest.fn(),
            trigger: jest.fn(),
            text: jest.fn(),
            length: 1,
            closest: () => {
              return {
                find: () => {
                  return {
                    each: (callback) => callback(),
                  };
                },
              };
            },
            html: jest.fn(),
            clone: () => {
              return {
                appendTo: jest.fn(),
              };
            },
            removeClass: jest.fn(),
            parent: () => {
              return {
                removeClass: jest.fn(),
              };
            },
            filter: () => {
              return {
                filter: () => {
                  return {
                    map: jest.fn().mockReturnValue({
                      get: jest.fn().mockReturnValue([{}]),
                    }),
                  };
                },
              };
            },
          };
        },
        attr: () => {
          return {
            split: jest.fn().mockReturnValue([{}, {}]),
            includes: jest.fn(),
          };
        },
        empty: jest.fn(),
        append: jest.fn(),
        on: (event, callback) => callback({}, "2024", "Q1"),
        data: jest.fn((type) => {
          if (type === "file-type") {
            return "pdf";
          } else {
            return 1;
          }
        }),
        css: jest.fn(),
        siblings: () => {
          return {
            click: jest.fn(),
          };
        },
        keypress: (callback) => callback({ which: 13 }),
        val: jest.fn(),
        removeClass: jest.fn(),
        html: jest.fn(),
        prop: jest.fn(),
        parents: () => {
          return {
            length: 1,
            is: jest.fn(),
          };
        },
        trigger: jest.fn(),
        width: jest.fn().mockReturnValue(768),
        hasClass: jest.fn().mockReturnValue(true),
        toggleClass: jest.fn(),
        is: jest.fn(),
      };
    });

    new TestListViewDocuments();
    expect(document.readyState).toBe("complete");
  });
});
