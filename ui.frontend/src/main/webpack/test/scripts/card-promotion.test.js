import { describe, expect, jest, test } from '@jest/globals';

class TestCardPromotion {
  constructor() {
    require('../../site/scripts/card-promotion');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        resize: (callback) => callback({}),
        click: (callback) => callback(),
        show: jest.fn(),
        hide: jest.fn(),
        width: () => {
          return {
            css: () => {
              return {
                top: 100,
                left: 100,
                position: 'absolute',
                show: jest.fn(),
              };
            },
          };
        },
        length: 1,
        offset: () => {
          return {
            top: 100,
            left: 100,
          };
        },
      };
    });

    new TestCardPromotion();
    expect(document.readyState).toBe('complete');
  });
});
