import { describe, expect, jest, test } from '@jest/globals';

class TestListCardInfo {
  constructor() {
    document.body.innerHTML = `
    <div class="mySlides"></div>
    `;
    require('../../site/scripts/list-card-info');
  }
}

describe('test list card info', () => {
  test('should document ready', () => {
    document.getElementsByClassName = jest.fn().mockReturnValue([
      {
        style: {
          display: 'test',
        },
      },
    ]);
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        find: () => {
          return {
            length: 1,
            find: () => {
              return {
                length: 1,
                click: (callback) => callback(),
                removeClass: jest.fn(),
                css: jest.fn(),
              };
            },
            addClass: jest.fn(),
            not: () => {
              return {
                not: () => {
                  return {
                    slick: jest.fn(),
                  };
                },
                slick: jest.fn(),
              };
            },
            each: (callback) => callback(),
            attr: jest.fn(),
            text: jest.fn(),
            filter: () => {
              return {
                slick: jest.fn(),
              };
            },
            css: jest.fn(),
          };
        },
        data: () => {
          return {
            value: true,
            slice: jest.fn(),
          };
        },
        closest: () => {
          return {
            length: -1,
          };
        },
        hasClass: jest.fn().mockReturnValue(true),
        width: jest.fn().mockReturnValue(767),
        css: jest.fn(),
        on: (event, callback) => callback(),
      };
    });
    new TestListCardInfo();
    expect(document.readyState).toBe('complete');
  });
});
