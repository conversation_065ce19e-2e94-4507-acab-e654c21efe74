import { describe, expect, jest, test } from '@jest/globals';

class TestColumnPanel {
  constructor() {
    require('../../site/scripts/column-panel');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    // global.setTimeout = jest.fn((callback) => callback());
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        resize: (callback) => callback(),
        find: (param) => {
          if (typeof param === 'string' && param.includes('option')) {
            return [];
          }
          return {
            find: () => {
              return {
                text: jest.fn(),
                css: jest.fn(),
              };
            },
            remove: jest.fn(),
            css: jest.fn(),
            length: 1,
            each: (callback) => callback(),
            click: (callback) => callback(),
          };
        },
        data: jest.fn(),
        width: jest.fn().mockReturnValue(768),
        each: (callback) => callback(),
        0: {
          value: [
            {
              clientHeight: '12',
              style: {
                height: '12',
              },
            },
            {
              clientHeight: '12',
              style: {
                height: '12',
              },
            },
          ],
          offsetHeight: 10,
          scrollHeight: 20,
          querySelectorAll: jest.fn().mockReturnValue([
            {
              clientHeight: '12',
              style: {
                height: '12',
              },
            },
            {
              clientHeight: '12',
              style: {
                height: '12',
              },
            },
          ]),
        },
      };
    });
    new TestColumnPanel();
    expect(document.readyState).toBe('complete');
  });
});
