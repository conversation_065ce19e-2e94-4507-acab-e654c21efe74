import { afterEach, beforeEach, describe, expect, it, jest } from '@jest/globals';
import {
	TcbGoogleSearch,
	initGcseScript,
	getSearchHistoryKeywords,
	removeSearchHistoryKeyword
} from '../../site/scripts/tcb-google-search';
import { LANG_EN, LANG_VI } from '../../site/scripts/translations';

const GSC_CONTENT = `
<div class="search-engine__wrapper search-result__wrapper">
	<div class="gcse-search"></div>
  <div class="popup-download"></div>
  <div id="___gcse_1">
    <div class="gsc-control-cse gsc-control-cse-vi">
      <div class="gsc-control-wrapper-cse" dir="ltr">
        <form class="gsc-search-box gsc-search-box-tools" accept-charset="utf-8">
          <table cellspacing="0" cellpadding="0" role="presentation" class="gsc-search-box">
            <tbody>
            <tr>
              <td class="gsc-input">
                <div class="gsc-input-box" id="gsc-iw-id2">
                  <table cellspacing="0" cellpadding="0" role="presentation" id="gs_id51" class="gstl_51 gsc-input"
                         style="width: 100%; padding: 0px;">
                    <tbody>
                    <tr>
                      <td class="gsc-input-prefix-icon"><img
                              src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg">
                      </td>
                      <td id="gs_tti51" class="gsib_a"><input autocomplete="off" type="text" size="10" class="gsc-input"
                                                              name="search" title="tìm kiếm" aria-label="tìm kiếm"
                                                              id="gsc-i-id2" dir="ltr" spellcheck="false"
                                                              style="width: 100%; padding: 0px; border: none; margin: -0.0625em 0px 0px; height: 1.25em; outline: none; --searchIconUrl: url(/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/search-primary-icon.svg);"
                                                              placeholder="">
                      </td>
                      <td class="gsib_b">
                        <div class="gsst_b" id="gs_st51" dir="ltr"><a class="gsst_a" href="javascript:void(0)"
                                                                      title="Xóa nội dung trong hộp tìm kiếm"
                                                                      role="button" style="">
                          <div><span></span></div>
                        </a></div>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </td>
              <td class="gsc-search-button">
                <button class="gsc-search-button gsc-search-button-v2">
                  <svg width="13" height="13" viewBox="0 0 13 13"><title>tìm kiếm</title>
                    <path d="m4.8495 7.8226c0.82666 0 1.5262-0.29146 2.0985-0.87438 0.57232-0.58292 0.86378-1.2877 0.87438-2.1144 0.010599-0.82666-0.28086-1.5262-0.87438-2.0985-0.59352-0.57232-1.293-0.86378-2.0985-0.87438-0.8055-0.010599-1.5103 0.28086-2.1144 0.87438-0.60414 0.59352-0.8956 1.293-0.87438 2.0985 0.021197 0.8055 0.31266 1.5103 0.87438 2.1144 0.56172 0.60414 1.2665 0.8956 2.1144 0.87438zm4.4695 0.2115 3.681 3.6819-1.259 1.284-3.6817-3.7 0.0019784-0.69479-0.090043-0.098846c-0.87973 0.76087-1.92 1.1413-3.1207 1.1413-1.3553 0-2.5025-0.46363-3.4417-1.3909s-1.4088-2.0686-1.4088-3.4239c0-1.3553 0.4696-2.4966 1.4088-3.4239 0.9392-0.92727 2.0864-1.3969 3.4417-1.4088 1.3553-0.011889 2.4906 0.45771 3.406 1.4088 0.9154 0.95107 1.379 2.0924 1.3909 3.4239 0 1.2126-0.38043 2.2588-1.1413 3.1385l0.098834 0.090049z"></path>
                  </svg>
                </button>
              </td>
              <td class="gsc-clear-button">
                <div class="gsc-clear-button" title="xóa các kết quả">&nbsp;</div>
              </td>
            </tr>
            </tbody>
          </table>
        </form>
        <div class="gsc-results-wrapper-nooverlay gsc-results-wrapper-visible">
          <div class="gsc-positioningWrapper">
            <div class="gsc-tabsAreaInvisible">
              <div aria-label="refinement" role="tab" class="gsc-tabHeader gsc-inline-block gsc-tabhActive"><span>Techcombank MS1 AEM</span>
              </div>
              <span class="gs-spacer"> </span></div>
          </div>
          <div class="gsc-positioningWrapper">
            <div class="gsc-refinementsArea">
              <div class="gsc-select-custom"><span>Tất cả kết quả</span>
                <div class="gsc-select-custom-selector"><img
                        src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/chevron-bottom-icon.svg">
                </div>
              </div>
              <div class="gsc-refinementBlock">
                <div aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-inline-block gsc-refinementhActive"><span>Tất cả kết quả</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Cá nhân</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Doanh nghiệp</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Nhà đầu tư</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Về chúng tôi</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Công cụ &amp; Tiện ích</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Personal</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Business</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Investors</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>About Us</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Tool &amp; Utilities</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block"><span>Sản phẩm Cá nhân</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block">
                  <span>Sản phẩm Doanh nghiệp</span></div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block">
                  <span>Thông tin mới Cá nhân</span></div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block"><span>Thông tin mới Doanh nghiệp</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Personal products and services</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Personal updates</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Business products and service</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Business updates</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Personal Products</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Personal Promotions</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Business Products</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Investors Financial information</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Investors Annual General Shareholders Meeting</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Investors News and events</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Investors Disclosures</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Personal All results</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Business All results</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>Investors All results</span>
                </div>
                <span class="gs-spacer"> </span>
                <div tabindex="0" aria-label="refinement" role="tab"
                     class="gsc-refinementHeader gsc-refinementhInactive gsc-inline-block" style="display: none;"><span>About us All results</span>
                </div>
                <span class="gs-spacer"> </span></div>
            </div>
          </div>
          <div class="gsc-above-wrapper-area" style="display: none;">
            <div class="gsc-above-wrapper-area-backfill-container"></div>
            <table cellspacing="0" cellpadding="0" role="presentation" class="gsc-above-wrapper-area-container">
              <tbody>
              <tr>
                <td class="gsc-result-info-container">
                  <div class="gsc-result-info" id="resInfo-1">Khoảng 370 kết quả (0.25 giây)</div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
          <div class="gsc-adBlockNoHeight" style="height: 0px;"></div>
          <div class="gsc-wrapper">
            <div class="gsc-adBlockNoHeight" style="height: 0px;">
              <iframe frameborder="0" marginwidth="0" marginheight="0" allowtransparency="true" scrolling="no"
                      width="100%"
                      name="master-a-2|{&quot;name&quot;:&quot;master-2&quot;,&quot;master-a-2&quot;:{&quot;fexp&quot;:&quot;21404,********,********,********,********,********,********&quot;,&quot;masterNumber&quot;:2,&quot;number&quot;:0,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;s&quot;,&quot;adLoadedCallback&quot;:null,&quot;client_gdprApplies&quot;:0,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:0,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;},&quot;master-b-2&quot;:{&quot;fexp&quot;:&quot;21404,********,********,********,********,********,********&quot;,&quot;masterNumber&quot;:2,&quot;number&quot;:0,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;s&quot;,&quot;adLoadedCallback&quot;:null,&quot;client_gdprApplies&quot;:0,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:0,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;},&quot;master-2&quot;:{&quot;cx&quot;:&quot;b6e708782620e4010&quot;,&quot;fexp&quot;:&quot;********,********,20606,********,********,********,********,********,********&quot;,&quot;gcsc&quot;:true,&quot;masterNumber&quot;:2,&quot;number&quot;:null,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;m&quot;,&quot;source&quot;:&quot;gcsc&quot;,&quot;sct&quot;:&quot;ID=0d06a15185a4457c:T=17********:RT=17********:S=ALNI_MY27Se2vyRu_c8rrIkLHoANTrp6iQ&quot;,&quot;sc_status&quot;:6,&quot;hl&quot;:&quot;vi&quot;,&quot;ivt&quot;:false,&quot;client_gdprApplies&quot;:0,&quot;position&quot;:&quot;top&quot;,&quot;cseGoogleHosting&quot;:&quot;partner&quot;,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:4,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;}}"
                      id="master-a-2" src="https://www.adsensecustomsearchads.com/afs/ads/i/iframe.html"
                      style="visibility: hidden; height: 0px; display: block;"></iframe>
              <iframe frameborder="0" marginwidth="0" marginheight="0" allowtransparency="true" scrolling="no"
                      width="100%"
                      name="{&quot;name&quot;:&quot;master-2&quot;,&quot;master-a-2&quot;:{&quot;fexp&quot;:&quot;21404,********,********,********,********,********,********&quot;,&quot;masterNumber&quot;:2,&quot;number&quot;:0,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;s&quot;,&quot;adLoadedCallback&quot;:null,&quot;client_gdprApplies&quot;:0,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:0,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;},&quot;master-b-2&quot;:{&quot;fexp&quot;:&quot;21404,********,********,********,********,********,********&quot;,&quot;masterNumber&quot;:2,&quot;number&quot;:0,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;s&quot;,&quot;adLoadedCallback&quot;:null,&quot;client_gdprApplies&quot;:0,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:0,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;},&quot;master-2&quot;:{&quot;cx&quot;:&quot;b6e708782620e4010&quot;,&quot;fexp&quot;:&quot;********,********,20606,********,********,********,********,********,********&quot;,&quot;gcsc&quot;:true,&quot;masterNumber&quot;:2,&quot;number&quot;:null,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;m&quot;,&quot;source&quot;:&quot;gcsc&quot;,&quot;sct&quot;:&quot;ID=0d06a15185a4457c:T=17********:RT=17********:S=ALNI_MY27Se2vyRu_c8rrIkLHoANTrp6iQ&quot;,&quot;sc_status&quot;:6,&quot;hl&quot;:&quot;vi&quot;,&quot;ivt&quot;:false,&quot;client_gdprApplies&quot;:0,&quot;position&quot;:&quot;top&quot;,&quot;cseGoogleHosting&quot;:&quot;partner&quot;,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:4,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;}}"
                      id="master-2"
                      src="https://www.adsensecustomsearchads.com/cse_v2/ads?adsafe=high&amp;cx=b6e708782620e4010&amp;fexp=********%2C********%2C20606%2C********%2C********%2C********%2C********%2C********%2C********&amp;client=google-coop&amp;q=Apple&amp;r=m&amp;sct=ID%3D0d06a15185a4457c%3AT%3D17********%3ART%3D17********%3AS%3DALNI_MY27Se2vyRu_c8rrIkLHoANTrp6iQ&amp;sc_status=6&amp;hl=vi&amp;ivt=0&amp;type=0&amp;oe=UTF-8&amp;ie=UTF-8&amp;client_gdprApplies=0&amp;format=p4&amp;ad=p4&amp;nocache=****************&amp;num=0&amp;output=uds_ads_only&amp;source=gcsc&amp;v=3&amp;bsl=10&amp;pac=0&amp;u_his=4&amp;u_tz=420&amp;dt=*************&amp;u_w=1920&amp;u_h=1080&amp;biw=1920&amp;bih=968&amp;psw=1920&amp;psh=1130&amp;frm=0&amp;uio=-&amp;drt=0&amp;jsid=csa&amp;nfp=1&amp;jsv=*********&amp;rurl=https%3A%2F%2Faem-uat.techcombank.com%2Ftim-kiem%3Fq%3DApple2%23gsc.tab%3D0%26gsc.q%3DApple&amp;referer=https%3A%2F%2Faem-uat.techcombank.com%2F"
                      style="visibility: hidden; height: 0px; display: block;"></iframe>
              <iframe frameborder="0" marginwidth="0" marginheight="0" allowtransparency="true" scrolling="no"
                      width="100%"
                      name="master-b-2|{&quot;name&quot;:&quot;master-2&quot;,&quot;master-a-2&quot;:{&quot;fexp&quot;:&quot;21404,********,********,********,********,********,********&quot;,&quot;masterNumber&quot;:2,&quot;number&quot;:0,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;s&quot;,&quot;adLoadedCallback&quot;:null,&quot;client_gdprApplies&quot;:0,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:0,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;},&quot;master-b-2&quot;:{&quot;fexp&quot;:&quot;21404,********,********,********,********,********,********&quot;,&quot;masterNumber&quot;:2,&quot;number&quot;:0,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;s&quot;,&quot;adLoadedCallback&quot;:null,&quot;client_gdprApplies&quot;:0,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:0,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;},&quot;master-2&quot;:{&quot;cx&quot;:&quot;b6e708782620e4010&quot;,&quot;fexp&quot;:&quot;********,********,20606,********,********,********,********,********,********&quot;,&quot;gcsc&quot;:true,&quot;masterNumber&quot;:2,&quot;number&quot;:null,&quot;pubId&quot;:&quot;google-coop&quot;,&quot;query&quot;:&quot;Apple&quot;,&quot;role&quot;:&quot;m&quot;,&quot;source&quot;:&quot;gcsc&quot;,&quot;sct&quot;:&quot;ID=0d06a15185a4457c:T=17********:RT=17********:S=ALNI_MY27Se2vyRu_c8rrIkLHoANTrp6iQ&quot;,&quot;sc_status&quot;:6,&quot;hl&quot;:&quot;vi&quot;,&quot;ivt&quot;:false,&quot;client_gdprApplies&quot;:0,&quot;position&quot;:&quot;top&quot;,&quot;cseGoogleHosting&quot;:&quot;partner&quot;,&quot;columns&quot;:1,&quot;horizontalAlignment&quot;:&quot;left&quot;,&quot;resultsPageQueryParam&quot;:&quot;query&quot;,&quot;ie&quot;:&quot;UTF-8&quot;,&quot;maxTop&quot;:4,&quot;minTop&quot;:0,&quot;oe&quot;:&quot;UTF-8&quot;,&quot;type&quot;:&quot;ads&quot;,&quot;linkTarget&quot;:&quot;_top&quot;}}"
                      id="master-b-2" src="https://www.adsensecustomsearchads.com/afs/ads/i/iframe.html"
                      style="visibility: hidden; height: 0px; display: block;"></iframe>
            </div>
            <div class="gsc-resultsbox-visible">
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdActive">
                <div class="gsc-results gsc-webResult">
                  <div class="gsc-expansionArea gs-spelling gs-spelling-original gs-no-results-result gsc-promotion">
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAAQAQ&amp;usg=AOvVaw0KismCli2EhIxjuCp4AOri&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay">Thanh
                            toán với <b>Apple</b> Pay | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/khach-hang-ca.../chi.../thanh-toan-voi-<b>apple</b>-pay
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › Cá nhân</span><span> › Chi tiêu</span><span> › Thẻ</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAAQAQ&amp;usg=AOvVaw0KismCli2EhIxjuCp4AOri&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay">Thanh
                              toán với <b>Apple</b> Pay | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">Thanh toán dễ dàng với <b>Apple</b>
                              Pay · Với iPhone: nhấn đúp vào nút sườn, xác thực bằng Face ID, Touch ID hoặc mật mã, sau
                              đó giữ thiết bị gần đầu đọc để thanh toán&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/khach-hang-ca.../chi.../thanh-toan-voi-<b>apple</b>-pay
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/thanh-toan-voi-apple-pay">
                              <span>Đã gắn nhãn</span><a class="gs-label" data-refinementlabel="sản_phẩm_cá_nhân"
                                                         label-with-op="more:Sản phẩm Cá nhân" dir="ltr" tabindex="0">Sản
                              phẩm ...</a><span></span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAkQAQ&amp;usg=AOvVaw0t-rWlirAJHMY15SZbqG3B&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay">Payment
                            with <b>Apple</b> Pay | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/en/personal/spend/cards/payment-with-<b>apple</b>-pay
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › ...</span><span> › Personal</span><span> › Spend</span><span> › Cards</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAkQAQ&amp;usg=AOvVaw0t-rWlirAJHMY15SZbqG3B&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay">Payment
                              with <b>Apple</b> Pay | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">Now you can pay with your Techcombank
                              card anywhere you see the <b>Apple</b> Pay logo or the contactless payment icon in stores,
                              online, or in your favorite apps –&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/en/personal/spend/cards/payment-with-<b>apple</b>-pay
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/en/personal/spend/cards/payment-with-apple-pay"><span>Đã gắn nhãn</span><a
                                    class="gs-label" data-refinementlabel="personal" label-with-op="more:personal"
                                    dir="ltr" tabindex="0">Personal</a><span></span><a class="gs-label"
                                                                                       data-refinementlabel="personal_products_and_services"
                                                                                       label-with-op="more:Personal products and services"
                                                                                       dir="ltr" tabindex="0">Personal
                              ...</a><span></span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAYQAQ&amp;usg=AOvVaw0c0CvoO_6i1DD5-5xy-8RO&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers">Techcombank
                            Brings <b>Apple</b> Pay to Customers | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/en/.../techcombank-brings-<b>apple</b>-pay-to-customer...
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › ...</span><span> › About us</span><span> › Press &amp; Media</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAYQAQ&amp;usg=AOvVaw0c0CvoO_6i1DD5-5xy-8RO&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers">Techcombank
                              Brings <b>Apple</b> Pay to Customers | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">Techcombank Brings <b>Apple</b> Pay to
                              Customers ... To use, customers simply double click and hold their iPhone or <b>Apple</b>
                              Watch near a payment terminal to make a&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">https://techcombank.com/en/.../techcombank-brings-<b>apple</b>-pay-to-customer...
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/en/about-us/press-media/techcombank-brings-apple-pay-to-customers"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAQQAQ&amp;usg=AOvVaw3t9G0G1u47zrTgSB418vPr&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang">Techcombank
                            Brings <b>Apple</b> Pay to Customers | Techcombank ...</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/.../techcombank-gioi-thieu-<b>apple</b>-pay-den-khach-...
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › ...</span><span> › About Us</span><span> › Press &amp; Media</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAQQAQ&amp;usg=AOvVaw3t9G0G1u47zrTgSB418vPr&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang">Techcombank
                              Brings <b>Apple</b> Pay to Customers | Techcombank ...</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">A safer, more secure, and private way
                              to pay with iPhone, <b>Apple</b> Watch, iPad and Mac.
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">https://techcombank.com/.../techcombank-gioi-thieu-<b>apple</b>-pay-den-khach-...
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/en/about-us/press-media/techcombank-gioi-thieu-apple-pay-den-khach-hang"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAcQAQ&amp;usg=AOvVaw2ndoJnR93KDZAZwVJH1WUk&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop">Giảm
                            500,000 VND khi trả góp mua sắm <b>Apple</b> tại FPT Shop ...</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/.../giam-500-000-vnd-khi-tra-gop-mua-sam-<b>appl</b>...
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › tin-tuc-bao-chi</span><span> › chuong-trinh-uu-dai</span><span> › giam-500...</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAcQAQ&amp;usg=AOvVaw2ndoJnR93KDZAZwVJH1WUk&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop">Giảm
                              500,000 VND khi trả góp mua sắm <b>Apple</b> tại FPT Shop ...</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">Chủ thẻ tín dụng Techcombank có thể sở
                              những sản phẩm <b>Apple</b> thời thượng với ưu đãi giảm 500000 VND khi mua hàng trả góp
                              online tại FPT Shop.
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/.../giam-500-000-vnd-khi-tra-gop-mua-sam-<b>appl</b>...
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/giam-500-000-vnd-khi-tra-gop-mua-sam-apple-tai-fpt-shop"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAEQAQ&amp;usg=AOvVaw0bbVvPNKLQdr_1ekyU_sQz&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the">Tiện
                            ích thẻ | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › Cá nhân</span><span> › Chi tiêu</span><span> › Thẻ</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAEQAQ&amp;usg=AOvVaw0bbVvPNKLQdr_1ekyU_sQz&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the">Tiện
                              ích thẻ | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">Thanh toán với <b>Apple</b> Pay · <b>Apple</b>
                              Pay là phương thức thanh toán dễ dàng, an toàn và riêng tư bằng thẻ thanh toán và thẻ tín
                              dụng Techcombank Visa. Liên kết thẻ&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/khach-hang-ca-nhan/chi-tieu/the/tien-ich-the"><span>Đã gắn nhãn</span><a
                                    class="gs-label" data-refinementlabel="sản_phẩm_cá_nhân"
                                    label-with-op="more:Sản phẩm Cá nhân" dir="ltr" tabindex="0">Sản phẩm
                              ...</a><span></span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAIQAQ&amp;usg=AOvVaw3pbNGF8ivT-XRp6X5s2ZNO&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai">Sắm
                            Táo đón hè - Nhận ngàn ưu đãi | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/ve.../sam-tao-don-he-nhan-ngan-uu-dai
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › tin-tuc-bao-chi</span><span> › sam-tao-don-he-nhan-ngan-uu-dai</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAIQAQ&amp;usg=AOvVaw3pbNGF8ivT-XRp6X5s2ZNO&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai">Sắm
                              Táo đón hè - Nhận ngàn ưu đãi | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr"><b>Apple</b> Macbook Air 2016 MMGF2
                              128GB 13 inches Bạc (Hàng chính hãng), 20.550.000, 19.750.000. <b>Apple</b> The New
                              MacBook Retina 2016 MLHE2 12 inches Vàng (Hàng chính&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/ve.../sam-tao-don-he-nhan-ngan-uu-dai
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/sam-tao-don-he-nhan-ngan-uu-dai"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/khach-hang-ca-nhan/uu-dai"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/khach-hang-ca-nhan/uu-dai&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAUQAQ&amp;usg=AOvVaw2aqXWxTkkQU0LfuxzgAIHs&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/khach-hang-ca-nhan/uu-dai">Ưu
                            đãi dành cho khách hàng cá nhân | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">https://techcombank.com/khach-hang-ca-nhan/uu-dai
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › Cá nhân</span></div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/khach-hang-ca-nhan/uu-dai"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/khach-hang-ca-nhan/uu-dai&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAUQAQ&amp;usg=AOvVaw2aqXWxTkkQU0LfuxzgAIHs&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/khach-hang-ca-nhan/uu-dai">Ưu
                              đãi dành cho khách hàng cá nhân | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">Thẻ tín dụng,Thẻ thanh toán
                              <b>Apple</b> Pay. Hoàn tiền trị giá 100,000 vnđ khi thực hiện giao dịch thanh toán hợp lệ
                              qua ứng dụng <b>Apple</b> Wallet. time-icon Kết thúc&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">https://techcombank.com/khach-hang-ca-nhan/uu-dai
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels" url="https://techcombank.com/khach-hang-ca-nhan/uu-dai">
                              <span>Đã gắn nhãn</span><a class="gs-label" data-refinementlabel="sản_phẩm_cá_nhân"
                                                         label-with-op="more:Sản phẩm Cá nhân" dir="ltr" tabindex="0">Sản
                              phẩm ...</a><span></span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAgQAg&amp;usg=AOvVaw0LdyDt1Xec6TN6ppSaIyoQ&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS">Upgrade
                            new version of Smart OTP iOS | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/en/.../upgrade-new-version-of-smart-OTP-iOS
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › ...</span><span> › Day to day</span><span> › Digital services</span>
                          </div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAgQAg&amp;usg=AOvVaw0LdyDt1Xec6TN6ppSaIyoQ&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS">Upgrade
                              new version of Smart OTP iOS | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">30 thg 10, 2022 <b>...</b> ... <b>Apple</b>
                              Store. Note: The new TCB OTP application only supports phones that are using iOS version
                              11 or higher. To ensure the smooth use of the&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/en/.../upgrade-new-version-of-smart-OTP-iOS
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/en/business/day-to-day/digital-services/fast-ebank/upgrade-new-version-of-smart-OTP-iOS">
                              <span>Đã gắn nhãn</span><a class="gs-label" data-refinementlabel="business"
                                                         label-with-op="more:business" dir="ltr"
                                                         tabindex="0">Business</a><span></span><a class="gs-label"
                                                                                                  data-refinementlabel="business_products_and_service"
                                                                                                  label-with-op="more:Business products and service"
                                                                                                  dir="ltr"
                                                                                                  tabindex="0">Business
                              ...</a><span></span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-webResult gsc-result">
                      <div class="gs-webResult gs-result">
                        <div class="gsc-thumbnail-inside">
                          <div class="gs-title"><a class="gs-title"
                                                   href="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang"
                                                   target="_self" dir="ltr"
                                                   data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAMQAg&amp;usg=AOvVaw3ixb_p_QNedtC60Q-E2Jyi&amp;fexp=********,********"
                                                   data-ctorig="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang">Khai
                            trường rộn ràng, ngập tràn quà tặng | Techcombank</a></div>
                        </div>
                        <div class="gsc-url-top">
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">techcombank.com
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                               style="word-break:break-all;">
                            https://techcombank.com/.../khai-truong-ron-rang-ngap-tran-qua-tang
                          </div>
                          <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-breadcrumb">
                            <span>Techcombank</span><span> › khai-truong-ron-rang-ngap-tran-qua-tang</span></div>
                        </div>
                        <div class="gsc-table-result">
                          <div class="gsc-table-cell-snippet-close">
                            <div class="gs-title gsc-table-cell-thumbnail gsc-thumbnail-left"><a class="gs-title"
                                                                                                 href="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang"
                                                                                                 target="_self"
                                                                                                 dir="ltr"
                                                                                                 data-cturl="https://www.google.com/url?client=internal-element-cse&amp;cx=b6e708782620e4010&amp;q=https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang&amp;sa=U&amp;ved=2ahUKEwjVvKSWsfuGAxUX4DQHHZdPCeUQFnoECAMQAg&amp;usg=AOvVaw3ixb_p_QNedtC60Q-E2Jyi&amp;fexp=********,********"
                                                                                                 data-ctorig="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang">Khai
                              trường rộn ràng, ngập tràn quà tặng | Techcombank</a></div>
                            <div class="gs-bidi-start-align gs-snippet" dir="ltr">15 thg 8, 2017 <b>...</b> Cụ thể,
                              trong thời gian này, khách hàng khi mua các sản phẩm máy tính xách tay (*) của
                              <b>Apple</b> (Macbook Air, Macbook Pro, iMac), HP (Pavilon&nbsp;...
                            </div>
                            <div class="gsc-url-bottom">
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-short" dir="ltr">
                                techcombank.com
                              </div>
                              <div class="gs-bidi-start-align gs-visibleUrl gs-visibleUrl-long" dir="ltr"
                                   style="word-break:break-all;">
                                https://techcombank.com/.../khai-truong-ron-rang-ngap-tran-qua-tang
                              </div>
                            </div>
                            <div class="gs-richsnippet-box" style="display: none;"></div>
                            <div class="gs-per-result-labels"
                                 url="https://techcombank.com/ve-chung-toi/tin-tuc-bao-chi/chuong-trinh-uu-dai/khuyen-mai-cho-san-pham/khai-truong-ron-rang-ngap-tran-qua-tang"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="gsc-adBlockNoHeight" style="height: 0px;"></div>
                  </div>
                  <div class="gsc-cursor-box gs-bidi-start-align" dir="ltr">
                    <div class="gsc-cursor">
                      <div class="gsc-cursor-page gsc-cursor-current-page" tabindex="0">1</div>
                      <div class="gsc-cursor-page" aria-label="Trang 2" role="link" tabindex="0">2</div>
                      <div class="gsc-cursor-page" aria-label="Trang 3" role="link" tabindex="0">3</div>
                      <div class="gsc-cursor-page" aria-label="Trang 4" role="link" tabindex="0">4</div>
                      <div class="gsc-cursor-page" aria-label="Trang 5" role="link" tabindex="0">5</div>
                      <div class="gsc-cursor-page" aria-label="Trang 6" role="link" tabindex="0">6</div>
                      <div class="gsc-cursor-page" aria-label="Trang 7" role="link" tabindex="0">7</div>
                      <div class="gsc-cursor-page" aria-label="Trang 8" role="link" tabindex="0">8</div>
                      <div class="gsc-cursor-page" aria-label="Trang 9" role="link" tabindex="0">9</div>
                      <div class="gsc-cursor-page" aria-label="Trang 10" role="link" tabindex="0">10</div>
                    </div>
                  </div>
                  <div class="gcsc-more-maybe-branding-root"><a
                          href="https://www.google.com/search?client=ms-google-coop&amp;q=Apple&amp;cx=b6e708782620e4010"
                          target="_self">
                    <div class="gcsc-find-more-on-google">
                      <svg width="12" height="12" viewBox="0 0 13 13" class="gcsc-find-more-on-google-magnifier"><title>
                        tìm kiếm</title>
                        <path d="m4.8495 7.8226c0.82666 0 1.5262-0.29146 2.0985-0.87438 0.57232-0.58292 0.86378-1.2877 0.87438-2.1144 0.010599-0.82666-0.28086-1.5262-0.87438-2.0985-0.59352-0.57232-1.293-0.86378-2.0985-0.87438-0.8055-0.010599-1.5103 0.28086-2.1144 0.87438-0.60414 0.59352-0.8956 1.293-0.87438 2.0985 0.021197 0.8055 0.31266 1.5103 0.87438 2.1144 0.56172 0.60414 1.2665 0.8956 2.1144 0.87438zm4.4695 0.2115 3.681 3.6819-1.259 1.284-3.6817-3.7 0.0019784-0.69479-0.090043-0.098846c-0.87973 0.76087-1.92 1.1413-3.1207 1.1413-1.3553 0-2.5025-0.46363-3.4417-1.3909s-1.4088-2.0686-1.4088-3.4239c0-1.3553 0.4696-2.4966 1.4088-3.4239 0.9392-0.92727 2.0864-1.3969 3.4417-1.4088 1.3553-0.011889 2.4906 0.45771 3.406 1.4088 0.9154 0.95107 1.379 2.0924 1.3909 3.4239 0 1.2126-0.38043 2.2588-1.1413 3.1385l0.098834 0.090049z"></path>
                      </svg>
                      <span class="gcsc-find-more-on-google-text">Tìm kiếm </span><span
                            class="gcsc-find-more-on-google-query">Apple</span><span
                            class="gcsc-find-more-on-google-text"> trên Google</span></div>
                  </a></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
              <div class="gsc-resultsRoot gsc-tabData gsc-tabdInactive">
                <div>
                  <div class="gsc-expansionArea"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<input type="hidden" data-search-placeholder="sample" />
`;

describe('TCBGoogleSearch', () => {
	const localStorageMock = (() => {
		let store = {};
		return {
			getItem: (key) => store[key] || null,
			setItem: (key, value) => store[key] = value.toString(),
			clear: () => store = {},
		};
	})();
	Object.defineProperty(window, 'localStorage', { value: localStorageMock });

	let component;
	let searchResultWrapper;
	let REFINEMENT_TABS;
	let PROMOTION_LIST = {};
	let AD_BANNER_DATA = {};
	customElements.define('tcb-google-search', TcbGoogleSearch);

	beforeEach(() => {
		document.body.innerHTML = ``;
		document.body.innerHTML = GSC_CONTENT;
		window.innerWidth = 1025;
		document.documentElement.lang = 'en';
		window.history.pushState(
			{},
			'Test Page',
			`/${LANG_EN}/path?q=apple#gsc.tab=0&gsc.q=apple&gsc.ref=more%3Apersonal-products`
		);
		component = new TcbGoogleSearch();
		searchResultWrapper = document.querySelector('.search-result__wrapper');
		jest.useFakeTimers('modern');
		jest.setSystemTime(new Date(2024, 5, 29, 0, 0, 0));

		REFINEMENT_TABS = [
			'Cá nhân Sản phẩm',
			'Cá nhân Ưu đãi',
			'Cá nhân Thông tin mới',
			'Doanh nghiệp Tất cả kết quả',
			'Doanh nghiệp Sản phẩm',
			'Nhà đầu tư Thông tin tài chính',
			'Nhà đầu tư Đại hội đồng cổ đông',
			'Nhà đầu tư Tin tức và sự kiện',
			'Nhà đầu tư Công bố thông tin',
			'Về chúng tôi Tất cả kết quả',
			'Tất cả',
		];

		AD_BANNER_DATA = {
			'title': 'Title',
			'description': 'Description',
			'ctaLabel': 'Đăng ký',
			'ctaIcon': '/content/dam/techcombank/adobe-qa/about-us/red_arrow_54cb6ae59e(1).svg',
			'bannerImageDesktop': '/content/dam/techcombank/adobe-qa/A_black_image.jpg',
			'bannerImageMobile': '/content/dam/techcombank/adobe-qa/Frame_46442_1_a37fd6aad5.jpg',
			'theme': 'dark',
			'url': 'https://sample.com',
			'startDate': 'Jun 28, 2024, 12:00:00 AM',
			'endDate': 'Jul 30, 2024, 10:33:00 AM',
			'defaultBanner': false,
			'triggeredKeywords': [
				'Thẻ tín dụng',
				'Card',
				'Apple'
			],
			'externalUrl': 'sample',
			'matchedKeyword': 'Apple'
		};

		PROMOTION_LIST = {
			'total': '69',
			'results': [
				{
					'thumbnail': '/content/dam/techcombank/public-site/articles/non-blog/Techcare-Travel-thumbnail-0635ed110c-3109b18cf5.jpg.rendition/cq5dam.thumbnail.319.319.png',
					'description': '\u003cp\u003eHưởng quyền lợi y tế trên toàn thế giới trị giá lên đến 2.5 tỷ đồng, miễn phí cho trẻ em trong gói gia đình\u003c/p\u003e\n\u003cp\u003e\u0026nbsp;\u003c/p\u003e\n',
					'expiryDate': '30 Jun 2024',
					'favourite': 'true',
					'url': '/content/dam/techcombank/public-site/vi/documents/The-le-uu-dai-the-tin-dung-Techcombank-x-Lazada-e5d4b2e25b.pdf',
					'openInNewTab': 'false',
					'noFollow': 'false',
					'interactionType': 'other',
					'products': [
						'Bảo hiểm'
					],
					'partner': [
						'Techcombank'
					]
				},
				{
					'thumbnail': '/content/dam/techcombank/public-site/articles/non-blog/Techcare-Travel-thumbnail-0635ed110c-3109b18cf5.jpg.rendition/cq5dam.thumbnail.319.319.png',
					'description': '\u003cp\u003eHưởng quyền lợi y tế trên toàn thế giới trị giá lên đến 2.5 tỷ đồng, miễn phí cho trẻ em trong gói gia đình\u003c/p\u003e\n\u003cp\u003e\u0026nbsp;\u003c/p\u003e\n',
					'expiryDate': '30 Jun 2024',
					'favourite': 'false',
					'url': 'https://sample.com',
					'openInNewTab': 'true',
					'noFollow': 'false',
					'interactionType': 'other',
					'products': [
						'Bảo hiểm'
					],
					'partner': [
						'Techcombank'
					]
				},
				{
					'thumbnail': '/content/dam/techcombank/public-site/articles/non-blog/Techcare-Travel-thumbnail-0635ed110c-3109b18cf5.jpg.rendition/cq5dam.thumbnail.319.319.png',
					'description': '\u003cp\u003eHưởng quyền lợi y tế trên toàn thế giới trị giá lên đến 2.5 tỷ đồng, miễn phí cho trẻ em trong gói gia đình\u003c/p\u003e\n\u003cp\u003e\u0026nbsp;\u003c/p\u003e\n',
					'expiryDate': '30 Jun 2024',
					'favourite': 'false',
					'url': null,
					'openInNewTab': 'true',
					'noFollow': 'false',
					'interactionType': 'other',
					'products': [
						'Bảo hiểm'
					],
					'partner': [
						'Techcombank'
					]
				}
			]
		}
	});

	afterEach(() => {
		jest.useRealTimers();
	});

	it('should document is ready', () => {
		expect(document.readyState).toBe('complete');
	});

	describe('init GCSE Script', () => {
		beforeEach(() => {
			initGcseScript();
		});

		it('should init GCSE Script case did not exist cseScriptId', () => {
			const searchEngineId = 'b6e708782620e4010';
			const cseScriptId = 'cse-tcb-script';
			const gcseScriptElement = document.querySelector(
				`head script#${cseScriptId}[src='https://cse.google.com/cse.js?cx=${searchEngineId}']`);
			expect(gcseScriptElement).toBeTruthy();
		});

		it('should init GCSE Script case existed cseScriptId', () => {
			const cseScriptId = 'cse-tcb-script';
			const gcseScriptElement = document.getElementById(cseScriptId);
			expect(gcseScriptElement).toBeTruthy();
		});
	});

	describe('should store search history keywords on localStorage', () => {
		it('should get search history keywords', () => {
			component.addSearchHistoryKeyword('sample');
			const historyKeywords = getSearchHistoryKeywords();
			expect(historyKeywords).toEqual(['sample']);
		});

		it('should add search history keywords', () => {
			const listKeywords = ['sample 1', 'sample 2', 'sample 3', 'sample 4', 'sample 5', 'sample 6'];
			component.addSearchHistoryKeyword(listKeywords[0]);
			component.addSearchHistoryKeyword(listKeywords[1]);
			component.addSearchHistoryKeyword(listKeywords[2]);
			component.addSearchHistoryKeyword(listKeywords[3]);
			component.addSearchHistoryKeyword(listKeywords[4]);
			component.addSearchHistoryKeyword(listKeywords[5]);
			const historyKeywords = getSearchHistoryKeywords();
			expect(historyKeywords).toEqual(['sample 6', 'sample 5', 'sample 4', 'sample 3', 'sample 2']);
		});

		it('should remove search history keywords', () => {
			const listKeywords = ['sample 1', 'sample 2', 'sample 3', 'sample 4', 'sample 5'];
			component.addSearchHistoryKeyword(listKeywords[0]);
			component.addSearchHistoryKeyword(listKeywords[1]);
			component.addSearchHistoryKeyword(listKeywords[2]);
			component.addSearchHistoryKeyword(listKeywords[3]);
			component.addSearchHistoryKeyword(listKeywords[4]);
			removeSearchHistoryKeyword(listKeywords[0]);
			const historyKeywords = getSearchHistoryKeywords();
			expect(historyKeywords).toEqual(['sample 5', 'sample 4', 'sample 3', 'sample 2']);
		});
	});

	describe('search callbacks listener', () => {
		it('starting status case EN', () => {
			const spyRenderAdBannerElement = jest.spyOn(component, 'renderAdBannerElement');
			window.__gcse.searchCallbacks.web.starting();
			jest.advanceTimersByTime(1000);
			expect(spyRenderAdBannerElement).not.toHaveBeenCalled();
		});

		it('starting status case VI', () => {
			jest.replaceProperty(component, 'isEn', false);
			const spyRenderAdBannerElement = jest.spyOn(component, 'renderAdBannerElement');
			window.__gcse.searchCallbacks.web.starting();
			jest.advanceTimersByTime(1000);
			expect(spyRenderAdBannerElement).not.toHaveBeenCalled();
		});

		it('ready status case result not empty', () => {
			const result = [{ richSnippet: { metatags: { ogDescription: 'sample' } } }];
			const spyRenderAdBannerElement = jest.spyOn(component, 'renderAdBannerElement');
			window.__gcse.searchCallbacks.web.ready('', '', '', result);
			jest.advanceTimersByTime(1000);
			expect(spyRenderAdBannerElement).not.toHaveBeenCalled();
		});

		it('ready status case metatags null', () => {
			const result = [{ richSnippet: { metatags: null } }];
			const spyRenderAdBannerElement = jest.spyOn(component, 'renderAdBannerElement');
			window.__gcse.searchCallbacks.web.ready('', '', '', result);
			jest.advanceTimersByTime(1000);
			expect(spyRenderAdBannerElement).not.toHaveBeenCalled();
		});

		it('ready status case result is empty', () => {
			const spyRenderAdBannerElement = jest.spyOn(component, 'renderAdBannerElement');
			window.__gcse.searchCallbacks.web.ready('', '', '', []);
			jest.advanceTimersByTime(1000);
			expect(spyRenderAdBannerElement).not.toHaveBeenCalled()
		});

		it('rendered status case first load', () => {
			const spyDefineCustomDropdownElements = jest.spyOn(component, 'gsDefineCustomDropdownElements');
			window.__gcse.searchCallbacks.web.rendered('', '', '', '');
			jest.advanceTimersByTime(1000);
			expect(spyDefineCustomDropdownElements).toHaveBeenCalled();
		});

		it('rendered status case not first load', () => {
			const spyDefineCustomSearchResultContent = jest.spyOn(component, 'defineCustomSearchResultContent');
			window.__gcse.searchCallbacks.web.rendered('', '', '', '');
			jest.advanceTimersByTime(1000);
			expect(spyDefineCustomSearchResultContent).toHaveBeenCalled()
		});
	});

	describe('init Custom Search Input', () => {
		it('should init Custom Search Input case language is EN', () => {
			const spyInitCustomSearchInput = jest.spyOn(component, 'initCustomSearchInput');
			window.__gcse.initializationCallback();
			jest.advanceTimersByTime(1000);
			expect(spyInitCustomSearchInput).toBeCalled();
		});

		it('should init Custom Search Input case language is VI', () => {
			window.history.pushState({}, 'Test Page', `/${LANG_VI}/path`);
			const spyInitCustomSearchInput = jest.spyOn(component, 'initCustomSearchInput');
			window.__gcse.initializationCallback();
			jest.advanceTimersByTime(1000);
			expect(spyInitCustomSearchInput).toBeCalled();
		});

		it('should not set input placeholder when content have inserted prefix icon', () => {
			const input = searchResultWrapper.querySelector('table input');
			window.__gcse.initializationCallback();
			jest.advanceTimersByTime(1000);
			expect(input.placeholder).toBe('');
		});

		it('should set input placeholder when content did not contain inserted prefix icon', () => {
			const input = searchResultWrapper.querySelector('table input');
			const insertedPrefixIcon = searchResultWrapper.querySelector('td.gsc-input-prefix-icon');
			insertedPrefixIcon.remove();
			window.__gcse.initializationCallback();
			jest.advanceTimersByTime(1000);
			expect(input.placeholder).toBe('sample');
		});

		describe('should listen event', () => {
			let dropdownWrapper;
			let dropdownWrapperClassList;
			let dropdownItem;

			beforeEach(() => {
				window.__gcse.initializationCallback();
				jest.advanceTimersByTime(1000);
				dropdownWrapper = searchResultWrapper.querySelector('.dropdown__wrapper');
				dropdownWrapperClassList = dropdownWrapper.classList;
				dropdownItem = dropdownWrapper.querySelector('.dropdown__content ul li');
			});

			it('when click dropdown button', () => {
				const dropdownButton = dropdownWrapper.querySelector('.dropdown__button');
				dropdownButton.dispatchEvent(new Event('click'));
				expect(dropdownWrapperClassList).toContain('dropdown__wrapper');
			});

			it('when click dropdown menu', () => {
				dropdownItem.dispatchEvent(new Event('click'));
				expect(dropdownWrapperClassList).toContain('dropdown__wrapper');
			});

			it('should close dropdown when click outside', () => {
				dropdownItem.dispatchEvent(new Event('click'));
				document.body.dispatchEvent(new Event('click'));
				expect(dropdownWrapperClassList).not.toContain('expanded');
			});

			it('should keep expand dropdown when click inside', () => {
				dropdownItem.dispatchEvent(new Event('click'));
				$('.dropdown__button').trigger('click');
				expect(dropdownWrapperClassList).not.toContain('expanded');
			});
		});
	});

	describe('render ad-banner', () => {
		beforeEach(() => {
			jest.replaceProperty(component, 'googleInit', { resultsData: ['1', '2', '3', '4'] });
		});

		it('should handle when ad-banner have undefined data', async () => {
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve({ data: {} }));
			await component.getAdsBannerData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should handle when ad-banner have no data', async () => {
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve(null));
			await component.getAdsBannerData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should handle when ad-banner response data undefined', async () => {
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.reject(''));
			await component.getAdsBannerData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render ad-banner case desktop', async () => {
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve({ data: AD_BANNER_DATA }));
			await component.getAdsBannerData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render ad-banner case mobile', async () => {
			window.innerWidth = 1023;
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve({ data: AD_BANNER_DATA }));
			await component.getAdsBannerData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render ad-banner case externalURL', async () => {
			delete AD_BANNER_DATA.externalUrl;
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve({ data: AD_BANNER_DATA }));
			await component.getAdsBannerData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render ad-banner when response data available', () => {
			jest.replaceProperty(component, 'adsBannerData', AD_BANNER_DATA);
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			const wrapperClassList = wrapper.classList;
			component.defineCustomSearchResultContent();
			expect(wrapperClassList).toContain('gsc-wrapper');
		});

		it('should remove ad-banner case desktop', () => {
			window.innerWidth = 1025;
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			wrapper.remove();
			component.removeAdBanner();
			expect(wrapper).not.toBeUndefined();
		});

		it('should remove ad-banner case mobile', () => {
			window.innerWidth = 1023;
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			wrapper.remove();
			component.gsDefineAdBannerElements();
			expect(wrapper).not.toBeUndefined();
		});
	});

	describe('render promotions list', () => {
		it('should render promotions list case failed', async () => {
			const gscInput = searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
			gscInput.setAttribute('value', 'sample');
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.reject(''));
			await component.getPromotionListData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render promotions list case success', async () => {
			const gscInput = searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
			gscInput.setAttribute('value', 'sample');
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve(PROMOTION_LIST));
			await component.getPromotionListData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render promotions list case success vi', async () => {
			jest.replaceProperty(component, 'isEn', false);
			const gscInput = searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
			gscInput.setAttribute('value', 'sample');
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.resolve(PROMOTION_LIST));
			await component.getPromotionListData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render promotions list case failed vi', async () => {
			jest.replaceProperty(component, 'isEn', false);
			const gscInput = searchResultWrapper.querySelector(`input[name='search'].gsc-input`);
			gscInput.setAttribute('value', 'sample');
			const spyFetchData = jest.spyOn(component, 'fetchData');
			spyFetchData.mockReturnValue(Promise.reject(''));
			await component.getPromotionListData();
			expect(spyFetchData).toHaveBeenCalled();
		});

		it('should render promotion when select promotion refinement', () => {
			jest.replaceProperty(component, 'selectedRefinement', 'Promotions');
			jest.replaceProperty(component, 'promotionListData', PROMOTION_LIST);
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			const wrapperClassList = wrapper.classList;
			component.defineCustomSearchResultContent();
			expect(wrapperClassList).toContain('gsc-promotion-list');
		});

		it('should render promotion when select promotion refinement', () => {
			jest.replaceProperty(component, 'selectedRefinement', 'Promotions');
			jest.replaceProperty(component, 'promotionListData', null);
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			const wrapperClassList = wrapper.classList;
			component.defineCustomSearchResultContent();
			expect(wrapperClassList).toContain('gsc-promotion-list');
		});

		it('should render expired label', () => {
			const expiredElement = component.renderExpiredLabelElement();
			expect(expiredElement).not.toBeUndefined();
		});

		it('should render expired label case VI', () => {
			jest.replaceProperty(component, 'isEn', false);
			const expiredElement = component.renderExpiredLabelElement();
			expect(expiredElement).not.toBeUndefined();
		});

		it('should remove promotions list', () => {
			jest.replaceProperty(component, 'promotionListData', PROMOTION_LIST);
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			component.gsDefinePromotionListElements();
			component.removePromotionList();
			expect(wrapper.classList).not.toContain('gsc-promotion-list');
		});

		it('should not update expired timer when promotion element is empty', () => {
			const spyUpdateExpiredProgressBar = jest.spyOn(component, 'updateExpiredProgressBar');
			component.updateExpiredTimer(null);
			expect(spyUpdateExpiredProgressBar).not.toHaveBeenCalled();
		});

		it('should handle render promotion when total page is undefined', () => {
			component.renderPromotionListElement(PROMOTION_LIST, 20, -10);
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			expect(wrapper.classList).not.toContain('gsc-promotion-list');
		});

		it('should handle render promotion when total page is not undefined', () => {
			component.renderPromotionListElement(PROMOTION_LIST, 24, 12);
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			expect(wrapper.classList).not.toContain('gsc-promotion-list');
		});

		it('should render promotion expired element case vi', () => {
			jest.replaceProperty(component, 'lang', 'vi');
			component.renderPromotionExpiredElement('30 Jun 2024');
			const wrapper = searchResultWrapper.querySelector('.gsc-wrapper');
			expect(wrapper.classList).not.toContain('gsc-promotion-list');
		});
	});

	it(`should use default language when can't detect current lang`, () => {
		jest.replaceProperty(component, 'lang', null);
		const gcseSearchElement = searchResultWrapper.querySelector('.gcse-search');
		component.setSearchResultLang();
		const searchResultLang = gcseSearchElement.getAttribute('data-lr');
		expect(searchResultLang).toEqual(`lang_${LANG_VI}`);
	});

	it('should fetch data when empty selectedLanding', () => {
		jest.replaceProperty(component, 'selectedLanding', null);
		const spyFetch = jest.spyOn(component, 'fetchData');
		window.__gcse.searchCallbacks.web.rendered('', '', '', '');
		expect(spyFetch).toHaveBeenCalled();
	});

	it('should clear search result data', () => {
		const spyRemoveAdBanner = jest.spyOn(component, 'removeAdBanner');
		component.clearAllConfiguration();
		expect(spyRemoveAdBanner).toHaveBeenCalled();
	});

	it('should filter refinement tab', () => {
		jest.replaceProperty(component, 'refinementTabs', REFINEMENT_TABS);
		component.googleInit.refinementBlockTabs = document.querySelectorAll('.refinementBlockTabs');
		const spyToggleResultBox = jest.spyOn(component, 'toggleResultBox');
		component.filterTabByLocale();
		jest.advanceTimersByTime(1000);
		expect(spyToggleResultBox).toHaveBeenCalled();
	});

	it('should set google init', () => {
		const root = searchResultWrapper.querySelector('.gsc-control-cse');
		root.remove();
		component.gsDefineElements();
		expect(component.googleInit).not.toBeUndefined();
	});

	it('should get desktop ad banner case null', () => {
		const desktopAdBanner = component.getDesktopAdBanner(null);
		expect(desktopAdBanner).toBeNull();
	});

	it('should get mobile ad banner case null', () => {
		const mobileAdBanner = component.getMobileAdBanner(null);
		expect(mobileAdBanner).toBeNull();
	});

	it('should return when offset is null', () => {
		component.renderPromotionPagination(-1, -1, 1);
		const spyFetch = jest.spyOn(component, 'fetchData');
		expect(spyFetch).not.toBeCalled();
	});
});