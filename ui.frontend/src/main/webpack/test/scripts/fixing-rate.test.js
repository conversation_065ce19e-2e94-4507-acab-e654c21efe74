import { describe, expect, test, jest } from "@jest/globals";

class TestFixingRate {
  constructor() {
    document.body.innerHTML = `
    <div class="fixing-rate" data-url="test">
        <div class="time-select"></div>
        <div class="table-content-container"></div>
        <div class="exchange-rate__empty-label"></div>
        <div class="exchange-rate-table-content"></div>
        <div class="calendar__input-field"></div>
        <div class="calendar_real_estate"></div>
    </div>
    
    `;
    require("../../site/scripts/fixing-rate");
  }
}

describe("test fixing rate", () => {
  test("should document ready", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
      return { articleFilter: "sample" };
    });
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        attr: () => {
          return "test";
        },
        data: jest.fn(),
        on: (event, selector, callback) => {
          if (callback) {
            callback({ currentTarget: "test" });
          } else {
            selector({ currentTarget: "test" });
          }
        },
        find: () => {
          return {
            each: (callback) => callback(),
            on: (event, callback) => callback(),
            removeClass: jest.fn(),
            addClass: jest.fn(),
            changeOptions: jest.fn(),
          };
        },
        text: jest.fn(),
        next: () => {
          return {
            css: jest.fn(),
            hasClass: jest.fn(),
            removeClass: jest.fn(),
            addClass: jest.fn(),
          };
        },
        outerHeight: jest.fn(),
        empty: jest.fn(),
        append: jest.fn(),
      };
    });

    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({
            fixingRate: {
              data: [{ test: "test" }],
              updatedTimes: "01/01/2024",
            },
          });
        }
        if (typeof callback === "function") {
          error({ error: "test" });
        }
      });
    new TestFixingRate();
    expect(document.readyState).toBe("complete");
  });
});
