import { describe, expect, jest, test } from '@jest/globals';

class TestModal {
  constructor() {
    document.body.innerHTML = '';
    document.body.innerHTML = `
      <a href="#sample"></a>
      <div id="sample" class="popup">
        <iframe src="https://www.youtube.com/embed/M5xVGLzVKA0"></iframe>
      </div>
    `;
    require('../../site/scripts/modal');
  }
}

describe('Modal', () => {
  test('should create an instance', () => {
    const divElement = document.createElement('div');
    divElement.id = 'sample';
    divElement.classList.add('popup');

    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      if (callback === document) {
        return {
          on: (event, param, callback) => callback({
            preventDefault: () => {},
          }),
        };
      }

      return {
        ready: callback => callback(),
        each: callback => callback(),
        on: (event, callback) => callback(),
        hide: () => {},
        closest: () => {
          return {
            length: 0,
          };
        },
        parents: () => {
          return {
            css: () => 'flex',
            hide: () => {},
          };
        },
        attr: () => {
          return {
            slice: () => 'sample',
          };
        },
        find: () => {
          return {
            attr: () => {},
          };
        },
      };
    });

    window.addEventListener = jest.fn().mockImplementation(({}, callback) => callback({
      target: document.querySelector('div.popup'),
    }));

    new TestModal();
    window.dispatchEvent(new Event('click'));
    expect(document.readyState).toBe('complete');

    const modal = document.getElementById('sample');
    const iframe = modal.querySelector('iframe');
    modal.dispatchEvent(new Event('click'));
    expect(iframe.src).toEqual('https://www.youtube.com/embed/M5xVGLzVKA0');
  });
});
