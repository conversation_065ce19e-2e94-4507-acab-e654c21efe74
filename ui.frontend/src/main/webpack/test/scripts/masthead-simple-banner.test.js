import { describe, expect, jest, test } from '@jest/globals';

class TestMastheadSimpleBanner {
  constructor() {
    document.body.innerHTML = `
        <button class="tcb-hero-banner_looking-for-wrapper" name>
            <input name="search-document" value=""/>    
        </button>
        <div class="looking-for_submit"></div>
        <div class="looking-for_input-options-roots"></div>
        <div class="looking-for_input-options-content"></div>
`;
    require('../../site/scripts/masthead-simple-banner');
  }
}

describe('MastheadSimpleBanner', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        width: jest.fn().mockReturnValue(200),
        find: () => {
          return {
            length: null,
            addClass: () => {},
            0: {
              scrollIntoView: () => {},
            },
          };
        },
      };
    });

    new TestMastheadSimpleBanner();
    const searchDocument = document.querySelector('.tcb-hero-banner_looking-for-wrapper [name="search-document"]');
    searchDocument.dispatchEvent(new Event('focus'));
    searchDocument.dispatchEvent(new Event('keyup'));
    searchDocument.dispatchEvent(new Event('blur'));
    expect(document.readyState).toBe('complete');
  });
});
