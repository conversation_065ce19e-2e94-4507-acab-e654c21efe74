import { describe, expect, test, jest } from "@jest/globals";
import { TcbAutoEarning } from "../../site/scripts/tcb-auto-earning";

class TcbAutoEarningTest extends TcbAutoEarning {
  constructor() {
    super();
  }
  maxAmount = 500_000_000_000;

  querySelector(selector) {
    if (
      [
        "#initialAmount",
        "#monthlyIncome",
        "#monthlySpending",
        "#spendingEstimates",
        "#dailySpending",
        "#dailyIncome",
        ".dropdown__wrapper",
        ".auto-earning-calculator .cta-button--dark .cta-button",
        'input[name="auto-earning-daily-income"]:checked',
        ".auto-earning-component",
        ".try-again-button a.cta-button",
        "details",
        ".result-container",
        ".auto-earning-input",
        ".personalFormInput",
        ".merchantFormInput",
        ".auto-earning-result",
        ".error-message",
        "input",
        "span.auto-profit-value",
        "span.demand-profit-value",
        ".minHoldingDay",
      ].includes(selector)
    ) {
      return {
        value: "abc",
        querySelector: () => {
          return {
            value: "test",
            querySelector: () => {
              return {
                value: "test",
                appendChild: jest.fn(),
                addEventListener: (event, callback) =>
                  callback({
                    target: {
                      classList: {},
                      parentElement: {},
                    },
                  }),
              };
            },
            querySelectorAll: () => {
              return [
                {
                  value: "test",
                  forEach: () => {
                    return {
                      value: "test",
                    };
                  },
                  addEventListener: (event, callback) =>
                    callback({
                      which: true,
                      preventDefault: jest.fn(),
                      target: { value: "test" },
                    }),
                  classList: {
                    remove: jest.fn(),
                    add: jest.fn(),
                    contains: jest.fn(),
                  },
                },
              ];
            },
            addEventListener: (event, callback) => {
              if (event === "click" && typeof callback === "function") {
                callback({});
              }
            },
            classList: {
              toggle: jest.fn(),
              remove: jest.fn(),
              add: jest.fn(),
              contains: jest.fn(),
            },
            removeAttribute: jest.fn(),
            setAttribute: jest.fn(),
          };
        },
        classList: {
          add: jest.fn(),
          remove: jest.fn(),
          contains: jest.fn(),
        },
        addEventListener: (event, callback) => {
          if (event === "click" && typeof callback === "function") {
            callback({});
          }
        },
        getAttribute: jest.fn(),
        setAttribute: jest.fn(),
        closest: () => {
          return {
            value: "abc",
            classList: {
              remove: jest.fn(),
              add: jest.fn(),
              contains: jest.fn(),
            },
          };
        },
        removeAttribute: jest.fn(),
      };
    }
  }

  querySelectorAll(selector) {
    return [
      {
        setAttribute: jest.fn(),
        addEventListener: (event, callback) =>
          callback({
            which: true,
            preventDefault: jest.fn(),
            target: { value: "", selectionStart: "1", selectionEnd: "1" },
            key: 3,
          }),
        querySelector: () => {
          return {
            innerHTML: {
              trim: jest.fn(),
            },
          };
        },
        classList: {
          contains: jest.fn().mockReturnValue(true),
        },
      },
    ];
  }

  getElementsByTagName() {
    return {
      0: {
        textContent: "test",
      },
      1: {
        textContent: "test",
      },
    };
  }
}

customElements.define("tcb-auto-earning", TcbAutoEarningTest);
describe("Tcb Auto Earning", () => {
  beforeEach(() => {
    document.body.addEventListener = (event, callback) =>
      callback({
        target: {
          classList: {
            contains: jest.fn(),
          },
          parentElement: {
            classList: { contains: jest.fn() },
          },
        },
      });
    document.documentElement.lang = "en";
    document.getElementsByName = jest.fn().mockReturnValue([
      {
        checked: true,
        value: "test",
        addEventListener: (event, callback) =>
          callback({ target: { value: "test" } }),
      },
    ]);

    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({
            status: true,
            msg: "Success",
            errors: [],
            data: {
              autoEarningProfit: 0,
              demandProfit: 20547534,
            },
          });
        }
        if (typeof callback === "function") {
          error({});
        }
      });
  });
  test("should document is ready", () => {
    let tcbAutoEarning = new TcbAutoEarningTest();
    expect(tcbAutoEarning).toBeTruthy();
  });
});
