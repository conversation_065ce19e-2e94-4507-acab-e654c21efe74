import { describe, expect, jest, test } from '@jest/globals';

class TestScrollMarginTop {
  constructor() {
    document.body.innerHTML = `
      <a href="#id1"></a>
      <a href="#id2"></a>
      <a href="#id3"></a>
      <div id="id1"></div>
      <div id="id2"></div>
      <div id="id3"></div>
    `;
    require('../../site/scripts/scroll-margin-top');
  }
}

describe('ScrollMarginTop', () => {
  test('should document ready', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
      };
    });
    new TestScrollMarginTop();
    const elementA = document.querySelector('div#id1');
    const elementB = document.querySelector('div#id2');
    const elementC = document.querySelector('div#id3');
    expect(elementA.classList).toContain('scroll-margin-top-header');
    expect(elementB.classList).toContain('scroll-margin-top-header');
    expect(elementC.classList).toContain('scroll-margin-top-header');
    expect(document.readyState).toBe('complete');
  });
});
