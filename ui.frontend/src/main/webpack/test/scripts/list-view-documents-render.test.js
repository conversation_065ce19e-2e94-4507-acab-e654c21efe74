import { describe, expect, test, jest } from "@jest/globals";

class TestListViewDocumentsRender {
  constructor() {
    document.body.innerHTML = `
    <div class="mySlides"></div>
    `;
    require("../../site/scripts/list-view-documents-render");
  }
}

jest.mock("moment", () => {
  return () => jest.requireActual("moment")("2020-01-01T00:00:00.000Z");
});

describe("test list view documents render", () => {
  test("should document ready", () => {
    window.jQuery = jest.fn().mockImplementation((callback) => {
      if (typeof callback === "function") {
        callback();
      }
      return {};
    });
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === "function") {
        callback();
      }
      return {
        each: (callback) => callback(),
        find: () => {
          return {
            find: () => {
              return {
                append: jest.fn(),
                find: () => {
                  return {
                    eq: () => {
                      return {
                        prop: jest.fn(),
                      };
                    },
                  };
                },
                map: jest
                  .fn()
                  .mockReturnValue({ get: jest.fn().mockReturnValue([{}]) }),
                text: jest.fn(),
                on: (event, callback) => callback(),
                remove: jest.fn(),
              };
            },
            each: (callback) => callback(),
            map: jest
              .fn()
              .mockReturnValue({ get: jest.fn().mockReturnValue([{}]) }),
            html: jest.fn(),
            clone: () => {
              return {
                removeClass: jest.fn(),
                find: () => {
                  return {
                    text: jest.fn(),
                    find: () => {
                      return {
                        clone: () => {
                          return {
                            addClass: jest.fn(),
                            removeClass: jest.fn(),
                            find: () => {
                              return {
                                text: jest.fn(),
                                removeClass: jest.fn(),
                                addClass: jest.fn(),
                                find: () => {
                                  return {
                                    attr: jest.fn(),
                                    eq: () => {
                                      return {
                                        prop: jest.fn(),
                                      };
                                    },
                                    prop: jest.fn(),
                                    text: jest.fn(),
                                  };
                                },
                              };
                            },
                          };
                        },
                      };
                    },
                    append: jest.fn(),
                  };
                },
              };
            },
            remove: jest.fn(),
          };
        },
        attr: jest.fn(),
        data: jest.fn().mockReturnValue("test/"),
        text: jest.fn(),
        addClass: jest.fn(),
        prepend: jest.fn(),
      };
    });

    window.$.get = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({
            data: {
              listViewDocumentFragmentList: {
                items: [
                  {
                    categoryTitle: {
                      plaintext: "plaintext",
                    },
                    documentItems: [
                      {
                        length: 1,
                        documentTitle: {
                          plaintext: "plaintext",
                        },
                        linkIcon: {
                          _path: "path",
                        },
                        linkLabel: "linkLabel",
                        externalDocumentPath: "externalDocumentPath",
                      },
                    ],
                    category: "test",
                  },
                ],
              },
            },
          });
        }
        if (typeof callback === "function") {
          error({ error: "test" });
        }
      });
    new TestListViewDocumentsRender();
    expect(document.readyState).toBe("complete");
  });
});
