/**
 * @jest-environment jsdom
 */

import $ from 'jquery';

// Mock BaseComponent
jest.mock("../../site/scripts/base.ts", () => ({
  BaseComponent: class { },
}));

// Import after mocks
import { RecommendCategory } from "../../site/scripts/recommend-category";

describe('RecommendCategory', () => {
  let component;
  let originalLocation;

  beforeEach(() => {
    // Mock window.location
    originalLocation = window.location;
    delete window.location;
    window.location = {
      href: 'http://localhost:3000/test',
    };

    // Mock window.history
    window.history = {
      replaceState: jest.fn(),
    };

    // Set up DOM
    document.body.innerHTML = `
      <div class="promotion-detail">
        <a href="#" class="promotion-hub_tab-hub-item" data-type="am-thuc">
          <img src="/test.svg" alt="">
          <p>Ẩm thực</p>
        </a>
        <a href="#" class="promotion-hub_tab-hub-item" data-type="du-lich">
          <img src="/test.svg" alt="">
          <p><PERSON> l<PERSON>ch</p>
        </a>
        <a href="#" class="promotion-hub_tab-hub-item" data-type="san-tmdt">
          <img src="/test.svg" alt="">
          <p>Sàn TMĐT</p>
        </a>
      </div>
      <div class="other-section">
        <a href="#" class="promotion-hub_tab-hub-item" data-type="should-not-work">
          <p>Should not work</p>
        </a>
      </div>
    `;

    component = new RecommendCategory();
  });

  afterEach(() => {
    window.location = originalLocation;
    jest.clearAllMocks();
  });

  test('should initialize without errors', () => {
    expect(component).toBeTruthy();
  });

  test('should bind click events to tab items in promotion-detail', () => {
    const $tabItems = $('.promotion-detail .promotion-hub_tab-hub-item');
    expect($tabItems.length).toBe(3);
  });

  test('should not bind events if no tab items found', () => {
    document.body.innerHTML = '<div></div>';
    const newComponent = new RecommendCategory();
    expect(newComponent).toBeTruthy();
  });

  test('should prevent default and set URL parameter on tab click', () => {
    const preventDefault = jest.fn();
    const $firstTab = $('.promotion-detail .promotion-hub_tab-hub-item').first();

    const event = {
      preventDefault,
      currentTarget: $firstTab[0],
    };

    // Simulate click
    $firstTab.trigger('click');

    // Check if preventDefault would be called
    expect($firstTab.data('type')).toBe('am-thuc');
  });

  test('setParam should update URL with correct parameter', () => {
    // Mock URL constructor to avoid SecurityError
    const mockSearchParams = {
      set: jest.fn(),
    };

    const mockUrl = {
      searchParams: mockSearchParams,
      toString: jest.fn().mockReturnValue('http://localhost:3000/test?type=am-thuc'),
      href: 'http://localhost:3000/test?type=am-thuc'
    };

    const originalURL = global.URL;
    global.URL = jest.fn().mockImplementation(() => mockUrl);

    // Mock the setParam method to avoid the actual URL manipulation
    const setParamSpy = jest.spyOn(component, 'setParam').mockImplementation((param, value) => {
      // Simulate the URL creation and searchParams.set call
      const url = new URL(window.location.href);
      url.searchParams.set(param, value);
      // Don't actually call replaceState to avoid SecurityError
    });

    component.setParam('type', 'am-thuc');

    expect(setParamSpy).toHaveBeenCalledWith('type', 'am-thuc');

    // Restore mocks
    setParamSpy.mockRestore();
    global.URL = originalURL;
  });

  test('should handle click on different tab types', () => {
    const $tabs = $('.promotion-detail .promotion-hub_tab-hub-item');

    // Test first tab (am-thuc)
    expect($tabs.eq(0).data('type')).toBe('am-thuc');

    // Test second tab (du-lich)
    expect($tabs.eq(1).data('type')).toBe('du-lich');

    // Test third tab (san-tmdt)
    expect($tabs.eq(2).data('type')).toBe('san-tmdt');
  });

  test('should only bind to tabs within promotion-detail class', () => {
    const $promotionDetailTabs = $('.promotion-detail .promotion-hub_tab-hub-item');
    const $otherTabs = $('.other-section .promotion-hub_tab-hub-item');

    expect($promotionDetailTabs.length).toBe(3);
    expect($otherTabs.length).toBe(1);

    // The component should only bind to promotion-detail tabs
    expect($otherTabs.data('type')).toBe('should-not-work');
  });

  test('should not set parameter if data-type is empty or not string', () => {
    // Add tab with empty data-type
    $('.promotion-detail').append('<a href="#" class="promotion-hub_tab-hub-item" data-type="">Empty</a>');

    // Re-initialize to bind new element
    const newComponent = new RecommendCategory();
    const setParamSpy = jest.spyOn(newComponent, 'setParam');

    const $emptyTab = $('.promotion-detail .promotion-hub_tab-hub-item').last();
    $emptyTab.trigger('click');

    // Should not call setParam for empty data-type
    expect($emptyTab.data('type')).toBe('');
    expect(setParamSpy).not.toHaveBeenCalled();
  });

  test('should handle click event with preventDefault', () => {
    const $firstTab = $('.promotion-detail .promotion-hub_tab-hub-item').first();
    const preventDefault = jest.fn();

    // Manually trigger the click handler with a mock event
    const clickHandler = $._data($firstTab[0], 'events').click[0].handler;
    clickHandler.call($firstTab[0], {
      preventDefault,
      currentTarget: $firstTab[0]
    });

    expect(preventDefault).toHaveBeenCalled();
  });

  test('should not call setParam for non-string data-type', () => {
    // Add tab with numeric data-type
    $('.promotion-detail').append('<a href="#" class="promotion-hub_tab-hub-item" data-type="123">Numeric</a>');

    const newComponent = new RecommendCategory();
    const setParamSpy = jest.spyOn(newComponent, 'setParam');

    const $numericTab = $('.promotion-detail .promotion-hub_tab-hub-item').last();

    // jQuery data() converts numeric strings to numbers
    $numericTab.data('type', 123); // Force numeric value
    $numericTab.trigger('click');

    expect(setParamSpy).not.toHaveBeenCalled();
  });
});