import $ from 'jquery';

// Mock getQueryParam
jest.mock('../../site/scripts/utils/params.util', () => ({
  getQueryParam: jest.fn(),
}));

// Mock BaseComponent
jest.mock('../../site/scripts/base.ts', () => ({
  BaseComponent: class { },
}));

// Mock slick-carousel
jest.mock('slick-carousel', () => ({}));

import { getQueryParam } from '../../site/scripts/utils/params.util';
import { CardTypeFilterComponent } from '../../site/scripts/credit-card-hub';

describe('CardTypeFilterComponent', () => {
  beforeEach(() => {
    document.body.innerHTML = `
      <tcb-credit-card-hub>
        <div class="promotion-hub_group-credit-card">
          <div data-promotion-hub-card-type="the-tin-dung,am-thuc" class="promotion-hub_credit-card"></div>
          <div data-promotion-hub-card-type="the-thanh-toan,du-lich" class="promotion-hub_credit-card"></div>
          <div data-promotion-hub-card-type="the-tin-dung" class="promotion-hub_credit-card"></div>
          <div data-promotion-hub-card-type="" class="promotion-hub_credit-card empty"></div>
          <div class="promotion-hub_credit-card no-type"></div>
        </div>
      </tcb-credit-card-hub>
    `;

    // Mock jQuery methods
    $.fn.slick = jest.fn();
    $.fn.hasClass = jest.fn();
    $.fn.hide = jest.fn();
    $.fn.show = jest.fn();
    $.fn.filter = jest.fn().mockReturnThis();
    $.fn.is = jest.fn().mockReturnValue(true);

    // Mock window object
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should do nothing if no query param', () => {
    getQueryParam.mockReturnValue(null);

    const filter = new CardTypeFilterComponent();

    // expect no DOM change
    const cards = $('[data-promotion-hub-card-type]');
    expect(cards.length).toBe(4);
  });

  test('should filter by card-types parameter only', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return 'the-tin-dung';
      if (param === 'types') return null;
      return null;
    });

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    new CardTypeFilterComponent();

    expect(cssSpy).toHaveBeenCalledWith('display', 'flex');
    expect(removeSpy).toHaveBeenCalled();
  });

  test('should filter by types parameter only', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return null;
      if (param === 'types') return 'am-thuc';
      return null;
    });

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    new CardTypeFilterComponent();

    expect(cssSpy).toHaveBeenCalledWith('display', 'flex');
    expect(removeSpy).toHaveBeenCalled();
  });

  test('should filter by both card-types and types parameters (AND logic)', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return 'the-tin-dung';
      if (param === 'types') return 'am-thuc';
      return null;
    });

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    new CardTypeFilterComponent();

    // Should match card with data-promotion-hub-card-type="the-tin-dung,am-thuc"
    expect(cssSpy).toHaveBeenCalledWith('display', 'flex');
    expect(removeSpy).toHaveBeenCalled();
  });

  test('should remove cards when both parameters required but only one matches', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return 'the-tin-dung';
      if (param === 'types') return 'du-lich';
      return null;
    });

    const removeSpy = jest.spyOn($.fn, 'remove');

    new CardTypeFilterComponent();

    // Should remove cards that don't match both criteria
    expect(removeSpy).toHaveBeenCalled();
  });

  test('should remove cards with no data attribute or empty type', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return 'the-tin-dung';
      if (param === 'types') return null;
      return null;
    });

    const removeSpy = jest.spyOn($.fn, 'remove');

    new CardTypeFilterComponent();

    // Cards with empty or missing data-promotion-hub-card-type should be removed
    expect(removeSpy).toHaveBeenCalled();
  });

  test('should initialize carousel on mobile screen size', () => {
    getQueryParam.mockImplementation(() => null);

    // Mock mobile screen size
    Object.defineProperty(window, 'innerWidth', {
      value: 767,
      writable: true
    });

    // Mock jQuery width method
    $.fn.width = jest.fn().mockReturnValue(767);
    $(window).width = jest.fn().mockReturnValue(767);
    $.fn.hasClass = jest.fn().mockReturnValue(false);

    new CardTypeFilterComponent();

    expect($.fn.slick).toHaveBeenCalledWith({
      slidesToShow: 1,
      slidesToScroll: 1,
      dots: true,
      arrows: false,
      infinite: false,
      mobileFirst: true,
      adaptiveHeight: true,
      responsive: [
        {
          breakpoint: 768,
          settings: 'unslick'
        }
      ]
    });
  });

  test('should not initialize carousel on desktop screen size', () => {
    getQueryParam.mockImplementation(() => null);

    // Mock desktop screen size
    Object.defineProperty(window, 'innerWidth', {
      value: 1024,
      writable: true
    });

    // Mock jQuery width method
    $.fn.width = jest.fn().mockReturnValue(1024);
    $(window).width = jest.fn().mockReturnValue(1024);
    $.fn.hasClass = jest.fn().mockReturnValue(false);

    new CardTypeFilterComponent();

    expect($.fn.slick).not.toHaveBeenCalled();
  });

  test('should destroy carousel when resizing from mobile to desktop', () => {
    getQueryParam.mockImplementation(() => null);

    // Mock desktop screen size
    $(window).width = jest.fn().mockReturnValue(1024);
    $.fn.hasClass = jest.fn().mockReturnValue(true); // carousel is initialized

    const component = new CardTypeFilterComponent();

    // Trigger resize event manually
    component.configCarousel();

    expect($.fn.slick).toHaveBeenCalledWith('unslick');
  });

  test('should hide tcb-credit-card-hub when no cards are visible', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return 'nonexistent-type';
      if (param === 'types') return null;
      return null;
    });

    // Mock that no cards are visible
    $.fn.filter = jest.fn().mockReturnValue({ length: 0 });

    new CardTypeFilterComponent();

    expect($.fn.hide).toHaveBeenCalled();
  });

  test('should show tcb-credit-card-hub when cards are visible', () => {
    getQueryParam.mockImplementation(() => null);

    // Mock that cards are visible
    $.fn.filter = jest.fn().mockReturnValue({ length: 2 });

    new CardTypeFilterComponent();

    expect($.fn.show).toHaveBeenCalled();
  });

  test('should hide hub when all cards are filtered out', () => {
    getQueryParam.mockImplementation((param) => {
      if (param === 'card-types') return 'nonmatching-filter';
      if (param === 'types') return null;
      return null;
    });

    // Mock that all cards get removed/hidden
    $.fn.filter = jest.fn().mockReturnValue({ length: 0 });

    new CardTypeFilterComponent();

    expect($.fn.hide).toHaveBeenCalled();
  });
});
