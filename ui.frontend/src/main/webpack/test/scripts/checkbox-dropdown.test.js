import { describe, expect, test, jest } from "@jest/globals";

window.TcbDropdown = class TestTcbDropdown {
  constructor() {}

  setData([]) {}

  clearValue() {}
};

class TestCheckboxDropdown {
  constructor() {
    document.body.innerHTML = `
        <div>
            <li>
                <span></span>
            </li>
        </div>
        <div class="dropdown-wrapper city-list">
          <input>
        </div>
        <div class="dropdown-wrapper district-list"></div>
        <div class="dropdown-wrapper branch-list"></div>
        <div class="dropdown-wrapper"></div>
    `;

    require("../../site/scripts/checkbox-dropdown");
  }
}

describe("test checkbox dropdown", () => {
  test("should document ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        on: (param, callback) => {
          if (param === "select") {
            return {
              handleSelectItem1: (callback) => callback(),
              handleSelectItem2: (callback) => callback(),
              handleSelectItem3: (callback) => callback(),
            };
          }

          if (param === "input") {
            callback({ target: { value: "test" } });
          }
        },
      };
    });

    window.$.ajax = jest
      .fn()
      .mockImplementation(({ url, type, dataType, success, error }) => {
        if (typeof success === "function") {
          success({
            data: {
              branchFragmentList: {
                items: [
                  {
                    cityCode: "10",
                    cityName: "test",
                    districtCode: "10",
                    districtName: "test",
                  },
                  {
                    cityCode: "10",
                    cityName: "test",
                    districtCode: "10",
                    districtName: "test",
                  },
                ],
              },
            },
          });
        }
        if (typeof callback === "function") {
          error({ error: "test" });
        }
      });

    new TestCheckboxDropdown();
    expect(document.readyState).toBe("complete");
  });
});
