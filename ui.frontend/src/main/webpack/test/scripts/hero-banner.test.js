import { describe, expect, jest, test } from '@jest/globals';

class TestHeroBanner {
  constructor() {
    document.body.innerHTML = `
        <div class="page-menu"></div>
        <div class="tcb-hero-banner_control--next"></div>
    `;
    require('../../site/scripts/hero-banner');
  }
}

describe('HeroBanner', () => {
  test('should create an instance', () => {
    window.innerWidth = 20;
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: callback => callback(),
        on: (param, callback) => callback(),
        hasClass: () => true,
        addClass: () => {},
        querySelector: () => {
          return {
            scrollWidth: 10,
            style: {
              display: 'block',
            },
            addEventListener: (param, callback) => callback(),
            querySelector: () => {},
            querySelectorAll: () => [],
          };
        },
      };
    });

    new TestHeroBanner();
    new HeroBanner($('div'));
    expect(document.readyState).toBe('complete');
  });
});
