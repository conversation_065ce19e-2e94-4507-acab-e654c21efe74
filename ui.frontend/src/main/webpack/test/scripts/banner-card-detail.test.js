import { slickCardBtn } from '../../site/scripts/banner-card-detail';
import $ from 'jquery';

jest.mock('slick-carousel', () => jest.fn());

describe('slickCardBtn', () => {
  beforeEach(() => {
    document.body.innerHTML = `
      <div class="slick-text" data-autoplay-speed="2000"></div>
    `;
    $.fn.slick = jest.fn();
  });

  it('should initialize slick carousel with correct options when data-autoplay-speed is present', () => {
    slickCardBtn();

    expect($.fn.slick).toHaveBeenCalledWith({
      slidesToShow: 1,
      slidesToScroll: 1,
      infinite: true,
      autoplay: true,
      arrows: false,
      autoplaySpeed: 2000,
      centerPadding: '0px',
      draggable: false,
    });
  });

  it('should use default autoplaySpeed if data-autoplay-speed is not present', () => {
    document.body.innerHTML = `<div class="slick-text"></div>`;

    slickCardBtn();

    expect($.fn.slick).toHaveBeenCalledWith(expect.objectContaining({
      autoplaySpeed: 1000,
    }));
  });

  it('should not initialize slick if the element is already initialized', () => {
    document.body.innerHTML = `<div class="slick-text slick-initialized" data-autoplay-speed="3000"></div>`;

    slickCardBtn();

    setTimeout(() => {
      expect($.fn.slick).not.toHaveBeenCalled();
    }, 100);
  });

  it('should initialize slick carousel on the specified selector', () => {
    document.body.innerHTML = `
      <div class="custom-slick-text" data-autoplay-speed="1500"></div>
    `;

    slickCardBtn('.custom-slick-text');
    setTimeout(() => {
      expect($.fn.slick).toHaveBeenCalledWith(expect.objectContaining({
        autoplaySpeed: 1500,
      }));
    }, 100);
  });
});
