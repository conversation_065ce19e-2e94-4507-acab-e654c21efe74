import { describe, expect, test, jest } from "@jest/globals";
import { submitMultiStepForm } from "../../site/scripts/multi-step-form";

class TestMultiStepForm {
  constructor() {
    document.body.innerHTML = `
    <div class="multi-step-form">
    </div>`;
    require("../../site/scripts/multi-step-form");
  }

}

window.scrollTo = jest.fn();
window.$ = jest.fn().mockImplementation(() => {
  return {
    ready: (callback) => callback(),
    each: (callback) =>
      callback(
        {},
        {
          id: "test",
          classList: {
            add: jest.fn(),
            remove: jest.fn(),
          },
        }
      ),
    find: () => {
      return {
        width: jest.fn(),
        closest: () => {
          return {
            css: jest.fn(),
          };
        },
        css: jest.fn(),
        find: () => {
          return {
            css: jest.fn(),
            eq: jest.fn().mockReturnValue({
              offset: () => {
                return {
                  top: 50,
                };
              },
            }),
          };
        },
        click: (callback) => callback(),
        toggle: jest.fn(),
        toggleClass: jest.fn(),
        html: jest.fn(),
        each: (callback) => callback(),
        attr: jest.fn(),
        height: jest.fn(),
        length: 0,
        text: jest.fn().mockReturnValue("test string"),
        append: jest.fn(),
      };
    },
    prop: jest.fn(),
    closest: () => {
      return {
        attr: jest.fn(),
      };
    },
    on: (event, callback) => callback(),
    attr: jest.fn(),
    hasClass: jest.fn(),
    height: jest.fn(),
    get: () => {
      return {
        offsetTop: 50,
      };
    },
  };
});

describe("test multi test form", () => {
  test("should document ready", () => {
    new TestMultiStepForm();
    expect(document.readyState).toBe("complete");
  });
});

describe("test multi test form", () => {
  test("should call submit multi step form function", () => {
    const validationType = "c-cash";
    let validationResult = false;
    const submit = submitMultiStepForm(validationType);
    expect(submit).toBe(validationResult);
  });
});

