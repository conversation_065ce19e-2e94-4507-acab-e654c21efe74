import { describe, expect, jest, test } from '@jest/globals';
import { TcbGlossaryTooltip } from '../../site/scripts/glossary-tooltip';

customElements.define('tcb-glossary-tooltip', TcbGlossaryTooltip);

describe('Test Glossary Tooltip', () => {
  beforeEach(() => {
    document.body.innerHTML = '';
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => {
          if (typeof callback === 'function') {
            callback({});
          }
        },
        attr: jest.fn().mockReturnValue('abc'),
        find: () => {
          return {
            length: 0,
            attr: jest.fn().mockReturnValue('cba'),
            find: () => {
              return {
                outerHeight: jest.fn().mockReturnValue(50),
                css: jest.fn(),
                removeClass: jest.fn(),
                addClass: jest.fn(),
                append: jest.fn(),
              };
            },
            empty: jest.fn(),
            append: jest.fn(),
          };
        },
        append: jest.fn(),
        on: (param, callback) => {
          if (param == 'scroll' && typeof callback === 'function') {
            callback({});
          }

          if (param == 'click mouseover' && typeof callback === 'function') {
            callback({});
          }

          if (param == 'mouseover click' && typeof callback === 'function') {
            callback({
              stopPropagation: jest.fn(),
            });
          }
        },
        hasClass: () => {
          return {
            removeClass: jest.fn().mockReturnValue(''),
            addClass: jest.fn().mockReturnValue(''),
          };
        },
        removeClass: jest.fn(),
        addClass: jest.fn(),
        parents: () => {
          return {
            length: 0,
          };
        },
        get: () => {
          return {
            value: 'abc',
            getBoundingClientRect: () => {
              return {
                top: 50,
                left: 1,
              };
            },
          };
        },
        width: jest.fn(),
      };
    });
    window.$.ajax = jest.fn().mockImplementation(({ url, type, dataType, success, error }) => {
      if (typeof success === 'function') {
        success({
          data: {
            vi: {
              items: [
                {
                  keyword: 'W387 EN Lorem ipsum',
                  preview: {
                    html: '<p>W387 EN Lorem ipsum dolor sit amet</p>',
                  },
                  keywordSlugVi: 'w387-vi-lorem-ipsum',
                  keywordSlugEn: 'w387-en-lorem-ipsum',
                },
              ],
            },
            en: {
              items: [],
            },
          },
        });
      }
      if (typeof error === 'function') {
        error({});
      }
    });
  });

  test('should document is ready', () => {
    let tcbGlossaryTooltip = new TcbGlossaryTooltip($('a'));
    expect(tcbGlossaryTooltip).toBeTruthy();
  });
});
