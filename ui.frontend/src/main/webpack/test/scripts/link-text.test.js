import { describe, expect, test, jest } from "@jest/globals";

class TestLinkText {
  constructor() {
    require("../../site/scripts/link-text");
  }
}

describe("Test Link Text", () => {
    test("should document is ready", () => {
        window.$ = jest.fn().mockImplementation(() => {
            return {
                ready: (callback) => callback(),
                each: (callback) => callback(),
                attr: jest.fn(),
                length: 3,
                click: (callback) => {
                    if (typeof callback === "function") {
                        callback({});
                    }
                },
                show: jest.fn(),
                hide: jest.fn(),
                on: (param, callback) => {
                    if (param === "resize" && typeof callback === "function") {
                        callback({});
                    }
                },
            }
        });

        window.addEventListener = jest.fn().mockImplementation((param, callback) => {
            if (param === "message" && typeof callback === "function") {
                callback({
                    data: "reloadLinkText"
                });
            }
        });
        new TestLinkText();
        expect(document.readyState).toBe("complete");
    });
});