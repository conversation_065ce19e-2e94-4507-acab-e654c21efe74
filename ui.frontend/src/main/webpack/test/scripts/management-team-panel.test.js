import { describe, expect, jest, test } from '@jest/globals';

class TestManagementTeamPanel {
  constructor() {
    require('../../site/scripts/management-team-panel');
  }
}

describe('ManagementTeamPanel', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        each: callback => callback(),
        click: callback => callback({
          target: 'sample',
        }),
        on: (param, callback) => callback(),
        width: jest.fn().mockReturnValue(200),
        hide: () => {},
        show: () => {},
        addClass: () => {},
        removeClass: () => {},
        is: () => false,
        css: () => {},
        closest: () => {
          return {
            find: () => {
              return {
                text: () => {},
                html: () => {},
                attr: () => {},
              };
            },
          };
        },
        find: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    text: () => {},
                    html: () => {},
                    attr: () => {},
                    click: callback => callback(),
                  };
                },
              };
            },
            click: callback => callback(),
            each: callback => callback(),
            show: () => {},
            addClass: () => {},
            removeClass: () => {},
            hasClass: () => true,
          };
        },
        parent: () => {
          return {
            find: () => {
              return {
                removeClass: () => {},
                addClass: () => {},
              };
            },
            is: () => false,
          };
        },
        parents: () => {
          return {
            is: () => false,
          };
        },
      };
    });

    new TestManagementTeamPanel();
    expect(document.readyState).toBe('complete');
  });
});
