import { describe, expect, jest, test } from '@jest/globals';

class TestTcbInputRange {
  constructor() {
    require('../../site/scripts/tcb-input-range');
  }
}

describe('SiteMap', () => {
  test('should create an instance', () => {
    window.jQuery = jest.fn().mockImplementation(() => {
      return {
        each: (callback) => callback({}, jQuery('div')),
        on: (param, callback) => {
          callback({
            detail: 'sample',
            preventDefault: () => {},
            type: {
              includes: () => {},
            },
            touches: {
              0: {
                pageX: 'sample',
                pageY: 'sample',
              },
            },
          });
          return {
            on: (param, callback) => {
              callback({
                detail: 'sample',
                preventDefault: () => {},
                type: {
                  includes: () => {},
                },
                touches: {
                  0: {
                    pageX: 'sample',
                    pageY: 'sample',
                  },
                },
              });
              return {
                on: (param, callback) => {
                  callback({
                    detail: 'sample',
                    preventDefault: () => {},
                    type: {
                      includes: () => {},
                    },
                    touches: {
                      0: {
                        pageX: 'sample',
                        pageY: 'sample',
                      },
                    },
                  });
                },
              };
            },
          };
        },
        append: () => {},
        css: () => {},
        off: () => {},
        dispatchEvent: () => {},
        getBoundingClientRect: () => {
          return {
            left: 0,
          };
        },
        dataset: {
          min: 0,
          max: 1,
        },
      };
    });

    new TestTcbInputRange();
    expect(document.readyState).toBe('complete');
  });
});
