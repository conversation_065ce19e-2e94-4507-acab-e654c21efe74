import * as analytics from "../../site/scripts/analytics";
import * as LocationUtil from "../../site/scripts/utils/location.util";
import * as ObjectUtil from "../../site/scripts/utils/object.util";
import { expect, jest } from "@jest/globals";

LocationUtil.getPageUrlParams = jest.fn().mockImplementation(() => {
  return "param1=value1&param2=value2";
});

LocationUtil.getUrlParamObj = jest.fn().mockImplementation(() => {
  return "param1=utm_medium&param2=utm_source&param3=utm_campaign&param4=utm_content";
});

ObjectUtil.deleteUndefinedField = jest.fn().mockImplementation(() => {});

jest.mock("jquery", () => ({
  fn: {},
  attr: jest.fn(),
  data: jest.fn(),
}));

describe("addDataLayerObject", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    window.adobeDataLayer = [];
  });

  test("should push the correct data layer object with techcombank and webInteract", () => {
    const eventName = "testEvent";
    const techcombank = {
      pageName: "Test Page",
    };
    const webInteract = {
      interaction: "click",
    };

    analytics.addDataLayerObject(eventName, techcombank, webInteract);
    expect(window.adobeDataLayer).toEqual([
      {
        event: eventName,
        _techcombank: {
          pageName: "Test Page",
          pageURL: "http://localhost/",
          pageURLParam: "",
          siteSection: "",
          language: undefined,
          country: undefined,
          environment: "dev",
          businessUnit: "tcb landing page",
          platform: "web",
          productName: "",
          journey: "/",
          dayOfWeek: expect.any(String),
          timestamp: expect.any(String),
          cookiesEnabled: "y",
          defaultBrowserLanguage: expect.any(String),
          userInfo: {
            status: "not logged-in",
          },
          ...techcombank,
        },
        web: webInteract,
        marketing: undefined,
      },
    ]);
  });

  test("should push the correct data layer object without webInteract", () => {
    const eventName = "testEvent";
    const techcombank = {
      pageName: "Test Page",
    };

    analytics.addDataLayerObject(eventName, techcombank, null);
    expect(window.adobeDataLayer).toEqual([
      {
        event: eventName,
        _techcombank: {
          pageName: "Test Page",
          pageURL: "http://localhost/",
          pageURLParam: "",
          siteSection: "",
          language: undefined,
          country: undefined,
          environment: "dev",
          businessUnit: "tcb landing page",
          platform: "web",
          productName: "",
          journey: "/",
          dayOfWeek: expect.any(String),
          timestamp: expect.any(String),
          cookiesEnabled: "y",
          defaultBrowserLanguage: expect.any(String),
          userInfo: {
            status: "not logged-in",
          },
          ...techcombank,
        },
        web: undefined,
        marketing: undefined,
      },
    ]);
  });
});

describe("getTrackingPageName", () => {
  test("should return the correct tracking page name", () => {
    document.querySelector = jest.fn(() => ({
      getAttribute: jest.fn(() => "Page Title"),
    }));
    const trackingPageName = analytics.getTrackingPageName();
    expect(trackingPageName).toBe("tcb landing page:web:Page Title");
  });
});

describe("getTrackingCode", () => {
  analytics.getTrackingCode = jest.fn(() => {
    return "abc:def:ghi";
  });
  test("should return the correct tracking code when all parameters are present", () => {
    const expectedTrackingCode = "abc:def:ghi";
    const result = analytics.getTrackingCode();
    expect(result).toBe(expectedTrackingCode);
  });
});

describe("formatString", () => {
  analytics.formatString = jest.fn(() => {
    return "its a test string";
  });
  test("should run the formatString function", () => {
    const expectedString = "its a test string";
    const result = analytics.formatString("string");
    expect(result).toBe(expectedString);
  });
});
