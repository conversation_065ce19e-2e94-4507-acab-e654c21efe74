import { describe, expect, jest, test } from '@jest/globals';

class TestMastheadExtend {
  constructor() {
    require('../../site/scripts/masthead-extend');
  }
}

describe('MastheadExtend', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        length: 1,
        ready: callback => callback(),
        each: callback => callback(),
        find: () => {
          return {
            css: () => {
              return {
                match: () => [1, 2, 3],
              };
            },
          };
        },
        css: () => {
          return {
            match: () => [1, 2, 3],
          };
        },
      };
    });

    new TestMastheadExtend();
    expect(document.readyState).toBe('complete');
  });
});
