import { describe, expect, jest, test } from '@jest/globals';

class TestGlobal {
  constructor() {
    require('../../site/scripts/global');
  }
}

describe('Global', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      switch (callback) {
        case '.navigation_sub .discover_dropdown_btn':
        case '.sidebar-menu .sidebar-menu_chat .float-chat-icon':
        case '.footer-expand':
          return {
            click: () => {},
            on: () => {},
          };

        case '.mobile-menu':
        case '.navigation_sub_wraper':
          return {
            on: (param, selector, callback) => callback({
              target: {},
            }),
          };

        default:
          return {
            0: {
              click: () => {},
            },
            length: 0,
            ready: callback => callback(),
            each: callback => callback(),
            mouseover: callback => callback({
              target: {
                closest: () => {
                  return {
                    classList: {
                      add: () => {},
                    },
                  };
                },
              },
            }),
            mouseout: callback => callback({
              target: {},
            }),
            resize: callback => callback(),
            removeClass: () => {},
            remove: () => {},
            clone: () => {
              return {
                appendTo: () => {},
              };
            },
            closest: () => {
              return {
                length: 0,
              };
            },
            addClass: () => {},
            css: () => {},
            prop: () => {},
            toggleClass: () => {},
            html: () => 'add',
            is: () => false,
            hasClass: () => false,
            parent: () => {
              return {
                find: () => {
                  return {
                    css: () => {},
                  };
                },
              };
            },
            parents: () => {
              return {
                0: {},
                is: () => false,
                length: 0,
              };
            },
            click: callback => callback({
              target: {
                closest: () => {
                  return {
                    classList: {
                      contains: () => {},
                      remove: () => {},
                      add: () => {},
                    },
                  };
                },
              },
              stopPropagation: () => {},
            }),
            not: () => {
              return {
                removeClass: () => {},
                html: () => {},
              };
            },
            on: (param, callback) => callback({
              stopPropagation: () => {},
            }),
            index: () => 0,
            find: () => {
              return {
                text: () => {},
                addClass: () => {},
                first: () => {
                  return {
                    addClass: () => {},
                  };
                },
              };
            },
            siblings: () => {
              return {
                toggleClass: () => {},
              };
            },
          };
      }
    });

    new TestGlobal();
    window.dispatchEvent(new Event('beforeunload'));
    expect(document.readyState).toBe('complete');
  });
});
