import { describe, expect, jest, test } from '@jest/globals';

class TestMediaCarousel {
  constructor() {
    require('../../site/scripts/media-carousel');
  }
}

describe('MediaCarousel', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        each: callback => callback(),
        on: (param, callback) => callback(),
        click: callback => callback({
          target: 'sample',
        }),
        attr: () => true,
        addClass: () => {},
        removeClass: () => {},
        hasClass: () => true,
        parent: () => {
          return {
            find: () => {
              return {
                trigger: () => {},
              };
            },
          };
        },
        find: () => {
          return {
            attr: () => {},
            css: () => {},
            each: callback => callback(),
            click: callback => callback(),
            addClass: () => {},
            height: jest.fn().mockReturnValue(200),
            removeClass: () => {},
            find: () => {
              return {
                find: () => {
                  return {
                    attr: () => {},
                    click: callback => callback(),
                  };
                },
              };
            },
          };
        },
      };
    });

    new TestMediaCarousel();
    expect(document.readyState).toBe('complete');
  });
});
