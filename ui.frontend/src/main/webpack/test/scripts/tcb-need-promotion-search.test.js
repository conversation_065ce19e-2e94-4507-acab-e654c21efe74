import { TcbNeedPromotionSearch } from '../../site/scripts/tcb-need-promotion-search';
import { setURLParams, removeURLParams } from '../../site/scripts/utils/params.util';
import { renderViewCard } from '../../site/scripts/utils/get-data-result-page.util';
import { fetchData } from '../../site/scripts/offer-helper';
import { CLASS_HIDDEN_ELEMENT } from '../../site/scripts/constants/offer-common';

jest.mock('../../site/scripts/offer-helper', () => ({
  fetchData: jest.fn(() => Promise.resolve(['Mock Merchant'])),
}));

jest.mock('../../site/scripts/utils/params.util', () => ({
  setURLParams: jest.fn(),
  removeURLParams: jest.fn(),
  getQueryParam: jest.fn(),
}));

jest.mock('../../site/scripts/utils/get-data-result-page.util', () => ({
  renderViewCard: jest.fn(),
}));

customElements.define('tcb-need-promotion-search', TcbNeedPromotionSearch);

describe('TcbNeedPromotionSearch Component', () => {
  let component;

  beforeEach(() => {
    document.body.innerHTML = `
      <tcb-need-promotion-search
        data-url="https://api.example.com"
        data-notfound-wishlist="No results found"
      >
        <div class="tcb-need-promotion-search">
          <input class="need-search-input" />
          <div class="need-input-dropdown ${CLASS_HIDDEN_ELEMENT}">
            <div class="need-input-dropdown-list"></div>
          </div>
          <div class="need-search-container"></div>
          <button class="need-promotions-filter"></button>
          <span class="need-icon-remove-search ${CLASS_HIDDEN_ELEMENT}"></span>
          <div class="need-infor-search"></div>
          <div class="need-group-promotion-dropdown result-search">
            <div class="need-tilte-type-brand"></div>
            <div class="need-tilte-type-brand not-wishlist"></div>
            <div class="need-input-dropdown-list"></div>
          </div>
          <div class="render-recent-search">
            <div class="need-input-dropdown-list"></div>
          </div>
        </div>
      </tcb-need-promotion-search>
    `;

    component = document.querySelector('tcb-need-promotion-search');
    component.connectedCallback();
  });

  afterEach(() => {
    component.disconnectedCallback();
    document.body.innerHTML = '';
    jest.clearAllMocks();
  });

  test('should initialize and cache DOM elements', () => {
    expect(component.elementInputSearch).toBeInstanceOf(HTMLInputElement);
    expect(component.buttonSearch).toBeInstanceOf(HTMLButtonElement);
  });

  test('should handle input event and fetchSearchResults call', () => {
    const input = component.elementInputSearch;
    input.value = 'Test';
    input.dispatchEvent(new Event('input'));

    expect(component.comonObject.value).toBe('');
  });

  test('should handle enter key press', () => {
    component.elementInputSearch.value = 'abc';
    component.elementInputSearch.focus();
    const event = new KeyboardEvent('keydown', { keyCode: 13 });
    component.elementInputSearch.dispatchEvent(event);

    expect(component.searchBar.classList.contains(component.CLASS_POPUP_MOBILE)).toBe(false);
    expect(document.body.classList.contains(component.CLASS_SHOW_POPUP)).toBe(false);
  });

  test('should handle focus event and adjust classes', () => {
    window.innerWidth = 767;
    component.elementInputSearch.focus();

    expect(component.searchBar.classList.contains(component.CLASS_POPUP_MOBILE)).toBe(true);
    expect(document.body.classList.contains(component.CLASS_SHOW_POPUP)).toBe(true);
    expect(component.inputDropdown.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  test('should clear input value', () => {
    const input = component.elementInputSearch;
    input.value = 'Test';
    component.clearInputValue({ stopPropagation: jest.fn() });

    expect(input.value).toBe('');
  });

  test('should set URL parameters on search', () => {
    component.elementInputSearch.value = 'Test';
    component.comonObject.value = 'Merchant123';
    component.setParamSearchTextInUrl();

    expect(setURLParams).toHaveBeenCalledWith('q', 'Test', true);
    expect(setURLParams).toHaveBeenCalledWith('search-with-merchant', 'Merchant123', true);
  });

  test('should remove URL param when search text is empty', () => {
    component.elementInputSearch.value = '';
    component.setParamSearchTextInUrl();
    expect(removeURLParams).toHaveBeenCalledWith('q', true);
  });

  test('fetchSearchResults calls fetchData with correct params', async () => {
    component.elementInputSearch.value = 'Test';
    await component.fetchSearchResults();

    expect(fetchData).toHaveBeenCalled();
    const calledUrl = fetchData.mock.calls[0][0];
    expect(calledUrl).toContain('searchText=Test');
  });

  test('renderSuggestions shows no results for empty data', () => {
    component.elementInputSearch.value = 'Test';
    component.renderSuggestions([]);
    expect(component.resultSearch.querySelector('.not-wishlist').classList.contains('hidden')).toBe(false);
  });

  test('should handle dropdown item click and update input', () => {
    const keyword = 'Merchant';
    const dropdownItem = document.createElement('div');
    dropdownItem.className = 'need-input-dropdown-item';
    dropdownItem.textContent = keyword;
    dropdownItem.dataset.keyword = keyword;

    const dropdownList = component.elementRecentSearch.querySelector('.need-input-dropdown-list');
    dropdownList.appendChild(dropdownItem);

    dropdownItem.addEventListener('click', component.handleDropdownItemClick.bind(component));

    dropdownItem.dispatchEvent(new MouseEvent('click', { bubbles: true }));

    expect(component.elementInputSearch.value).toBe(keyword);
    expect(component.iconRemoveSearch.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  test('handleDropdownMouseDown prevents default', () => {
    const event = { preventDefault: jest.fn() };
    component.handleDropdownMouseDown(event);
    expect(event.preventDefault).toHaveBeenCalled();
  });

  test('handleDocumentClick hides dropdown when clicked outside', () => {
    component.inputDropdown.classList.remove(CLASS_HIDDEN_ELEMENT);
    const outside = document.createElement('div');
    document.body.appendChild(outside);

    outside.dispatchEvent(new MouseEvent('click', { bubbles: true }));

    expect(component.inputDropdown.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(true);
  });

  test('handleWindowResize calls updateBorderRadius', () => {
    const spy = jest.spyOn(component, 'updateBorderRadius');
    window.dispatchEvent(new Event('resize'));
    expect(spy).toHaveBeenCalled();
  });

  test('should remove CLASS_HIDDEN_ELEMENT when input is focused', () => {
    component.connectedCallback();
    component.elementInputSearch.focus();
    component.renderSuggestions(['test']);
    expect(component.resultSearch.parentElement.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  test('should display not found message when data is empty', () => {
    component.contentWishList = 'Không tìm thấy kết quả cho';
    component.elementInputSearch.value = 'abc';
    component.connectedCallback();
    component.renderSuggestions([]);
    const notWishlist = component.resultSearch.querySelector('.need-tilte-type-brand.not-wishlist');
    expect(notWishlist.innerHTML).toContain('Không tìm thấy kết quả cho “abc”');
    expect(notWishlist.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);
  });

  test('should highlight keyword in suggestions', () => {
    component.elementInputSearch.value = 'shop';
    component.connectedCallback();
    component.renderSuggestions(['Shop ABC']);
    const dropdownItem = component.resultSearch.querySelector('.need-input-dropdown-item');
    expect(dropdownItem.innerHTML).toContain('<strong>Shop</strong> ABC');
  });

  test('handleInputBlur hides dropdown after delay on desktop', () => {
    jest.useFakeTimers();
    window.innerWidth = 1024;

    component.inputDropdown.classList.remove(CLASS_HIDDEN_ELEMENT);
    component.searchBar.classList.add(component.CLASS_POPUP_MOBILE);

    component.handleInputBlur();

    expect(component.inputDropdown.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(false);

    jest.advanceTimersByTime(100);

    expect(component.inputDropdown.classList.contains(CLASS_HIDDEN_ELEMENT)).toBe(true);
    expect(component.searchBar.classList.contains(component.CLASS_POPUP_MOBILE)).toBe(false);

    jest.useRealTimers();
  });

  test('fetchSearchResults handles fetchData rejection gracefully', async () => {
    fetchData.mockImplementationOnce(() => Promise.reject(new Error('Network error')));
    component.elementInputSearch.value = 'ErrorTest';

    await component.fetchSearchResults();

    expect(fetchData).toHaveBeenCalled();
    expect(renderViewCard).not.toHaveBeenCalled();
  });
});
