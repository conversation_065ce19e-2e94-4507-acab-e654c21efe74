import { TcbPromotionCard } from '../../site/scripts/tcb-promotion-card';
import * as dayExpiredUtil from '../../site/scripts/utils/day-expired.util';
import * as imageUtil from '../../site/scripts/utils/default-image.util';
import * as exclusiveHelper from '../../site/scripts/exclusive-helper';

customElements.define('tcb-promotion-card', TcbPromotionCard);

jest.mock('../../site/scripts/utils/day-expired.util', () => ({
  renderDayExpired: jest.fn(),
}));

jest.mock('../../site/scripts/utils/default-image.util', () => ({
  renderImage: jest.fn(),
}));

jest.mock('../../site/scripts/exclusive-helper', () => ({
  scrollText: jest.fn(),
}));

describe('TcbPromotionCard', () => {
  let card;

  beforeEach(() => {
    document.body.innerHTML = '<div id="test-card"></div>';
    const el = document.getElementById('test-card');
    card = new TcbPromotionCard();
    el.appendChild(card);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  describe('constructor', () => {
    test('should create .popup-download if not exists', () => {
      expect(card.querySelector('.popup-download')).toBeTruthy();
    });

    test('should set default previewPdf labels', () => {
      expect(card.previewPdf.downloadPdfLabel).toBeDefined();
      expect(card.previewPdf.pdfTitle).toBeDefined();
      expect(card.previewPdf.loadingPdf).toBeDefined();
    });
  });

  describe('init', () => {
    test('should call helper functions and setInterval', () => {
      const spyHandlePdfClick = jest.spyOn(card, 'handlePdfClick');
      card.init();

      expect(dayExpiredUtil.renderDayExpired).toHaveBeenCalled();
      expect(imageUtil.renderImage).toHaveBeenCalled();
      expect(exclusiveHelper.scrollText).toHaveBeenCalled();
      expect(spyHandlePdfClick).toHaveBeenCalled();
      expect(card.intervalId).toBeDefined();
    });
  });

  describe('showPreviewModalListener', () => {
    beforeEach(() => {
      card.triggerShowPreviewModal = jest.fn();
    });

    test('should call triggerShowPreviewModal for .pdf links', () => {
      const mockEvent = {
        currentTarget: document.createElement('a'),
        preventDefault: jest.fn(),
      };
      mockEvent.currentTarget.setAttribute('href', 'test.pdf');

      card.showPreviewModalListener(mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(card.triggerShowPreviewModal).toHaveBeenCalledWith(
        'test.pdf',
        card.previewPdf.pdfTitle,
        card.previewPdf.loadingPdf,
        card.previewPdf.downloadPdfLabel,
      );
    });

    test('should not call triggerShowPreviewModal for non-pdf links', () => {
      const mockEvent = {
        currentTarget: document.createElement('a'),
        preventDefault: jest.fn(),
      };
      mockEvent.currentTarget.setAttribute('href', 'test.jpg');

      card.showPreviewModalListener(mockEvent);

      expect(card.triggerShowPreviewModal).not.toHaveBeenCalled();
    });
  });

  describe('triggerShowPreviewModal', () => {
    test('should trigger show-preview-modal event on .popup-download', () => {
      const popup = card.querySelector('.popup-download');
      const spy = jest.fn();
      $(popup).on('show-preview-modal', spy);

      card.triggerShowPreviewModal('test.pdf', 'PDF Title', 'Loading...', 'Download');

      expect(spy).toHaveBeenCalledWith(expect.anything(), {
        url: 'test.pdf',
        documentType: 'pdf',
        title: 'PDF Title',
        loadingLabel: 'Loading...',
        downloadLabel: 'Download',
      });
    });
  });

  describe('destroy', () => {
    test('should clear interval when destroy is called', () => {
      const spy = jest.spyOn(global, 'clearInterval');
      card.intervalId = 123;
      card.destroy();
      expect(spy).toHaveBeenCalledWith(123);
      expect(card.intervalId).toBeNull();
    });
  });
});
