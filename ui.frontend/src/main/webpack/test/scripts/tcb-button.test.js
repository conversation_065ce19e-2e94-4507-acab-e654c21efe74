import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { TcbButton } from '../../site/scripts/tcb-button';

const SAMPLE = `
	<tcb-button data-link-to-previous-page="yes">
		<a class="cta-button"></a>
	</tcb-button>
`;

describe('TCBButton', () => {
	let component;
	customElements.define('tcb-button', TcbButton);

	beforeEach(() => {
		document.body.innerHTML = SAMPLE;
		Object.defineProperty(document, 'referrer', {
			value: 'https://techcombank.com',
			writable: true,
		});
		Object.defineProperty(window, 'location', {
			value: {
				href: 'https://techcombank.com/personal',
			},
			writable: true
		});
		component = new TcbButton();
	});

	it('should document is ready', () => {
		expect(document.readyState).toBe('complete');
	});

	it('should move to previous page when user click cta', () => {
		const cta = document.querySelector('a.cta-button');
		expect(cta.href).toEqual('https://techcombank.com/');
	});
});
