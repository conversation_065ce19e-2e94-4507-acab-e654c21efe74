import { describe, expect, jest, test } from '@jest/globals';

class TestScrollNavigation {
  constructor() {
    require('../../site/scripts/scroll-navigation');
  }
}

describe('ScrollNavigation', () => {
  test('should create an instance', () => {
    window.ScrollCurrencyConverter = (callback) => callback([{}]);
    window.setTimeout = (callback) => callback([{}]);
    window.$ = jest.fn().mockImplementation(() => {
      return {
        find: () => {
          return {
            0: {
              children: {
                length: 4,
                0: {
                  getAttribute: () => {
                    return false;
                  }
                }
              },
              scrollWidth: 20,
              clientWidth: 10
            }, on: (param, callback) => callback(),
          };
        },
        animate: () => {
        },
        on: (param, callback) => {
          callback();
        },
        append: jest.fn(),
      };
    });

    new TestScrollNavigation();
    new ScrollNavigation(document.createElement('div'));
    expect(document.readyState).toBe('complete');
  });
});
