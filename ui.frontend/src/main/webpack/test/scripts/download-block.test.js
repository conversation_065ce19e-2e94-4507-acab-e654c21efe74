import { describe, expect, test, jest } from "@jest/globals";

class TestDownloadBlock {
  constructor() {
    require("../../site/scripts/download-block");
  }
}

describe("Test Download Block", () => {
    test("should document is ready", () => {
        window.$ = jest.fn().mockImplementation(() => {
            return {
                ready: (callback) => callback(),
                each: (callback) => callback(),
                closest: () => {
                    return {
                        value: "abc",
                        find: () => {
                            return {
                                length: 2,
                                css: jest.fn().mockReturnValue(24),
                            }
                        },
                    }
                },
                find: () => {
                    return {
                        length: 2,
                        css: jest.fn(),
                    }
                },
            }
        });
        new TestDownloadBlock();
        expect(document.readyState).toBe("complete");
    });
});