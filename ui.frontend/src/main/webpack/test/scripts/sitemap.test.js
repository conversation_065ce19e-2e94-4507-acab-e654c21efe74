import { describe, expect, jest, test } from '@jest/globals';

class TestSitemap {
  constructor() {
    require('../../site/scripts/sitemap');
  }
}

describe('SiteMap', () => {
  test('should create an instance', () => {
    window.jQuery = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {};
    });
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        each: (callback) => callback(),
        find: () => {
          return {
            slideToggle: () => {
            },
            toggleClass: () => {
            },
            on: (param, callback) => callback(),
          };
        },
      };
    });

    new TestSitemap();
    expect(document.readyState).toBe('complete');
  });
});
