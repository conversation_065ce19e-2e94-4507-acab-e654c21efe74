import { describe, expect, test, jest } from "@jest/globals";

class TestLoanCalc {
  constructor() {
    document.body.innerHTML = `
        <div id="myRange"></div>
    `;

    require("../../site/scripts/loan-calc");
  }
}

describe("Test Loan Calc", () => {
  test("should document is ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        attr: jest.fn(),
        data: jest.fn(),
        next: () => {
          return {
            length: 10,
            hasClass: jest.fn().mockReturnValue(false),
            removeClass: jest.fn().mockReturnValue(false),
            addClass: jest.fn().mockReturnValue(false),
            css: jest.fn().mockReturnValue(false),
            parents: () => {
              return {
                hasClass: jest.fn().mockReturnValue(false),
              };
            },
          };
        },
        toggleClass: jest.fn(),
        prev: jest.fn(),
        hasClass: jest.fn().mockReturnValue(true),
        addClass: jest.fn(),
        css: jest.fn(),
        click: (callback) => callback(),
        val: jest.fn().mockReturnValue("7.3"),
        removeAttr: jest.fn(),
        keypress: (callback) =>
          callback({
            which: "ABC",
            keyCode: "ABC",
            target: {
              value: "A",
            },
            getAttribute: () => {
              return "day";
            },
          }),
        keyup: (callback) => callback(),
        on: (parms, callback1, callback2) => {
          if (typeof callback1 === "function") {
            callback1({
              target: {
                value: "123",
              },
            });
          }

          if (typeof callback2 === "function") {
            callback2({
              currentTarget: {},
            });
          }
        },
        removeClass: jest.fn(),
        html: jest.fn(),
        find: (params) => {
          if (params === ".option") {
            return {
              each: (callback) => callback(),
            };
          }
          return {
            keypress: (callback) =>
              callback({
                which: "ABC",
                keyCode: "ABC",
                target: {
                  value: "A",
                },
                getAttribute: () => {
                  return "day";
                },
              }),
            keyup: (callback) => callback(),
            val: () => {
              return "1,000,000,000";
            },
            attr: jest.fn(),
            text: jest.fn().mockReturnValue(true),
            css: jest.fn(),
            html: jest.fn(),
            on: (params, callback) => {
              callback({
                originalEvent: {
                  clipboardData: {
                    getData: () => {
                      return {
                        match: jest.fn().mockReturnValue(true),
                      };
                    },
                  },
                },
                target: {
                  value: "123",
                },
                preventDefault: jest.fn(),
              });
            },
            click: (callback) =>
              callback({
                target: {},
              }),
            0: {
              value: "123",
            },
          };
        },
        length: 10,
        parent: () => {
          return {
            outerHeight: () => {
              return 100;
            },
          };
        },
      };
    });

    new TestLoanCalc();
    expect(document.readyState).toBe("complete");
  });
});
