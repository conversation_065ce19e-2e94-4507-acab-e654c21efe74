import { describe, expect, jest, test } from '@jest/globals';

class TestFaqPanel {
  constructor() {
    require('../../site/scripts/faq-panel');
  }
}

describe('test calendar', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    window.location = {
      href: 'https://techcombank.com/?cardId=1',
    };
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        ready: (callback) => callback(),
        attr: jest.fn(),
        data: jest.fn(),
        width: jest.fn(),
        text: jest.fn().mockReturnValue('test'),
        on: (event, element, callback) => callback({ currentTarget: 'test' }),
        find: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    each: jest.fn(),
                  };
                }, addClass: jest.fn(),
              };
            }, attr: jest.fn(), click: (callback) => callback(), each: (callback) => callback(), 5: {
              style: {
                display: jest.fn().mockReturnValue('none')
              }
            }, 6: {
              style: {
                display: jest.fn().mockReturnValue('none')
              }
            }, 7: {
              style: {
                display: jest.fn().mockReturnValue('none')
              }
            }, 8: {
              style: {
                display: jest.fn().mockReturnValue('none')
              }
            }, 9: {
              style: {
                display: jest.fn().mockReturnValue('none')
              }
            },
          };
        },
        filter: () => {
          return {
            each: (callback) => callback(),
          };
        },
        css: jest.fn(),
        click: (callback) => callback(),
        hasClass: jest.fn(),
        addClass: jest.fn(),
        hide: jest.fn(),
        parent: () => {
          return {
            hasClass: jest.fn(),
          };
        },
        each: (callback) => callback(),
      };
    });
    new TestFaqPanel();
    expect(document.readyState).toBe('complete');
  });
});
