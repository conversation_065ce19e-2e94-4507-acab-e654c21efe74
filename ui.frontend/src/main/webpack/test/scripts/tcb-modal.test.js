import { describe, expect, jest, test } from '@jest/globals';

class TestTcbModal {
  constructor() {
    require('../../site/scripts/tcb-modal');
  }
}

describe('TcbModal', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        on: (param, callback) => callback(),
        append: () => {},
        prepend: () => {},
        parentElement: {
          removeChild: () => {},
        },
        removeClass: () => {},
        addClass: () => {},
      };
    });

    new TestTcbModal();
    new TcbModal({ length: 0 });
    new ModalHandler('title',
      'action',
      $('button'),
      $('div'),
    );
    expect(document.readyState).toBe('complete');
  });
});
