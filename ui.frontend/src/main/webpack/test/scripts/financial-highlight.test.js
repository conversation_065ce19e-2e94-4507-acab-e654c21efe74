import { describe, expect, jest, test } from '@jest/globals';

class TestFinancialHighlight {
  constructor() {
    require('../../site/scripts/financial-highlight');
  }
}

describe('test financial highlight', () => {
  test('should document ready', () => {
    JSON.parse = jest.fn().mockReturnValue([{}]);
    Array.each = jest.fn();
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        find: jest.fn().mockImplementation((classEl) => {
          if (classEl === '.year-item[data-year]') {
            return {
              each: (callback) => callback(0),
            };
          }
          return {
            find: () => {
              return {
                change: (callback) => callback(),
                each: (callback) => callback(),
                removeClass: jest.fn(),
                addClass: jest.fn(),
                find: () => {
                  return {
                    empty: jest.fn(),
                    on: (event, callback) => callback(),
                    filter: () => {
                      return {
                        filter: () => {
                          return {
                            map: () => {
                              return {
                                get: () => {
                                  return {
                                    length: 1,
                                    join: jest.fn(),
                                    includes: () => {
                                      return {
                                        val: jest.fn(),
                                      };
                                    },
                                  };
                                },
                              };
                            },
                          };
                        },
                        prop: jest.fn(),
                      };
                    },
                  };
                },
                add: () => {
                  return {
                    filter: () => {
                      return {
                        each: (callback) => callback(),
                      };
                    },
                  };
                },
                text: jest.fn(),
                on: (event, callback) =>
                  callback({
                    stopPropagation: jest.fn(),
                  }),
                filter: () => {
                  return {
                    filter: () => {
                      return {
                        map: () => {
                          return {
                            get: () => {
                              return {
                                length: 1,
                                join: jest.fn(),
                              };
                            },
                          };
                        },
                      };
                    },
                    prop: jest.fn(),
                  };
                },
              };
            },
            removeClass: jest.fn(),
            each: (callback) => callback(),
            length: 1,
            data: () => {
              return {
                trackingClickInfoValue: 'test',
              };
            },
            attr: jest.fn(),
            clone: () => {
              return {
                appendTo: jest.fn(),
              };
            },
            on: (event, callback) =>
              callback({
                stopPropagation: jest.fn(),
              }),
            parent: () => {
              return {
                removeClass: jest.fn(),
              };
            },
          };
        }),
        attr: jest.fn().mockReturnValue(2024),
        is: jest.fn().mockReturnValue(true),
        val: jest.fn().mockReturnValue(2024),
        addClass: jest.fn(),
        click: jest.fn(),
        parents: () => {
          return {
            length: 1,
            find: () => {
              return {
                data: () => {
                  return {
                    trackingClickInfoValue: 'test',
                  };
                },
                attr: jest.fn(),
                trigger: jest.fn(),
              };
            },
          };
        },
        prop: jest.fn(),
        trigger: jest.fn(),
        width: jest.fn().mockReturnValue(768),
        toggleClass: jest.fn(),
        on: (event, callback) =>
          callback({
            target: {
              classList: {
                contains: jest.fn(),
              },
            },
          }),
        removeClass: jest.fn(),
      };
    });
    new TestFinancialHighlight();
    expect(document.readyState).toBe('complete');
  });
});
