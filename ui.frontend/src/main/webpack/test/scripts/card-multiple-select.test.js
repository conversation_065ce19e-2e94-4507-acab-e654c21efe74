import { describe, expect, jest, test } from '@jest/globals';

class TestCardMultipleSelect {
  constructor() {
    require('../../site/scripts/card-multiple-select');
  }
}

describe('CardMultipleSelect', () => {

  test('should create an instance', () => {
    window.jQuery = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {};
    });

    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        each: callback => callback(),
        click: callback => callback(),
        data: () => [
          {
            id: 'credit',
            title: 'credit',
          },
          {
            id: 'debit',
            title: 'debit',
          },
        ],
        cardMultipleSelectOnChanged: () => {},
        addClass: () => {},
        removeClass: () => {},
        empty: () => {},
        append: () => {},
        trigger: () => {},
        hasClass: () => true,
        find: () => {
          return {
            find: () => {
              return {
                find: () => {
                  return {
                    each: callback => callback(),
                  };
                },
                append: () => {},
              };
            },
          };
        },
      };
    });

    window.$.fn = jest.fn().mockImplementation(() => {
      return {
        cardMultipleSelectOnChanged: () => {},
      };
    });

    new TestCardMultipleSelect();
    expect(document.readyState).toBe('complete');
  });
});
