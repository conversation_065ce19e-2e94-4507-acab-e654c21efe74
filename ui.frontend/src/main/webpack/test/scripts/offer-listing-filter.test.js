import {
	afterEach,
	beforeEach,
	describe,
	expect,
	jest,
	test,
  } from "@jest/globals";

  const SAMPLE_DATA = {
	results: [
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>Nhận phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>N<PERSON><PERSON>n phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>Nhận phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>Nhận phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>Nhận phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>Nhận phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	  {
		thumbnail:
		  "/content/dam/techcombank/public-site/en/images/personal-banking/Frame-48325-4348845ffc-3ea9cacf64.jpg.rendition/cq5dam.thumbnail.319.319.png",
		description:
		  "<p>Nhận phần thưởng lên tới 2,000,000đ khi giới thiệu thành công KHDN mở tài khoản doanh nghiệp Techcombank</p>\n",
		expiryDate: "30/09/2024",
		favourite: "false",
		url: "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf",
		openInNewTab: "false",
		noFollow: "false",
		interactionType: "download",
		products: ["Tài khoản"],
		partner: ["Techcombank"],
	  },
	],
	total: "12",
  };

  class TestOfferListing {
	constructor() {
	  document.body.innerHTML = `
			  <div class="offer-listing-filter__body">
				  <div class="offer-listing-promotions__wrapper">
					  <input aria-invalid="false" id="searchAreaMobile" autocomplete="off"
				   type="text" aria-autocomplete="list" value="sample"
				   autocapitalize="none" spellcheck="false"
				   class="ui-autocomplete-input search-input"
			/>
				  </div>
			  </div>
		  `;

	  require("../../site/scripts/offer-listing-filter");
	}
  }

  describe("Test Offer Listing Filter", () => {
	JSON.parse = jest.fn().mockReturnValue({ articleFilter: "sample" });

	test("should document is ready", () => {
	  document.getElementsByClassName = jest.fn().mockReturnValue([
		{
		  contains: jest.fn(),
		},
	  ]);
	  window.event = { preventDefault: jest.fn() };
	  window.scrollTo = jest.fn();
	  window.url = new URL(window.location);

	  window.$ = jest.fn().mockImplementation((callback) => {
		if (typeof callback === "function") {
		  callback();
		}

		if (callback === ".offer-listing-promotions__wrapper .search-input") {
		  return {
			on: jest.fn(),
			each: (callback) =>
			  callback(
				{},
				{
				  addEventListener: (type, callback) =>
					callback({
					  target: {
						value: "sample",
					  },
					}),
				}
			  ),
			val: jest.fn().mockReturnValue("text input search"),
		  };
		}
		return {
		  attr: (type) => {
			if (typeof type === "string" && type.includes("href")) {
			  return "/content/dam/techcombank/public-site/documents/MGM-H2-2024.pdf";
			}
			if (typeof type === "string" && type.includes("data-expiry")) {
			  return "30/09/2024";
			}
			if (typeof type === "string" && type.includes("data-value")) {
			  return "most-popular";
			}
			if (typeof type === "string" && type.includes("data-filter-type")) {
			  return "products";
			}
			return {
			  split: jest.fn(),
			};
		  },
		  addClass: jest.fn(),
		  toggleClass: jest.fn(),
		  removeClass: jest.fn(),
		  hasClass: jest.fn().mockReturnValue(true),
		  prop: jest.fn(),
		  trigger: jest.fn(),
		  show: jest.fn(),
		  hide: jest.fn(),
		  resize: jest.fn(),
		  empty: jest.fn(),
		  append: jest.fn(),
		  value: 2,
		  length: 5,
		  data: jest.fn().mockReturnValue({
			trackingClickInfoValue: {
			  articleFilter: "sample",
			  replace: jest.fn(),
			},
		  }),
		  text: jest.fn().mockReturnValue(2),
		  is: jest.fn().mockReturnValue(true),
		  width: jest.fn().mockReturnValue(1440),
		  val: jest.fn().mockReturnValue("the-tin-dung"),
		  ready: (callback) => callback(),
		  each: (callback) =>
			callback(
			  {},
			  {
				addEventListener: (type, callback) =>
				  callback({
					target: {
					  value: "sample",
					},
				  }),
			  }
			),
		  click: (callback) =>
			callback({
			  length: 1,
			  preventDefault: jest.fn(),
			  stopPropagation: jest.fn(),
			}),
		  toggleClass: jest.fn(),
		  parent: () => {
			return {
			  hasClass: jest.fn().mockReturnValue(true),
			  removeClass: jest.fn(),
			  height: jest.fn(),
			};
		  },
		  prev: () => {
			return {
			  attr: jest.fn(),
			};
		  },
		  closest: () => {
			return {
			  find: () => {
				return {
				  prop: jest.fn(),
				  is: jest.fn(),
				  slideToggle: jest.fn(),
				  map: () => {
					return {
					  get: jest.fn().mockReturnValue({
						length: 1,
						join: () => {
						  return {
							split: () => {
							  return {
								join: () => {
								  return {
									split: () => {
									  return {
										join: jest.fn(),
									  };
									},
								  };
								},
							  };
							},
						  };
						},
					  }),
					};
				  },
				  length: 1,
				  join: () => {
					return {};
				  },
				};
			  },
			};
		  },
		  change: (callback) => callback(),
		  on: (event, element, callback) => {
			if (callback) {
			  callback({
				preventDefault: jest.fn(),
			  });
			} else {
			  element({
				keyCode: 13,
				stopPropagation: jest.fn(),
				target: 12,
				preventDefault: jest.fn(),
			  });
			}
		  },
		  children: () => {
			return {
			  length: 5,
			};
		  },
		  prev: () => {
			return {
			  attr: jest.fn(),
			};
		  },
		  closest: () => {
			return {
			  attr: jest.fn(),
			  find: (callback) => {
				if (typeof callback === "string") {
				  return {
					val: jest.fn().mockReturnValue("sample"),
					filter: jest.fn().mockReturnValue(0),
					slideToggle: jest.fn(),
					prop: jest.fn(),
					is: jest.fn().mockReturnValue(true),
				  };
				}
				return {
				  length: 1,
				  map: () => {
					return {
					  get: jest.fn().mockReturnValue({
						length: 1,
						join: () => {
						  return {
							split: () => {
							  return {
								join: () => {
								  return {
									split: () => {
									  return {
										join: jest.fn(),
									  };
									},
								  };
								},
							  };
							},
						  };
						},
					  }),
					};
				  },
				};
			  },
			};
		  },
		  next: () => {
			return {
			  text: jest.fn().mockReturnValue("text"),
			};
		  },
		  parent: () => {
			return {
			  hasClass: jest.fn(),
			  removeClass: jest.fn(),
			  addClass: jest.fn(),
			  height: jest.fn(),
			  css: jest.fn(),
			};
		  },
		  outerHeight: jest.fn(),
		  find: (param) => {
			if (typeof param === "string" && param.includes("btn-open-filter")) {
			  return {
				on: (type, callback) => {
				  if (type === "click") {
					callback();
				  }
				},
			  };
			}
			return {
			  attr: (type) => {
				if (
				  typeof type === "string" &&
				  type.includes("data-language-label")
				) {
				  return "vi";
				}
				if (
				  typeof type === "string" &&
				  type.includes("data-search-count")
				) {
				  return {
					replace: jest.fn(),
				  };
				}
				return jest.fn().mockReturnValue("text");
			  },
			  text: jest.fn().mockReturnValue(true),
			  css: jest.fn(),
			  html: jest.fn(),
			  hasClass: jest.fn().mockReturnValue(true),
			  eq: jest.fn().mockReturnValue({
				text: jest.fn().mockReturnValue("unit-test"),
				hasClass: jest.fn().mockReturnValue(true),
				removeClass: jest.fn(),
				addClass: jest.fn(),
			  }),
			  first: jest.fn().mockReturnValue({
				text: jest.fn().mockReturnValue("unit-test"),
			  }),
			  addClass: jest.fn(),
			  filter: jest.fn().mockReturnValue(["a", "b", "c"]),
			  prop: jest.fn(),
			  removeClass: jest.fn(),
			  show: jest.fn(),
			  on: (type, callback) => {
				if (type === "click") {
				  callback();
				}
			  },
			  click: (callback) => callback(),
			  each: (callback) => callback(),
			  length: 1,
			  find: () => {
				return {
				  on: (type, callback) => {
					if (typeof type === "string" && type.includes("click")) {
					  callback();
					}
					return jest.fn().mockReturnValue((callback) => callback());
				  },
				  hide: jest.fn(),
				  length: 1,
				  hide: jest.fn(),
				};
			  },
			  val: () => {
				return {
				  toLowerCase: () => {
					return {
					  split: () => {
						return {
						  join: () => {},
						};
					  },
					};
				  },
				};
			  },
			  map: () => {
				return {
				  get: () => {
					return {
					  length: 1,
					  join: () => {},
					};
				  },
				};
			  },
			  filter: jest.fn().mockReturnValue({ length: 1 }),
			  prop: jest.fn(),
			};
		  },
		};
	  });

	  window.$.ajax = jest
		.fn()
		.mockImplementationOnce(
		  ({ url, method, contentType, success, error }) => {
			if (typeof success === "function") {
			  success(SAMPLE_DATA);
			}
			if (typeof error === "function") {
			  error({});
			}
		  }
		);

	  window.location = {
		...window.location,
		search: "partner=agoda&products=the+tin+dung&sort=moi+nhat",
		href: "https://test.com/en/test.html?partner=agoda&products=the+tin+dung&sort=moi+nhat",
		origin: "https://test.com/en/test.html",
		protocol: "https:",
		host: "test.com",
		hostname: "test.com",
		get: jest.fn(),
		toString: jest.fn(),
	  };

	  window.URL = () => {
		return {
		  searchParams: {
			size: 3,
			has: jest.fn((param) => {
			  switch (param) {
				case "sort":
				case "products":
				case "types":
				case "partner":
				case "limit":
				case "offset":
				case "q":
				case "card-types":
				case "memberships":
				case "location":
				  return true;
			  }
			}),
			get: jest.fn((param) => {
			  switch (param) {
				case "sort":
				  return "";

				case "products":
				  return "the+tin+dung";

				case "types":
				  return "am+thuc";

				case "partner":
				  return "avis";

				case "limit":
				  return "12";

				case "offset":
				  return "0";

				case "q":
				  return "";

				case "card-types":
				  return "card-types-1/cardtype11";

				case "memberships":
				  return "avis";

				case "location":
				  return "ha-noi";
			  }
			}),
			set: jest.fn(),
			sort: jest.fn(),
			delete: jest.fn(),
		  },
		};
	  };

	//   new TestOfferListing();
	  expect(document.readyState).toEqual("complete");
	});
  });
