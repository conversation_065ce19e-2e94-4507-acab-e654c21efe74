import { describe, expect, jest, test } from '@jest/globals';

class TestTimeDropdown {
  constructor() {
    require('../../site/scripts/time-dropdown');
    document.body.innerHTML = '';
    document.body.innerHTML = `<div class="time-dropdown"></div>`;
  }
}

describe('TimeDropdown', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        changeOptions: jest.fn(),
        ready: callback => callback(),
        each: callback => callback(),
        click: callback => callback(),
        on: (param, callback) => callback({
          target: 'sample',
        }),
        attr: () => {},
        empty: () => {},
        append: () => {},
        toggleClass: () => {},
        removeClass: () => {},
        parents: () => {
          return {
            length: 0,
          };
        },
        height: () => 48,
        next: () => {
          return {
            hasClass: () => false,
            css: () => {},
            toggleClass: () => {},
          };
        },
        find: () => {
          return {
            each: callback => callback(),
            toggleClass: () => {},
          };
        },
      };
    });

    window.$.fn = jest.fn().mockImplementation(() => {
      return {
        changeOptions: jest.fn(),
        right: jest.fn(),
      };
    });

    new TestTimeDropdown();
    const element = $('div.time-dropdown');
    element.changeOptions(1000, 100);
    expect(document.readyState).toBe('complete');
  });
});
