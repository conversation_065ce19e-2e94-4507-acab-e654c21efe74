import { describe, expect, test, jest } from "@jest/globals";

class TestInsuranceCalc {
  constructor() {
    document.body.innerHTML = `
    <div class="container">
        <div class="insurance-gain">1</div>
        <div class="insurance-gain">2</div>
        <div class="insurance-gain">3</div>
    </div>`;

    require("../../site/scripts/insurance-calc");
  }
}

describe("Test Insurance Calc", () => {
  test("should document is ready", () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
        attr: jest.fn(),
        data: jest.fn(),
        on: (parms, callback1, callback2) => {
          if (typeof callback1 === "function") {
            callback1({
              target: {
                value: "123",
              },
            });
          }

          if (typeof callback2 === "function") {
            callback2({
              currentTarget: {},
            });
          }
        },
        next: jest.fn(),
        css: jest.fn(),
        toggleClass: jest.fn(),
        prev: jest.fn(),
        hasClass: (params) => {
          if (
            params === "drop-down__container" ||
            params === "drop-down__controls" ||
            params === "drop-down__select"
          ) {
            return false;
          }
          return true;
        },
        addClass: jest.fn(),
        removeClass: jest.fn(),
        html: jest.fn(),
        parent: () => {
          return {
            outerHeight: jest.fn(),
          };
        },
        parents: () => {
          return {
            hasClass: jest.fn().mockReturnValue(false),
          };
        },
        find: (params) => {
          if (params === ".option") {
            return {
              each: (callback) => callback(),
            };
          }
          return {
            keypress: (callback) =>
              callback({
                which: "ABC",
                keyCode: "ABC",
                target: {
                  value: "A",
                },
                getAttribute: () => {
                  return "day";
                },
              }),
            keyup: (callback) => callback(),
            val: () => {
              return "1,000,000,000";
            },
            attr: jest.fn(),
            text: jest.fn().mockReturnValue(true),
            css: jest.fn(),
            html: jest.fn(),
            removeClass: jest.fn(),
            on: (params, callback) => {
              callback({
                originalEvent: {
                  clipboardData: {
                    getData: () => {
                      return {
                        match: jest.fn().mockReturnValue(true),
                      };
                    },
                  },
                },
                preventDefault: jest.fn(),
              });
            },
            click: (callback) =>
              callback({
                target: {},
              }),
          };
        },
      };
    });

    new TestInsuranceCalc();
    expect(document.readyState).toBe("complete");
  });
});
