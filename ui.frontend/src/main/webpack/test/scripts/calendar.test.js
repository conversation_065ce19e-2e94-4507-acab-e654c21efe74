import { describe, expect, jest, test } from '@jest/globals';

class TestCalendar {
  constructor() {
    require('../../site/scripts/calendar');
  }
}

describe('Calendar', () => {
  jest.mock('jquery-ui/ui/widgets/datepicker.js', () => {});
  jest.mock('jquery-ui/ui/widgets/selectmenu.js', () => {});
  jest.mock('jquery-ui/ui/i18n/datepicker-vi', () => {});

  beforeEach(() => {
    document.documentElement.lang = 'vi';
  });

  test('should create an instance', () => {
    window.jQuery = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        datepicker: {},
      };
    });

    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === 'function') {
        callback();
      }

      return {
        ready: callback => callback(),
        each: callback => callback(),
        click: callback => callback({
          target: 'sample',
          stopPropagation: () => {},
        }),
        on: (param, callback) => callback({
          target: 'sample',
          stopPropagation: () => {},
        }),
        attr: () => {},
        toggleClass: () => {},
        addClass: () => {},
        removeClass: () => {},
        datepicker: () => {},
        wrap: () => {},
        css: () => {},
        selectmenu: () => {},
        parent: () => {},
        hasClass: () => true,
        is: () => false,
        height: () => 50,
        outerHeight: () => 200,
        parents: () => {
          return {
            length: 0,
            is: () => false,
          };
        },
        offset: () => {
          return {
            top: 700,
          };
        },
        next: () => {
          return {
            siblings: () => {
              return {
                find: () => {
                  return {
                    val: () => {},
                  };
                },
              };
            },
            css: () => {},
            toggleClass: () => {},
            datepicker: () => {},
            empty: () => {},
            addClass: () => {},
            on: (param, callback) => callback(),
          };
        },
        find: () => {
          return {
            find: () => {
              return {
                datepicker: () => {},
                change: callback => callback(),
              };
            },
            data: () => {},
            on: (param, callback) => callback({
              stopPropagation: () => {},
            }),
          };
        },
      };
    });

    window.$.datepicker = {
      regional: {
        vi: {},
      },
    };

    new TestCalendar();
    expect(document.readyState).toBe('complete');
  });
});
