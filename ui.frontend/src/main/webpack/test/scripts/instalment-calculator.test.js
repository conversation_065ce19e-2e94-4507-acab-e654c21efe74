import { describe, expect, jest, test } from '@jest/globals';

class TestInstalmentCalculator {
	constructor() {
		document.body.innerHTML = `<div class="instalment-calculator__container"></div>`;
		require('../../site/scripts/instalment-calculator');
	}
}

describe('instalment-calculator', () => {
	test('should document is ready', () => {
		window.$ = jest.fn().mockImplementation(() => {
			return {
				length: 2,
				attr: jest.fn(),
				text: jest.fn(),
				empty: jest.fn(),
				data: jest.fn(),
				show: jest.fn(),
				css: jest.fn(),
				click: jest.fn(),
				append: jest.fn(),
				change: (callback) => callback(),
				on: (event, callback) => callback(),
				ready: (callback) => callback(),
				bind: (event, callback) => callback(),
				keypress: (callback) => callback({
					which: '12',
					keyCode: '12',
				}),
				keydown: (callback) => callback({
					key: '12',
				}),
				keyup: (callback) => callback(),
				each: (callback) => callback(0),
				val: () => {
					return {
						replaceAll: () => {
							return {
								replace: () => {
									return {
										toString: () => {
											return {
												replaceAll: () => {
													return {
														replace: () => {
															return {
																replaceAll: jest.fn()
															}
														}
													}
												},
											}
										}
									}
								}
							}
						},
					};
				},
				find: () => {
					return {
						text: jest.fn(),
						hide: jest.fn(),
						val: jest.fn(),
						each: (callback) => callback(),
					}
				},
			};
		});

		new TestInstalmentCalculator();
		expect(document.readyState).toEqual('complete');
	});
});