import { describe, expect, jest, test } from '@jest/globals';

class TestArticleListing {
  constructor() {
    document.body.innerHTML = `
        <tcb-header data-alt-label-text="altLabelText"
                data-alt-label-link="altLabelLink"
                data-alt-target="altTarget"
                data-sticky-cta-label-text="ctaLabelText"
                data-sticky-cta-label-link="ctaLabelLink"
                data-sticky-cta-target="_blank"
                data-hide-sticky-cta="true"
        ></tcb-header>
    `;
    require("../../site/scripts/article-listing");
  }
}

describe("test article card cross", () => {
  beforeEach(() => {
    global.setTimeout = jest.fn((cb) => cb());
  });
  test("should document ready", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
      return { articleFilter: "sample" };
    });
    window.scrollTo = jest.fn();
    window.$ = jest.fn().mockImplementation((callback) => {
      if (typeof callback === "function") {
        callback();
      }
      return {
        each: (callback) => callback(),
        attr: jest.fn().mockReturnValue(1),
        find: () => {
          return {
            length: 1,
            find: () => {
              return {
                on: (event, callback) => callback(),
              };
            },
            css: jest.fn(),
            offset: () => {
              return {
                top: 12,
              };
            },
            0: true,
          };
        },
        click: (callback) => {
          if (typeof callback === "function") {
            callback({
              preventDefault: jest.fn(),
              stopImmediatePropagation: jest.fn(),
            });
          }
        },
        on: (event, callback) => callback(),
        outerHeight: jest.fn().mockReturnValue(1),
        get: () => {
          return {
            getBoundingClientRect: () => {
              return {
                top: 0,
              };
            },
          };
        },
        length: 1,
        css: jest.fn(),
        hasClass: jest.fn(),
        offset: () => {
          return {
            top: 2,
          };
        },
      };
    });
    new TestArticleListing();
    window.dispatchEvent(new Event('scroll'));
    expect(document.readyState).toBe("complete");
  });
});
