import { describe, expect, jest, test } from '@jest/globals';

class TestLcd {
  constructor() {
    document.body.innerHTML = `
    <div class="mySlides"></div>
    `;
    require('../../site/scripts/lcd');
  }
}

describe('test lcd', () => {
  test('should document ready', () => {
    document.getElementsByClassName = jest.fn().mockReturnValue([
      {
        style: {
          display: 'test',
        },
      },
    ]);
    window.$ = jest.fn().mockImplementation(() => {
      return {
        ready: (callback) => callback(),
        each: (callback) => callback(),
      };
    });
    new TestLcd();
    expect(document.readyState).toBe('complete');
  });
});
