import { describe, expect, jest, test } from '@jest/globals';

class TestTcbDropdown {
  constructor() {
    require('../../site/scripts/tcb-dropdown');
  }
}

describe('TcbDropdown', () => {
  test('should create an instance', () => {
    window.$ = jest.fn().mockImplementation(() => {
      return {
        0: {
          classList: {
            add: () => {},
            remove: () => {},
          },
          addEventListener: (param, callback) => callback(),
          setAttribute: () => {},
          removeAttribute: () => {},
        },
        find: () => {
          return {
            0: {
              addEventListener: (param, callback) => callback({
                which: 1,
              }),
              removeAttribute: () => {},
              setAttribute: () => {},
              dispatchEvent: () => {},
              getBoundingClientRect: () => {
                return {
                  height: 31,
                };
              },
              style: {
                top: 2,
              },
            },
            each: callback => callback(),
            filter: () => {
              return {
                find: () => {
                  return {
                    text: () => {},
                  };
                },
              };
            },
            val: () => {},
          };
        },
        on: (param, callback) => callback({
          currentTarget: {
            dataset: {
              value: 'sample',
            },
            innerText: 'sample',
          },
        }),
        append: () => {},
        prepend: () => {},
        text: () => {},
        hasClass: () => {},
      };
    });

    new TestTcbDropdown();
    new TcbDropdown(document.createElement('div'));
    expect(document.readyState).toBe('complete');
  });
});
