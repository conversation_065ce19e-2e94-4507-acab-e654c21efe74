import { beforeEach, describe, expect, jest, test } from '@jest/globals';

class TestStockChart {
  constructor() {
    require('../../site/scripts/stock-chart');
  }
}

const SAMPLE = `
<div class="stockchart section">
  <div class="stock-information" data-current-date-label="đến" data-url="/content/techcombank/web/vn/vi/nha-dau-tu/_jcr_content.stockchart.json">
    <div class="stock-information_summary-details">
      <input type="hidden" name="stockChartApiUrl" data-stock-api-url="https://apipubaws.tcbs.com.vn/stock-insight/v1/stock/bars-long-term">
      <span class="stock-label"><PERSON><PERSON><PERSON> cu<PERSON>i cùng (tính bằng VND)</span>
      <span class="stock-information_price">
        <i class="tcb-icon stock-down decrease">
          <img class="down-icon" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/stock-down-icon_1712283921267.svg">
          <img class="up-icon" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/stock_up_icon_1712283921267.svg">
          <img class="equal-icon" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/stock_equal_icon.svg">
        </i>
        <span class="value">21,000.00</span>
        <span class="stock-information_changes" style="color: rgb(255, 58, 47);">-250.00 (-1.18%)</span>
      </span>
      <span class="stock-dateLastPrice">đến 13 Tháng 8 2024</span>
    </div>
    <div class="stock-information_stock-chart" id="stock-chart"></div>
  </div>
</div>
`;

const SAMPLE_DATA = {
  data: [
    {
      'open': 16365,
      'high': 16390,
      'low': 16220,
      'close': 16390,
      'volume': 4575286,
      'tradingDate': '2023-08-14T00:00:00.000Z'
    },
    {
      'open': 16438,
      'high': 16681,
      'low': 16414,
      'close': 16511,
      'volume': 4612576,
      'tradingDate': '2023-08-15T00:00:00.000Z'
    },
    {
      'open': 16487,
      'high': 17141,
      'low': 16462,
      'close': 17117,
      'volume': ********,
      'tradingDate': '2023-08-16T00:00:00.000Z'
    },
    {
      'open': 17117,
      'high': 17117,
      'low': 16826,
      'close': 16826,
      'volume': 5309921,
      'tradingDate': '2023-08-17T00:00:00.000Z'
    },
    {
      'open': 16753,
      'high': 16753,
      'low': 15735,
      'close': 15759,
      'volume': 13224652,
      'tradingDate': '2023-08-18T00:00:00.000Z'
    },
    {
      'open': 15759,
      'high': 15977,
      'low': 15565,
      'close': 15880,
      'volume': 5357852,
      'tradingDate': '2023-08-21T00:00:00.000Z'
    },
    {
      'open': 15880,
      'high': 16050,
      'low': 15468,
      'close': 16050,
      'volume': 6718788,
      'tradingDate': '2023-08-22T00:00:00.000Z'
    },
  ]
};

describe('StockChart', () => {
  const element = document.createElement('div');

  beforeEach(() => {
    document.documentElement.lang = 'vi';
    document.body.innerHTML = '';
    document.body.innerHTML = SAMPLE;
  });

  test('should create an instance', () => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // Deprecated
        removeListener: jest.fn(), // Deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    window.$ = jest.fn().mockImplementation((param) => {
      return {
        0: element,
        ready: (callback) => callback(),
        attr: () => {
        },
        append: () => () => {
        },
        length: param === '.tv-lightweight-charts' ? 0 : 2,
        text: jest.fn().mockReturnValue('2000'),
        find: () => {
          return {
            text: jest.fn().mockReturnValue('2000'),
            addClass: () => {
            },
          };
        },
      };
    });

    window.$.ajax = jest.fn().mockImplementationOnce(({ url, type, dataType, success, error }) => {
      if (typeof success === 'function') {
        success(SAMPLE_DATA);
      }
      if (typeof error === 'function') {
        error({});
      }
    });

    new TestStockChart();
    expect(document.readyState).toBe('complete');
  });
});
