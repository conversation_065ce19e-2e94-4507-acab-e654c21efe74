import { initializeSlickCarousel } from '../../site/scripts/banner';

jest.mock('slick-carousel', () => jest.fn());

describe('initializeSlickCarousel', () => {
  beforeEach(() => {
    document.body.innerHTML = `
      <div class="cardslider-carousel-slicklist"></div>
    `;
  });

  it('should initialize slick carousel with correct options', () => {
    const slickMock = jest.fn();
    $.fn.slick = slickMock;

    initializeSlickCarousel();

    expect(slickMock).toHaveBeenCalledWith({
      slidesToShow: 1,
      slidesToScroll: 1,
      swipe: true,
      swipeToSlide: true,
      autoplay: false,
      accessibility: true,
      arrows: false,
      dots: true,
      infinite: true,
    });
  });

  it('should only initialize slick carousel once', () => {
    const slickMock = jest.fn();
    $.fn.slick = slickMock;

    initializeSlickCarousel();

    setTimeout(() => {
      expect(slickMock).toHaveBeenCalledTimes(1);
    }, 100);
  });

  it('should not initialize slick carousel if it is already initialized', () => {
    const slickMock = jest.fn();
    $.fn.slick = slickMock;

    $('.cardslider-carousel-slicklist').addClass('slick-initialized');

    initializeSlickCarousel();

    setTimeout(() => {
      expect(slickMock).not.toHaveBeenCalled();
    }, 100);
  });
});
