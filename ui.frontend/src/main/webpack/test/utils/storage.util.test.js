import { StorageUtils } from "../../site/scripts/utils/storage.util";
import { expect } from "@jest/globals";

describe('StorageUtils', () => {
    beforeEach(() => {
      localStorage.clear(); // Clear localStorage before each test
    });

    afterEach(() => {
      localStorage.clear(); // Clear localStorage after each test
    });

    describe('get', () => {
      it('should return null if the key does not exist in localStorage', () => {
        const key = 'nonexistentKey';
        const result = StorageUtils.get(key);
        expect(result).toBeNull();
      });

      it('should return the parsed value if the key exists in localStorage', () => {
        const key = 'existingKey';
        const value = { name: 'John', age: 30 };
        localStorage.setItem(key, JSON.stringify(value));

        const result = StorageUtils.get(key);
        expect(result).toEqual(value);
      });
    });

    describe('set', () => {
      it('should set the value in localStorage', () => {
        const key = 'newKey';
        const value = { name: '<PERSON>', age: 25 };

        StorageUtils.set(key, value);

        const storedValue = localStorage.getItem(key);
        const parsedValue = JSON.parse(storedValue);
        expect(parsedValue).toEqual(value);
      });
    });

    describe('remove', () => {
      it('should remove the key from localStorage', () => {
        const key = 'keyToRemove';
        const value = { name: 'Alex', age: 40 };
        localStorage.setItem(key, JSON.stringify(value));

        StorageUtils.remove(key);

        const storedValue = localStorage.getItem(key);
        expect(storedValue).toBeNull();
      });
    });
  });