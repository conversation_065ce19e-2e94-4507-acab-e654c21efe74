/**
 * @jest-environment jsdom
 */
import $ from "jquery";
import {
    loadImageWithFallback,
    renderImage,
} from "../../site/scripts/utils/default-image.util";

const defaultImage = '/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/logo-default.png';

describe('Image Fallback Loader', () => {
    let originalImageConstructor;

    beforeEach(() => {
        // Save original Image constructor
        originalImageConstructor = global.Image;

        // Prepare HTML
        document.body.innerHTML = `
      <img data-offer-check-image src="original.png" />
    `;
    });

    afterEach(() => {
        // Restore original Image constructor
        global.Image = originalImageConstructor;
    });

    test('loadImageWithFallback sets original src on successful load', async () => {
        // Mock Image to simulate success
        global.Image = function () {
            setTimeout(() => this.onload(), 0); // simulate async load
            return this;
        };

        const $img = $('[data-offer-check-image]');
        const attrMock = jest.fn();
        $img.attr = attrMock;

        loadImageWithFallback('valid.png', defaultImage, $img);

        await new Promise((resolve) => setTimeout(resolve, 10));
        expect(attrMock).toHaveBeenCalledWith('src', 'valid.png');
    });

    test('loadImageWithFallback sets default src on error', async () => {
        // Mock Image to simulate error
        global.Image = function () {
            setTimeout(() => this.onerror(), 0); // simulate async error
            return this;
        };

        const $img = $('[data-offer-check-image]');
        const attrMock = jest.fn();
        $img.attr = attrMock;

        loadImageWithFallback('broken.png', defaultImage, $img);

        await new Promise((resolve) => setTimeout(resolve, 10));
        expect(attrMock).toHaveBeenCalledWith('src', defaultImage);
    });

    test('renderImage runs loadImageWithFallback on all matching elements', async () => {
        // Mock Image that always succeeds
        global.Image = function () {
            setTimeout(() => this.onload(), 0);
            return this;
        };

        const $img = $('[data-offer-check-image]');
        const attrMock = jest.fn((key) => {
            if (key === 'src') {return 'mock-src.png';}
        });
        $img.attr = attrMock;

        renderImage();

        setTimeout(() => {
            expect(attrMock).toHaveBeenCalledWith('src'); // called inside renderImage
            expect(attrMock).toHaveBeenCalledWith('src', 'mock-src.png'); // set after load success
        }, 10);
    });
});
