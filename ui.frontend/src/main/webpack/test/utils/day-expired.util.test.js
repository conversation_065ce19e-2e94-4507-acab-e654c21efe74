import $ from 'jquery';
import { formatExpiryDate, renderDayExpired } from '../../site/scripts/utils/day-expired.util';

jest.useFakeTimers().setSystemTime(new Date('2025-04-28T10:00:00+07:00')); // Mock ngày hôm nay

describe('formatExpiryDate', () => {
    let $card;

    beforeEach(() => {
        document.body.innerHTML = `
      <div class="card" data-lang="vi" data-label-expired="Còn" data-label-expired-count-down="Hết hạn vào" data-day-text="ngày">
        <div class="progress-bar-container" style="display: none;">
          <div class="progress-bar" style="width: 0%;"></div>
        </div>
        <span></span>
      </div>
    `;
        $card = $('.card');
    });

    it('should return countdown time if days left <= 3', () => {
        const expiryDateStr = '29/04/2025';

        const result = formatExpiryDate(expiryDateStr, $card);

        expect(result).toContain('Còn 1 ngày');
        expect($card.find('.progress-bar').css('width')).not.toBe('0%');
        expect($card.find('.progress-bar-container').css('display')).toBe('block');
    });

    it('should return formatted date if days left > 3', () => {
        const expiryDateStr = '10/05/2025';

        const result = formatExpiryDate(expiryDateStr, $card);

        expect(result).toContain('Hết hạn vào');
        expect(result).toMatch(/\d{2} thg \d{1,2}, \d{4}/);
    });

    it('should remove card if expired', () => {
        const expiryDateStr = '27/04/2025';

        const parentRemoveMock = jest.fn();
        const cardWithMock = $('<div class="card"></div>');
        const card = $('<div></div>').appendTo(cardWithMock);

        // mock parent().remove()
        jest.spyOn(card, 'parents').mockReturnValue({
            remove: parentRemoveMock
        });

        formatExpiryDate(expiryDateStr, card);

        expect(parentRemoveMock).toHaveBeenCalled();
    });
});

describe('renderDayExpired', () => {
    beforeEach(() => {
        document.body.innerHTML = `
      <div data-promotion-card-expired="29/04/2025" class="card" data-lang="vi" data-label-expired="Còn" data-label-expired-count-down="Hết hạn vào" data-day-text="ngày">
        <div class="progress-bar-container" style="display: none;">
          <div class="progress-bar" style="width: 0%;"></div>
        </div>
        <span></span>
      </div>
    `;
    });

    it('should render expiry text inside span', () => {
        renderDayExpired();

        const text = $('.card span').text();
        expect(text).toContain('Còn 1 ngày');
    });
});
