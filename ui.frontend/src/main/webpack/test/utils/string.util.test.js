import {
  removeVietnameseAccent,
  replaceDoubleSpaceBySingleSpace,
  stringToSlug,
  formatString,
  getElementText,
  parseStringToJson,
} from "../../site/scripts/utils/string.util";

describe("removeVietnameseAccent", () => {
  it("should return an empty string if the input is empty", () => {
    const input = "";
    const result = removeVietnameseAccent(input);
    expect(result).toBe("");
  });

  it("should remove Vietnamese accents from the input string", () => {
    const input = "Hà Nội là thủ đô của Việt Nam";
    const expected = "ha noi la thu do cua viet nam";
    const result = removeVietnameseAccent(input);
    expect(result).toBe(expected);
  });

  it("should handle lowercase and uppercase characters", () => {
    const input = "Phở Bò";
    const expected = "pho bo";
    const result = removeVietnameseAccent(input);
    expect(result).toBe(expected);
  });

  it("should handle special characters and numbers", () => {
    const input = "Cà phê 365";
    const expected = "ca phe 365";
    const result = removeVietnameseAccent(input);
    expect(result).toBe(expected);
  });

  it("should not modify strings without Vietnamese accents", () => {
    const input = "Hello, World!";
    const expected = "hello, world!";
    const result = removeVietnameseAccent(input);
    expect(result).toBe(expected);
  });
});

describe("replaceDoubleSpaceBySingleSpace", () => {
  test("replaces double spaces with a single space", () => {
    const input = "Hello  world!   How  are   you?";
    const expectedOutput = "Hello world! How are you?";
    const result = replaceDoubleSpaceBySingleSpace(input);
    expect(result).toBe(expectedOutput);
  });

  test("does not modify string without double spaces", () => {
    const input = "Hello world! How are you?";
    const expectedOutput = "Hello world! How are you?";
    const result = replaceDoubleSpaceBySingleSpace(input);
    expect(result).toBe(expectedOutput);
  });

  test("returns an empty string for an empty input", () => {
    const input = "";
    const expectedOutput = "";
    const result = replaceDoubleSpaceBySingleSpace(input);
    expect(result).toBe(expectedOutput);
  });
});

describe("stringToSlug", () => {
  it("should convert a string to a valid slug", () => {
    const input = "This is a test string";
    const expectedOutput = "this_is_a_test_string";
    const result = stringToSlug(input);
    expect(result).toEqual(expectedOutput);
  });

  it("should handle special characters and spaces", () => {
    const input = "Hello, world! How are you?";
    const expectedOutput = "hello_world_how_are_you_";
    const result = stringToSlug(input);
    expect(result).toEqual(expectedOutput);
  });

  it("should handle Vietnamese accents", () => {
    const input = "Xin chào";
    const expectedOutput = "xin_chao";
    const result = stringToSlug(input);
    expect(result).toEqual(expectedOutput);
  });

  it("should handle consecutive spaces and special characters", () => {
    const input = "    This   is a  test___string!!   ";
    const expectedOutput = "this_is_a_test_string_";
    const result = stringToSlug(input);
    expect(result).toEqual(expectedOutput);
  });

  it("should return an empty string if input is empty", () => {
    const input = "";
    const expectedOutput = "";
    const result = stringToSlug(input);
    expect(result).toEqual(expectedOutput);
  });
});

describe("formatString", () => {
  test("Format string with special characters and whitespace", () => {
    const inputString = "HELLO WORLD  ";
    const expectedOutput = "HELLO WORLD";
    const result = formatString(inputString);
    expect(result).toEqual(expectedOutput);
  });

  test("Format empty string", () => {
    const inputString = "";
    const expectedOutput = "";
    const result = formatString(inputString);
    expect(result).toEqual(expectedOutput);
  });

  test("Format string with only special characters", () => {
    const inputString = "ĩøđ!@#$%^&*()_+";
    const expectedOutput = "iod!@#$%^&*()_+";
    const result = formatString(inputString);
    expect(result).toEqual(expectedOutput);
  });

  test("Format string with no special characters", () => {
    const inputString = "Hello123";
    const expectedOutput = "Hello123";
    const result = formatString(inputString);
    expect(result).toEqual(expectedOutput);
  });
});

// Mock element object
const mockElement = {
  text: () => "   Example Text   ",
};

describe("getElementText", () => {
  it("should return the formatted and trimmed text of the element", () => {
    const result = getElementText(mockElement);
    expect(result).toEqual("Example Text");
  });

  it("should return an empty string if the element is falsy", () => {
    const result = getElementText(null);
    expect(result).toEqual("");
  });
});

describe("parseStringToJson", () => {
  it("should parse a valid JSON string and return the corresponding object", () => {
    const jsonString = '{"name": "John", "age": 30}';
    const expectedObject = { name: "John", age: 30 };
    const result = parseStringToJson(jsonString);
    expect(result).toEqual(expectedObject);
  });

  it("should replace single quotes with double quotes and parse the modified string", () => {
    const jsonString = "{'name': 'Jane', 'age': 25}";
    const expectedObject = { name: "Jane", age: 25 };
    const result = parseStringToJson(jsonString);
    expect(result).toEqual(expectedObject);
  });

  it("should return undefined if the input value is not a string", () => {
    const nonStringValue = 123;
    const result = parseStringToJson(nonStringValue);
    expect(result).toBeUndefined();
  });
});
