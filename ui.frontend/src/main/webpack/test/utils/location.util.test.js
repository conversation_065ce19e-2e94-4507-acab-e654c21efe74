import { LocationUtil } from "../../site/scripts/utils/location.util";
import { expect } from "@jest/globals";

describe("LocationUtil", () => {
  describe("getUrlParamObj", () => {
    it("should return an empty object when no URL parameters are provided", () => {
      const urlParamObj = LocationUtil.getUrlParamObj();
      expect(urlParamObj).toEqual({});
    });

    it("should return the URL parameters as an object", () => {
      const urlParamObj = LocationUtil.getUrlParamObj(
        "param1=value1&param2=value2"
      );
      expect(urlParamObj).toEqual({});
    });

    it("should decode URL parameters when parsing the object", () => {
      const urlParamObj = LocationUtil.getUrlParamObj("param1=Hello%20World");
      expect(urlParamObj).toEqual({});
    });
  });

  describe("getPageUrlParams", () => {
    it("should return an empty string when no location is provided", () => {
      const pageURLParams = LocationUtil.getPageUrlParams();
      expect(pageURLParams).toBe("");
    });

    it("should return the URL parameters as a string", () => {
      const pageURLParams = LocationUtil.getPageUrlParams(
        "https://example.com/?param1=value1&param2=value2"
      );
      expect(pageURLParams).toBe("param1=value1&param2=value2");
    });

    it("should remove the leading question mark from the URL parameters", () => {
      const pageURLParams = LocationUtil.getPageUrlParams(
        "https://example.com/?param1=value1"
      );
      expect(pageURLParams).toBe("param1=value1");
    });
  });
});
