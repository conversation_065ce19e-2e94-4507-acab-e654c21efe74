import { ObjectUtil } from "../../site/scripts/utils/object.util";
import { expect } from "@jest/globals";

describe("ObjectUtil", () => {
  describe("deleteUndefinedField", () => {
    it("should delete undefined fields from the object", () => {
      const obj = {
        name: "<PERSON>",
        age: undefined,
        address: {
          street: "123 Main St",
          city: undefined,
        },
      };

      ObjectUtil.deleteUndefinedField(obj);

      expect(obj).toEqual({
        name: "<PERSON>",
        address: {
          street: "123 Main St",
        },
      });
    });

    it("should not modify the object if there are no undefined fields", () => {
      const obj = {
        name: "<PERSON>",
        age: 30,
        address: {
          street: "123 Main St",
          city: "New York",
        },
      };

      ObjectUtil.deleteUndefinedField(obj);

      expect(obj).toEqual({
        name: "<PERSON>",
        age: 30,
        address: {
          street: "123 Main St",
          city: "New York",
        },
      });
    });
  });
});
