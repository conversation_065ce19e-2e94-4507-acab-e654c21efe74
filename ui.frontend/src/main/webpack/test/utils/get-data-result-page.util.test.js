import {
  renderViewCard,
  getDataResultPage,
  renderTcbCard,
  renderPromotionCard,
} from '../../site/scripts/utils/get-data-result-page.util';
import { fetchData } from '../../site/scripts/offer-helper';
import { TcbPromotionCard } from '../../site/scripts/tcb-promotion-card';
import DOMPurify from 'dompurify';
import {
  TYPE_PROMOTION_SEARCH,
  TYPE_PROMOTION_FILTER,
} from '../../site/scripts/constants/offer-common';

jest.mock('../../site/scripts/offer-helper', () => ({
  fetchData: jest.fn(),
}));

jest.mock('../../site/scripts/tcb-promotion-card', () => ({
  TcbPromotionCard: jest.fn().mockImplementation(() => ({
    init: jest.fn(),
  })),
}));

jest.mock('dompurify', () => ({
  sanitize: jest.fn((html) => html),
}));

global.$ = jest.fn((selector) => ({
  length: selector === '.card-group' || selector === '.card-group.new-goup-card' ? 1 : 0,
  attr: jest.fn((attr) => (attr === 'class' ? 'card-group new-goup-card' : '')),
  includes: jest.fn().mockReturnValue(false),
  append: jest.fn(),
}));

describe('get-data-result-page.util.js', () => {
  const mockDataExpiry = {
    lang: 'en',
    dayText: 'days',
    labelExpired: 'Expired',
    labelExpiredCountDown: 'Soon',
  };

  const mockParams = {
    dataUrl: '/mock-url',
    classGroupCardName: '.card-group',
    dataExpiryDate: mockDataExpiry,
    isCardlabel: true,
    totalPromotionData: jest.fn(),
  };

  beforeEach(() => {
    document.body.innerHTML = '';
    jest.clearAllMocks();
  });

  describe('renderViewCard', () => {
    it('should do nothing if elements are not found', () => {
      document.getElementById = jest.fn().mockReturnValue(null);
      expect(() => renderViewCard(TYPE_PROMOTION_SEARCH)).not.toThrow();
      expect(() => renderViewCard(TYPE_PROMOTION_FILTER)).not.toThrow();
    });
  });

  describe('getDataResultPage', () => {
    it('should call fetchData and render cards when data is valid', async () => {
      const mockData = {
        total: 2,
        results: [
          {
            partner: ['Partner A'],
            products: ['Product A'],
            url: '/promo',
            thumbnail: '/img.jpg',
            description: 'Description A',
            expiryDate: '2025-06-01',
            favourite: 'true',
            isFavorite: true,
          },
        ],
      };

      fetchData.mockResolvedValue(mockData);
      const container = document.createElement('div');
      container.className = 'card-group new-goup-card';
      document.body.appendChild(container);

      await getDataResultPage(mockParams);

      expect(fetchData).toHaveBeenCalledWith('/mock-url');
      expect(mockParams.totalPromotionData).toHaveBeenCalledWith(2);
    });

    it('should not call totalPromotionData if it is not a function', async () => {
      fetchData.mockResolvedValue({ total: 3, results: [] });
      const params = {
        ...mockParams,
        totalPromotionData: null,
      };
      await getDataResultPage(params);
      expect(mockParams.totalPromotionData).not.toHaveBeenCalled();
    });

    it('should handle 0 promotions safely', async () => {
      fetchData.mockResolvedValue({ total: 0, results: [] });
      await getDataResultPage(mockParams);
      expect(mockParams.totalPromotionData).toHaveBeenCalledWith(0);
    });

    it('should not call renderTcbCard if results is empty', async () => {
      fetchData.mockResolvedValue({ total: 1, results: [] });
      await getDataResultPage(mockParams);
      expect(mockParams.totalPromotionData).toHaveBeenCalledWith(1);
    });
  });

  describe('renderTcbCard', () => {
    beforeEach(() => {
      global.$ = jest.fn((selector) => ({
        length: selector === '.card-group' || selector === '.card-group.new-goup-card' ? 1 : 0,
        attr: jest.fn((attr) =>
          attr === 'class' ? (selector.includes('new-goup-card') ? 'card-group new-goup-card' : 'card-group') : '',
        ),
        includes: jest.fn().mockImplementation((str) => str === 'new-goup-card'),
        append: jest.fn(),
      }));
    });

    it('should append sanitized cards and initialize TcbPromotionCard', () => {
      const promotions = [
        {
          partner: ['Partner X'],
          products: ['Prod X'],
          url: '/promo-x',
          thumbnail: '/img-x.jpg',
          description: 'Desc X',
          expiryDate: '2025-07-01',
          favourite: 'true',
          isFavorite: true,
        },
      ];

      renderTcbCard(promotions, '.card-group', mockDataExpiry, true);

      expect(DOMPurify.sanitize).toHaveBeenCalled();
      expect(TcbPromotionCard).toHaveBeenCalled();
    });

    it('should render without new-card class if not in classList', () => {
      global.$ = jest.fn((selector) => ({
        length: 1,
        attr: jest.fn((attr) => (attr === 'class' ? 'card-group' : '')),
        includes: jest.fn().mockReturnValue(false),
        append: jest.fn(),
      }));

      const promo = [
        {
          partner: ['P1'],
          products: [],
          url: '/',
          thumbnail: '',
          description: '',
          expiryDate: '',
          favourite: 'false',
          isFavorite: false,
        },
      ];

      renderTcbCard(promo, '.card-group', {}, true);
      expect(TcbPromotionCard).toHaveBeenCalled();
    });

    it('should handle empty promotions array', () => {
      renderTcbCard([], '.card-group', {}, false);
      expect(TcbPromotionCard).toHaveBeenCalled();
    });
  });

  describe('renderPromotionCard', () => {
    it('should render full card with label and newCard', () => {
      const item = {
        partner: ['Teco'],
        products: ['Banking'],
        url: '/promo-teco',
        thumbnail: '/img.jpg',
        description: 'Bank promo',
        expiryDate: '2025-07-01',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('Teco');
      expect(html).toContain('Banking');
      expect(html).toContain('new-card');
      expect(html).toContain('data-promotion-card-expired="01/07/2025"');
    });

    it('should render card without label and not newCard', () => {
      const item = {
        partner: ['SimpleCo'],
        products: [],
        url: '/promo-simple',
        thumbnail: '',
        description: '',
        expiryDate: '',
        favourite: 'false',
        isFavorite: false,
      };

      const html = renderPromotionCard(item, false, false, {});
      expect(html).toContain('SimpleCo');
      expect(html).not.toContain('card__label');
      expect(html).not.toContain('new-card');
    });

    it('should render without product labels when products array is empty', () => {
      const item = {
        partner: ['Partner'],
        products: [],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'false',
        isFavorite: false,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).not.toContain('group-card__label-mobile');
      expect(html).not.toContain('group-card__label');
    });

    it('should handle favourite="true" but isFavorite=false', () => {
      const item = {
        partner: ['Partner'],
        products: ['Product'],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'true',
        isFavorite: false,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('display-none');
    });

    it('should handle missing thumbnail', () => {
      const item = {
        partner: ['Partner'],
        products: ['Product'],
        url: '/promo',
        thumbnail: undefined,
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('src=""');
    });

    it('should handle missing partner', () => {
      const item = {
        partner: [],
        products: ['Product'],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('alt=""');
      expect(html).toContain('card__title"></div>');
    });

    it('should handle missing expiryDate', () => {
      const item = {
        partner: ['Partner'],
        products: ['Product'],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('data-promotion-card-expired=""');
    });
  });
});
