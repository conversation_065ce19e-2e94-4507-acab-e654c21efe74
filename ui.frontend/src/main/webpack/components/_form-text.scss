.cmp-form-text {
    position: relative;
    padding: 12px 0;
    >h3 {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-weight: 400;
        font-size: 16px;
        text-transform: unset;
        letter-spacing: initial; // added by adobe
        line-height: 24px;

        >span {
            color: var(--primary-red);
            padding: 0 12px 0 4px;
            line-height: normal;
        }
    }

    > input {
        height: 56px;
        border: 1px solid var(--secondary-light-grey-100);
        border-radius: 8px;
        padding: 16px;
        background-color: var(--primary-white);
        width: 100%;
        font-size: 16px;
        line-height: 24px;
        &::placeholder {
            color: #A5A5A5;
        }
      }

    >input:focus {
        outline: 4px solid #daecff;
    }
    .datepicker input:read-only:focus { // added by adobe
        outline: 4px solid #daecff;
      }
    .datepicker-section {
        position: relative;
        flex-direction: column;
        align-items: flex-start;
        .form-error-message {
            top: 100%;
        }
        .date-icon {
            top: 16px;
        }
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    input[type="number"] {
        -moz-appearance: textfield;
    }

    input[type="currency"]::-webkit-inner-spin-button,
    input[type="currency"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    input[type="currency"] {
        -moz-appearance: textfield;
    }

}
    //added by adobe
    #captchaError {
        display: none;
    }

.cmp-form-text__help-block {}

.cmp-form-text__textarea {
    border: 1px solid var(--secondary-light-grey-100);
    border-radius: 8px;
    padding: 16px;
    background-color: var(--primary-white)!important;
    width: 100%;
    font-size: 16px;
    line-height: 24px;
    resize: vertical;
}

.cmp-form-text__text {}
