.tcb-sectionContainer {
  max-width: 1920px;
  margin: auto;
}

.tcb-sectionContainer > .tcb-content-container,
.tcb-sectionContainer > .tcb-bgColor > .tcb-content-container,
.tcb-sectionContainer .tcb-bgImage > .tcb-content-container {
  max-width: 1312px;
  margin: 0 auto;
}

.tcb-full-width > .tcb-sectionContainer > .tcb-content-container,
.tcb-full-width > .tcb-sectionContainer > .tcb-bgColor > .tcb-content-container,
.tcb-full-width > .tcb-sectionContainer .tcb-bgImage > .tcb-content-container {
  max-width: 1920px;
}

.tcb-sectionContainer {
  .tcb-bgImage {
    width: 100%;
    min-height: 96px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;

    &.rounded {
      border-radius: 0.5rem;
    }

    .background-position-right & {
      background-position: right;
      @media screen and (max-width: 767px) {
        background-position: center;
      }
    }
  }

  .tcb-title {
    padding-bottom: 20px;
  }

  h2.tcb-title {
    font-size: 28px;
    font-weight: 500;
    line-height: 36px;
  }

  h3.tcb-title {
    font-size: 24px;
    font-weight: 300;
    line-height: 36px;
  }

  &__height-small {
    min-height: 14.25rem !important;
    .tcb-bgImage {
      min-height: 14.25rem !important;
    }
    .tcb-bgColor {
      min-height: 14.25rem !important;
    }
  }

  &__height-x-large {
    min-height: 26.375rem !important;
    .tcb-bgImage {
      min-height: 26.375rem !important;
    }
    .tcb-bgColor {
      min-height: 26.375rem !important;
    }
  }

  &__height-medium {
    min-height: 15.75rem !important;
    .tcb-bgImage {
      min-height: 15.75rem !important;
    }
    .tcb-bgColor {
      min-height: 15.75rem !important;
    }
  }

  &__height-large {
    min-height: 22.125rem !important;
    .tcb-bgImage {
      min-height: 22.125rem !important;
    }
    .tcb-bgColor {
      min-height: 22.125rem !important;
    }
  }
  &__height-large,
  &__height-default,
  &__height-medium,
  &__height-small,
  &__height-x-large {
    .tcb-teaser {
      padding-top: 0;
      &--larger-height {
        .tcb-teaser_body {
          min-height: unset;
        }
      }
    }
  }
  &__height-default {
    min-height: 287px !important;
    .tcb-bgImage {
      min-height: 287px !important;
    }
    .tcb-bgColor {
      min-height: 287px !important;
    }
  }

  &__border-round {
    border-radius: 0.5rem !important;
    overflow: hidden;
  }

  &__border-square {
    border-radius: 0 !important;
  }
}
.container-width {
  .tcb-sectionContainer {
    max-width: 82pc;

    .tcb-bgImage {
      min-height: 228px !important;
    }
  }

  &.tcb-sectionContainer__border-round {
    .tcb-bgColor,
    .tcb-bgImage {
      border-radius: 0.5rem !important;
      overflow: hidden;
    }
  }
}

.tcb-bgColor {
  min-height: 96px;
}

.tcb-sectionContainer .cmp-image {
  display: flex;
  flex-direction: column;

  .cmp-image__image.rounded {
    border-radius: 0.5rem;
  }
}

@media screen and (max-width: 767px) {
  .tcb-largeBgColor {
    background-color: unset !important;
  }
  .tcb-largeImage {
    background-image: unset !important;
  }
}

@media screen and (min-width: 768px) {
  .tcb-onlySmallImage {
    background-image: unset !important;
  }

  .tcb-smallImage {
    background-image: unset !important;
  }
}

@media screen and (min-width: 1200px) and (max-width: 1439px) {
  .tcb-sectionContainer > .tcb-content-container,
  .tcb-sectionContainer > .tcb-bgColor > .tcb-content-container,
  .tcb-sectionContainer .tcb-bgImage > .tcb-content-container,
  .container-width > .tcb-sectionContainer {
    margin: 0 64px;
  }

  .tcb-full-width > .tcb-sectionContainer > .tcb-content-container,
  .tcb-full-width > .tcb-sectionContainer > .tcb-bgColor > .tcb-content-container,
  .tcb-full-width > .tcb-sectionContainer .tcb-bgImage > .tcb-content-container {
    margin: 0;
  }

  .tcb-content-container:has(.news-filter.open, .filter-panel) {
    margin: 0;
  }
}

@media screen and (max-width: 1199px) {
  .tcb-sectionContainer > .tcb-content-container,
  .tcb-sectionContainer > .tcb-bgColor > .tcb-content-container,
  .tcb-sectionContainer .tcb-bgImage > .tcb-content-container,
  .container-width > .tcb-sectionContainer {
    margin: 0 calc(100% / 22.5);
  }

  .tcb-full-width > .tcb-sectionContainer > .tcb-content-container,
  .tcb-full-width > .tcb-sectionContainer > .tcb-bgColor > .tcb-content-container,
  .tcb-full-width > .tcb-sectionContainer .tcb-bgImage > .tcb-content-container {
    margin: 0;
  }

  .tcb-content-container:has(.news-filter.open, .filter-panel) {
    margin: 0 !important;
  }
}

@media (max-width: 768px) {
  .tcb-full-width-mobile > .tcb-sectionContainer > .tcb-content-container,
  .tcb-full-width-mobile > .tcb-sectionContainer > .tcb-bgColor,
  .tcb-full-width-mobile > .tcb-sectionContainer .tcb-bgImage {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
@media (max-width: 768px) {
  .mobile-large .tcb-content-container {
    padding-top: 188px;
  }
}
.faq-container .tcb-sectionContainer>.tcb-content-container {
  margin: 0;
  @media screen and (max-width: 1199px) {
      margin: 0 calc(100% / 22.5);
  }

}