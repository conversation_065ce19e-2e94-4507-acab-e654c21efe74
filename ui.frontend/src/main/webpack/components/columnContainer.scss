.column-wrapper {
    .column-container {
        display: flex;

        .vertical-align-middle & {
            align-items: center;
        }

        .vertical-align-top & {
            align-items: flex-start;
        }

        .form-modal & {
            gap: 24px;
        }

        .filter-panel & {
            max-width: 1312px;
        }

        .column-item {
            &.divider--vertical-right{
                border-right: 1px solid #000;
            }
        }

        .col-w20 {
            width: 20%;
        }

        .col-w30 {
            width: 30%;
        }

        .col-w33 {
            width: 33%;
        }

        .col-w40 {
            width: 40%;
        }

        .col-w50 {
            width: 50%;
        }

        .col-w60 {
            width: 60%;
        }

        .col-w70 {
            width: 70%;
        }

        .col-w80 {
            width: 80%;
        }
        .currencyconvertor {
            height: 100%;
            .container {
                height: 100%;
            }
        }
        // Change by Adobe for QuickAccess height in column control component
        .quickaccess {
            height: 100%;
            .quick-access {
                height: 100%;
                .quick-access-wrapper {
                    height: 100%;
                }
            }
        }
        
        .linktext {
            height: 100%;
        }
    }
    .column-container .column-item .currency-converter .priority-exchange__container.content-wrapper {
        padding-left: 0;
        padding-right: 0;
      }
}

@media only screen and (min-width: 768px) and (max-width: 1023px) {
	.column-wrapper {
		.column-container {
			flex-direction: column;
            .form-modal & {
                flex-direction: row;
            }
            &:not(.horizontal-tablet-view) > .column-item {
                padding-left:0 !important;
                padding-right:0 !important;
            }

            &:not(.horizontal-tablet-view) > :is(.col-w20, .col-w30, .col-w33, .col-w40, .col-w50, .col-w60, .col-w70, .col-w80) {
				width: auto !important;
			}

            &:not(.horizontal-tablet-view) > .divider--vertical-right {
                border-right: none !important;
            }
		}
		.column-reverse{
            flex-direction: column-reverse;
        }

        .column-container.horizontal-tablet-view  {
            flex-direction: row;
            &.column-reverse{
                flex-direction: row-reverse;
            }
        }
	}
}

@media only screen and (max-width: 767px) {
    .form-modal {
        .column-wrapper {
            .column-container {
                flex-direction: column;
                gap: 16px;
            }
        }
    }

    .column-wrapper {
        .column-container {
            flex-direction: column;
            .form-modal & {
                flex-direction: row;
            }
            .column-item {
                padding-left:0 !important;
                padding-right:0 !important;
            }

            .col-w20, .col-w30, .col-w33, .col-w40, .col-w50, .col-w60, .col-w70, .col-w80 {
                width: auto !important;
            }

            .divider--vertical-right {
                border-right: none !important;
            }
        }
        .column-reverse{
            flex-direction: column-reverse;
        }
    }
}
