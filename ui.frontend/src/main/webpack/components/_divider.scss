.divider--vertical {
    width: unset;
}

hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border-top: 1px solid var(--divider-color);
}

@media screen and (min-width: 429px) {
    .divider--vertical {
        width: 1px;
        background-color: black;
    }
}

.divider--dotted {
    position: relative;
    display: block;
    text-align: center;
    color: #A2A2A2;
    margin: 32px 0;
}

.divider--dotted::before {
    content: "";
    display: block;
    width: calc(50% - 30px);
    height: 1px;
    top: 50%;
    right: 0;
    position: absolute;
    background-image: linear-gradient(to right, var(--divider-color) 50%, rgba(255, 255, 255, 0) 0%);
    background-size: 10px 33px;
    background-repeat: repeat-x;
}

.divider--dotted::after {
    content: "";
    display: block;
    width: calc(50% - 30px);
    height: 1px;
    top: 50%;
    position: absolute;
    background-image: linear-gradient(to right, var(--divider-color) 50%, rgba(255, 255, 255, 0) 0%);
    background-size: 10px 33px;
    background-repeat: repeat-x;
}

.no-content.divider--dotted::before,

.no-content.divider--dotted::after {
    width: calc(50% - 1px);
}

.divider--dotted.no-content {
    padding-bottom: 10px;
}

/** divider dotted with content fixes : start **/
.divider--dotted {
    display: flex;
    align-items: center;

	&::after, &::before {
		position: static;
		flex: 1;
	}

	&::after{
	 margin-left: 20px;
	}

	&::before{
	 margin-right: 20px;
	}

}

.divider--dotted.no-content{
	display: block;

	&::after, &::before{
		position: absolute;
	}

	&::after{
	 margin-left:0px;
	}

	&::before{
	  margin-right:0px;
	}

}
/** divider dotted with content fixes : end **/
