.cta-button {
  border-radius: 8px;
  padding: 16px 24px;
  cursor: pointer;
  min-width: 200px;
  width: fit-content;
  transition: all 0.3s ease-in;
  text-decoration: none;
  align-items: center;
  display: flex;
  gap: 12px;
  justify-content: space-between;
  font-weight: 600;
  width: fit-content;
  &.has-desktop-width {
    @media screen and (min-width: 1025px) {
      width: var(--desktop-width) !important;
    }
  }
  &.hide-on-desktop { 
    display: none;
  }
  &.qr-button {
    padding: 0 0 0 16px;
    border-radius: 8px;
  }

  .button:not(:is(.cta-button--dark, .cta-button--light, .cta-button--grey, .cta-button--dark--white-border)) & {
    &:not(.qr-button):not(.cta-button--link):hover {
      background-color: var(--secondary-grey-60) !important;
      color: var(--primary-white) !important;

      img {
        filter: brightness(0) invert(1);
      }

      /* Configuration for dark mode */
      .dark & {
        background-color: var(--primary-white) !important;
        color: var(--secondary-grey-60) !important;

        img {
          filter: brightness(0) saturate(100%) invert(39%) sepia(0%) saturate(492%) hue-rotate(266deg) brightness(96%) contrast(98%);
        }
      }
    }
  }

  .cta-button--dark & {
    background: var(--primary-black);
    color: var(--primary-white);
    border: solid 1px var(--primary-black);
    &:not(.qr-button):hover {
      background: var(--primary-white) !important;
      color: var(--primary-black) !important;

      img {
        filter: brightness(0%);
      }
    }
  }
  .cta-button--light & {
    background: var(--primary-white);
    color: var(--primary-black);
    border: solid 1px var(--primary-black);
    &:not(.qr-button):hover {
      background: var(--primary-black) !important;
      color: var(--primary-white) !important;

      img {
        filter: brightness(0) invert(1);
      }
    }
  }
  .cta-button--grey & {
    position: relative;
    background: linear-gradient(180deg, #8d8175 -24.11%, #35322b 305.36%);
    z-index: 1;
    color: var(--primary-white);
    &:before {
      position: absolute;
      content: "";
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background-image: linear-gradient(to bottom, hsl(0, 0%, 100%), hsl(0, 0%, 100%));
      z-index: -1;
      transition: opacity 0.5s linear;
      opacity: 0;
      border-radius: 8px;
    }
    &:hover {
      &:before {
        opacity: 1;
      }
      color: var(--secondary-grey-60);
    }
  }
  .qr-button {
    border: none;
    padding: 0 12px;
    justify-content: center;
    .cmp-button__text {
      display: flex;
      align-items: center;
    }
  }
  .text-center & {
    justify-content: flex-end;
    .cmp-button__text {
      display: flex;
      flex: 1;
      justify-content: center;
    }
  }
  .cmp-button__icon {
    display: block;
  }
  :disabled &,
  [disabled] &,
  .disabled & {
    background: var(--cta-disabled);
    cursor: not-allowed;
    pointer-events: none;
    color: var(--secondary-mid-grey-100);
    .cmp-button__icon {
      filter: brightness(0) invert(1);
    }
  }

  .full-width & {
    width: 100%;
  }

  .form-modal & {
    .cmp-button__text {
      white-space: break-spaces;
    }
    width: 100%;
  }
}
// Change By Adobe
.cta-button.qr-button picture {
  width: 80px;
  height: 80px;
}

//Change by Adobe
.cta-button--link {
  border: none;
  padding: 0;
  min-width: fit-content;
  &:not(.qr-button):hover {
    text-decoration: underline;
  }
}

.cta-button--dark--white-border .cta-button {
  background: var(--primary-black);
  color: var(--primary-white);
  border: solid 1px var(--primary-white);
  &:not(.qr-button):hover {
    background: var(--secondary-grey-60);
  }
}

.qr-button .cmp-button__icon {
  width: 80px;
  height: auto;
}

// Button Alignment
.btn {
  &--center {
    display: flex;
    justify-content: center;
  }
  &--right {
    display: flex;
    justify-content: flex-end;
  }
}

// Change By Adobe
@media screen and (max-width: 767px) {
  .cta-button {
    white-space: unset;
    width: 100%;
    &.has-mobile-width {
      width: var(--mobile-width) !important;
    }
    .form-modal & {
      margin: 0;
    }
    &.qr-button {
      display: none;
    }
    &.hide-on-desktop { 
      display: flex;
    }
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .cta-button {
    &.has-tablet-width {
      width: var(--tablet-width) !important;
    }
  }
}

.button:has(.has-mobile-width) {
  width: 100%;
}

.button:has(.has-tablet-width) {
  width: 100%;
}

.button:has(.has-desktop-width) {
  width: 100%;
}